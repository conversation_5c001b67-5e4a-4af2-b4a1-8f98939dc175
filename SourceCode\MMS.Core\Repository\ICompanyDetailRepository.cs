﻿using System.Collections.Generic;
using System.Threading.Tasks;
using MMS.Core.Entities;
using MMS.Infrastructure.Commons;
using MMS.Model.ApiModelRequest;
using MMS.Model.ApiModelResponse;
using MMS.Model.ApiModelResponse.Company;

namespace MMS.Core.Repository
{
    public interface ICompanyDetailRepository : IBaseRepository<CompanyDetails>
    {
        /// <summary>
        /// Get Companies master
        /// </summary>
        /// <param name="skip"></param>
        /// <param name="take"></param>
        /// <param name="searchKey"></param>
        /// <returns></returns>
        Task<IList<CompanyDetails>> GetCompaniesMasterAsync(int skip, int take, string searchKey);

        /// <summary>
        /// Get Companies master
        /// </summary>
        /// <param name="searchKey">Search key for filtering companies</param>
        /// <param name="pagingParameter">Paging parameters</param>
        /// <returns>Paged list of companies</returns>
        Task<PagingResponse<CompanyListResponse>> GetCompaniesMasterAsync(string searchKey, PagingParameter pagingParameter);

        /// <summary>
        /// Get sub companies by Parent id
        /// </summary>
        /// <param name="parentId"></param>
        /// <returns></returns>
        Task<IList<CompanyDetails>> GetSubCompaniesByParentIdAsync(int parentId);

        /// <summary>
        /// Asynchronously retrieves a list of all master companies (companies without a parent).
        /// </summary>
        /// <returns>
        /// A task containing a list of <see cref="CompanyDetails"/> objects, ordered by <c>OrderIndex</c>.
        /// </returns>
        Task<List<CompanyDetails>> GetAllMasterCompanyAsync();

        /// <summary>
        /// Asynchronously retrieves a list of all master companies (companies without a parent).
        /// </summary>
        /// <returns>
        /// A task containing a list of <see cref="CompanyDetails"/> objects, ordered by <c>OrderIndex</c>.
        /// </returns>
        Task<List<CompanyDetails>> GetAllCompanyExcludingCurrentAsync(List<int> excludingCompanyIds);

        /// <summary>
        /// check deleted company master in hierarchy
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        Task<HashSet<int>> GetDeletedCompanyIdsInHierarchy(List<int> ids);

        /// <summary>
        /// Get company info by id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<CompanyInfoResponse> GetCompanyInfoByIdAsync(int id);

        /// <summary>
        /// Get users for select
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="selectedIds"></param>
        /// <param name="searchKey"></param>
        /// <param name="filterType"></param>
        /// <param name="pagingParameter"></param>
        /// <returns></returns>
        Task<PagingResponse<DataItemResponse>> GetSourceForSelectUserAsync(int companyId, List<int> selectedIds, string searchKey, SelectedFilterType filterType, PagingParameter pagingParameter);

        /// <summary>
        /// Retrieves a paged list of companies that can be selected, filtering by the provided parameters.
        /// </summary>
        /// <param name="selectedIds">List of company IDs that are already selected</param>
        /// <param name="selectRemoveFormParameter">Parameters for pagination, filtering and sorting</param>
        /// <returns>A paged response containing company data items</returns>
        Task<PagingResponse<DataItemResponse>> GetSourceForSelectCompanyAsync(List<int> ignoreItems, List<int> selectedIds, SelectRemoveFormParameter pagingParameter);
        
        /// <summary>
        /// Get all companies from Linkly
        /// </summary>
        /// <returns>List of companies</returns>
        Task<IList<CompanyDetails>> GetLinklyCompaniesAsync();
    }
}
