import { Spin } from 'antd';
import './index.scss';
import { useEffect } from 'react';
import { useState } from 'react';

const data = [
  {
    version: '8.8.0',
    total: 9000,
    groups: [
      { color: '#C04664', value: 1000 },
      { color: '#2F7AFD', value: 4000 },
      { color: '#4D6E8C', value: 1500 },
      { color: '#FC9517', value: 2500 },
    ],
    count: 10,
  },
  {
    version: '8.6.0',
    total: 7000,
    groups: [
      { color: '#C04664', value: 2500 },
      { color: '#2F7AFD', value: 700 },
      { color: '#4D6E8C', value: 100 },
      { color: '#FC9517', value: 1800 },
    ],
    count: 8,
  },
  {
    version: '9.0.2',
    total: 10020,
    groups: [{ color: '#2F7AFD', value: 3000 }],
    count: 2,
  },
  {
    version: '9.5.0',
    total: 15000,
    groups: [{ color: '#C04664', value: 1500 }],
    count: 1,
  },
  {
    version: '9.5.0',
    total: 15000,
    groups: [{ color: '#C04664', value: 1500 }],
    count: 1,
  },
  {
    version: '9.5.0',
    total: 15000,
    groups: [{ color: '#C04664', value: 1500 }],
    count: 1,
  },
  {
    version: '9.5.0',
    total: 15000,
    groups: [{ color: '#C04664', value: 1500 }],
    count: 1,
  },
  {
    version: '9.5.0',
    total: 15000,
    groups: [{ color: '#C04664', value: 1500 }],
    count: 1,
  },
];

function StackedBarChart() {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <>
      {isLoading && <Spin className="loading-container" />}
      {!isLoading && (
        <div className="store-client-distribution">
          {data.map(({ version, total, groups, count }) => {
            const sumGroups = groups.reduce((sum, g) => sum + g.value, 0);
            const leftover = total - sumGroups;

            return (
              <div key={version} className="store-client-distribution__row">
                <div className="store-client-distribution__header">
                  <div>
                    {version} ({total})
                  </div>
                  <div>{count}</div>
                </div>

                <div className="store-client-distribution__bar-container">
                  {groups.map(({ color, value }, idx) => {
                    const widthPercent = (value / total) * 100;
                    return (
                      <div
                        key={idx}
                        className="store-client-distribution__bar-segment"
                        style={{
                          width: `${widthPercent}%`,
                          backgroundColor: color,
                        }}
                        title={`${value} clients`}
                      />
                    );
                  })}
                  {leftover > 0 && (
                    <div
                      className="store-client-distribution__bar-segment store-client-distribution__bar-segment--leftover"
                      style={{
                        width: `${(leftover / total) * 100}%`,
                      }}
                      title={`${leftover} clients (leftover)`}
                    />
                  )}
                </div>
              </div>
            );
          })}
        </div>
      )}
    </>
  );
}

export default StackedBarChart;
