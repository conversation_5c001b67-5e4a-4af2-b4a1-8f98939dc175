using Autofac.Extensions.DependencyInjection;
using Microsoft.EntityFrameworkCore;
using MMS.Api.ApiModels.IDM;
using MMS.Api.Middleware;
using MMS.Api.Security;
using MMS.Api.Utils;
using MMS.Core;
using MMS.Core.dbContext;
using MMS.Core.Extensions;
using MMS.Core.HealthChecks;
using MMS.Core.Middlewares;
using MMS.Core.Models;
using MMS.Core.Services;
using MMS.Core.Services.Base;
using MMS.Core.Services.Impl;
using MMS.Core.Utils;
using MMS.Infrastructure.Commons;
using MMS.Infrastructure.Extensions;
using MMS.Infrastructure.Files;
using MMS.Infrastructure.HealthChecks;
using MMS.Infrastructure.Logging;
using MMS.Infrastructure.Middlewares;
using Serilog;

namespace MMS.Api
{
    public static class Program
    {
        public static void Main(string[] args)
        {
            // Configure bootstrap logger
            LoggingConfig.CreateBootstrapLogger();

            try
            {
                var builder = WebApplication.CreateBuilder(args);

                // Configure Serilog
                LoggingConfig.ConfigureSerilogFromConfiguration(builder.Configuration);

                builder.Host.UseServiceProviderFactory(new AutofacServiceProviderFactory());

                // Add services to the container.

                builder.Services.AddFileServices();
                builder.Services.AddHealthCheckCustom();

                // Health check within mms.core
                var tags = new List<string> { InfrastructureConstant.HealthCheckDependencyTag };
                builder.Services.AddHealthChecks().AddCheck<HealthCheckSql>("Sql Dependency", tags: tags);

                builder.Services.AddRouting(options => options.LowercaseUrls = true);
                builder.Services.AddControllers();
                // Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
                builder.Services.AddEndpointsApiExplorer();
                builder.Services.AddSwaggerGen();

                builder.Services.AddJwtAuthentication(builder.Configuration);
                builder.Services.RegisterMMSCoreServices();

                builder.Services.AddScoped<ITokenClaimReader, TokenClaimReader>();
                builder.Services.AddHttpContextAccessor(); // Required for IHttpContextAccessor

                // Register HttpClient for WebApiCaller
                builder.Services.AddHttpClient<Core.Utils.WebApiCaller>();
                builder.Services.AddScoped<Core.Utils.WebApiCaller>();

                builder.Services.Configure<HostDomainSetting>(builder.Configuration.GetSection("HostDomainApiSetting"));

                builder.Services.AddAutoMapper(typeof(MappingProfile));

                // Add Hangfire services
                //builder.Services.AddScoped<HangfireAuthorizationFilterCore>();
                //builder.Services.CoreConfigureHangfire();

                // Register LinklyFake services
                builder.Services.AddLinklyFakeServices(builder.Configuration);

                // Register terminal source configuration (used for both terminals and companies)
                builder.Services.AddTerminalSourceConfig(builder.Configuration);

                builder.Services.AddCors(options =>
                {
                    options.AddPolicy("AllowMMSClient", policy =>
                    {
                        policy.AllowAnyOrigin()
                              .AllowAnyMethod()
                              .AllowAnyHeader();
                    });
                });

                var app = builder.Build();

                AppDependencyResolver.Init(app.Services);

                // Apply database migrations
                using (var scope = app.Services.CreateScope())
                {
                    var db = scope.ServiceProvider.GetRequiredService<MMSContext>();
                    db.Database.Migrate();
                    Log.Information("Database migrations applied successfully");

                    // Initialize API permissions
                    var pagePermissionService = scope.ServiceProvider.GetRequiredService<IPagePermissionService>();
                    ApiPermissionInitializer.InitApiPermissions(pagePermissionService);
                    Log.Information("API permissions initialized successfully");
                }

                // Because set is developement the release env is lot of impact so we leave it here for Nong can use.
                app.UseSwagger();
                app.UseSwaggerUI();
                app.UseCors("AllowMMSClient");

                // Configure the HTTP request pipeline.
                if (app.Environment.IsDevelopment())
                {
                    app.UseSwagger();
                    app.UseSwaggerUI();
                }

                app.UseHttpsRedirection();

                // Configure static files and ensure directories exist
                var rootPath = Directory.GetCurrentDirectory();
                // Ensure specific subdirectories exist
                DirectoryHelper.EnsureUserDataSubdirectoryExists(rootPath, "PartialSync");
                DirectoryHelper.EnsureUserDataSubdirectoryExists(rootPath, "TerminaMasterSetupUpgradeRecord");
                DirectoryHelper.EnsureUserDataSubdirectoryExists(rootPath, "Paxstore");
                DirectoryHelper.EnsureUserDataSubdirectoryExists(rootPath, "Cloud/User");

                // Configure static files using our extension method
                // Store the wwwroot path for use in other components
                var wwwrootPath = app.ConfigureStaticFiles(rootPath);

                // Add wwwroot path to configuration for access in other components
                builder.Configuration[FileConstant.WEB_ROOT_PATH_KEY] = wwwrootPath;
                Log.Information("WebRootPath configured in configuration: {Path}", wwwrootPath);

                app.UseAuthentication();

                app.UseMiddleware<RevokedTokenMiddleware>();

                // Add API permission middleware
                app.UseApiAuthorityMiddleware();

                app.UseApiErrorHandling();

                app.UseAuthorization();

                app.MapControllers();

                app.MapHealthCheckCustom();

                //app.ConfigHangfireDashboard(app.Services);

                app.Run();
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "Application terminated unexpectedly");
            }
            finally
            {
                Log.CloseAndFlush();
            }
        }
    }
}
