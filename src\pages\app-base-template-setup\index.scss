.app-template-setup {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
  height: 100%;

  .discard-button {
    background: var(--function-5);
    color: var(--neutral-2);

    &:hover {
      background: var(--function-5) !important;
      color: var(--neutral-2) !important;
      border-color: var(--function-5) !important;
    }
  }

  .app-info-section {
    padding-top: 16px;

    .form-row {
      display: flex;
      gap: 16px;
      margin-bottom: 32px;
    }

    .form-item-flex {
      width: 33%;
      margin-bottom: 0;
      height: 70px;
    }

    .ant-input {
      &:disabled {
        background: var(--neutral-neutrals-2-day, #f5f6fa);
        border: none;
        box-shadow: none;
      }
    }
  }

  .ant-card-body {
    padding: 0;
  }

  .ant-divider {
    margin: 0;
  }

  .header-app-base-template-setup {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
  }

  .header-app-base-template-setup h1 {
    font-family: 'Poppins', sans-serif;
    font-size: 14px;
    font-weight: 600;
    margin: 0;
  }

  .button-group {
    display: flex;
    height: 32px;
    padding: 8px 16px;
    justify-content: center;
    align-items: center;
    gap: 16px;
  }

  .save-button {
    background: #00a26d;
  }

  .terminal-setup-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: calc(100vh - 320px);
  }
}
