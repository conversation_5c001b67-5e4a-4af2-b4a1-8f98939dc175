import { BellOutlined, MoreOutlined, UserOutlined } from '@ant-design/icons';
import { Avatar, Breadcrumb, Dropdown, MenuProps, Space } from 'antd';
import './index.scss';
import FontIcon from '../../shared/icons/font-icon';
import { UserProfileModel } from '../../../models/user.model';
import LocalUtils from '../../../utils/local.utils';
import { AuthApi } from '../../../apis/auth.api';
import { ROUTE_PATHS } from '../../../constants/router.constants';
import { useTranslation } from 'react-i18next';
import { useBreadcrumb } from '../../../hooks/useBreadcrumb';
import { useMemo } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { CompanyItemType } from '../../../constants/app.enums';
import { useRecoilState, useRecoilValue } from 'recoil';
import { companyTreeDataState } from '../../../states/companyTree';
import { layoutCollapseState } from '../../../states/layout';

enum UserMenuItemKey {
  None,
  Profile,
  Settings,
  Logout,
}

const AppHeader: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const userInfor: UserProfileModel = LocalUtils.getUserInfo();
  const [layoutCollapse, setLayoutCollapse] =
    useRecoilState(layoutCollapseState);
  const { breadcrumbs } = useBreadcrumb();
  const companyTreeData = useRecoilValue(companyTreeDataState);
  const { lastSelectedItem, parentItem } = companyTreeData;

  // Toggle company tree collapse state
  const toggleCollapseCompanyTree = () => {
    setLayoutCollapse({
      ...layoutCollapse,
      companyTreeCollapsed: !layoutCollapse.companyTreeCollapsed,
    });
  };

  // Get the page title from the last breadcrumb item (current page)
  const pageTitle = useMemo(() => {
    if (breadcrumbs.length > 0) {
      // Get the last item in the breadcrumbs array as it represents the current page
      const lastBreadcrumb = breadcrumbs[breadcrumbs.length - 1];
      return lastBreadcrumb.title as string;
    }
    return 'Dashboard';
  }, [breadcrumbs]);

  // Function to get company name based on selected item
  const getCompanyName = () => {
    console.log(companyTreeData);
    let displayName = '';
    if (!lastSelectedItem) {
      return '';
    }

    if (lastSelectedItem.itemType === CompanyItemType.Company) {
      displayName = lastSelectedItem.label;
    } else {
      displayName = parentItem?.label || '';
    }

    return displayName;
  };

  // Menu cho user dropdown
  const items: MenuProps['items'] = [
    {
      label: 'Profile',
      key: UserMenuItemKey.Profile,
      icon: <FontIcon size={14} className="icon-manager" />,
    },
    {
      label: 'Settings',
      key: UserMenuItemKey.Settings,
      icon: <FontIcon size={14} className="icon-setting" />,
    },
    {
      type: 'divider',
    },
    {
      label: 'Logout',
      key: UserMenuItemKey.Logout,
      icon: <FontIcon size={14} className="icon-logout" />,
    },
  ];

  // Convert breadcrumbs to Ant Design format but use React Router for navigation
  const breadcrumbItems = useMemo(() => {
    return breadcrumbs.map((item) => ({
      title: item.path ? (
        <Link to={item.path} state={item.state}>
          {item.title}
        </Link>
      ) : item.href ? (
        <a href={item.href}>{item.title}</a>
      ) : (
        item.title
      ),
      onClick: item.path
        ? () => navigate(item.path as string, { state: item.state })
        : undefined,
    }));
  }, [breadcrumbs, navigate]);

  const handleMenuClick: MenuProps['onClick'] = (e) => {
    if (e.key === '3') {
      AuthApi.signOut();
      navigate(ROUTE_PATHS.SignIn);
    }
  };

  const menuProps = {
    items,
    onClick: handleMenuClick,
  };

  return (
    <div className="header-content">
      {layoutCollapse.companyTreeCollapsed && (
        <div className="company-collapse">
          <Space align="center">
            <FontIcon
              size={16}
              className="icon-up"
              onClick={toggleCollapseCompanyTree}
            />
            <div>
              <span className="body-2-bold">Company</span>
              <br />
              <span
                title={getCompanyName()}
                className="body-2-regular text-overflow"
              >
                {getCompanyName()}
              </span>
            </div>
          </Space>

          <div className="company-collapse-menu">
            <FontIcon size={20} className="icon-search" />
            <FontIcon size={20} className="icon-hamburger-menu" />
          </div>
        </div>
      )}

      <div className="app-header" style={{ flexGrow: 1 }}>
        <div className="header-left">
          <span className="h5-bold">{pageTitle}</span>
          <Breadcrumb items={breadcrumbItems} />
        </div>
        <div className="header-right">
          <Space>
            <FontIcon
              size={20}
              className="icon-notification"
              hoverEffect={true}
            />

            <Avatar icon={<UserOutlined />} src={userInfor.profilePictureUrl} />
            <div className="user-info">
              <span className="user-name">{userInfor.fullName}</span>
              <span className="user-role">
                {userInfor.isAdmin ? 'Admin' : 'Normal User'}
              </span>
            </div>
            <Dropdown menu={menuProps} trigger={['click']}>
              <FontIcon
                size={20}
                className="icon-meatballs-menu-2"
                hoverEffect={true}
              />
            </Dropdown>
          </Space>
        </div>
      </div>
    </div>
  );
};

export default AppHeader;
