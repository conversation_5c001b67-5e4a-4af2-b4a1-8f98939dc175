using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

using MMS.Core.CoreUTI;
using MMS.Core.Entities.EntityInterface;
using MMS.Core.CoreUTI.Enum;

namespace MMS.Core.Entities
{
    public class CompanyDetails : BaseEntitySort, IViewItem
    {
        public string Name
        {
            get
            {
                if(!String.IsNullOrEmpty(CompanyTradingName)){
                    return CompanyTradingName;
                }
                else{
                     return CompanyRegisteredName;
                }
            }
        }

        public string LinklyId { get; set; }

        public int CompanyIdNumber { get; set; }
        [MultiLanguageSupport]
        public string CompanyRegisteredName { get; set; }
        public int TaxID { get; set; }
        [MultiLanguageSupport]
        public string CompanyTradingName { get; set; }
        public string CompanyPhoto { get; set; }

        public DateTime? FormationDate { get; set; }
        public DateTime? StartedDoingBusiness { get; set; }
        public DateTime? FinishedDoingBusiness { get; set; }
        public string BusinessFormationDate { get; set; }
        public string TurnoverPerAnnum { get; set; }
        public bool ServiceSupplier { get; set; }
        public string CompanyRegistration { get; set; }

        public string AfterHoursPhone { get; set; }
        public string AfterHoursExtension { get; set; }
        public bool UserChangesAllowed { get; set; }
        public int? UserMasterId { get; set; }
        public int? MerchantMasterId { get; set; }
        public string Email { get; set; }
        public string PhoneNumber { get; set; }
        public string Address { get; set; }

        [NotMapped]
        public virtual IList<MerchantMaster> MerchantCompanyDetails { get; set; }



        [NotMapped]
        public virtual IList<MerchantMaster> MerchantMasters { get; set; }
        [NotMapped]
        public virtual IList<CompanyDetails> CompanyDetailses { get; set; }
        [NotMapped]
        public virtual IList<CompanyDetails> CompanyDetailsesOf { get; set; }
        [NotMapped]
        public virtual IList<BusinessTypeMaster> BusinessTypeMasters { get; set; }
        public virtual IList<UsersCompany> UsersCompany { get; set; }

        public int? PreferredLanguageId { get; set; }
        [ForeignKey("PreferredLanguageId")]
        public virtual LanguageMaster PreferredLanguage { get; set; }

        public bool UseSortBusinessType { get; set; }
        public bool UseSortIndustryType { get; set; }
        public bool UseSortAccessStatus { get; set; }
        public bool UseSortUserAccess { get; set; }

        [InverseProperty(nameof(CompanyDeviceSetupMapping.CompanyDetails))]
        public virtual IList<CompanyDeviceSetupMapping> CompanyDeviceSetupMappings { get; set; }

        [InverseProperty(nameof(CompanyDeviceSetupMapping.ParentCompanyDetails))]
        public virtual IList<CompanyDeviceSetupMapping> ParentCompanyDeviceSetupMappings { get; set; }

        [InverseProperty(nameof(CompanySubsidiaryMapping.CompanyDetails))]
        public virtual IList<CompanySubsidiaryMapping> CompanySubsidiaryMappings { get; set; }

        [InverseProperty(nameof(CompanySubsidiaryMapping.Subsidiary))]
        public virtual IList<CompanySubsidiaryMapping> SubsidiaryCompanyMappings { get; set; }

        [InverseProperty(nameof(CompanyGroupMapping.Company))]
        public virtual IList<CompanyGroupMapping> CompanyGroupMappings { get; set; }

        [InverseProperty(nameof(CompanyGroupMapping.PartnerAccess))]
        public virtual IList<CompanyGroupMapping> PartnerAccessGroupMappings { get; set; }

        public int? ParentId { get; set; }

        [ForeignKey(nameof(ParentId))]
        public virtual CompanyDetails Parent { get; set; }

        public bool HasSubColumn { get; set; }

        public CompanyDetails()
        {
            StartedDoingBusiness= DateTime.Now;
            FinishedDoingBusiness = DateTime.Now;
        }
    }

    public class CompanyDetailsMap : IEntityTypeConfiguration<CompanyDetails>
    {
        public void Configure(EntityTypeBuilder<CompanyDetails> builder)
        {
          builder.ToTable(Constants.CompanyDetails);
          builder.HasKey(reg => reg.Id);
        }
    }
}
