using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MMS.Api.ApiModels.LocationLevelModels;
using MMS.Api.Filters;
using MMS.Api.Security;
using MMS.Api.Utils;
using MMS.Core.CoreUTI.Enum;
using MMS.Core.Entities;
using MMS.Core.Entities.Locations;
using MMS.Core.Extensions;
using MMS.Core.Services;
using MMS.Core.Services.Locations;
using MMS.Model.ApiModelRequest;

namespace MMS.Api.Controllers
{
    [Authorize]
    [ApiController]
    [Route("location")]
    public class LocationController : ControllerBase
    {
        private readonly ILocationItemService _locationItemService;
        private readonly IApplicationForDeviceConfigService _applicationForDeviceConfigService;
        private readonly ILanguageExecuteService _languageExecuteService;
        private readonly ILocationService _locationService;
        private readonly ICloudSecurityAccessMappingService _cloudSecurityAccessMappingService;
        private readonly IMerchantRestrictDeviceService _merchantRestrictDeviceService;
        private readonly IApplicationUsedService _applicationUsedService;
        private readonly IUserMasterService _userMasterService;
        private readonly IMerchantLocationsService _merchantLocationsService;
        private readonly IMerchantMasterService _merchantMasterService;

        public LocationController(ILocationItemService locationItemService,
            IApplicationForDeviceConfigService applicationForDeviceConfigService,
            ILanguageExecuteService languageExecuteService,
            ILocationService locationService,
            ICloudSecurityAccessMappingService cloudSecurityAccessMappingService,
            IMerchantRestrictDeviceService merchantRestrictDeviceService,
            IApplicationUsedService applicationUsedService,
            IUserMasterService userMasterService,
            IMerchantLocationsService merchantLocationsService,
            IMerchantMasterService merchantMasterService)
        {
            _locationItemService = locationItemService;
            _applicationForDeviceConfigService = applicationForDeviceConfigService;
            _languageExecuteService = languageExecuteService;
            _locationService = locationService;
            _cloudSecurityAccessMappingService = cloudSecurityAccessMappingService;
            _merchantRestrictDeviceService = merchantRestrictDeviceService;
            _applicationUsedService = applicationUsedService;
            _userMasterService = userMasterService;
            _merchantLocationsService = merchantLocationsService;
            _merchantMasterService = merchantMasterService;
        }

        [HttpGet("reload-all-location-level")]
        [ApiAuthority(
            Function = ApiSecure.ApiLocationManagement,
            Action = ApiSecure.ViewAction,
            IsRoot = true,
            ActionType = ApiSecure.FullAction)]
        [ApiSecurityCustomName("Location Management")]
        public async Task<ActionResult> ReloadAllLocationLevel([FromQuery] ReloadAllLocationRequestModel request)
        {
            //var securityLocationFilter = WebUtils.GetSession<LocationSecuritySession>(SecuritySessionConst.LocationSecuritySession) ?? new LocationSecuritySession(); // Location Security Filter
            var securityLocationFilter = new LocationSecuritySession(); // Location Security Filter

            if (request.LastSelectedLocations == null)
            {
                request.LastSelectedLocations = new Dictionary<int, int>();
            }

            if (request.LocationItemId != 0)
            {
                request.LastSelectedLocations = _locationItemService.GetLocationItemsSelectedPre(request.LocationItemId);
            }

            var locationLevelViewModel = new LocationLevelResponseModel(request.LastSelectedLocations);
            var kvPreviousLocation = new KeyValuePair<int, int>();
            var kvNearestLocation = new KeyValuePair<int, int>();
            var kvLocation = new KeyValuePair<int, int>();
            var kvDevice = new KeyValuePair<int, int>();
            var allChainOptionSelected = false;

            if (!securityLocationFilter.IsViewGlobal)
            {
                foreach (var item in request.LastSelectedLocations.Where(kvp => !securityLocationFilter.ViewLocationItemIdsAllow.Contains(kvp.Value)).ToList())
                {
                    request.LastSelectedLocations.Remove(item.Key);
                }
            }
            else
            {
                foreach (var item in request.LastSelectedLocations.Where(kvp => securityLocationFilter.ViewLocationItemIdsRestricted.Contains(kvp.Value)).ToList())
                {
                    request.LastSelectedLocations.Remove(item.Key);
                }
            }

            if (request.LastSelectedLocations.Values.Any())
            {
                var highestLastSelectedLocationItems = _locationItemService.GetHighestLevel(request.LastSelectedLocations.Values.ToList());
                var highestLocationItemAllowed = _locationItemService.GetHighestLevel(securityLocationFilter.ViewLocationItemIdsAllow);
                if (
                    (highestLastSelectedLocationItems != null && highestLocationItemAllowed != null && highestLocationItemAllowed.Location.LocationLevel < highestLastSelectedLocationItems.Location.LocationLevel)
                    ||
                    (highestLastSelectedLocationItems != null && securityLocationFilter.IsViewGlobal && MMS.Core.CoreUTI.Enum.LocationLevel.Region < highestLastSelectedLocationItems.Location.LocationLevel) // Can view at every location.
                    )
                {
                    request.LastSelectedLocations = new Dictionary<int, int>(); // Clear last selected.
                }
            }

            var allSelectedLocations = _locationItemService.GetAllSelectedLocations(request.LastSelectedLocations.Select(p => p.Key).ToList()).ToDictionary(p => p.Id, p => p);

            Func<LocationItem, bool> notIncludeDeleted = p => p.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD;

            for (int i = 0; i < request.LastSelectedLocations.Count; i++)
            {
                var locationValue = request.LastSelectedLocations.ElementAt(i);

                //Refactor this loop to reduce query time
                var locationGlobal = new LocationGlobalDisplay();
                if (locationValue.Key == 99)
                {
                    continue;
                }

                Location location = null;

                allSelectedLocations.TryGetValue(locationValue.Key, out location);

                if (location == null)
                {
                    continue;
                }

                switch (location.LocationLevel)
                {
                    case MMS.Core.CoreUTI.Enum.LocationLevel.Locations:
                        kvLocation = locationValue;
                        break;
                    case MMS.Core.CoreUTI.Enum.LocationLevel.DevicesUsed:
                        kvDevice = locationValue;
                        break;
                }

                if (allChainOptionSelected && location.LocationLevel > MMS.Core.CoreUTI.Enum.LocationLevel.Chain && location.LocationLevel < MMS.Core.CoreUTI.Enum.LocationLevel.Locations)
                {
                    continue;
                }

                //Check if All is selected for Chain
                allChainOptionSelected = location.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.Chain && locationValue.Value == 0;

                var locationItems = Enumerable.Empty<LocationItem>();
                if (location.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.Region)
                {
                    locationItems = location.LocationItems.Where(p => p.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD);
                }
                if (location.Parent == null || location.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.Applications)
                {
                    if (location.LocationLevel != MMS.Core.CoreUTI.Enum.LocationLevel.Applications && location.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.Industry)
                    {
                        locationItems = location.LocationItems.Where(p => p.IsIndustrySelected && p.BusinessTypeId != null && p.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD);
                    }
                    else if (request.ScreenType == ScreenType.MerchantLocation && location.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.Region)
                    {
                        IList<LocationItemSelect> locationItemSelects = [];

                        if (request.GroupMappingId.HasValue && request.GroupMappingId > 0)
                        {
                            locationItemSelects = await _locationItemService.GetSelectedLocations(null, request.ScreenType, Core.CoreUTI.Enum.LocationLevel.Region, groupMappingId: request.GroupMappingId);
                        }

                        locationItems = locationItemSelects.Where(p => p.ChildrenLocationItem.IsActive).Select(p => p.ChildrenLocationItem).ToList();
                    }
                    else if (location.LocationLevel != MMS.Core.CoreUTI.Enum.LocationLevel.Applications)
                    {
                        locationItems = location.LocationItems;
                    }
                    else
                    {
                        locationItems = _locationItemService.GetAppsByDevice(kvDevice.Value);
                    }
                }
                else
                {
                    if (location.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.LocationAreas)
                    {
                        var locationItem = _locationItemService.GetById(locationValue.Value);
                        locationItems = locationItem != null ? _locationItemService.GetAllLocationAreaByParent(locationItem.ParentId.GetValueOrDefault()) : location.LocationItems;
                    }
                    else if (location.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.DevicesUsed)
                    {
                        locationItems = _locationItemService.GetDevice(kvLocation.Value, true);
                    }
                    else if (location.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.Menus)
                    {
                        locationItems = location.LocationItems.Where(p => p.ParentId == kvPreviousLocation.Value && p.LocationParentId == kvLocation.Value);
                    }
                    else if (location.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.SpecialMenus)
                    {
                        locationItems = location.LocationItems.Where(p => p.LocationParentId == kvLocation.Value);
                    }
                    else
                    {
                        locationItems = location.LocationItems?.Where(p => ApiUtil.HasNearestParentAtLevel(p, kvNearestLocation.Value)).Where(notIncludeDeleted);

                        if (request.ScreenType == ScreenType.MerchantLocation)
                        {
                            var selectedLocations = await _locationItemService.GetSelectedLocations(0, request.ScreenType, null);
                            var activeLocationIds = selectedLocations
                                .Where(p => p.IsActive)
                                .Select(p => p.ChildrenId)
                                .ToList();

                            locationItems = locationItems
                                .Where(item => activeLocationIds.Contains(item.Id))
                                .ToList();
                        }

                        if (!locationItems.Any())
                        {
                            locationItems = location.LocationItems.Where(notIncludeDeleted);
                            locationItems = locationItems.Where(o => o.ParentId == request.LastSelectedLocations.ElementAt(i - 1).Value);
                        }
                    }
                }

                if (locationValue.Key == (int)MMS.Core.CoreUTI.Enum.LocationLevel.LocationAreas)
                {
                    var locationItem = _locationItemService.GetById(locationValue.Value);

                    if (locationItem == null) break;

                    var mapBefore = _applicationForDeviceConfigService.GetSelected(locationItem.Id).Where(p => p.IsActive).ToList();
                    if (mapBefore.Count > 0 && locationItem.IsEcommerce && !mapBefore.FirstOrDefault().Application.ApplicationUsed.IsEcommerce)
                    {
                        DefaultSelectEcommerceAppsForDevice(locationItem.UsedByItemId.GetValueOrDefault(), locationItem.IsEcommerce);
                        break;
                    }
                    DefaultSelectEcommerceAppsForDevice(locationItem.UsedByItemId.GetValueOrDefault(), locationItem.IsEcommerce);
                    var mapAfter = _applicationForDeviceConfigService.GetSelected(locationItem.Id).Where(p => p.IsActive).ToList();
                    if (!locationItem.IsEcommerce && mapAfter.Count == 0)
                    {
                        break;
                    }
                }

                locationGlobal.Location = LocationLevelApiModel.ToModel(location);

                //Order Location Level
                locationItems = ApiUtil.OrderLocationItems(locationItems, location.LocationLevel);
                locationItems = _languageExecuteService.GetLanguage(locationItems.ToList());

                if (location.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.Menus)
                {
                    locationGlobal.LocationItems = locationItems.ToListModel(useItemId: kvDevice.Value, selectedId: locationValue.Value).ToList();
                }
                else
                {
                    locationGlobal.LocationItems = locationItems.ToListModel(useItemId: kvPreviousLocation.Value, selectedId: locationValue.Value).ToList();
                }
                locationLevelViewModel.LocationGlobalLists.Add(locationGlobal);

                if (locationValue.Value > 0 && locationValue.Key > 0)
                {
                    kvNearestLocation = locationValue;
                }

                if (!location.IsFinal)
                {
                    kvPreviousLocation = locationValue;
                }
            }
            //Last location
            var previousLocation = _locationService.GetById(kvPreviousLocation.Key, p => p.Childs);
            Location lastLocation = null;

            if (previousLocation == null)
            {
                // Test get highest workign right?
                if (securityLocationFilter.IsViewGlobal)
                {
                    lastLocation = _locationItemService.GetHighestLocation(securityLocationFilter.ViewLocationItemIdsRestricted, false);
                }
                else
                {
                    lastLocation = _locationItemService.GetHighestLocation(securityLocationFilter.ViewLocationItemIdsAllow, true);
                }

                if (lastLocation == null)
                {
                    lastLocation = _locationService.GetByLevel(MMS.Core.CoreUTI.Enum.LocationLevel.Region);
                }
            }
            else
            {
                if (allChainOptionSelected)
                {
                    lastLocation = _locationService.GetByLevel(MMS.Core.CoreUTI.Enum.LocationLevel.Locations);
                }
                else
                {
                    if (request.IsHasLocationZone)
                    {
                        lastLocation = previousLocation.Childs.FirstOrDefault(p => p.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.LocationZone);
                    }
                    else
                    {
                        lastLocation = previousLocation.Childs.FirstOrDefault(p => p.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.DevicesUsed);
                    }

                    lastLocation = _languageExecuteService.GetLanguage(lastLocation);
                }
            }

            if (lastLocation != null)
            {
                var locationItems = _locationItemService.GetAllByLocationLevel(lastLocation.LocationLevel);

                if (request.ScreenType == ScreenType.MerchantLocation && lastLocation.LocationLevel == Core.CoreUTI.Enum.LocationLevel.Region)
                {
                    IList<LocationItemSelect> locationItemSelects = [];

                    if (request.GroupMappingId.HasValue && request.GroupMappingId > 0)
                    {
                        locationItemSelects = await _locationItemService.GetSelectedLocations(null, request.ScreenType, Core.CoreUTI.Enum.LocationLevel.Region, request.GroupMappingId);
                    }

                    locationItems = locationItemSelects.Where(p => p.ChildrenLocationItem.IsActive).Select(p => p.ChildrenLocationItem).ToList();
                }

                var locationGlobal = new LocationGlobalDisplay
                {
                    Location = LocationLevelApiModel.ToModel(lastLocation),
                };

                if (previousLocation == null
                    || lastLocation.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.Applications)
                {
                    if (lastLocation.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.Applications)
                    {
                        locationItems = _locationItemService.GetAppsByDevice(kvDevice.Value);
                    }

                    var tempLocationItems = ApiUtil.OrderLocationItems(locationItems, lastLocation.LocationLevel);
                    locationGlobal.LocationItems = tempLocationItems.ToListModel(useItemId: kvPreviousLocation.Value);
                }
                else if (lastLocation.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.Menus)
                {
                    locationGlobal.LocationItems = ApiUtil.OrderLocationItems(locationItems.Where(p => p.Parent != null && p.ParentId == kvPreviousLocation.Value && p.LocationParentId == kvLocation.Value), lastLocation.LocationLevel).ToListModel(useItemId: kvDevice.Value);
                }
                else if (lastLocation.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.SpecialMenus)
                {
                    locationGlobal.LocationItems = ApiUtil.OrderLocationItems(locationItems.Where(p => p.LocationParent != null && p.LocationParentId == kvLocation.Value), lastLocation.LocationLevel).ToListModel(useItemId: kvPreviousLocation.Value);
                }
                else if (previousLocation.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.LocationAreas && lastLocation.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.LocationZone)
                {
                    locationGlobal.LocationItems = ApiUtil.OrderLocationItems(locationItems.Where(p => p.ParentId == kvNearestLocation.Value).ToList(), lastLocation.LocationLevel).ToListModel();
                }
                else if (previousLocation != null && previousLocation.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.LocationAreas
                    || previousLocation.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.Locations && lastLocation.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.DevicesUsed)
                {
                    if (previousLocation.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.Locations)
                    {
                        locationGlobal.LocationItems = ApiUtil.OrderLocationItems(locationItems.Where(p => p.Parent != null && p.Parent.Location.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.Locations
                        && p.LocationLevelPOSDeviceUsed != null
                        && p.LocationLevelPOSDeviceUsed.Asset != null
                        && (p.LocationLevelPOSDeviceUsed.Asset.IsECRDevice
                        || p.LocationLevelPOSDeviceUsed.Asset.IsPaymentDevice)), lastLocation.LocationLevel).ToListModel();
                    }
                    else
                    {
                        var prevLocationItem = _locationItemService.GetById(kvPreviousLocation.Value);
                        var parentId = prevLocationItem != null ? prevLocationItem.ParentId.GetValueOrDefault() : 0;
                        locationGlobal.LocationItems = ApiUtil.OrderLocationItems(locationItems.Where(p => p.Parent != null && p.Parent.Location.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.Locations && ApiUtil.HasNearestParentAtLevel(p, parentId)
                        && p.LocationLevelPOSDeviceUsed != null
                        && p.LocationLevelPOSDeviceUsed.Asset != null
                        && (p.LocationLevelPOSDeviceUsed.Asset.IsECRDevice
                        || p.LocationLevelPOSDeviceUsed.Asset.IsPaymentDevice)), lastLocation.LocationLevel).ToListModel(useItemId: kvPreviousLocation.Value);
                    }
                }
                else
                {
                    locationGlobal.LocationItems = ApiUtil.OrderLocationItems(locationItems.Where(p => ApiUtil.HasNearestParentAtLevel(p, kvNearestLocation.Value)), lastLocation.LocationLevel).ToListModel(useItemId: kvPreviousLocation.Value);
                }

                locationLevelViewModel.LocationGlobalLists.Add(locationGlobal);
            }

            locationLevelViewModel = LocationSecurityFilter(locationLevelViewModel);

            if (request.CloudSecurityAccessMappingId > 0)
            {
                for (int i = 0; i < locationLevelViewModel.LocationGlobalLists.Count; i++)
                {
                    var locationGlobal = locationLevelViewModel.LocationGlobalLists[i];
                    if (locationGlobal.LocationItems.Count > 0)
                    {
                        var merchantRestrictDeviceType = locationGlobal.Location.LocationLevel switch
                        {
                            Core.CoreUTI.Enum.LocationLevel.Region => MerchantRestrictDeviceType.Region,
                            Core.CoreUTI.Enum.LocationLevel.Country => MerchantRestrictDeviceType.Country,
                            Core.CoreUTI.Enum.LocationLevel.State => MerchantRestrictDeviceType.State,
                            Core.CoreUTI.Enum.LocationLevel.City => MerchantRestrictDeviceType.City,
                            Core.CoreUTI.Enum.LocationLevel.Suburb => MerchantRestrictDeviceType.Suburb,
                            Core.CoreUTI.Enum.LocationLevel.SuburbArea => MerchantRestrictDeviceType.SuburbArea,
                            Core.CoreUTI.Enum.LocationLevel.Locations => MerchantRestrictDeviceType.Locations,
                            Core.CoreUTI.Enum.LocationLevel.LocationAreas => MerchantRestrictDeviceType.LocationAreas,
                            Core.CoreUTI.Enum.LocationLevel.Merchants => MerchantRestrictDeviceType.Merchants,
                            _ => MerchantRestrictDeviceType.None
                        };

                        var cloudSecurityAccessMapping = _cloudSecurityAccessMappingService.GetById(request.CloudSecurityAccessMappingId);
                        if (cloudSecurityAccessMapping.UserId != null)
                        {
                            var securityTemplateMapping = await _cloudSecurityAccessMappingService.GetCloudSecurityAccessMapping(
                                cloudSecurityAccessMapping.CompanyId,
                                cloudSecurityAccessMapping.GroupId,
                                cloudSecurityAccessMapping.SecurityTemplateId ?? 0,
                                null,
                                cloudSecurityAccessMapping.IsPartner);

                            var restrictListForSecurityTemplate = await _merchantRestrictDeviceService
                                .GetListRestrictByTypeAsync(securityTemplateMapping.Id, merchantRestrictDeviceType);
                            List<int> restrictIdsForTemplate = restrictListForSecurityTemplate.Select(o => o.RestrictId).ToList();

                            if (locationGlobal.LocationItems.Any(o => restrictIdsForTemplate.Contains(o.Id)))
                            {
                                locationGlobal.LocationItems = locationGlobal.LocationItems
                                    .Where(o => !restrictIdsForTemplate.Contains(o.Id))
                                    .ToList();

                                locationLevelViewModel.LocationGlobalLists = locationLevelViewModel.LocationGlobalLists
                                    .Take(i + 1)
                                    .ToList();

                                locationLevelViewModel.Selecteds = locationLevelViewModel.Selecteds
                                    .Take(i)
                                    .ToList();

                                break;
                            }
                            else
                            {
                                locationGlobal.LocationItems = locationGlobal.LocationItems
                                    .Where(o => !restrictIdsForTemplate.Contains(o.Id))
                                    .ToList();
                            }
                        }

                        var merchantRestrictDeviceList = await _merchantRestrictDeviceService
                            .GetListRestrictByTypeAsync(request.CloudSecurityAccessMappingId, merchantRestrictDeviceType);
                        List<int> restrictLocationIds = merchantRestrictDeviceList.Select(o => o.RestrictId).ToList();
                        foreach (var item in locationGlobal.LocationItems)
                        {
                            item.DeActivate = restrictLocationIds.Contains(item.Id);
                        }
                    }
                }
            }
            else
            {
                //TODO: remove hardcode
                var currentUser = _userMasterService.GetById(1);
                if (currentUser != null && !currentUser.EnableAdminAccess)
                {
                    var accessStatusId = User.GetAccessStatusId();
                    var companyId = User.GetCompanyId();
                    var securityTemplateMapping = await _cloudSecurityAccessMappingService.GetCloudSecurityAccessMapping(companyId, null, accessStatusId, null);
                    var userAccessMapping = await _cloudSecurityAccessMappingService.GetCloudSecurityAccessMapping(companyId, null, accessStatusId, currentUser.Id);

                    if (securityTemplateMapping != null || userAccessMapping != null)
                    {
                        foreach (var locationGlobal in locationLevelViewModel.LocationGlobalLists)
                        {
                            if (locationGlobal.LocationItems.Count > 0)
                            {
                                var merchantRestrictDeviceType = locationGlobal.Location.LocationLevel switch
                                {
                                    Core.CoreUTI.Enum.LocationLevel.Region => MerchantRestrictDeviceType.Region,
                                    Core.CoreUTI.Enum.LocationLevel.Country => MerchantRestrictDeviceType.Country,
                                    Core.CoreUTI.Enum.LocationLevel.State => MerchantRestrictDeviceType.State,
                                    Core.CoreUTI.Enum.LocationLevel.City => MerchantRestrictDeviceType.City,
                                    Core.CoreUTI.Enum.LocationLevel.Suburb => MerchantRestrictDeviceType.Suburb,
                                    Core.CoreUTI.Enum.LocationLevel.SuburbArea => MerchantRestrictDeviceType.SuburbArea,
                                    Core.CoreUTI.Enum.LocationLevel.Locations => MerchantRestrictDeviceType.Locations,
                                    Core.CoreUTI.Enum.LocationLevel.LocationAreas => MerchantRestrictDeviceType.LocationAreas,
                                    Core.CoreUTI.Enum.LocationLevel.Merchants => MerchantRestrictDeviceType.Merchants,
                                    _ => MerchantRestrictDeviceType.None
                                };

                                var merchantRestrictDeviceList = await _merchantRestrictDeviceService.GetListRestrictForFilterDataAsync(
                                    securityTemplateMapping?.Id, userAccessMapping?.Id, merchantRestrictDeviceType);

                                List<int> restrictLocationIds = merchantRestrictDeviceList.Select(o => o.RestrictId).Distinct().ToList();

                                var itemsToRemove = new List<LocationItemDisplayModel>();
                                foreach (var item in locationGlobal.LocationItems)
                                {
                                    if (restrictLocationIds.Contains(item.Id))
                                    {
                                        itemsToRemove.Add(item);
                                    }
                                }

                                foreach (var itemToRemove in itemsToRemove)
                                {
                                    locationGlobal.LocationItems.Remove(itemToRemove);
                                }
                            }
                        }
                    }
                }
            }

            return Ok(locationLevelViewModel);
        }

        [HttpGet("load-merchant-location-areas")]
        public async Task<IActionResult> LoadMerchantLocationAreas(int locationAreaId, int cloudSecurityAccessMappingId)
        {
            var merchantLocations = await _merchantLocationsService.GetMerchantLocationListByLocationAreaIdIncludeMerchantMasterAsync(locationAreaId);

            var listItems = MerchantLocationModel.ToListModels([.. merchantLocations.Where(p => p.IsActive)]);

            if (cloudSecurityAccessMappingId > 0)
            {
                var cloudSecurityAccessMapping = _cloudSecurityAccessMappingService.GetById(cloudSecurityAccessMappingId);
                if (cloudSecurityAccessMapping.UserId != null)
                {
                    var securityTemplateMapping = await _cloudSecurityAccessMappingService.GetCloudSecurityAccessMapping(cloudSecurityAccessMapping.CompanyId, cloudSecurityAccessMapping.GroupId,
                        cloudSecurityAccessMapping.SecurityTemplateId ?? 0, null, cloudSecurityAccessMapping.IsPartner);

                    var restrictListForSecurityTemplate = await _merchantRestrictDeviceService.GetListRestrictByTypeAsync(securityTemplateMapping.Id, MerchantRestrictDeviceType.Merchants);
                    List<int> restrictIdsForTemplate = restrictListForSecurityTemplate.Select(o => o.RestrictId).ToList();
                    listItems = listItems.Where(o => !restrictIdsForTemplate.Contains(o.Id)).ToList();
                }

                var merchantRestrictDeviceList = await _merchantRestrictDeviceService.GetListRestrictByTypeAsync(cloudSecurityAccessMappingId, MerchantRestrictDeviceType.Merchants);
                List<int> restrictMerchantIds = merchantRestrictDeviceList.Select(o => o.RestrictId).ToList();
                foreach (var item in listItems)
                {
                    item.IsActive = true;
                    if (restrictMerchantIds.Contains(item.Id))
                    {
                        item.IsActive = false;
                    }
                }
            }
            else
            {
                //TODO: remove hardcode
                var currentUser = _userMasterService.GetById(1);
                if (currentUser != null && !currentUser.EnableAdminAccess)
                {
                    var accessStatusId = 1;
                    var companyId = 1;
                    var securityTemplateMapping = await _cloudSecurityAccessMappingService.GetCloudSecurityAccessMapping(companyId, null, accessStatusId, null);
                    var userAccessMapping = await _cloudSecurityAccessMappingService.GetCloudSecurityAccessMapping(companyId, null, accessStatusId, currentUser.Id);

                    if (securityTemplateMapping != null || userAccessMapping != null)
                    {
                        var merchantRestrictDeviceList = await _merchantRestrictDeviceService.GetListRestrictForFilterDataAsync(securityTemplateMapping?.Id, userAccessMapping?.Id, MerchantRestrictDeviceType.Merchants);

                        List<int> restrictIds = merchantRestrictDeviceList.Select(o => o.RestrictId).Distinct().ToList();
                        listItems = listItems.Where(o => !restrictIds.Contains(o.Id)).ToList();
                    }
                }
            }

            return Ok(listItems);
        }

        [HttpGet("load-location-below")]
        [ApiAuthority(
            Function = ApiSecure.ApiLocationManagement,
            Action = ApiSecure.ViewAction)]
        public async Task<IActionResult> LoadLocationBelow(int locationItemId, int locationId, [FromQuery] int[] locationIdsToLoad,
            int parentId = 0, int locationParentId = 0, int usedItemId = 0, bool? isEcommerce = null,
            string locationParentIdsString = null, bool isMerchantLocation = false, ScreenType screenType = ScreenType.None, int cloudSecurityAccessMappingId = 0, int? groupMappingId = null)
        {
            var viewModel = await LoadItemBelow(locationItemId, locationId, locationIdsToLoad, parentId: parentId,
                locationParentId: locationParentId, usedItemId: usedItemId, isEcommerce: isEcommerce, isMerchantLocation: isMerchantLocation, screenType: screenType, cloudSecurityAccessMappingId: cloudSecurityAccessMappingId, groupMappingId: groupMappingId);

            if (locationParentIdsString != null)
            {

            }

            return Ok(viewModel);
        }

        [HttpGet("load-item-below")]
        public async Task<IActionResult> LoadItemBelow(int locationItemId, int locationId, [FromQuery] int[] locationIdsToLoad,
            int locationItemIdSelected = 0, int parentId = 0, int locationParentId = 0, int usedItemId = 0,
            bool? isEcommerce = null, bool isMerchantLocation = false, ScreenType screenType = ScreenType.None, int cloudSecurityAccessMappingId = 0, int? groupMappingId = null)
        {
            var locationLevelViewModel = new LocationLevelResponseModel();

            if (locationIdsToLoad == null || locationIdsToLoad.Length <= 0)
            {
                return Ok(locationLevelViewModel);
            }

            if (locationId != 0 && _locationService.IsFinal(locationId))
            {
                return Ok(locationLevelViewModel);
            }

            var location = _locationService.GetById(locationId, p => p.LocationItems);
            var locationItem = _locationItemService.GetById(locationItemId);
            if (location == null)
            {
                return Ok(locationLevelViewModel);
            }

            if (location.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.Chain &&
                !location.LocationItems.Any(p => p.Id == locationItemId))
            {
                var locationToLoad = _locationService.GetByLevel(MMS.Core.CoreUTI.Enum.LocationLevel.Locations);
                locationIdsToLoad = new int[1] { locationToLoad.Id };
            }

            foreach (var nextLocationId in locationIdsToLoad)
            {
                if (nextLocationId == 0)
                {
                    continue;
                }

                var locationToLoad = _locationService.GetById(nextLocationId);

                var tempLocationItems = _locationItemService.GetLocationItems(0, nextLocationId, true);

                if (screenType == ScreenType.MerchantLocation)
                {
                    var locationItemSelects = await _locationItemService.GetSelectedLocations(0, screenType, locationToLoad.LocationLevel, groupMappingId: groupMappingId);
                    tempLocationItems = locationItemSelects.Where(p => p.ChildrenLocationItem.IsActive).Select(p => p.ChildrenLocationItem).ToList();
                }

                if (location.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.Applications &&
                     locationToLoad.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.Menus || locationToLoad.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.LineSheets)
                {
                    //Below device
                    tempLocationItems = tempLocationItems.Where(p => p.ParentId == locationItemId && p.LocationParentId == locationParentId).ToList();
                    locationItemId = parentId;
                }
                else if ((location.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.Locations ||
                          location.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.LocationAreas)
                         && locationToLoad.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.DevicesUsed)
                {
                    //Load device
                    //Parent could be Location Area OR Location
                    if (locationItem != null)
                    {
                        if (location.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.Locations)
                        {
                            parentId = locationItem.Id;
                        }
                        else if (location.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.LocationAreas)
                        {
                            parentId = locationItem.ParentId.GetValueOrDefault();
                        }
                    }
                    else
                    {
                        parentId = 0;
                    }

                    //Filter from all LocationItem in Location
                    tempLocationItems = tempLocationItems.Where(
                        p => p.Parent != null
                        && p.LocationLevelPOSDeviceUsed != null
                        //&& p.LocationLevelPOSDeviceUsed.Asset != null
                        //&& (p.LocationLevelPOSDeviceUsed.Asset.IsPaymentDevice //Flags removed
                        //|| p.LocationLevelPOSDeviceUsed.Asset.IsECRDevice)
                        //&& p.LocationLevelPOSDeviceUsed.Asset.AssetSubCategorieId != null //Add condition to get only new Asset (with SubCategory)
                        && p.Parent.Location.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.Locations
                        && ApiUtil.HasNearestParentAtLevel(p, parentId)).ToList();

                    if (location.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.Locations)
                    {
                        locationItemId = 0;
                    }

                    if (location.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.LocationAreas && usedItemId != 0)
                    {
                        DefaultSelectEcommerceAppsForDevice(usedItemId, isEcommerce.GetValueOrDefault());
                    }
                }
                else if (location.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.Locations &&
                         locationToLoad.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.LocationAreas)
                {
                    tempLocationItems = _locationItemService.GetAllLocationAreaByParent(locationItemId);
                }
                else if (location.LocationLevel == Core.CoreUTI.Enum.LocationLevel.Industry)
                {
                    if (isMerchantLocation)
                    {
                        tempLocationItems = tempLocationItems.Where(p => p.IsChainSelected && p.ParentId == locationItemId).ToList();
                    }
                }
                else if (locationToLoad.LocationLevel < MMS.Core.CoreUTI.Enum.LocationLevel.DevicesUsed && locationItemId > 0)
                {
                    tempLocationItems = tempLocationItems.Where(p => ApiUtil.HasNearestParentAtLevel(p, locationItemId)).ToList();
                }
                else if (location.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.Applications
                         || locationToLoad.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.Menus)
                {
                    tempLocationItems = tempLocationItems.Where(p => ApiUtil.HasNearestParentAtLevel(p, locationItemId)).ToList();
                    locationItemId = parentId;
                }
                //Load for Special Menu
                else if (locationToLoad.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.SpecialMenus)
                {
                    tempLocationItems = tempLocationItems.Where(p => p.LocationParentId == locationParentId).ToList();
                }
                //Load for Application from Device
                else if (location.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.DevicesUsed &&
                         locationToLoad.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.Applications)
                {
                    tempLocationItems = _locationItemService.GetAppsByDevice(locationItem);
                }
                else if (location.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.LocationAreas &&
                         locationToLoad.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.LocationZone)
                {
                    tempLocationItems = tempLocationItems.Where(p => p.ParentId == locationItemId).ToList();
                }
                else //Other
                {
                    tempLocationItems = tempLocationItems.Where(p => ApiUtil.HasNearestParentAtLevel(p, parentId)).ToList();
                }

                tempLocationItems = _languageExecuteService.GetLanguage(tempLocationItems.ToList());
                tempLocationItems = ApiUtil.OrderLocationItems(tempLocationItems, locationToLoad.LocationLevel).ToList();

                var locationGlobal = new LocationGlobalDisplay
                {
                    Location = LocationLevelApiModel.ToModel(locationToLoad)
                };

                locationLevelViewModel.LocationGlobalLists.Add(locationGlobal);

                if (tempLocationItems.Any())
                {
                    locationGlobal.LocationItems = tempLocationItems.ToListModel(useItemId: locationItemId,
                        selectedId: locationItemIdSelected);
                }
            }

            locationLevelViewModel = LocationSecurityFilter(locationLevelViewModel);
            if (cloudSecurityAccessMappingId > 0)
            {
                foreach (var locationGlobal in locationLevelViewModel.LocationGlobalLists)
                {
                    if (locationGlobal.LocationItems.Count > 0)
                    {
                        var merchantRestrictDeviceType = locationGlobal.Location.LocationLevel switch
                        {
                            Core.CoreUTI.Enum.LocationLevel.Region => MerchantRestrictDeviceType.Region,
                            Core.CoreUTI.Enum.LocationLevel.Country => MerchantRestrictDeviceType.Country,
                            Core.CoreUTI.Enum.LocationLevel.State => MerchantRestrictDeviceType.State,
                            Core.CoreUTI.Enum.LocationLevel.City => MerchantRestrictDeviceType.City,
                            Core.CoreUTI.Enum.LocationLevel.Suburb => MerchantRestrictDeviceType.Suburb,
                            Core.CoreUTI.Enum.LocationLevel.SuburbArea => MerchantRestrictDeviceType.SuburbArea,
                            Core.CoreUTI.Enum.LocationLevel.Locations => MerchantRestrictDeviceType.Locations,
                            Core.CoreUTI.Enum.LocationLevel.LocationAreas => MerchantRestrictDeviceType.LocationAreas,
                            Core.CoreUTI.Enum.LocationLevel.Merchants => MerchantRestrictDeviceType.Merchants,
                            _ => MerchantRestrictDeviceType.None
                        };

                        var cloudSecurityAccessMapping = _cloudSecurityAccessMappingService.GetById(cloudSecurityAccessMappingId);
                        if (cloudSecurityAccessMapping.UserId != null)
                        {
                            var securityTemplateMapping = await _cloudSecurityAccessMappingService.GetCloudSecurityAccessMapping(cloudSecurityAccessMapping.CompanyId, cloudSecurityAccessMapping.GroupId,
                                cloudSecurityAccessMapping.SecurityTemplateId ?? 0, null, cloudSecurityAccessMapping.IsPartner);

                            var restrictListForSecurityTemplate = await _merchantRestrictDeviceService.GetListRestrictByTypeAsync(securityTemplateMapping.Id, merchantRestrictDeviceType);
                            List<int> restrictIdsForTemplate = restrictListForSecurityTemplate.Select(o => o.RestrictId).ToList();

                            locationGlobal.LocationItems = locationGlobal.LocationItems.Where(o => !restrictIdsForTemplate.Contains(o.Id)).ToList();

                            if (locationItemId == 0 && merchantRestrictDeviceType != MerchantRestrictDeviceType.Region)
                            {
                                var restrictListForSecurityTemplatePrev = await _merchantRestrictDeviceService.GetListRestrictByTypeAsync(securityTemplateMapping.Id, (MerchantRestrictDeviceType)((int)merchantRestrictDeviceType - 1));
                                List<int> restrictIdsForTemplatePrev = restrictListForSecurityTemplatePrev.Select(o => o.RestrictId).ToList();
                                locationGlobal.LocationItems = locationGlobal.LocationItems.Where(o => !restrictIdsForTemplatePrev.Contains(o.ParentId ?? 0)).ToList();
                            }
                        }

                        var merchantRestrictDeviceList = await _merchantRestrictDeviceService.GetListRestrictByTypeAsync(cloudSecurityAccessMappingId, merchantRestrictDeviceType);
                        List<int> restrictLocationIds = merchantRestrictDeviceList.Select(o => o.RestrictId).ToList();
                        foreach (var item in locationGlobal.LocationItems)
                        {
                            item.DeActivate = false;
                            if (restrictLocationIds.Contains(item.Id))
                            {
                                item.DeActivate = true;
                            }
                        }
                    }
                }
            }
            else
            {
                //TODO: remove hardcode
                var currentUser = _userMasterService.GetById(1);
                if (currentUser != null && !currentUser.EnableAdminAccess)
                {
                    var accessStatusId = User.GetAccessStatusId();
                    var companyId = User.GetCompanyId();
                    var securityTemplateMapping = await _cloudSecurityAccessMappingService.GetCloudSecurityAccessMapping(companyId, null, accessStatusId, null);
                    var userAccessMapping = await _cloudSecurityAccessMappingService.GetCloudSecurityAccessMapping(companyId, null, accessStatusId, currentUser.Id);

                    if (securityTemplateMapping != null || userAccessMapping != null)
                    {
                        foreach (var locationGlobal in locationLevelViewModel.LocationGlobalLists)
                        {
                            if (locationGlobal.LocationItems.Count > 0)
                            {
                                var merchantRestrictDeviceType = locationGlobal.Location.LocationLevel switch
                                {
                                    Core.CoreUTI.Enum.LocationLevel.Region => MerchantRestrictDeviceType.Region,
                                    Core.CoreUTI.Enum.LocationLevel.Country => MerchantRestrictDeviceType.Country,
                                    Core.CoreUTI.Enum.LocationLevel.State => MerchantRestrictDeviceType.State,
                                    Core.CoreUTI.Enum.LocationLevel.City => MerchantRestrictDeviceType.City,
                                    Core.CoreUTI.Enum.LocationLevel.Suburb => MerchantRestrictDeviceType.Suburb,
                                    Core.CoreUTI.Enum.LocationLevel.SuburbArea => MerchantRestrictDeviceType.SuburbArea,
                                    Core.CoreUTI.Enum.LocationLevel.Locations => MerchantRestrictDeviceType.Locations,
                                    Core.CoreUTI.Enum.LocationLevel.LocationAreas => MerchantRestrictDeviceType.LocationAreas,
                                    Core.CoreUTI.Enum.LocationLevel.Merchants => MerchantRestrictDeviceType.Merchants,
                                    _ => MerchantRestrictDeviceType.None
                                };

                                var merchantRestrictDeviceList = await _merchantRestrictDeviceService.GetListRestrictForFilterDataAsync(securityTemplateMapping?.Id, userAccessMapping?.Id, merchantRestrictDeviceType);

                                List<int> restrictLocationIds = merchantRestrictDeviceList.Select(o => o.RestrictId).Distinct().ToList();

                                var itemsToRemove = new List<LocationItemDisplayModel>();
                                foreach (var item in locationGlobal.LocationItems)
                                {
                                    if (restrictLocationIds.Contains(item.Id))
                                    {
                                        itemsToRemove.Add(item);
                                    }
                                }

                                foreach (var itemToRemove in itemsToRemove)
                                {
                                    locationGlobal.LocationItems.Remove(itemToRemove);
                                }
                            }
                        }
                    }
                }
            }

            return Ok(locationLevelViewModel);
        }

        /// <summary>
        /// Get location items for select location item
        /// </summary>
        /// <param name="parentLocationId"></param>
        /// <param name="currentLevel"></param>
        /// <param name="groupMappingId"></param>
        /// <param name="selectRemoveFormParameter"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        [HttpGet("select-location-data")]
        public async Task<ActionResult> SelectLocationItem(int parentLocationId, LocationLevel currentLevel, int? groupMappingId, [FromQuery] SelectRemoveFormParameter selectRemoveFormParameter)
        {
            var parentLocation = _locationItemService.GetById(parentLocationId, p => p.AddressLocalItem, p => p.Location);

            if (currentLevel != LocationLevel.Region && parentLocation == null)
            {
                throw new ArgumentException("Invalid ", nameof(parentLocationId));
            }

            var selected = await _locationItemService.GetSelectedLocations(parentLocationId, ScreenType.MerchantLocation, groupMappingId: groupMappingId);

            if (currentLevel == LocationLevel.Region)
            {
                selected = await _locationItemService.GetSelectedLocations(null, ScreenType.MerchantLocation, groupMappingId: groupMappingId);
            }

            var selectedIds = selected.Select(p => p.ChildrenId).ToList();

            var source = await _locationItemService.GetSourceForSelectLocationItemAsync(parentLocationId, currentLevel, selectedIds, selectRemoveFormParameter);

            return Ok(new
            {
                source,
                selectedIds
            });
        }

        /// <summary>
        /// Select remove location item
        /// </summary>
        /// <param name="vm"></param>
        /// <returns></returns>
        [HttpPost("select-location-item")]
        public async Task<ActionResult> SelectLocationItem(LocationItemSelectRequest vm)
        {
            var parentLocation = _locationItemService.GetById(vm.ParentLocationId);

            await _locationItemService.UpdateSelectedLocations(
                vm.SelectedIds,
                parentLocation?.Id,
                ScreenType.MerchantLocation,
                vm.GroupMappingId
            );

            return Ok();
        }

        /// <summary>
        /// Get merchant locations for select merchant item
        /// </summary>
        /// <param name="locationId"></param>
        /// <param name="selectRemoveFormParameter"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        [HttpGet("select-merchant-data")]
        public async Task<IActionResult> SelectMerchantLocationsLevel(int locationId, [FromQuery] SelectRemoveFormParameter selectRemoveFormParameter)
        {
            var locationItem = _locationItemService.GetById(locationId) ?? throw new ArgumentException("Invalid", nameof(locationId));
            var merchantLocations = await _merchantLocationsService.GetAllMerchantLocationAsync(locationItem.Id);
            var selectedIds = merchantLocations.Select(p => p.MerchantMasterId);
            var masterMerchants = await _merchantMasterService.GetSourceMerchantMasterListToSelectForLocationApiAsync(locationItem.Id, selectedIds, selectRemoveFormParameter);

            var parentLocationItemId = Convert.ToInt32(locationItem.Hierarchy.Split(',')[0]);
            var isSelectOne = _merchantLocationsService.CheckLocationIsMultiMerchantsOrNot(parentLocationItemId);

            return Ok(new
            {
                source = masterMerchants,
                selectedIds,
                isSelectOne
            });
        }

        /// <summary>
        /// Select or remove merchant locations level
        /// </summary>
        /// <param name="vm"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        [HttpPost("select-merchant-locations-level")]
        public IActionResult SelectMerchantLocationsLevel(MerchantItemSelectRequest vm)
        {
            var _ = _locationItemService.GetById(vm.LocationId) ?? throw new ArgumentException("Invalid", nameof(vm.LocationId));

            if (vm.IsSelectOne)
            {
                int selectedId = vm.SelectedIds.FirstOrDefault();
                _merchantLocationsService.UpdateSingleMerchantLocations(vm.LocationId, selectedId);
            }
            else
            {
                _merchantLocationsService.UpdateMultiMerchantLocations(vm.LocationId, vm.SelectedIds);
            }

            return Ok();
        }

        #region Private

        private void DefaultSelectEcommerceAppsForDevice(int usedItemId, bool isEcommerce)//NOSONAR
        {
            var devices = _locationItemService.GetDeviceByUsedItemId(usedItemId);
            foreach (var device in devices)
            {
                var selectedApps = _applicationForDeviceConfigService.GetSelectedApps(device.Id);
                var selectedMap = _applicationForDeviceConfigService.GetSelected(device.Id);
                var listAppUsedId = selectedApps.Select(p => p.AppUsedId.GetValueOrDefault()).ToList();
                var listEcomAppId = _applicationUsedService.GetAll().Where(p => p.IsEcommerce).Select(p => p.Id).ToList();

                if (isEcommerce)
                {
                    if (listAppUsedId.Intersect(listEcomAppId).Any())
                    {

                        foreach (var app in selectedApps)
                        {
                            if (app.ApplicationUsed.IsEcommerce)
                            {
                                var selectedItem = selectedMap.FirstOrDefault(p => p.AppId == app.Id);
                                if (selectedItem != null)
                                {
                                    selectedItem.IsActive = true;
                                }

                                var applicationParentMap = device.Children.FirstOrDefault(p => p.ChildLocationId == app.Id);
                                if (applicationParentMap == null)
                                {
                                    FastAssignApplication(app.Id, device.Id);
                                }
                            }
                            else
                            {
                                var selectedItem = selectedMap.FirstOrDefault(p => p.AppId == app.Id);
                                if (selectedItem != null)
                                {
                                    selectedItem.IsActive = false;
                                }

                            }
                        }
                    }
                    else
                    {
                        foreach (var app in selectedApps)
                        {
                            var selectedItem = selectedMap.FirstOrDefault(p => p.AppId == app.Id);
                            if (selectedItem != null)
                            {
                                selectedItem.IsActive = false;
                            }

                        }

                        var ecommerceApp = _locationItemService.GetAppsAlongEcommerce(true).FirstOrDefault();
                        if (ecommerceApp != null)
                        {
                            var mapConfig = _applicationForDeviceConfigService.GetByDeviceAndApp(device.Id, ecommerceApp.Id);
                            if (mapConfig == null)
                            {
                                _applicationForDeviceConfigService.Insert(new ApplicationForDeviceConfig(device.Id, ecommerceApp.Id, 0));
                            }
                            else
                            {
                                mapConfig.IsActive = true;
                                _applicationForDeviceConfigService.Update(mapConfig);
                            }
                            FastAssignApplication(ecommerceApp.Id, device.Id);
                        }
                    }
                }
                else
                {
                    foreach (var app in selectedApps)
                    {
                        if (app.ApplicationUsed == null)
                        {
                            continue;
                        }

                        if (app.ApplicationUsed.IsEcommerce)
                        {
                            var selectedItem = selectedMap.FirstOrDefault(p => p.AppId == app.Id);
                            if (selectedItem != null)
                            {
                                selectedItem.IsActive = false;
                            }

                        }
                    }
                }

                _applicationForDeviceConfigService.UpdateAll(selectedMap);
            }
        }

        private void FastAssignApplication(int childId, int parentId)
        {
            var child = _locationItemService.GetById(childId);
            var parent = _locationItemService.GetById(parentId);
            var applicationParentMap = parent.Children.FirstOrDefault(p => p.ChildLocationId == childId);

            applicationParentMap = applicationParentMap ?? new LocationItemParentChildrenMap { ParentLocationId = parentId, ChildLocationId = childId };

            if (applicationParentMap.Id == 0)
            {
                parent.Children.Add(applicationParentMap);
            }

            applicationParentMap.IsDeactivate = false;
            parent.Children.Where(p => p.Id != applicationParentMap.Id && p.ChildLocation.Location.LocationLevel == MMS.Core.CoreUTI.Enum.LocationLevel.Applications).ToList().ForEach(p => p.IsDeactivate = true);
            _locationItemService.Update(child);
        }

        private static LocationLevelResponseModel LocationSecurityFilter(LocationLevelResponseModel locationLevelViewModel)
        {
            //var securityLocationFilter = WebUtils.GetSession<LocationSecuritySession>(SecuritySessionConst.LocationSecuritySession) ?? new LocationSecuritySession(); // Location Security Filter
            var securityLocationFilter = new LocationSecuritySession(); //TODO: harded code always new LocationSecuritySession

            if (securityLocationFilter.IsViewGlobal)
            {
                foreach (var locationGlobal in locationLevelViewModel.LocationGlobalLists)
                {
                    locationGlobal.LocationItems = locationGlobal.LocationItems.Where(p => !securityLocationFilter.ViewLocationItemIdsRestricted.Contains(p.Id)).ToList();
                }
            }
            else
            {
                foreach (var locationGlobal in locationLevelViewModel.LocationGlobalLists)
                {
                    locationGlobal.LocationItems = locationGlobal.LocationItems.Where(p => securityLocationFilter.ViewLocationItemIdsAllow.Contains(p.Id)).ToList();
                }
            }

            return locationLevelViewModel;
        }

        #endregion
    }
}
