using System.Collections.Generic;
using System.Threading.Tasks;
using MMS.Core.Entities;
using MMS.Core.Services.Base;
using MMS.Model.LinklyFake;

namespace MMS.Core.Services
{
    /// <summary>
    /// Service for synchronizing merchant master data from external sources
    /// </summary>
    public interface IMerchantMasterSyncService : IBaseService<MerchantMaster>
    {
        /// <summary>
        /// Synchronizes merchant master data from Linkly to the MMS database
        /// </summary>
        /// <param name="linklyMerchantMasters">List of merchant masters from Linkly API</param>
        /// <returns>Summary of sync operation (inserted, updated, deleted counts)</returns>
        Task<(int inserted, int updated, int deleted)> SyncMerchantMastersFromLinklyAsync(List<LinklyMerchantMasterDto> linklyMerchantMasters);
    }
}
