using FluentValidation;
using Hangfire;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.Mvc;
using MMS.Core.CoreUTI.Enum;
using MMS.Core.Entities;
using MMS.Core.Entities.Ratings;
using MMS.Core.Services;
using MMS.Core.Services.Base;
using MMS.Core.Services.Paxstore;
using MMS.Core.Utils;
using MMS.Web.ClientInterface;
using MMS.Web.Models;
using MMS.Web.Models.ApiModels;
using MMS.Web.Models.ApiModels.AdvanceSetup;
using MMS.Web.Models.ApiModels.GeneralSetup;
using MMS.Web.Models.ApiModels.MerchantSetups;
using MMS.Web.Models.ApiModels.PaymentsAccepted;
using MMS.Web.Models.ApiModels.TerminalSetups;
using MMS.Web.Utils;
using MMS.Web.Utils.Services;
using Newtonsoft.Json;
using Serilog;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Linq;
using System.Xml.Serialization;


namespace MMS.Web.Controllers.Apis
{
    [Produces("application/json", "application/xml")]
    [AllowAnonymous]
    public class ClientController : BaseApiController
    {
        public string _rootPath;

        private readonly IIntegrationPaxstoreAndMMSService _integrationPaxstoreAndMMSService;
        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly IValidator<Resync> _resyncValidator;

        private readonly ITerminalService _terminalMasterService;
        private readonly IBaseService<Core.Entities.TerminalSetupGUI.TerminalRebootSchedule> _terminalRebootScheduleService;
        private readonly IProcessorMasterService _processorMasterService;
        private readonly IMerchantMasterService _merchantMasterService;
        private readonly ITerminalReportService _reportService;
        private readonly IUserMerchantReportService _merchantReportService;
        private readonly IMerchantAlertNotificationService _merchantAlertNotificationService;
        private readonly IHardCodeService _hardCodeService;
        private readonly IRS232CommService _rs232CommService;
        private readonly ILogMessageService _logMessageService;
        private readonly INotificationService _notificationService;
        private readonly IContactNotificationService _contactNotificationService;
        private readonly IContactDetailsService _contactDetailService;
        private readonly ITerminalApplicationService _terminalApplicationService;
        private readonly ICharityService _charityService;
        private readonly ICurrencyService _currencyService;
        private readonly IUserMasterService _userMasterService;
        private readonly IBaseService<TerminalSetup> _terminalSetupService;
        private readonly IMerchantTerminalService _merchantTerminalService;
        private readonly IProcessorMerchantService _processorMerchantService;
        private readonly IUserTerminalService _userTerminalService;
        private readonly IUserMerchantService _userMerchantService;
        private readonly IBaseService<LanguageOptionTerminal> _languageOptionTerminalService;
        private readonly IBaseService<SystemPosSaleType> _systemPosSaleTypeService;
        private readonly IProcessorTerminalService _processorTerminalService;
        private readonly IBaseService<SupportTerminal> _supportTerminalService;
        private readonly IBaseService<MerchantCharity> _merchantCharityService;
        private readonly IMessageMerchantService _messageMerchantService;
        private readonly IBaseService<BinTable> _binTableService;
        private readonly IBaseService<MerchantProcessorTypePaymentsSetup> _merchantProcessorTypePaymentsSetupService;
        private readonly IBaseService<MerchantProcessorFunctionType> _merchantProcessorFunctionTypeService;
        private readonly IBaseService<ProcessorMerchantSettleBatchSetup> _processorMerchantSettleBatchSetupService;
        private readonly IBaseService<MerchantProcessorTypeGivexSetup> _merchantProcessorTypeGivexSetupService;
        private readonly IBaseService<MerchantProcessorTypeGlobalOneSetup> _merchantProcessorTypeGlobalOneSetupService;
        private readonly IBaseService<ProcessorMerchantSetup> _processorMerchantSetupService;
        private readonly IBaseService<TerminalUpdateSchedule> _terminalUpdateScheduleService;
        private readonly IBaseService<DeditCardBackTerminal> _deditCardBackTerminalService;
        private readonly IBaseService<DateTimeZoneTerminal> _dateTimeZoneTerminalService;
        private readonly IBaseService<SoundTerminal> _soundTerminalService;
        private readonly IBaseService<TerminalUpdateHistory> _terminalUpdateHistoryService;
        private readonly IBaseService<TenderTypeMerchant> _tenderTypeMerchantService;
        private readonly IBaseService<MerchantGeneralSetup> _merchantGeneralSetupService;
        private readonly IBaseService<DefaultSaleTypeTerminal> _defaultSaleTypeTerminalService;
        private readonly IBaseService<StoreForwardTerminal> _storeForwardTerminalService;
        private readonly IBaseService<MerchantSecurityLevel> _merchantSecurityLevelService;
        private readonly IBaseService<TipsTerminal> _tipsTerminalService;
        private readonly IBaseService<CardAcceptTerminal> _cardAcceptTerminalService;
        private readonly IBaseService<DebitCardsAccepted> _debitCardsAcceptedService;
        private readonly IBaseService<CardLimit> _cardLimitService;
        private readonly IBaseService<ManualCardTerminal> _manualCardTerminalService;
        private readonly IBaseService<SurchargeTerminal> _surchargeTerminalService;
        private readonly IBaseService<CreditCashOutTerminal> _creditCashOutTerminalService;
        private readonly IBaseService<DccCard> _dccCardService;
        private readonly IBaseService<LanguageBy> _languageByService;
        private readonly IBaseService<TerminalUserSecurityAccess> _terminalUserSecurityAccessService;
        private readonly IBaseService<PosRequestTerminal> _posRequestTerminalService;
        private readonly IBaseService<LanguageByTerminal> _languageByTerminalService;
        private readonly IBaseService<TerminalCurrency> _terminalCurrencyService;
        private readonly IBaseService<ComTerminal> _comTerminalService;
        private readonly IBaseService<PosHttpServer> _posHttpServerService;
        private readonly IDeviceHealthCheckService _deviceHealthCheckService;
        private readonly IDeviceSetupUpgradeService _deviceSetupUpgradeService;
        private readonly IMerchantUserLogonService _merchantUserLogonService;
        private readonly IHostInterfacesService _hostInterfacesService;
        private readonly IBaseService<Core.Entities.MerchantCreditCardsAccepted> _merchantCreditCardsAcceptedService;
        private readonly IBaseService<Core.Entities.MerchantDebitCardsAccepted> _merchantDebitCardsAcceptedService;
        private readonly ISurchargeRuleService _surchargeRuleService;
        private readonly IAmountRangeService _amountRangeService;
        private readonly IMerchantPaymentMethodService _merchantPaymentMethodService;
        private readonly IApplicationDeviceMappingService _applicationDeviceMappingService;
        private readonly IDeviceTemplatesService _deviceTemplatesService;
        private readonly IBaseService<ServiceItem> _serviceItemService;
        private readonly IRegisteredAppService _registeredAppService;

        private const string ResponseYes = "<Response><Call>1</Call></Response>";
        private const string ResponseNo = "<Response><Call>0</Call></Response>";
        public ClientController(
            IIntegrationPaxstoreAndMMSService integrationPaxstoreAndMMSService,
            IWebHostEnvironment webHostEnvironment,
            IValidator<Resync> resyncValidator,
            IBaseService<Core.Entities.MerchantCreditCardsAccepted> merchantCreditCardsAcceptedService,
            IBaseService<Core.Entities.MerchantDebitCardsAccepted> merchantDebitCardsAcceptedService,
            IHostInterfacesService hostInterfacesService,
            IMerchantUserLogonService merchantUserLogonService,
            IDeviceHealthCheckService deviceHealthCheckService,
            ITerminalService terminalService,
            IBaseService<Core.Entities.TerminalSetupGUI.TerminalRebootSchedule> terminalRebootScheduleService,
            IProcessorMasterService processorMasterService,
            IMerchantMasterService merchantMasterService,
            ITerminalReportService reportService,
            IUserMerchantReportService merchantReportService,
            IMerchantAlertNotificationService merchantAlertNotificationService,
            IHardCodeService hardCodeService,
            IRS232CommService rs232CommService,
            ILogMessageService logMessageService,
            INotificationService notificationService,
            IContactNotificationService contactNotificationService,
            IContactDetailsService contactDetailService,
            ITerminalApplicationService terminalApplicationService,
            ICharityService charityService,
            ICurrencyService currencyService,
            IUserMasterService userMasterService,
            IBaseService<TerminalSetup> terminalSetupService,
            IMerchantTerminalService merchantTerminalService,
            IProcessorMerchantService processorMerchantService,
            IUserTerminalService userTerminalService,
            IUserMerchantService userMerchantService,
            IBaseService<LanguageOptionTerminal> languageOptionTerminalService,
            IBaseService<SystemPosSaleType> systemPosSaleTypeService,
            IProcessorTerminalService processorTerminalService,
            IBaseService<SupportTerminal> supportTerminalService,
            IBaseService<MerchantCharity> merchantCharityService,
            IMessageMerchantService messageMerhantService,
            IBaseService<BinTable> binTableService,
            IBaseService<MerchantProcessorTypePaymentsSetup> paymentSetupService,
            IBaseService<ProcessorMerchantSettleBatchSetup> processorMerchantSettleBatchSetupService,
            IBaseService<MerchantProcessorFunctionType> merchantProcessorFunctionTypeService,
            IBaseService<MerchantProcessorTypeGivexSetup> merchantProcessorTypeGivexSetupService,
            IBaseService<MerchantProcessorTypeGlobalOneSetup> merchantProcessorTypeGlobalOneSetupService,
            IBaseService<ProcessorMerchantSetup> processorMerchantSetup,
            IBaseService<TerminalUpdateSchedule> terminalUpdateScheduleService,
            IBaseService<DeditCardBackTerminal> deditCardBackTerminalService,
            IBaseService<DateTimeZoneTerminal> dateTimeZoneTerminalService,
            IBaseService<SoundTerminal> soundTerminalService,
            IBaseService<TerminalUpdateHistory> terminalUpdateHistoryService,
            IBaseService<TenderTypeMerchant> tenderTypeMerchantService,
            IBaseService<MerchantGeneralSetup> merchantGeneralSetupService,
            IBaseService<DefaultSaleTypeTerminal> defaultSaleTypeTerminalService,
            IBaseService<StoreForwardTerminal> storeForwardTerminalService,
            IBaseService<MerchantSecurityLevel> merchantSecurityLevelService,
            IBaseService<TipsTerminal> tipsTerminalService,
            IBaseService<CardAcceptTerminal> cardAcceptTerminalService,
            IBaseService<DebitCardsAccepted> debitCardsAcceptedService,
            IBaseService<CardLimit> cardLimitService,
            IBaseService<ManualCardTerminal> manualCardTerminalService,
            IBaseService<SurchargeTerminal> surchargeTerminalService,
            IBaseService<CreditCashOutTerminal> creditCashOutTerminalService,
            IBaseService<DccCard> dccCardService,
            IBaseService<LanguageBy> languageByService,
            IBaseService<TerminalUserSecurityAccess> terminalUserSecurityAccessService,
            IBaseService<PosRequestTerminal> posRequestTerminalService,
            IBaseService<LanguageByTerminal> languageByTerminalService,
            IBaseService<TerminalCurrency> terminalCurrencyService,
            IBaseService<ComTerminal> comTerminalService,
            IBaseService<PosHttpServer> posHttpServerService,
            IDeviceSetupUpgradeService deviceSetupUpgradeService,
            ISurchargeRuleService surchargeRuleService,
            IAmountRangeService amountRangeService,
            IMerchantPaymentMethodService merchantPaymentMethodService,
            IApplicationDeviceMappingService applicationDeviceMappingService,
            IDeviceTemplatesService deviceTemplatesService,
            IBaseService<ServiceItem> serviceItemService,
            IRegisteredAppService registeredAppService,
            IWebHostEnvironment env
        )
        {
            _webHostEnvironment = webHostEnvironment;
            _integrationPaxstoreAndMMSService = integrationPaxstoreAndMMSService;
            _resyncValidator = resyncValidator;
            _merchantDebitCardsAcceptedService = merchantDebitCardsAcceptedService;
            _merchantCreditCardsAcceptedService = merchantCreditCardsAcceptedService;
            _hostInterfacesService = hostInterfacesService;
            _merchantUserLogonService = merchantUserLogonService;
            _deviceHealthCheckService = deviceHealthCheckService;
            _terminalMasterService = terminalService;
            _terminalRebootScheduleService = terminalRebootScheduleService;
            _userMasterService = userMasterService;
            _processorMasterService = processorMasterService;
            _merchantMasterService = merchantMasterService;
            _reportService = reportService;
            _merchantReportService = merchantReportService;
            _rs232CommService = rs232CommService;
            _merchantAlertNotificationService = merchantAlertNotificationService;
            _hardCodeService = hardCodeService;
            _logMessageService = logMessageService;
            _notificationService = notificationService;
            _contactNotificationService = contactNotificationService;
            _contactDetailService = contactDetailService;
            _terminalApplicationService = terminalApplicationService;
            _charityService = charityService;
            _currencyService = currencyService;
            _terminalSetupService = terminalSetupService;
            _merchantTerminalService = merchantTerminalService;
            _processorMerchantService = processorMerchantService;
            _userTerminalService = userTerminalService;
            _userMerchantService = userMerchantService;
            _languageOptionTerminalService = languageOptionTerminalService;
            _systemPosSaleTypeService = systemPosSaleTypeService;
            _processorTerminalService = processorTerminalService;
            _supportTerminalService = supportTerminalService;
            _merchantCharityService = merchantCharityService;
            _messageMerchantService = messageMerhantService;
            _binTableService = binTableService;
            _merchantProcessorTypePaymentsSetupService = paymentSetupService;
            _merchantProcessorFunctionTypeService = merchantProcessorFunctionTypeService;
            _processorMerchantSettleBatchSetupService = processorMerchantSettleBatchSetupService;
            _merchantProcessorTypeGivexSetupService = merchantProcessorTypeGivexSetupService;
            _merchantProcessorTypeGlobalOneSetupService = merchantProcessorTypeGlobalOneSetupService;
            _processorMerchantSetupService = processorMerchantSetup;
            _terminalUpdateScheduleService = terminalUpdateScheduleService;
            _deditCardBackTerminalService = deditCardBackTerminalService;
            _dateTimeZoneTerminalService = dateTimeZoneTerminalService;
            _soundTerminalService = soundTerminalService;
            _terminalUpdateHistoryService = terminalUpdateHistoryService;
            _tenderTypeMerchantService = tenderTypeMerchantService;
            _merchantGeneralSetupService = merchantGeneralSetupService;
            _defaultSaleTypeTerminalService = defaultSaleTypeTerminalService;
            _storeForwardTerminalService = storeForwardTerminalService;
            _merchantSecurityLevelService = merchantSecurityLevelService;
            _tipsTerminalService = tipsTerminalService;
            _cardAcceptTerminalService = cardAcceptTerminalService;
            _debitCardsAcceptedService = debitCardsAcceptedService;
            _cardLimitService = cardLimitService;
            _manualCardTerminalService = manualCardTerminalService;
            _surchargeTerminalService = surchargeTerminalService;
            _creditCashOutTerminalService = creditCashOutTerminalService;
            _dccCardService = dccCardService;
            _languageByService = languageByService;
            _terminalUserSecurityAccessService = terminalUserSecurityAccessService;
            _posRequestTerminalService = posRequestTerminalService;
            _languageByTerminalService = languageByTerminalService;
            _terminalCurrencyService = terminalCurrencyService;
            _comTerminalService = comTerminalService;
            _posHttpServerService = posHttpServerService;
            _deviceSetupUpgradeService = deviceSetupUpgradeService;
            _rootPath = env.WebRootPath;
            _surchargeRuleService = surchargeRuleService;
            _amountRangeService = amountRangeService;
            _merchantPaymentMethodService = merchantPaymentMethodService;
            _applicationDeviceMappingService = applicationDeviceMappingService;
            _deviceTemplatesService = deviceTemplatesService;
            _serviceItemService = serviceItemService;
            _registeredAppService = registeredAppService;
        }

        #region Resync Down

        //resync all data of terminal
        [Route("api/synchronize/resync/down")]
        [HttpPost]
        public ActionResult<ResponseMessage> ReSynchronizeDown([FromBody] RequestTerminalModel model, string type = "json")
        {
            //param invaild
            if (model == null || !ModelState.IsValid)
                return GetResponseMessageError(HttpStatusCode.BadRequest);

            var terminal = _terminalMasterService.GetTerminalBySerialNumber(model.TerminalSerialNumber, model.TerminalMacAddress, model.TerminalModelId);

            if (terminal == null)
                return GetResponseMessageError(HttpStatusCode.NoContent);
            //get all merchant update 

            //var reponseModel = new Resync(terminal, Constants.FORMAT_MM_DD_YYYY, Constants.FORMAT_TIME_HH_MM);
            //set merchant processor for response model 
            //var merchantProcessors = _terminalMasterService.GetMerchantProcessorsByTerminalId(terminal.Id);
            //reponseModel.MerchantProcessors = null;
            //reponseModel.Users = null;

            var stringContent = GetStringContent(terminal);


            if (!string.IsNullOrEmpty(stringContent.ToString()))
                return GetResponseMessageSuccess(stringContent);
            return null;
            // reponseModel.
            //var ser = new XmlSerializer(typeof(Resync));
            //var sw = new StringWriter();
            //ser.Serialize(sw, responseModel);
            //return new IActionResult()
            //{
            //    Content = new StringContent(
            //        sw.ToString(),
            //        Encoding.UTF8,
            //        "application/xml"
            //        ),
            //    StatusCode = HttpStatusCode.OK,
            //};
            // return Request.CreateResponse(HttpStatusCode.OK, reponseModel, GetMediaTypeFormatter(type));
        }

        /// <summary>
        /// Remove ClientId and Update IsStatus to prepare fo resync data
        /// </summary>
        /// <param name="model"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        [Route("api/synchronize/resync2/prepare")]
        [HttpPost]
        public ActionResult<ResponseMessage> ReSynchronizeDownSecondPrepare([FromBody] RequestTerminalModel model, string type = "json")
        {
            if (model == null || !ModelState.IsValid)
                return GetResponseMessageError(HttpStatusCode.BadRequest);

            var terminal = _terminalMasterService.GetTerminalBySerialNumber(model.TerminalSerialNumber, model.TerminalMacAddress, model.TerminalModelId);
            if (terminal == null)
                return GetResponseMessageError(HttpStatusCode.NoContent);

            var terminalSetup = _terminalMasterService.GetTerminalSetupByTerminalId(terminal.Id);
            if (terminalSetup != null)
            {
                terminalSetup.Status = MMS.Core.CoreUTI.Constants.CHANGE_RECORD;
                _terminalSetupService.Update(terminalSetup);
            }
            terminal.IsStatus = MMS.Core.CoreUTI.Constants.CHANGE_RECORD;
            terminal.Support.IsStatus = MMS.Core.CoreUTI.Constants.CHANGE_RECORD;
            _terminalMasterService.Update(terminal);


            #region Processor
            var processorIds = _processorTerminalService.GetIdsTerminalProcessors(terminal.Id).ToList();
               
            _terminalMasterService.SetIsClientId(MMS.Core.CoreUTI.Constants.ProcessorTerminal, processorIds, 0);
            _terminalMasterService.SetIsStatus(MMS.Core.CoreUTI.Constants.ProcessorTerminal, processorIds, MMS.Core.CoreUTI.Constants.CHANGE_RECORD);
            #endregion

            #region Merchant Terminal
            var merchantIds = _merchantTerminalService.GetMerchantTerminalsByTerminalId(terminal.Id)
                    .Where(x => x.ClientId != 0 || x.IsStatus != MMS.Core.CoreUTI.Constants.UPDATED_RECORD)
                    .Select(x => x.Id)
                    .ToList();
            _terminalMasterService.SetIsClientId(MMS.Core.CoreUTI.Constants.MerchantTerminal, merchantIds, 0);
            _terminalMasterService.SetIsStatus(MMS.Core.CoreUTI.Constants.MerchantTerminal, merchantIds, MMS.Core.CoreUTI.Constants.CHANGE_RECORD);
            #endregion

            #region Merchant Processor
            var merchantProcessorIds = (_processorMerchantService.Select(x => x.Id, x => x.MerchantTerminal.TerminalId == terminal.Id &&
            x.MerchantTerminal.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD &&
            x.ProcessorTerminal.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD &&
            x.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD &&
            x.ClientId != 0 || x.IsStatus == MMS.Core.CoreUTI.Constants.UPDATED_RECORD, false,
            p => p.MerchantTerminal, p => p.ProcessorTerminal) as List<int>) ?? new List<int>();
     
            _terminalMasterService.SetIsClientId(MMS.Core.CoreUTI.Constants.ProcessorMerchant, merchantProcessorIds, 0);
            _terminalMasterService.SetIsStatus(MMS.Core.CoreUTI.Constants.ProcessorMerchant, merchantProcessorIds, MMS.Core.CoreUTI.Constants.CHANGE_RECORD);
            #endregion

            #region User Terminal
            var userIds = _userTerminalService.GetAll(includeDeactivated: true)
                    .Where(x => x.TerminalId == terminal.Id)
                    .Where(x => x.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD)
                    .Where(x => x.ClientId != 0 || x.IsStatus == MMS.Core.CoreUTI.Constants.UPDATED_RECORD)
                    .Select(x => x.Id)
                    .ToList();
            _terminalMasterService.SetIsClientId(MMS.Core.CoreUTI.Constants.UserTerminal, userIds, 0);
            _terminalMasterService.SetIsStatus(MMS.Core.CoreUTI.Constants.UserTerminal, userIds, MMS.Core.CoreUTI.Constants.CHANGE_RECORD);
            #endregion

            #region User Merchant
            var userMerchants = _userMerchantService.GetUserMerchantByTerminalId(terminal.Id)
                .Where(x => x.ClientId != 0 || x.IsStatus != MMS.Core.CoreUTI.Constants.UPDATED_RECORD)
                .Select(x => x.Id).ToList();
            _terminalMasterService.SetIsClientId(MMS.Core.CoreUTI.Constants.UserMerchantTerminal, userMerchants, 0);
            _terminalMasterService.SetIsStatus(MMS.Core.CoreUTI.Constants.UserMerchantTerminal, userMerchants, MMS.Core.CoreUTI.Constants.CHANGE_RECORD);
            #endregion

            #region Charity
            var chatityIds = _charityService.GetAll(includeDeactivated: true)
                .Where(x => x.ClientId != 0 || x.IsStatus != MMS.Core.CoreUTI.Constants.UPDATED_RECORD)
                .Select(x => x.Id).ToList();
            _terminalMasterService.SetIsClientId(MMS.Core.CoreUTI.Constants.Charity, chatityIds, 0);
            _terminalMasterService.SetIsStatus(MMS.Core.CoreUTI.Constants.Charity, chatityIds, MMS.Core.CoreUTI.Constants.CHANGE_RECORD);
            #endregion

            #region Merchant Charity
            var merchantCharityIds = _charityService.GetMerchantCharitiesByMerchantIds(merchantIds)
                .Where(x => x.ClientId != 0 || x.IsStatus == MMS.Core.CoreUTI.Constants.UPDATED_RECORD)
                .Select(x => x.Id).ToList();
            _terminalMasterService.SetIsClientId(MMS.Core.CoreUTI.Constants.MerchantCharity, merchantCharityIds, 0);
            _terminalMasterService.SetIsStatus(MMS.Core.CoreUTI.Constants.MerchantCharity, merchantCharityIds, MMS.Core.CoreUTI.Constants.CHANGE_RECORD);
            #endregion

            #region Message Merchant
            var messageMerchantIds = _messageMerchantService.GetMessagesByMerchantIds(merchantIds).Select(x => x.Id).ToList();
            _terminalMasterService.SetIsClientId(MMS.Core.CoreUTI.Constants.MessageMerchant, messageMerchantIds, 0);
            _terminalMasterService.SetIsStatus(MMS.Core.CoreUTI.Constants.MessageMerchant, messageMerchantIds, MMS.Core.CoreUTI.Constants.CHANGE_RECORD);
            #endregion

            #region Bin Table
            var binTableIds = _binTableService.GetAll(x=> x.TerminalId == terminal.Id).Select(x => x.Id).ToList();
            _terminalMasterService.SetIsClientId(MMS.Core.CoreUTI.Constants.BinTable, binTableIds, 0);
            _terminalMasterService.SetIsStatus(MMS.Core.CoreUTI.Constants.BinTable, binTableIds, MMS.Core.CoreUTI.Constants.CHANGE_RECORD);
            #endregion


            //remove terminal changes 
            var terminalChanges = _terminalMasterService.GetTerminalChanges(terminal.Id,
                MMS.Core.CoreUTI.Constants.CHANGE_RECORD);
            foreach (var terminalChange in terminalChanges)
            {
                terminalChange.IsClientUpdated = MMS.Core.CoreUTI.Constants.UPDATED_RECORD;
                _terminalMasterService.Update(terminalChange);
            }
            return GetResponseMessageSuccess("<Response > <Call>1</Call></Response>");
        }

        /// <summary>
        /// in turn get all each object of terminal
        /// </summary>
        /// <param name="model"></param>
        /// <param name="type"></param>
        /// <returns>a object of terminal</returns>
        [Route("api/synchronize/resync2/down")]
        [HttpPost]
        public ActionResult<ResponseMessage> ReSynchronizeDownSecond([FromBody] RequestTerminalModel model, string type = "json")
        {
            ComTerminal comip = null;
            RS232Comm rs232 = null;
            PosHttpServer httpServer = null;
            type = "xml";// will remove
            //param invaild
            if (model == null || !ModelState.IsValid)
                return GetResponseMessageError(HttpStatusCode.BadRequest);

            var terminal = _terminalMasterService.GetTerminalBySerialNumber(model.TerminalSerialNumber,
                model.TerminalMacAddress, model.TerminalModelId);
            if (terminal == null)
                return GetResponseMessageError(HttpStatusCode.NoContent);

            if (terminal.PosRequest != null)
            {
                comip = _comTerminalService.FirstOrDefault(x=> x.PosRequestId == terminal.PosRequest.Id);
                rs232 = _rs232CommService.FirstOrDefault(x=> x.PosRequestId == terminal.PosRequest.Id);
                httpServer = _posHttpServerService.FirstOrDefault(x=> x.PosRequestId == terminal.PosRequest.Id);
            }
            var merchantTerminalIds = _merchantTerminalService.GetMerchantByTerminal(terminal.Id, true).Select(x => x.Id).ToList();

            var responseModel = new Resync();

            #region Processor
            var processors =
                terminal.ProcessorTerminals.Where(x => x.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD)
                    .ToList();

            var payplusProcessor = terminal.PayplusProcessor;

            if (processors != null && processors.Any())
            {
                var processorList = new List<Processor>();

                foreach (var each in processors)
                {
                    if (each.ClientId == 0)
                    {
                        processorList.Add(new Processor(each));
                    }
                }
                if (processorList.Any())
                {
                    processorList.FirstOrDefault().Id = 1;

                    if (payplusProcessor != null)
                        processorList.Add(new Processor(payplusProcessor));

                    responseModel.Processors = processorList;
                    return GetResponseMessageSuccess(responseModel);
                            
                }
            }
            #endregion

            #region Merchant Terminal
            var merchants = _merchantTerminalService.GetMerchantTerminalsByTerminalId(terminal.Id, includeDeactivated: false);

            if (merchants != null && merchants.Any())
            {
                var merchantList = new List<Merchant>();

                //load notification hard code
                //****************************
                var dropdowText = _hardCodeService.GetListHardCodeByCode(MMS.Core.CoreUTI.Constants.Notifications)
                                  .Select(x => new HardCodeModel
                                  {
                                      Id = x.Id,
                                      Code = x.Code,
                                      Name = x.Name,
                                      Value = x.Value
                                  }).ToList();

                var paymentModeFisrt = _terminalMasterService.PaymentModeOptionGet(terminal.Id, merchants);
                foreach (var each in merchants)
                {
                    if (each.ClientId == 0)
                    {
                        //update xml missing
                        //-------------------------------------------------------------------------------------------------------------------
                        var alert = _merchantAlertNotificationService.GetAlertNotificationByMerchantId(each.Id);
                        var langby = _languageByTerminalService.FirstOrDefault(x=> x.MerchantTerminalId == each.Id);
                        var langOption = _languageOptionTerminalService.GetAll(p=> p.MerchantTerminalId  == each.Id);
                        var merchantCharity = _charityService.GetMerchantCharitiesByMerchantId(each.Id);
                        //-------------------------------------------------------------------------------------------------------------------

                        var eachMerchantLoadPaymentModes = _merchantTerminalService.GetPaymentModes(each.Id);
                        each.PaymentModes = eachMerchantLoadPaymentModes.PaymentModes;//update navigation props

                        var eachMerchantLoadMerchantCashs = _merchantTerminalService.GetMerchantCash(each.Id);
                        each.MerchantCash = eachMerchantLoadMerchantCashs.MerchantCash;

                        var eachMerchantLoadMOTOCard = _merchantTerminalService.GetMOTOCard(each.Id);
                        each.MOTOCards = eachMerchantLoadMOTOCard.MOTOCards;

                        var eachEOVMerchantCardCredit = _merchantTerminalService.GetEOVMerchantCreditCard(each.Id);
                        each.EOVMerchantCreditCard = eachEOVMerchantCardCredit.EOVMerchantCreditCard;

                        var eachEOVMerchantCardDebit = _merchantTerminalService.GetEOVMerchantDebitCard(each.Id);
                        each.EOVMerchantDebitCard = eachEOVMerchantCardDebit.EOVMerchantDebitCard;

                        var eachCardLimits = _merchantTerminalService.GetCardLimits(each.Id);
                        each.CardLimits = eachCardLimits.CardLimits;

                        var eachMerchantLoadCashOutFees = _merchantTerminalService.GetCashOutFees(each.Id);
                        each.CashOutFees = eachMerchantLoadCashOutFees.CashOutFees;

                        var eachMerchantMerchantReport = _merchantTerminalService.GetMerchantReport(each.Id);
                        each.MerchantReport = eachMerchantMerchantReport.MerchantReport;

                        var eachMerchantGetPaymentTenderType = _merchantTerminalService.GetPaymentTenderTypes(each.Id);
                        each.PaymentTenderTypes = eachMerchantGetPaymentTenderType.PaymentTenderTypes;

                        var eachEftTenderTypes = _merchantTerminalService.GetMerchantTenderTypes(each.Id);
                        each.TenderTypes = eachEftTenderTypes.TenderTypes;
                        
                        var eachMerchanSetups = _merchantTerminalService.GetSetups(each.Id);
                        each.Setups = eachMerchanSetups.Setups;

                        var eachMerchantSalesTypes = _merchantTerminalService.GetSalesTypes(each.Id);
                        each.SalesTypes = eachMerchantSalesTypes.SalesTypes;

                        var eachMerchantUsers = _merchantTerminalService.GetMerchantUsers(each.Id);
                        each.MerchantUsers = eachMerchantUsers.MerchantUsers;
                        
                        var eachMerchantTips = _merchantTerminalService.GetTips(each.Id);
                        each.Tips = eachMerchantTips.Tips;

                        var eachMerchantSecurityLevels = _merchantTerminalService.GetSecurityLevels(each.Id);
                        each.SecurityLevels = eachMerchantSecurityLevels.SecurityLevels;

                        var processorTerminal = _processorTerminalService.GetProcessorTerminals(x => x.TerminalId == terminal.Id);

                        var merchantCreditCards = _merchantCreditCardsAcceptedService.GetAll(p => p.MerchantTerminalId == each.Id).ToList();

                        var merchantDebitCards = _merchantDebitCardsAcceptedService.GetAll(p => p.MerchantTerminalId == each.Id).ToList();

                        var merchantUserLogon = _merchantUserLogonService.GetByMerchanTerminalId(each.Id);

                        merchantList.Add(new Merchant(each, langOption ?? null, langby ?? null, processorTerminal, alert, dropdowText, paymentModeFisrt, merchantUserLogon, merchantCreditCards, merchantDebitCards));
                    }
                }
                if (merchantList.Any())
                {
                    merchantList.FirstOrDefault().Id = 1;
                    responseModel.Merchants = merchantList;

                    return GetResponseMessageSuccess(responseModel);
                }
            }
            #endregion

            #region User Terminal
            var users =
                terminal.UserTerminals.Where(x => x.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD).ToList();
            if (users != null && users.Any())
            {
                var userList = new List<User>();
                foreach (var user in users)
                {
                    if (user.ClientId == 0)
                    {
                        userList.Add(new User(user));
                    }
                }
                if (userList.Any())
                {
                    var terminalSetupByTerminal = _terminalMasterService.GetTerminalSetupByTerminalId(terminal.Id);

                    responseModel.Users = userList;
                    return GetResponseMessageSuccess(responseModel);
                }
            }
            #endregion

            #region User Merchant Terminal
            var userMerchants = _userMerchantService.GetUserMerchantByTerminalId(terminal.Id);
            if (userMerchants != null && userMerchants.Any())
            {
                var userMerchantList = new List<UserMerchant>();
                foreach (var userMerchant in userMerchants)
                {
                    if (userMerchant.ClientId == 0)
                    {
                        userMerchantList.Add(new UserMerchant(userMerchant));
                    }
                }
                if (userMerchantList.Any())
                {
                    responseModel.UserMerchants = userMerchantList;
                    return GetResponseMessageSuccess(responseModel);
                }
            }
            #endregion

            #region System
            var terminalSetup = _terminalMasterService.GetTerminalSetupByTerminalId(terminal.Id);

            var terminalMasterFallback = _terminalMasterService.GetMerchantCardFallback(terminal.Id);
            terminal.MerchantCardFallbacks = terminalMasterFallback.MerchantCardFallbacks;

            var langugeby = _terminalMasterService.GetLanguageByByTerminalId(terminal.Id);
            if (terminalSetup.Status != MMS.Core.CoreUTI.Constants.UPDATED_RECORD)
            {
                var cloud = _terminalMasterService.GetTerminalCloudByTerminalId(terminal.Id);
                if (terminal.PosRequest != null)
                {
                    terminal.PosRequest.Sale = _systemPosSaleTypeService.GetById(terminal.PosRequest.SaleId.Value);
                    terminal.PosRequest.Bar = _systemPosSaleTypeService.GetById(terminal.PosRequest.BarId.Value);
                    terminal.PosRequest.Customer = _systemPosSaleTypeService.GetById(terminal.PosRequest.CustomerId.Value);
                    terminal.PosRequest.Room = _systemPosSaleTypeService.GetById(terminal.PosRequest.RoomId.Value);
                    terminal.PosRequest.Table = _systemPosSaleTypeService.GetById(terminal.PosRequest.TableId.Value);
                }

                var terminalConfiguration = new TerminalConfigParams()
                {
                    TerminalMaster = terminal,
                    TerminalSetup = terminalSetup,
                    TerminalCloud = cloud,
                    PosHttpServer = httpServer,
                    ComTerminal = comip,
                    RS232Comm = rs232,
                    LanguageBy = langugeby
                };
                
                var system = new SystemModel(terminalConfiguration, Constants.FORMAT_DATE_MM_DD_YYYY, Constants.FORMAT_TIME_HH__MM);
                responseModel.System = system;
                    return GetResponseMessageSuccess(responseModel);
            }
            #endregion

            #region Support
            if (terminal.Support.IsStatus != MMS.Core.CoreUTI.Constants.UPDATED_RECORD)
            {
                var support = new SupportTerminalApi(terminal.Support);
                responseModel.Support = support;
                return GetResponseMessageSuccess(responseModel);
            }
            #endregion

            #region Termial
            if (terminal.IsStatus != MMS.Core.CoreUTI.Constants.UPDATED_RECORD)
            {
                var terminalSetting = new TerminalModelApi(terminal, terminalSetup, Constants.FORMAT_DATE_MM_DD_YYYY, Constants.FORMAT_TIME_HH__MM);
                responseModel.Terminal = terminalSetting;
                return GetResponseMessageSuccess(responseModel);
            }
            #endregion

            #region Merchant Processor
            var merchantProcessors = _processorMerchantService.GetMerchantProcessorsByTerminalId(terminal.Id);
            var merchantProcessorList = new List<MerchantProcessorApi>();
            if (merchantProcessors != null && merchantProcessors.Any())
            {
                foreach (var each in merchantProcessors.Where(each => each.ProcessorTerminal.ClientId != 0 && each.MerchantTerminal.ClientId != 0 &&
                        each.IsStatus != MMS.Core.CoreUTI.Constants.UPDATED_RECORD && each.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD))
                {
                    MerchantProcessorFunctionType function = null;
                    MerchantProcessorTypePaymentsSetup payments = null;
                    MerchantProcessorTypeGivexSetup givex = null;
                    MerchantProcessorTypeGlobalOneSetup global = null;
                    var lib = each.ProcessorTerminal.Proccessor.Library;
                    if (lib != null)
                    {
                        switch (lib.Value)
                        {
                            case Constants.TSYSValue:
                                function =
                                    _merchantProcessorFunctionTypeService.FirstOrDefault(x=> x.MerchantProcessorId == each.Id);
                                break;
                            case Constants.CTPaymentsValue:
                                payments = _merchantProcessorTypePaymentsSetupService.FirstOrDefault(x=> x.MerchantProcessorId == each.Id);
                                break;
                            case Constants.GivexValue:
                                givex = _merchantProcessorTypeGivexSetupService.FirstOrDefault(x=> x.MerchantProcessorId == each.Id);
                                break;
                            case Constants.GlobalOneValue:
                                global = _merchantProcessorTypeGlobalOneSetupService.FirstOrDefault(x => x.MerchantProcessorId == each.Id);
                                break;
                        }
                    }
                    merchantProcessorList.Add(new MerchantProcessorApi(each, function, payments, givex, global));
                }
                if (merchantProcessorList.Any())
                {
                    responseModel.MerchantProcessors = merchantProcessorList;
                    return GetResponseMessageSuccess(responseModel);
                }
            }
            #endregion

            #region Charity
            var charities = _charityService.GetAll(includeDeactivate: false);
            if (charities != null && charities.Any())
            {
                var charityList = new List<CharityApiModel>();

                foreach (var each in charities)
                {
                    if (each.ClientId == 0)
                    {
                        charityList.Add(each.ToApiModel());
                    }
                }
                if (charityList.Any())
                {
                    responseModel.Charities = charityList;
                    return GetResponseMessageSuccess(responseModel);
                }
            }
            #endregion

            #region Merchant Charity
            var merchantCharities = _charityService.GetMerchantCharitiesByMerchantIds(merchantTerminalIds);
            if (merchantCharities != null && merchantCharities.Any())
            {
                var merchantCharityList = new List<MerchantCharityApiModel>();

                foreach (var each in merchantCharities)
                {
                    if (each.ClientId == 0)
                    {
                        merchantCharityList.Add(each.ToApiModel());
                    }
                }

                if (merchantCharityList.Any())
                {
                    responseModel.MerchantCharities = merchantCharityList;
                    return GetResponseMessageSuccess(responseModel);
                }
            }
            #endregion

            #region Bin Table
            var binTable = _binTableService.GetAll(x=> x.TerminalId == terminal.Id).OrderByDescending(x=> x.Created).FirstOrDefault();
            if (binTable != null)
            {
                var binTableApi = new BinTableApi();

                if (binTable.ClientId == 0)
                {
                    binTableApi = new BinTableApi(binTable);
                }
                if (binTableApi.Id != 0)
                {
                    responseModel.BinTable = binTableApi;
                    return GetResponseMessageSuccess(responseModel);
                }
            }
            #endregion
            return GetResponseMessageSuccess("<Resync></Resync>");
        }

        /// <summary>
        /// Client Call this api to add Client Id and change status after resync data
        /// </summary>
        /// <returns> if got full data rerutn recallFalse else return recall </returns>
        [Route("api/synchronize/resync2/down/response")]
        [HttpPost]
        public ActionResult<ResponseMessage> ReSynchronizeDownSecondResponse()
        {
            var type = "json";
            var xml_data = Request.GetRawBodyStringAsync().Result;
            if (string.IsNullOrWhiteSpace(xml_data))
            {
                return GetResponseMessageError(HttpStatusCode.BadRequest);
            }

            var model = XmlParser.XmlDeserialize(xml_data, typeof(ResponseModel)) as ResponseModel;

            if (model == null || !ModelState.IsValid)
                return GetResponseMessageError(HttpStatusCode.BadRequest);

            var terminalUpdated = _terminalMasterService.GetTerminalBySerialNumber(model.TerminalSerialNumber,
                model.TerminalMacAddress, model.TerminalModelId);

            if (terminalUpdated == null)
                return GetResponseMessageError(HttpStatusCode.NoContent);
            var recall = "<Response > <Call>1</Call></Response>";
            var recallFalse = "<Response > <Call>0</Call></Response>";
            try
            {
                #region Merchant
                if (model.Merchants != null && model.Merchants.Any())
                {
                    var i = 0;
                    foreach (var item in model.Merchants)
                    {
                        if (item.isSuccess != 1) continue;

                        var merchantTerminal = _merchantTerminalService.GetMerchantTerminalById(item.CloudId);
                        if (merchantTerminal == null) continue;
                        merchantTerminal.IsStatus = MMS.Core.CoreUTI.Constants.UPDATED_RECORD;
                        merchantTerminal.ClientId = item.ClientId;
                        _merchantTerminalService.Update(merchantTerminal);
                        model.Merchants[i].CloudId = merchantTerminal.Id;
                        model.Merchants[i].isSuccess = 1;
                        model.Merchants[i].Date = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_DATE_MM_DD_YYYY);
                        model.Merchants[i].Time = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_TIME_HH__MM);
                        i++;
                    }
                    return GetResponseMessageSuccess(recall);
                }
                #endregion

                #region Processor
                if (model.Processors != null && model.Processors.Any())
                {
                    var i = 0;
                    foreach (var item in model.Processors)
                    {
                        if (item.isSuccess != 1) continue;
                        var proccessorTerminal = _processorTerminalService.GetById(item.CloudId);
                        if (proccessorTerminal == null) continue;
                        proccessorTerminal.IsStatus = MMS.Core.CoreUTI.Constants.UPDATED_RECORD;
                        proccessorTerminal.ClientId = item.ClientId;
                        _processorTerminalService.Update(proccessorTerminal);
                        model.Processors[i].CloudId = proccessorTerminal.Id;
                        model.Processors[i].isSuccess = 1;
                        model.Processors[i].Date = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_DATE_MM_DD_YYYY);
                        model.Processors[i].Time = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_TIME_HH__MM);
                        i++;
                    }
                    return GetResponseMessageSuccess(recall);
                }
                #endregion

                #region User Terminal
                if (model.Users != null && model.Users.Any())
                {
                    var i = 0;
                    foreach (var item in model.Users)
                    {
                        if (item.isSuccess != 1) continue;
                        var userTerminal = _userTerminalService.GetById(item.CloudId);
                        if (userTerminal == null) continue;
                        userTerminal.IsStatus = MMS.Core.CoreUTI.Constants.UPDATED_RECORD;
                        userTerminal.ClientId = item.ClientId;
                        _userTerminalService.Update(userTerminal);
                        model.Users[i].CloudId = userTerminal.Id;
                        model.Users[i].isSuccess = 1;
                        model.Users[i].Date = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_DATE_MM_DD_YYYY);
                        model.Users[i].Time = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_TIME_HH__MM);
                        i++;
                    }
                    return GetResponseMessageSuccess(recall);
                }
                #endregion

                #region User Mechant
                if (model.UserMerchants != null && model.UserMerchants.Any())
                {
                    var i = 0;
                    foreach (var item in model.UserMerchants)
                    {
                        if (item.isSuccess != 1) continue;
                        var userMerchant = _userMerchantService.GetById(item.CloudId);
                        if (userMerchant == null) continue;
                        userMerchant.IsStatus = MMS.Core.CoreUTI.Constants.UPDATED_RECORD;
                        userMerchant.ClientId = item.ClientId;
                        _userMerchantService.Update(userMerchant);
                        model.UserMerchants[i].CloudId = userMerchant.Id;
                        model.UserMerchants[i].isSuccess = 1;
                        model.UserMerchants[i].Date = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_DATE_MM_DD_YYYY);
                        model.UserMerchants[i].Time = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_TIME_HH__MM);
                        i++;
                    }
                    return GetResponseMessageSuccess(recall);
                }
                #endregion

                #region System
                if (model.System != null && model.System.isSuccess == 1)
                {

                    var setup = _terminalMasterService.GetTerminalSetupByTerminalId(terminalUpdated.Id);
                    if (setup.Status != MMS.Core.CoreUTI.Constants.UPDATED_RECORD)
                    {
                        setup.Status = MMS.Core.CoreUTI.Constants.UPDATED_RECORD;
                        _terminalSetupService.Update(setup);

                        model.System.CloudId = terminalUpdated.Id;
                        model.System.isSuccess = 1;
                        model.System.Date = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_DATE_MM_DD_YYYY);
                        model.System.Time = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_TIME_HH__MM);
                        return GetResponseMessageSuccess(recall);
                    }
                }
                #endregion

                #region Support
                if (model.Support != null && model.Support.isSuccess == 1)
                {
                    var support = terminalUpdated.Support;
                    if (support.IsStatus != MMS.Core.CoreUTI.Constants.UPDATED_RECORD)
                    {
                        support.IsStatus = MMS.Core.CoreUTI.Constants.UPDATED_RECORD;
                        _supportTerminalService.Update(support);
                        model.Support.CloudId = support.Id;
                        model.Support.isSuccess = 1;
                        model.Support.Date = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_DATE_MM_DD_YYYY);
                        model.Support.Time = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_TIME_HH__MM);
                        return GetResponseMessageSuccess(recall);
                    }
                }
                #endregion

                #region Terminal
                if (model.Terminal != null && model.Terminal.isSuccess == 1)
                {
                    if (terminalUpdated.IsStatus != MMS.Core.CoreUTI.Constants.UPDATED_RECORD)
                    {
                        terminalUpdated.IsStatus = MMS.Core.CoreUTI.Constants.UPDATED_RECORD;
                        _terminalMasterService.Update(terminalUpdated);
                        model.Terminal.CloudId = terminalUpdated.Id;
                        model.Terminal.isSuccess = 1;
                        model.Terminal.Date = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_DATE_MM_DD_YYYY);
                        model.Terminal.Time = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_TIME_HH__MM);
                        return GetResponseMessageSuccess(recall);
                    }

                }
                #endregion

                #region Merchant Processor
                if (model.MerchantProcessors != null && model.MerchantProcessors.Any())
                {
                    var i = 0;
                    foreach (var each in model.MerchantProcessors)
                    {
                        if (each.isSuccess != 1) continue;

                        var merchantProcesor = _processorMerchantService.GetMerchantProcessorById(each.CloudId);
                        if (merchantProcesor == null) continue;
                        merchantProcesor.IsStatus = MMS.Core.CoreUTI.Constants.UPDATED_RECORD;
                        if (merchantProcesor.ProcessorMerchantSetup != null)
                        {
                            merchantProcesor.ProcessorMerchantSetup.IsStatus =
                                MMS.Core.CoreUTI.Constants.UPDATED_RECORD;
                        }
                        if (merchantProcesor.ProcessorMerchantSettleBatchSetup != null)
                        {
                            merchantProcesor.ProcessorMerchantSettleBatchSetup.IsStatus =
                                MMS.Core.CoreUTI.Constants.UPDATED_RECORD;
                        }

                        if (merchantProcesor.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD)
                        {
                            _processorMerchantService.Update(merchantProcesor);
                        }
                        model.MerchantProcessors[i].CloudId = merchantProcesor.Id;
                        model.MerchantProcessors[i].isSuccess = 1;
                        model.MerchantProcessors[i].Date = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_DATE_MM_DD_YYYY);
                        model.MerchantProcessors[i].Time = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_TIME_HH__MM);
                        i++;

                    }
                    return GetResponseMessageSuccess(recall);
                }
                #endregion

                #region Charity
                if (model.Charities != null && model.Charities.Any())
                {
                    var i = 0;
                    foreach (var item in model.Charities)
                    {
                        if (item.isSuccess != 1) continue;

                        var charity = _charityService.GetById(item.CloudId);
                        charity.IsStatus = MMS.Core.CoreUTI.Constants.UPDATED_RECORD;
                        charity.ClientId = item.ClientId;
                        _charityService.Update(charity);

                        model.Charities[i].CloudId = charity.Id;
                        model.Charities[i].isSuccess = 1;
                        model.Charities[i].Date = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_DATE_MM_DD_YYYY);
                        model.Charities[i].Time = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_TIME_HH__MM);


                        i++;
                    }
                    return GetResponseMessageSuccess(recall);
                }
                #endregion

                #region Merchant Charity
                if (model.MerchantCharities != null && model.MerchantCharities.Any())
                {
                    var i = 0;
                    foreach (var item in model.MerchantCharities)
                    {
                        if (item.isSuccess != 1) continue;

                        var merchantCharity = _merchantCharityService.GetById(item.CloudId);
                        merchantCharity.IsStatus = MMS.Core.CoreUTI.Constants.UPDATED_RECORD;
                        merchantCharity.ClientId = item.ClientId;
                        _merchantCharityService.Update(merchantCharity);

                        model.MerchantCharities[i].CloudId = merchantCharity.Id;
                        model.MerchantCharities[i].isSuccess = 1;
                        model.MerchantCharities[i].Date = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_DATE_MM_DD_YYYY);
                        model.MerchantCharities[i].Time = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_TIME_HH__MM);
                        i++;
                    }
                    return GetResponseMessageSuccess(recall);
                }
                #endregion

                #region Merchant Message
                if (model.MerchantPrintMessages != null && model.MerchantPrintMessages.Any())
                {
                    var isAllSuccess = true;
                    var i = 0;
                    foreach (var item in model.MerchantPrintMessages)
                    {
                        if (item.isSuccess != 1)
                        {
                            isAllSuccess = false;
                            continue;
                        }

                        var merchantPrintMessages = _messageMerchantService.GetMessageById(item.CloudId);
                        merchantPrintMessages.IsStatus = MMS.Core.CoreUTI.Constants.UPDATED_RECORD;
                        merchantPrintMessages.ClientId = item.ClientId;
                        _messageMerchantService.Update(merchantPrintMessages);

                        model.MerchantPrintMessages[i].CloudId = merchantPrintMessages.Id;
                        model.MerchantPrintMessages[i].isSuccess = 1;
                        model.MerchantPrintMessages[i].Date = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_DATE_MM_DD_YYYY);
                        model.MerchantPrintMessages[i].Time = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_TIME_HH__MM);
                        i++;
                    }
                    return GetResponseMessageSuccess(isAllSuccess ? recallFalse : recall);

                }
                #endregion Message

                #region Bin Table
                if (model.BinTables != null && model.BinTables.Any())
                {
                    var i = 0;
                    foreach (var item in model.BinTables)
                    {
                        if (item.isSuccess != 1) continue;

                        var binTable = _binTableService.GetById(item.CloudId);
                        binTable.IsStatus = MMS.Core.CoreUTI.Constants.UPDATED_RECORD;
                        binTable.ClientId = item.ClientId;
                        _binTableService.Update(binTable);

                        model.BinTables[i].CloudId = binTable.Id;
                        model.BinTables[i].isSuccess = 1;
                        model.BinTables[i].Date = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_DATE_MM_DD_YYYY);
                        model.BinTables[i].Time = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_TIME_HH__MM);

                        i++;
                    }
                    return GetResponseMessageSuccess(recall);
                }
                #endregion

                return GetResponseMessageSuccess(recallFalse);

            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
                return GetResponseMessageError(HttpStatusCode.InternalServerError);
            }
        }

        #endregion

        [Route("api/application/download")]
        [HttpPost]
        public ActionResult<ResponseMessage> ApplicationDownload(TerminalDownloadModel model)
        {
            try
            {
                var terminal = _terminalMasterService.GetTerminalDownload(model.TerminalSerialNumber, model.MacAddress, model.LastIP);

                if (terminal?.ApplicationUpgradeTerminal?.ApplicationVersion != null && !string.IsNullOrWhiteSpace(model.PublicKey))
                {
                    if (!string.IsNullOrWhiteSpace(terminal.ApplicationUpgradeTerminal.ApplicationVersion.FileSelected)
                        && terminal.ApplicationUpgradeTerminal.ApplicationVersion.Date != null
                        && terminal.ApplicationUpgradeTerminal.ApplicationVersion.Date >= DateTime.Now)
                    {
                        var outputfolderpath = Utils.WebUtils.PathDownloadApplicationFolder(model.TerminalSerialNumber);

                        var inputfile = Utils.WebUtils.PathApplicationFolder(terminal.ApplicationUpgradeTerminal.ApplicationVersion.FileSelected);

                        var keyfolderpath = Utils.WebUtils.CreateDirectory(Path.Combine(Directory.GetCurrentDirectory(),Constants.PATH_APPLICATION_KEY, model.TerminalSerialNumber));

#if !DEMO
                        var outputfilepath = Path.Combine(outputfolderpath, terminal.TerminalSerialNumber + ".epc");

                        Package package = new Package();

                        Package.ProgressCallback callback = new Package.ProgressCallback(Progress);

                        package.SetCallback(callback);

                        var server_publickeypath = outputfolderpath + "\\server_publickey";

                        var server_privatekeypath = keyfolderpath + "\\server_privatekey";

                        //write terminal public key from client to file
                        var terminal_publickeypath = outputfolderpath + "\\terminal_publickey";

                        System.IO.File.WriteAllBytes(terminal_publickeypath, Convert.FromBase64String(model.PublicKey));

                        int ret = package.GenerateKey(server_publickeypath, server_privatekeypath);

                        if (ret == 0)
                        {
                            //Encrypt file
                            package.PackageInit("", outputfilepath, outputfolderpath, terminal_publickeypath, server_privatekeypath, Package.PACKAGE_COMPRESS_ENCRYPT);

                            package.Add(inputfile, "/data/data/com.cctech.launchpadapp/files/", terminal.TerminalSerialNumber + ".apk");

                            ret = package.Run();

                            if (ret == 0)
                            {
                                var folderResult = outputfolderpath + "\\" + model.TerminalSerialNumber;

                                Directory.CreateDirectory(folderResult);

                                var keyResult = Path.Combine(folderResult, "server_publickey");
                                System.IO.File.Move(server_publickeypath, keyResult);

                                var apkResult = Path.Combine(folderResult, model.TerminalSerialNumber + ".epc");
                                System.IO.File.Move(outputfilepath, apkResult);

                                var terminalKey = Path.Combine(folderResult, "terminal_publickey");
                                System.IO.File.Move(terminal_publickeypath, terminalKey);

                                var zipPath = Path.Combine(outputfolderpath, model.TerminalSerialNumber + ".zip");

                                ZipFile.CreateFromDirectory(folderResult, zipPath);

                                Response.Headers.Add("ExpireDate", Utils.WebUtils.ConvertDateTimeToString(terminal?.ApplicationUpgradeTerminal?.ApplicationVersion.Date, Constants.FORMAT_DATE_MM_DD_YYYY));

                                var fileStream = System.IO.File.Open(zipPath, FileMode.Open, FileAccess.Read);
                                return File(fileStream, "application/octet-stream", model.TerminalSerialNumber + ".zip");
                            }
                            else
                            {
                                return GetResponseMessageError(HttpStatusCode.NotFound,
    new StringContent("Application version is can not be encrypted key with code " + ret,
Encoding.UTF8,
"application/json"
));
                            }
                        }
                        else
                        {
                            return GetResponseMessageError(HttpStatusCode.NotFound, 
                                new StringContent("Application version is can not be generate key with code " + ret,
                            Encoding.UTF8,
                            "application/json"
                            ));
                        }

#else
                        var zipPath = Path.Combine(outputfolderpath, model.TerminalSerialNumber + ".zip");

                        IActionResult result = new IActionResult(HttpStatusCode.OK);
                        result.Headers.Add("ExpireDate", WebUtils.ConvertDateTimeToString(terminal?.ApplicationUpgradeTerminal?.ApplicationVersion.Date, Constants.FORMAT_DATE_MM_DD_YYYY));

                        var stream = new FileStream(zipPath, FileMode.Open, FileAccess.Read);
                        result.Content = new StreamContent(stream);
                        result.Content.Headers.ContentType =
                            new MediaTypeHeaderValue("application/octet-stream");

                        return result;
#endif
                    }
                    else
                    {
                        return GetResponseMessageError(HttpStatusCode.NotFound, "Application version is not valid");
                    }
                }
                else
                {
                    return GetResponseMessageError(HttpStatusCode.NotFound, "File not found.");
                }
            }
            catch (Exception ex)
            {
                return GetResponseMessageError(HttpStatusCode.InternalServerError, ex.ToString());
            }
        }

        private void Progress(int code, int subcode, string message, UInt32 length)
        {

            switch (code)
            {
                case Package.PACKAGE_STATUS_COMPRESS:
                case Package.PACKAGE_STATUS_UNCOMPRESS:

                    switch (subcode)
                    {
                        case Package.PACKAGE_STATUS_START:

                            break;

                        case Package.PACKAGE_STATUS_DATA:

                            break;

                        case Package.PACKAGE_STATUS_END:

                            break;
                    }

                    break;

                case Package.PACKAGE_STATUS_ENCRYPT:
                case Package.PACKAGE_STATUS_DECRYPT:

                    switch (subcode)
                    {

                        case Package.PACKAGE_STATUS_START:

                            break;

                        case Package.PACKAGE_STATUS_DATA:

                            break;

                        case Package.PACKAGE_STATUS_END:

                            break;

                        case Package.PACKAGE_STATUS_VERIFIED:

                            break;

                        case 42:

                            break;

                        case Package.PACKAGE_STATUS_SIGNED:

                            break;

                    }

                    break;

            }


        }

        [Route("api/synchronize/sales/up/")]
        [HttpPost]
        public ActionResult<ResponseMessage> ReSynchronizeSalesData(string type = "json")
        {
            var text = Request.GetRawBodyStringAsync().Result;
            var xdoc = XDocument.Parse(text);
            var currentNode = (XElement)xdoc.DescendantNodes().ElementAt(1);

            var mac = currentNode.Value;
            currentNode = Next(currentNode);
            var serial = currentNode.Value;
            currentNode = Next(currentNode);
            var model = Convert.ToInt32(currentNode.Value);
            currentNode = Next(currentNode);
            var app = currentNode.Value;
            currentNode = Next(currentNode);
            var firmware = currentNode.Value;
            currentNode = Next(currentNode);
            var terminalUpdated = _terminalMasterService.GetTerminalBySerialNumber(serial,
                mac, model);
            if (terminalUpdated == null)
                return GetResponseMessageError(HttpStatusCode.NoContent);
            var builder = new StringBuilder();
            var writer = XmlWriter.Create(builder);
            //write xml response 
            writer.WriteStartDocument();
            writer.WriteStartElement(ApiLabelXmlConstants.Root);
            //terminal mac address
            writer.WriteStartElement(ApiLabelXmlConstants.TerminalMacAddress);
            writer.WriteString(mac);
            writer.WriteEndElement();
            //terminal serial Number
            writer.WriteStartElement(ApiLabelXmlConstants.TerminalSerialNumber);
            writer.WriteString(serial);
            writer.WriteEndElement();
            //terminal ModelId
            writer.WriteStartElement(ApiLabelXmlConstants.TerminalModelId);
            writer.WriteString(model.ToString());
            writer.WriteEndElement();
            var lastNode = false;
            // IList<TerminalReportBatch> batchesServer = new List<TerminalReportBatch>();
            // IList<TerminalReportOrder> ordersServer = new List<TerminalReportOrder>();
            while (!lastNode)
            {
                if (ApiLabelXmlConstants.Add.Equals(currentNode.Name.LocalName))
                {
                    currentNode = MoveToFirstChild(currentNode);
                    if (ApiLabelXmlConstants.Batch.Equals(currentNode.Name.LocalName))
                    {
                        var batchStr = currentNode.ToString();
                        var serializer = new XmlSerializer(typeof(BatchApiModel));
                        BatchApiModel result;

                        using (TextReader reader = new StringReader(batchStr))
                        {
                            result = (BatchApiModel)serializer.Deserialize(reader);
                        }
                        if (result != null)
                        {
                            // Insert Batch
                            TerminalReportBatch batch;
                            if (
                                _reportService.GetBatches()
                                    .SingleOrDefault(
                                        x => x.ClientId == result.IdBatch && x.TerminalId == terminalUpdated.Id) !=
                                null)
                            {
                                batch = _reportService.GetBatches()
                                    .SingleOrDefault(
                                        x => x.ClientId == result.IdBatch && x.TerminalId == terminalUpdated.Id);
                                batch = result.GetMaster(batch);
                                _reportService.Update(batch);
                            }
                            else
                            {
                                batch = new TerminalReportBatch();
                                batch = result.GetMaster(batch);
                                batch.TerminalId = terminalUpdated.Id;
                                _reportService.Insert(batch);
                            }
                            if (result.CT != null)
                            {
                                var ctError = result.CT.GetMaster(new TerminalReportCtError());
                                ctError.BatchId = batch.Id;
                                _reportService.Insert(ctError);
                            }
                            if (result.TSYS != null)
                            {
                                var tsysError = result.TSYS.GetMaster(new TerminalReportTsysError());
                                tsysError.BatchId = batch.Id;
                                _reportService.Insert(tsysError);
                            }
                            writer.WriteStartElement(ApiLabelXmlConstants.Add);
                            CreateStatusResponse(writer, batch.Id, batch.ClientId, "Batch", 1);
                            writer.WriteEndElement();
                        }
                    } //end if name == Batches 
                    else if (ApiLabelXmlConstants.Order.Equals(currentNode.Name.LocalName))
                    {
                        string orderXml = currentNode.ToString();
                        var serializer = new XmlSerializer(typeof(OrderApiModel));
                        OrderApiModel order = null;

                        using (TextReader reader = new StringReader(orderXml))
                        {
                            order = (OrderApiModel)serializer.Deserialize(reader);
                        }
                        if (order != null)
                        {
                            writer.WriteStartElement(ApiLabelXmlConstants.Add);

                            var orderServer = order.GetMaster(new TerminalReportOrder());
                            if (
                                _reportService.GetOrders()
                                    .SingleOrDefault(
                                        x => x.Batch.TerminalId == terminalUpdated.Id && x.ClientId == order.IdOrder) ==
                                null)
                            {
                                var batch =
                                    _reportService.GetBatches()
                                        .SingleOrDefault(
                                            x => x.ClientId == order.IdBatch && x.TerminalId == terminalUpdated.Id);
                                if (batch != null)
                                {
                                    orderServer.BatchId = batch.Id;
                                    _reportService.Insert(orderServer);
                                    CreateStatusResponse(writer, orderServer.Id, orderServer.ClientId, "Order", 1);
                                }
                                else
                                {
                                    CreateStatusResponse(writer, orderServer.Id, orderServer.ClientId, "Order", 0);
                                }
                            }
                            else
                            {
                                CreateStatusResponse(writer, orderServer.Id, orderServer.ClientId, "Order", 0);
                            }

                            writer.WriteEndElement();
                        }
                    }
                    else if (ApiLabelXmlConstants.Payment.Equals(currentNode.Name.LocalName))
                    {
                        writer.WriteStartElement(ApiLabelXmlConstants.Add);
                        string paymentXml = currentNode.ToString();
                        var serializer = new XmlSerializer(typeof(PaymentsApiModel));
                        PaymentsApiModel payment = null;

                        using (TextReader reader = new StringReader(paymentXml))
                        {
                            payment = (PaymentsApiModel)serializer.Deserialize(reader);
                        }
                        if (payment != null)
                        {
                            var paymentServer = payment.GetMaster(new TerminalReportPayment());
                            var batch =
                                _reportService.GetBatches()
                                    .SingleOrDefault(
                                        x => x.ClientId == payment.IdBatch && x.TerminalId == terminalUpdated.Id);
                            var order =
                                _reportService.GetOrders()
                                    .SingleOrDefault(x => x.ClientId == payment.IdOrder && x.BatchId == batch.Id);
                            if (batch != null && order != null)
                            {
                                paymentServer.BatchId = batch.Id;
                                paymentServer.TerminalId = terminalUpdated.Id;
                                paymentServer.OrderId = order.Id;
                                if (payment.CT != null)
                                {
                                    paymentServer.Ct = payment.CT.GetMaster(new TerminalReportPaymentCt());
                                }
                                if (payment.GIVEX != null)
                                {
                                    paymentServer.Givex = payment.GIVEX.GetMaster(new TerminalReportPaymentGivex());
                                }
                                if (payment.GLOBALONE != null)
                                {
                                    paymentServer.GlobalOne =
                                        payment.GLOBALONE.GetMaster(new TerminalReportPaymentGlobalOne());
                                }
                                if (payment.POS != null)
                                {
                                    paymentServer.Pos = payment.POS.GetMaster(new TerminalReportPaymentPos());
                                }
                                if (payment.TSYS != null)
                                {
                                    paymentServer.Tsys = payment.TSYS.GetMaster(new TerminalReportPaymentTsys());
                                }
                                _reportService.Insert(paymentServer);
                                CreateStatusResponse(writer, paymentServer.Id, paymentServer.ClientId, "Payment", 1);
                            }
                            else
                            {
                                CreateStatusResponse(writer, payment.IdPayment, payment.IdPayment, "Payment", 0);
                            }
                        } // end if payment!=null
                        else
                        {
                            CreateStatusResponse(writer, 0, 0, "Payment", 0);
                        }
                        writer.WriteEndElement();
                    }
                    currentNode = Parent(currentNode);
                    currentNode = Next(currentNode);
                    if (currentNode == null)
                    {
                        lastNode = true;
                    }
                }
                else if (ApiLabelXmlConstants.Update.Equals(currentNode.Name.LocalName))
                {
                    writer.WriteStartElement(ApiLabelXmlConstants.Update);
                    currentNode = MoveToFirstChild(currentNode);
                    if (ApiLabelXmlConstants.Batch.Equals(currentNode.Name.LocalName))
                    {
                        var batchStr = currentNode.ToString();
                        var serializer = new XmlSerializer(typeof(BatchApiModel));
                        BatchApiModel result;

                        using (TextReader reader = new StringReader(batchStr))
                        {
                            result = (BatchApiModel)serializer.Deserialize(reader);
                        }
                        if (result != null)
                        {
                            // Insert Batch
                            TerminalReportBatch batch;
                            if (
                                _reportService.GetBatches()
                                    .SingleOrDefault(
                                        x => x.ClientId == result.IdBatch && x.TerminalId == terminalUpdated.Id) !=
                                null)
                            {
                                batch = _reportService.GetBatches()
                                    .SingleOrDefault(
                                        x => x.ClientId == result.IdBatch && x.TerminalId == terminalUpdated.Id);
                                batch = result.GetMaster(batch);
                                _reportService.Update(batch);
                                CreateStatusResponse(writer, batch.Id, batch.ClientId, "Batch", 1);
                            }
                            else
                            {
                                CreateStatusResponse(writer, result.IdBatch, result.IdBatch, "Batch", 0);
                            }
                        }
                    }
                    else if (ApiLabelXmlConstants.Order.Equals(currentNode.Name.LocalName))
                    {
                        string orderXml = currentNode.ToString();
                        var serializer = new XmlSerializer(typeof(OrderApiModel));
                        OrderApiModel order = null;

                        using (TextReader reader = new StringReader(orderXml))
                        {
                            order = (OrderApiModel)serializer.Deserialize(reader);
                        }
                        if (order != null)
                        {
                            writer.WriteStartElement(ApiLabelXmlConstants.Add);

                            var orderServer =
                                _reportService.GetOrders()
                                    .SingleOrDefault(x => x.ClientId == order.IdOrder && x.Batch.TerminalId == terminalUpdated.Id);
                            var batch =
                                _reportService.GetBatches()
                                    .SingleOrDefault(
                                        x => x.ClientId == order.IdBatch && x.TerminalId == terminalUpdated.Id);
                            if (batch != null && orderServer != null)
                            {
                                orderServer = order.GetMaster(orderServer);
                                orderServer.BatchId = batch.Id;
                                _reportService.Update(orderServer);
                                CreateStatusResponse(writer, orderServer.Id, orderServer.ClientId, "Order", 1);
                            }
                            else
                            {
                                CreateStatusResponse(writer, order.IdOrder, order.IdOrder, "Order", 0);
                            }
                            writer.WriteEndElement();
                        }
                    }
                    else if (ApiLabelXmlConstants.Payment.Equals(currentNode.Name.LocalName))
                    {
                        string paymentXml = currentNode.ToString();
                        var serializer = new XmlSerializer(typeof(PaymentsApiModel));
                        PaymentsApiModel payment = null;

                        using (TextReader reader = new StringReader(paymentXml))
                        {
                            payment = (PaymentsApiModel)serializer.Deserialize(reader);
                        }
                        if (payment != null)
                        {
                            var paymentServer = payment.GetMaster(new TerminalReportPayment());
                            var batch =
                                _reportService.GetBatches()
                                    .SingleOrDefault(
                                        x => x.ClientId == payment.IdBatch && x.TerminalId == terminalUpdated.Id);
                            var order =
                                _reportService.GetOrders()
                                    .SingleOrDefault(x => x.ClientId == payment.IdOrder && x.BatchId == batch.Id);
                            if (batch != null && order != null)
                            {
                                paymentServer.BatchId = batch.Id;
                                paymentServer.TerminalId = terminalUpdated.Id;
                                paymentServer.OrderId = order.Id;
                                if (payment.CT != null)
                                {
                                    paymentServer.Ct = payment.CT.GetMaster(new TerminalReportPaymentCt());
                                }
                                if (payment.GIVEX != null)
                                {
                                    paymentServer.Givex = payment.GIVEX.GetMaster(new TerminalReportPaymentGivex());
                                }
                                if (payment.GLOBALONE != null)
                                {
                                    paymentServer.GlobalOne =
                                        payment.GLOBALONE.GetMaster(new TerminalReportPaymentGlobalOne());
                                }
                                if (payment.POS != null)
                                {
                                    paymentServer.Pos = payment.POS.GetMaster(new TerminalReportPaymentPos());
                                }
                                if (payment.TSYS != null)
                                {
                                    paymentServer.Tsys = payment.TSYS.GetMaster(new TerminalReportPaymentTsys());
                                }
                                _reportService.Insert(paymentServer);
                                CreateStatusResponse(writer, paymentServer.Id, paymentServer.ClientId, "Payment", 1);
                            }
                            else
                            {
                                CreateStatusResponse(writer, payment.IdPayment, payment.IdPayment, "Payment", 0);
                            }
                        } // end if payment!=null
                        else
                        {
                            CreateStatusResponse(writer, 0, 0, "Payment", 0);
                        }
                    }
                    else if (ApiLabelXmlConstants.MerchantProcessor.Equals(currentNode.Name.LocalName))
                    {
                        string merchantProcessor = currentNode.ToString();
                        var serializer = new XmlSerializer(typeof(MerchantProcessorApi));
                        MerchantProcessorApi merchant = null;

                        using (TextReader reader = new StringReader(merchantProcessor))
                        {
                            merchant = (MerchantProcessorApi)serializer.Deserialize(reader);
                        }
                        if (merchant != null)
                        {
                            #region Update Merchant Processor
                            var server = _processorMerchantService.GetMerchantProcessorsQueryable(x =>
                                      x.MerchantTerminal.ClientId == merchant.IdMerchant &&
                                      x.MerchantTerminal.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD &&
                                      x.ProcessorTerminal.ClientId == merchant.IdProcessor &&
                                      x.ProcessorTerminal.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD &&
                                      x.MerchantTerminal.TerminalId == terminalUpdated.Id &&
                                      x.ProcessorTerminal.TerminalId == terminalUpdated.Id &&
                                      x.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD)
                                .SingleOrDefault();
                            if (server != null)
                            {
                                var setupBatch =
                                    _processorMerchantService.GetProcessorSetupSettleByMerchantProcessorId(server.Id);
                                setupBatch = merchant.SettleBatch.GetEntityMaster(setupBatch);
                                var functionType =
                                    _merchantProcessorFunctionTypeService.FirstOrDefault( x=> x.MerchantProcessorId == server.Id);
                                if (functionType == null)
                                {
                                    functionType = new MerchantProcessorFunctionType()
                                    {
                                        MerchantProcessorId = server.Id
                                    };
                                    _merchantProcessorFunctionTypeService.Insert(functionType);
                                }

                                functionType = merchant.GetMasterEntity(functionType);
                                server = merchant.GetEntityMaster(server);
                                _processorMerchantService.Update(server);
                                _processorMerchantSettleBatchSetupService.Update(setupBatch);
                                _merchantProcessorFunctionTypeService.Update(functionType);
                                writer.WriteStartElement("CloudId");
                                writer.WriteString(server.Id.ToString());
                                writer.WriteEndElement();
                                //Client Id
                                writer.WriteStartElement("ClientId");
                                writer.WriteString(merchant.IdMerchant.ToString());
                                writer.WriteEndElement();

                                writer.WriteStartElement("ClientId2");
                                writer.WriteString(merchant.IdProcessor.ToString());
                                writer.WriteEndElement();
                                //Date
                                writer.WriteStartElement("Date");
                                writer.WriteString(DateTime.Now.ToString(Constants.FORMAT_MM_DD_YYYY));
                                writer.WriteEndElement();
                                //Time
                                writer.WriteStartElement("Time");
                                writer.WriteString(DateTime.Now.ToString(Constants.FORMAT_TIME_HH_MM));
                                writer.WriteEndElement();
                                //IsSuccess
                                writer.WriteStartElement("IsSuccess");
                                writer.WriteString("1");
                                writer.WriteEndElement();
                                MerchantProcessorFunctionType function = null;
                                MerchantProcessorTypePaymentsSetup payments = null;
                                MerchantProcessorTypeGivexSetup givex = null;
                                MerchantProcessorTypeGlobalOneSetup global = null;
                                ProcessorMerchantSetup tsys = null;

                                var lib = server.ProcessorTerminal.Proccessor.Library;
                                if (lib != null)
                                {
                                    switch (lib.Value)
                                    {
                                        case Constants.TSYSValue:
                                            if (merchant.TSYS != null)
                                            {
                                                tsys =
                                                    _processorMerchantService.GetProcessorSetupByMerchantId(
                                                        server.MerchantTerminalId,
                                                        server.ProcessorTerminalId);
                                                if (tsys != null)
                                                {
                                                    tsys = merchant.TSYS.GetEntityMaster(tsys);
                                                    _processorMerchantSetupService.Update(tsys);
                                                }
                                            }

                                            break;
                                        case Constants.CTPaymentsValue:
                                            if (merchant.CtPayments != null)
                                            {
                                                payments =
                                                    _merchantProcessorTypePaymentsSetupService.FirstOrDefault(x=> x.MerchantProcessorId == server.Id);
                                                if (payments == null)
                                                {
                                                    payments = new MerchantProcessorTypePaymentsSetup()
                                                    {
                                                        MerchantProcessorId = server.Id
                                                    };
                                                    _merchantProcessorTypePaymentsSetupService.Insert(payments);
                                                }
                                                payments = merchant.CtPayments.GetEntityMaster(payments);
                                                _merchantProcessorTypePaymentsSetupService.Insert(payments);
                                            }
                                            break;
                                        case Constants.GivexValue:
                                            if (merchant.Givex != null)
                                            {
                                                givex =
                                                    _merchantProcessorTypeGivexSetupService.FirstOrDefault(x=> x.MerchantProcessorId == server.Id);
                                                if (givex == null)
                                                {
                                                    givex = new MerchantProcessorTypeGivexSetup()
                                                    {
                                                        MerchantProcessorId = server.Id
                                                    };
                                                    _merchantProcessorTypeGivexSetupService.Insert(givex);
                                                }
                                                givex = merchant.Givex.GetEntityMaster(givex);
                                                _merchantProcessorTypeGivexSetupService.Update(givex);
                                            }
                                            break;
                                        case Constants.GlobalOneValue:
                                            if (merchant.Global != null)
                                            {
                                                global =
                                                    _merchantProcessorTypeGlobalOneSetupService.FirstOrDefault(x => x.MerchantProcessorId == server.Id);
                                                if (global == null)
                                                {
                                                    global = new MerchantProcessorTypeGlobalOneSetup()
                                                    {
                                                        MerchantProcessorId = server.Id
                                                    };
                                                    _merchantProcessorTypeGlobalOneSetupService.Insert(global);
                                                }
                                                global = merchant.Global.GetEntityMaster(global);
                                                _merchantProcessorTypeGlobalOneSetupService.Update(global);
                                            }
                                            break;
                                    }
                                }
                            }
                            else
                            {
                                writer.WriteStartElement("CloudId");
                                writer.WriteString("0");
                                writer.WriteEndElement();
                                //Client Id
                                writer.WriteStartElement("ClientId");
                                writer.WriteString(merchant.IdMerchant.ToString());
                                writer.WriteEndElement();

                                writer.WriteStartElement("ClientId2");
                                writer.WriteString(merchant.IdProcessor.ToString());
                                writer.WriteEndElement();
                                //Date
                                writer.WriteStartElement("Date");
                                writer.WriteString(DateTime.Now.ToString(Constants.FORMAT_MM_DD_YYYY));
                                writer.WriteEndElement();
                                //Time
                                writer.WriteStartElement("Time");
                                writer.WriteString(DateTime.Now.ToString(Constants.FORMAT_TIME_HH_MM));
                                writer.WriteEndElement();
                                //IsSuccess
                                writer.WriteStartElement("IsSuccess");
                                writer.WriteString("0");
                                writer.WriteEndElement();
                            }
                            #endregion
                        }
                    }
                    currentNode = Parent(currentNode);
                    currentNode = Next(currentNode);
                    if (currentNode == null)
                    {
                        lastNode = true;
                    }
                    writer.WriteEndElement();
                }
            }
            writer.WriteEndElement();
            writer.WriteEndDocument();
            writer.Close();
            return GetResponseMessageSuccess(builder.ToString());
        }

        [Route("api/synchronize/alert")]
        [HttpPost]
        public ActionResult<ResponseMessage> GetMerchantAlert()
        {
            var text = Request.GetRawBodyStringAsync().Result;
            var xdoc = XDocument.Parse(text);
            //
            var result = new AlertApiModel();
            using (var stringReader = new System.IO.StringReader(text))
            {
                var serializer = new XmlSerializer(typeof(AlertApiModel));
                result = serializer.Deserialize(stringReader) as AlertApiModel;
            }
            var terminal = _terminalMasterService.GetTerminalBySerialNumber(result.TerminalSerialNumber,
                result.TerminalMacAddress, 1);
            if (terminal != null)
            {
                var history = new TerminalAlertHistory();
                history.TerminalId = terminal.Id;
                var user = _userTerminalService.GetAll(x => x.ClientId == result.IdUser && x.TerminalId == terminal.Id)
                    .SingleOrDefault(x => x.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD);

                // Send message will be implemented later

                history.Type = result.Type;
                history.Date = result.Date;
                history.Time = result.Time;
                _merchantReportService.Insert(history);
            }
            return GetResponseMessageSuccess("<success/>");
        }

        #region Sync Down

        /// <summary>
        /// Get the changes of terminal to update in client
        /// </summary>
        /// <param name="model">RequestTerminalModel</param>
        /// <param name="type">json or xml</param>
        /// <returns>XmlChanges</returns>
        [Route("api/synchronize/sync/down")]
        [HttpPost]
        public ActionResult<ResponseMessage> SynchronizeDown([FromBody] RequestTerminalModel model, string type = "json")
        {
            if (model == null || !ModelState.IsValid)
                return GetResponseMessageError(HttpStatusCode.BadRequest);
            var terminal = _terminalMasterService.GetTerminalBySerialNumber(model.TerminalSerialNumber,
                model.TerminalMacAddress, model.TerminalModelId);

            if (terminal == null)
                return GetResponseMessageError(HttpStatusCode.NoContent);
            try
            {
                //var reponseModel = new ApiModel(terminal, Constants.FORMAT_MM_DD_YYYY, Constants.FORMAT_TIME_HH_MM);

                var xmlResponse = "";
                var scheduleDownloading = _terminalUpdateScheduleService.GetAll(x => x.TerminalId == terminal.Id && x.Status == MMS.Core.CoreUTI.Constants.TERMINAL_SCHEDULE_DOWNLOADING);

                if (scheduleDownloading.Any())
                {
                    xmlResponse = scheduleDownloading.First().XmlChanges;
                    return GetResponseMessageSuccess(xmlResponse);
                }
                var schedules = _terminalUpdateScheduleService.GetAll(x => x.TerminalId == terminal.Id);

                if (schedules.Any())
                {
                    var schedule =
                        schedules.First(x => x.Status == MMS.Core.CoreUTI.Constants.TERMINAL_SCHEDULE_NORMAL);
                    xmlResponse = schedule.XmlChanges;
                    schedule.Status = MMS.Core.CoreUTI.Constants.TERMINAL_SCHEDULE_DOWNLOADING;
                    _terminalUpdateScheduleService.Update(schedule);
                    return GetResponseMessageSuccess(xmlResponse);
                }
                else
                {
                    return GetResponseMessageError(HttpStatusCode.NoContent);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
                return GetResponseMessageError(HttpStatusCode.InternalServerError);
            }
        }

        #endregion

        #region Sync Status Call Back

        [Route("api/synchronize/resync/down/response")]
        [HttpPost]
        public ActionResult<ResponseMessage> ResynchronizeStatusCallBack([FromBody]ResponseModel model, string type = "json")
        {
            //var text = Request.Content.ReadAsStringAsync().Result;
            //System.IO.File.WriteAllText(WebUtils.PathImagesFolder("test.txt"), text);
            //return Request.CreateResponse(HttpStatusCode.OK, text);
            if (model == null || !ModelState.IsValid)
                return GetResponseMessageError(HttpStatusCode.BadRequest);

            var terminalUpdated = _terminalMasterService.GetTerminalBySerialNumber(model.TerminalSerialNumber,
                model.TerminalMacAddress, model.TerminalModelId);

            if (terminalUpdated == null)
                return GetResponseMessageError(HttpStatusCode.NoContent);

            try
            {
                //check support
                if (model.Support != null && model.Support.isSuccess == 1)
                {
                    var support = terminalUpdated.Support;
                    support.IsStatus = MMS.Core.CoreUTI.Constants.NOTCHANGE_RECORD;
                    _supportTerminalService.Update(support);
                    model.Support.CloudId = support.Id;
                    model.Support.isSuccess = 1;
                    model.Support.Date = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                        Utils.Constants.FORMAT_DATE_MM_DD_YYYY);
                    model.Support.Time = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                        Utils.Constants.FORMAT_TIME_HH__MM);
                }

                //check terminal
                if (model.Terminal != null && model.Terminal.isSuccess == 1)
                {
                    terminalUpdated.IsStatus = MMS.Core.CoreUTI.Constants.NOTCHANGE_RECORD;
                    _terminalMasterService.Update(terminalUpdated);
                    model.Terminal.CloudId = terminalUpdated.Id;
                    model.Terminal.isSuccess = 1;
                    model.Terminal.Date = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                        Utils.Constants.FORMAT_DATE_MM_DD_YYYY);
                    model.Terminal.Time = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                        Utils.Constants.FORMAT_TIME_HH__MM);
                }

                //check system
                if (model.System != null && model.System.isSuccess == 1)
                {

                    var cashback = terminalUpdated.CashBack;
                    cashback.IsStatus = MMS.Core.CoreUTI.Constants.NOTCHANGE_RECORD;
                    _deditCardBackTerminalService.Update(cashback);

                    var date = terminalUpdated.Date;
                    date.IsStatus = MMS.Core.CoreUTI.Constants.NOTCHANGE_RECORD;
                    _dateTimeZoneTerminalService.Update(date);

                    var sound = terminalUpdated.Sound;
                    sound.IsStatus = MMS.Core.CoreUTI.Constants.NOTCHANGE_RECORD;
                    _soundTerminalService.Update(sound);

                    model.System.CloudId = terminalUpdated.Id;
                    model.System.isSuccess = 1;
                    model.System.Date = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                        Utils.Constants.FORMAT_DATE_MM_DD_YYYY);
                    model.System.Time = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                        Utils.Constants.FORMAT_TIME_HH__MM);
                }

                //check user
                if (model.Users != null)
                {
                    var i = 0;
                    foreach (var item in model.Users)
                    {
                        if (item.isSuccess != 1) continue;
                        var userTerminal = _userTerminalService.GetById(item.CloudId);
                        if (userTerminal == null) continue;
                        userTerminal.IsStatus = MMS.Core.CoreUTI.Constants.NOTCHANGE_RECORD;
                        userTerminal.ClientId = item.ClientId;
                        _userTerminalService.Update(userTerminal);
                        model.Users[i].CloudId = userTerminal.Id;
                        model.Users[i].isSuccess = 1;
                        model.Users[i].Date = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_DATE_MM_DD_YYYY);
                        model.Users[i].Time = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_TIME_HH__MM);
                        i++;
                    }
                }

                //check processor
                if (model.Processors != null)
                {
                    var i = 0;
                    foreach (var item in model.Processors)
                    {
                        if (item.isSuccess != 1) continue;
                        var proccessorTerminal = _processorTerminalService.GetById(item.CloudId);
                        if (proccessorTerminal == null) continue;
                        proccessorTerminal.IsStatus = MMS.Core.CoreUTI.Constants.NOTCHANGE_RECORD;
                        proccessorTerminal.ClientId = item.ClientId;
                        _processorTerminalService.Update(proccessorTerminal);
                        model.Processors[i].CloudId = proccessorTerminal.Id;
                        model.Processors[i].isSuccess = 1;
                        model.Processors[i].Date = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_DATE_MM_DD_YYYY);
                        model.Processors[i].Time = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_TIME_HH__MM);
                        i++;
                    }
                }

                //check merchant
                if (model.Merchants != null)
                {
                    var i = 0;
                    foreach (var item in model.Merchants)
                    {
                        if (item.isSuccess != 1) continue;
                        var merchantTerminal = _merchantTerminalService.GetById(item.CloudId);
                        if (merchantTerminal == null) continue;
                        merchantTerminal.IsStatus = MMS.Core.CoreUTI.Constants.NOTCHANGE_RECORD;
                        merchantTerminal.ClientId = item.ClientId;
                        _merchantTerminalService.Update(merchantTerminal);
                        model.Merchants[i].CloudId = merchantTerminal.Id;
                        model.Merchants[i].isSuccess = 1;
                        model.Merchants[i].Date = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_DATE_MM_DD_YYYY);
                        model.Merchants[i].Time = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_TIME_HH__MM);
                        i++;
                    }
                }

                if (model.MerchantProcessors != null)
                {
                    var i = 0;
                    foreach (
                        var merchantProcesor in
                            model.MerchantProcessors.Where(item => item.isSuccess == 1)
                                .Select(item => _processorMerchantService.GetMerchantProcessorById(item.CloudId))
                                .Where(merchantProcesor => merchantProcesor != null))
                    {
                        merchantProcesor.IsStatus = MMS.Core.CoreUTI.Constants.NOTCHANGE_RECORD;
                        merchantProcesor.ProcessorMerchantSetup.IsStatus =
                            MMS.Core.CoreUTI.Constants.NOTCHANGE_RECORD;
                        merchantProcesor.ProcessorMerchantSettleBatchSetup.IsStatus =
                            MMS.Core.CoreUTI.Constants.NOTCHANGE_RECORD;
                        if (merchantProcesor.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD)
                        {
                            _processorMerchantService.Update(merchantProcesor);
                        }
                        model.MerchantProcessors[i].CloudId = merchantProcesor.Id;
                        model.MerchantProcessors[i].isSuccess = 1;
                        model.MerchantProcessors[i].Date = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_DATE_MM_DD_YYYY);
                        model.MerchantProcessors[i].Time = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_TIME_HH__MM);
                        i++;
                    }
                }
                //clear changed
                var terminalChanges = _terminalMasterService.GetTerminalChanges(terminalUpdated.Id,
                    MMS.Core.CoreUTI.Constants.CHANGE_RECORD);
                foreach (var each in terminalChanges)
                {
                    each.IsClientUpdated = MMS.Core.CoreUTI.Constants.UPDATED_RECORD;
                    _terminalMasterService.Update(each);
                }
                //Update Client Id for Merchant Processor
                var merchantProcessorUpdates = _processorMerchantService.GetMerchantProcessorByTerminalIdAndStatus(terminalUpdated.Id, 0);
                foreach (var each in merchantProcessorUpdates)
                {
                    if (each.ProcessorTerminal.ClientId != 0 && each.MerchantTerminal.ClientId != 0)
                    {
                        if (each.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD)
                        {
                            each.IsStatus = MMS.Core.CoreUTI.Constants.ADD_RECORD;
                            _processorMerchantService.Update(each);
                        }
                    }
                }
                var users =
                    terminalUpdated.UserTerminals.Where(
                        x => !x.IsStatus.Equals(MMS.Core.CoreUTI.Constants.DELETE_RECORD));
                foreach (var each in users)
                {
                    if (each.ClientId != 0)
                    {
                        //if (each.Merchant != null)  //Todo Fix later
                        //{
                        //    if (each.Merchant.ClientId != 0)
                        //    {
                        //        var changeXml = CreateXmlForUserMerchantTerminal(each.ClientId, each.Id,
                        //            each.Merchant.ClientId);
                        //        _terminalMasterService.Insert(new TerminalChange()
                        //        {
                        //            ChangedXml = changeXml,
                        //            TerminalId = terminalUpdated.Id,
                        //            IsClientUpdated = MMS.Core.CoreUTI.Constants.CHANGE_RECORD
                        //        }, each.Id);
                        //    }
                        //}
                    }
                }
                if (terminalUpdated.Training != null || terminalUpdated.AutoLogin != null)
                {
                    var trainingId = terminalUpdated.Training == null
                        ? 0
                        : (terminalUpdated.Training.User == null ? 0 : terminalUpdated.Training.User.ClientId);
                    var autoLogonId = terminalUpdated.AutoLogin == null
                        ? 0
                        : (terminalUpdated.AutoLogin.User == null ? 0 : terminalUpdated.AutoLogin.User.ClientId);
                }
                return CreateResponseFromStatusCode(HttpStatusCode.OK, model, type);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
                return GetResponseMessageError(HttpStatusCode.InternalServerError);
            }
        }

        #endregion

        [Route("api/synchronize/sync/down/response")]
        [HttpPost]
        public ActionResult<ResponseMessage> SynchronizeDownResponse(string type = "json")
        {
            var text = Request.GetRawBodyStringAsync().Result;
            var xdoc = XDocument.Parse(text);
            var currentNode = (XElement)xdoc.DescendantNodes().ElementAt(1);

            var mac = currentNode.Value;
            currentNode = Next(currentNode);
            var serial = currentNode.Value;
            currentNode = Next(currentNode);
            var model = Convert.ToInt32(currentNode.Value);
            currentNode = Next(currentNode);
            var app = currentNode.Value;
            currentNode = Next(currentNode);
            var firmware = currentNode.Value;
            currentNode = Next(currentNode);
            var terminalUpdated = _terminalMasterService.GetTerminalBySerialNumber(serial,
                mac, model);
            if (terminalUpdated == null)
                return GetResponseMessageError(HttpStatusCode.NoContent);
            var builder = new StringBuilder();
            var writer = XmlWriter.Create(builder);
            var changes = _terminalMasterService.GetTerminalChanges(terminalUpdated.Id,
                MMS.Core.CoreUTI.Constants.CHANGE_RECORD);
            var isSuccess = true;
            while (true)
            {
                bool isEndXml = currentNode == null;
                if (currentNode != null && currentNode.HasElements)
                {
                    var code = GetChangeCodeByXml(currentNode);
                    if (code == null)
                    {
                        isSuccess = false;
                        break;
                    }
                    currentNode = (XElement)currentNode.NextNode;
                }
                if (currentNode == null)
                {
                    isEndXml = true;
                }
                if (isEndXml)
                {
                    break;
                }
            }
            if (isSuccess)
            {
                var schedule = _terminalUpdateScheduleService.FirstOrDefault(x => x.TerminalId == terminalUpdated.Id && x.Status == MMS.Core.CoreUTI.Constants.TERMINAL_SCHEDULE_DOWNLOADING);
                 
                if (schedule != null)
                {
                    var currentTime = DateTime.Now;
                    var updateTime = Utils.WebUtils.GetSecondsDifferenceBetweenTwoDays(currentTime, schedule.Modified);
                    var updateHistory = new TerminalUpdateHistory()
                    {
                        TerminalScheduleId = schedule.Id,
                        ErrorMessage = Constants.SyncDownErrorMessageNoError,
                        ConnectDate = schedule.Modified.ToString(Constants.FORMAT_DATE_MM_DD_YYYY),
                        ConnectTime = schedule.Modified.ToString(Constants.FORMAT_TIME_HH__MM__SS),
                        DownloadTime = updateTime,
                        UpdateStatus = MMS.Core.CoreUTI.Constants.TERMINAL_SCHEDULE_HISTORY_SUCCESS
                    };
                    _terminalUpdateHistoryService.Insert(updateHistory);
                    schedule.Status = MMS.Core.CoreUTI.Constants.TERMINAL_SCHEDULE_UPDATED;
                    _terminalUpdateScheduleService.Update(schedule);
                }

            }
            else
            {
                var schedule = _terminalUpdateScheduleService.FirstOrDefault(x => x.TerminalId == terminalUpdated.Id && x.Status == MMS.Core.CoreUTI.Constants.TERMINAL_SCHEDULE_DOWNLOADING);
                
                if (schedule != null)
                {
                    var currentTime = DateTime.Now;
                    var updateTime = Utils.WebUtils.GetSecondsDifferenceBetweenTwoDays(schedule.Modified, currentTime);
                    var updateHistory = new TerminalUpdateHistory()
                    {
                        TerminalScheduleId = schedule.Id,
                        ErrorMessage = Constants.SyncDownErrorMessageNoError,
                        ConnectDate = schedule.Modified.ToString(Constants.FORMAT_DATE_SCHEDULE_HISTORY),
                        ConnectTime = schedule.Modified.ToString(Constants.FORMAT_TIME_SCHEDULE_HISTORY),
                        DownloadTime = updateTime,
                        UpdateStatus = MMS.Core.CoreUTI.Constants.TERMINAL_SCHEDULE_HISTORY_FAIL
                    };
                    _terminalUpdateHistoryService.Insert(updateHistory);
                }
            }
            return GetResponseMessageSuccess(builder.ToString());
        }

        #region Sync API up

        [Route("api/synchronize/sync/up")]
        [HttpPost]
        public ActionResult<ResponseMessage> SynchronizeUp(string type = "json")
        {
            var text = Request.GetRawBodyStringAsync().Result;
            var xdoc = XDocument.Parse(text);
            var currentNode = (XElement)xdoc.DescendantNodes().ElementAt(1);

            var mac = currentNode.Value;
            currentNode = Next(currentNode);
            var serial = currentNode.Value;
            currentNode = Next(currentNode);
            var model = Convert.ToInt32(currentNode.Value);
            currentNode = Next(currentNode);
            var app = currentNode.Value;
            currentNode = Next(currentNode);
            var firmware = currentNode.Value;
            currentNode = Next(currentNode);
            var terminalUpdated = _terminalMasterService.GetTerminalBySerialNumber(serial,
                mac, model);
            if (terminalUpdated == null)
                return GetResponseMessageError(HttpStatusCode.NoContent);
            var builder = new StringBuilder();
            var writer = XmlWriter.Create(builder);

            //write xml response 
            writer.WriteStartDocument();
            writer.WriteStartElement(ApiLabelXmlConstants.Root);
            //terminal mac address
            writer.WriteStartElement(ApiLabelXmlConstants.TerminalMacAddress);
            writer.WriteString(mac);
            writer.WriteEndElement();
            //terminal serial Number
            writer.WriteStartElement(ApiLabelXmlConstants.TerminalSerialNumber);
            writer.WriteString(serial);
            writer.WriteEndElement();
            //terminal ModelId
            writer.WriteStartElement(ApiLabelXmlConstants.TerminalModelId);
            writer.WriteString(model.ToString());
            writer.WriteEndElement();
            var lastNode = false;
            while (!lastNode)
            {
                if (currentNode.Name.LocalName == ApiLabelXmlConstants.Update)
                {
                    //Start XML response
                    writer.WriteStartElement(ApiLabelXmlConstants.Update);

                    currentNode = MoveToFirstChild(currentNode);

                    switch (currentNode.Name.LocalName)
                    {
                        case ApiLabelXmlConstants.Support:
                            #region Parse Support
                            try
                            {
                                var sp = ParseSupport(MoveToFirstChild(currentNode), false);
                                //go outside Update
                                //Update Support
                                var support = sp.GetEntityMaster(terminalUpdated.Support);
                                support.IsStatus = MMS.Core.CoreUTI.Constants.NOTCHANGE_RECORD;
                                _supportTerminalService.Update(support);
                                CreateStatusResponse(writer, support.Id, sp.Id, ApiLabelXmlConstants.Support, 1);
                            }
                            catch
                            {
                                CreateStatusResponse(writer, 0, 0, ApiLabelXmlConstants.Support, 0);
                            }
                            #endregion
                            break;
                        case ApiLabelXmlConstants.Processor:
                            #region Parse Processor
                            try
                            {
                                var clientProcessor = ParseProcessor(MoveToFirstChild(currentNode));
                                var processorServer =
                                    terminalUpdated.ProcessorTerminals.SingleOrDefault(x => x.ClientId == clientProcessor.ClientId &&
                                        x.ClientId != 0 && x.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD);
                                if (processorServer != null)
                                {
                                    //Update processor Server 
                                    processorServer = clientProcessor.GetProcessorTerminal(processorServer);
                                    _processorTerminalService.Update(processorServer);
                                    var processorMaster = clientProcessor.GetProcessorMaster(processorServer.Proccessor, _processorMasterService);
                                    _processorMasterService.Update(processorMaster);
                                    clientProcessor.UpdateProcessorTSYS(processorServer.Id, _processorMasterService);
                                    clientProcessor.UpdateOtherType(processorServer.Id, _processorMasterService);
                                }
                                else
                                {
                                    CreateStatusResponse(writer, clientProcessor.Id, clientProcessor.ClientId, ApiLabelXmlConstants.Processor, 0);
                                }

                            }
                            catch
                            {
                                CreateStatusResponse(writer, 0, 0, ApiLabelXmlConstants.Processor, 0);
                            }
                            #endregion
                            break;
                        case ApiLabelXmlConstants.Terminal:
                            #region Parse Terminal
                            var client = ParseTerminalModel(MoveToFirstChild(currentNode), false);
                            try
                            {
                                terminalUpdated = client.GetEntityMaster(terminalUpdated, Constants.FORMAT_DATE_MM_DD_YYYY,
                                    Constants.FORMAT_TIME_HH_MM);
                                var setupTerminal =
                                    _terminalMasterService.GetTerminalSetupByTerminalId(terminalUpdated.Id);
                                _terminalMasterService.Update(terminalUpdated);
                                if (setupTerminal == null)
                                {
                                    setupTerminal = new TerminalSetup()
                                    {
                                        TerminalName = terminalUpdated.lszTerminalName
                                    };
                                    _terminalSetupService.Insert(setupTerminal);
                                }
                                else
                                {
                                    setupTerminal.TerminalName = terminalUpdated.lszTerminalName;
                                    _terminalSetupService.Update(setupTerminal);
                                }
                                CreateStatusResponse(writer, terminalUpdated.Id, client.Id, ApiLabelXmlConstants.Terminal, 1);
                            }
                            catch
                            {
                                CreateStatusResponse(writer, terminalUpdated.Id, client.Id, ApiLabelXmlConstants.Terminal, 0);
                            }
                            #endregion
                            break;
                        case ApiLabelXmlConstants.Merchant:
                            #region Parse Merchant
                            currentNode = MoveToFirstChild(currentNode);
                            //while current is value 
                            var clientId = Convert.ToInt32(currentNode.Value);
                            currentNode = Next(currentNode);
                            if (currentNode != null)
                            {
                                //check if merchant is edit merchant master 
                                var merchantTerminals = _merchantTerminalService.GetMerchantByTerminal(terminalUpdated.Id, true);
                                if (merchantTerminals != null)
                                {
                                    var merchant = merchantTerminals
                                        .SingleOrDefault(
                                            x =>
                                                x.ClientId == clientId && x.ClientId != 0 &&
                                                x.IsStatus != MMS.Core.CoreUTI.Constants.NOTCHANGE_RECORD &&
                                                x.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD);
                                    if (merchant != null)
                                    {
                                        ParseMerchant(currentNode, merchant, writer,
                                            _processorTerminalService.GetProcessorTerminals(x => x.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD &&  x.ClientId != 0),
                                        terminalUpdated.Id);
                                        CreateStatusResponse(writer, merchant.Id, clientId,
                                            ApiLabelXmlConstants.Merchant, 1);
                                    }
                                    else
                                    {
                                        CreateStatusResponse(writer, 0, clientId, ApiLabelXmlConstants.Merchant,
                                           0);
                                    }
                                }
                            }
                            currentNode = Parent(currentNode);
                            #endregion
                            break;
                        case ApiLabelXmlConstants.System:
                            #region Parse System
                            try
                            {
                                CreateStatusResponse(writer, terminalUpdated.Id, 0, ApiLabelXmlConstants.System, 1);
                            }
                            catch
                            {
                                CreateStatusResponse(writer, terminalUpdated.Id, 0, ApiLabelXmlConstants.System, 0);
                            }
                            break;
                        #endregion
                        case ApiLabelXmlConstants.User:
                            #region Parse User
                            try
                            {
                                var clientUserIdNode = MoveToFirstChild(currentNode);
                                var clientUserId = Convert.ToInt32(clientUserIdNode.Value);
                                if (terminalUpdated.UserTerminals != null && clientUserId != 0 && clientUserIdNode != null)
                                {
                                    var userTerminal = terminalUpdated.UserTerminals.SingleOrDefault(x => x.ClientId == clientUserId
                                        && x.ClientId != 0 && x.IsStatus != MMS.Core.CoreUTI.Constants.NOTCHANGE_RECORD);
                                    if (userTerminal != null)
                                    {
                                        var userClient = new User().ParseToObject(MoveToFirstChild(currentNode));
                                        userTerminal = (UserTerminal)userClient.GetEntityMaster(userTerminal);
                                        _userTerminalService.Update(userTerminal);
                                        CreateStatusResponse(writer, terminalUpdated.Id, clientUserId, ApiLabelXmlConstants.User, 1);
                                    }
                                    else
                                    {
                                        CreateStatusResponse(writer, terminalUpdated.Id, clientUserId, ApiLabelXmlConstants.User, 0);
                                    }
                                }
                            }
                            catch
                            {

                            }
                            #endregion
                            break;
                        case ApiLabelXmlConstants.MerchantProcessor:
                            #region Parse Merchant Processor
                            //try
                            //{
                            var result = ParseMerchantProcessor(MoveToFirstChild(currentNode),
                                ApiLabelXmlConstants.Update, terminalUpdated.Id);
                            if (result.Id != 0)
                            {
                                writer.WriteStartElement(ApiLabelXmlConstants.MerchantProcessor);
                                writer.WriteStartElement("CloudId");
                                writer.WriteString(result.Id.ToString());
                                writer.WriteEndElement();
                                //Client Id
                                writer.WriteStartElement("ClientId");
                                writer.WriteString(result.IdMerchant.ToString());
                                writer.WriteEndElement();

                                writer.WriteStartElement("ClientId2");
                                writer.WriteString(result.IdProcessor.ToString());
                                writer.WriteEndElement();
                                //Date
                                writer.WriteStartElement("Date");
                                writer.WriteString(DateTime.Now.ToString(Constants.FORMAT_MM_DD_YYYY));
                                writer.WriteEndElement();
                                //Time
                                writer.WriteStartElement("Time");
                                writer.WriteString(DateTime.Now.ToString(Constants.FORMAT_TIME_HH_MM));
                                writer.WriteEndElement();
                                //IsSuccess
                                writer.WriteStartElement("IsSuccess");
                                writer.WriteString("1");
                                writer.WriteEndElement();
                                writer.WriteEndElement();
                            }
                            else
                            {
                                writer.WriteStartElement(ApiLabelXmlConstants.MerchantProcessor);
                                writer.WriteStartElement("CloudId");
                                writer.WriteString(result.Id.ToString());
                                writer.WriteEndElement();
                                //Client Id
                                writer.WriteStartElement("ClientId");
                                writer.WriteString(result.IdMerchant.ToString());
                                writer.WriteEndElement();

                                writer.WriteStartElement("ClientId2");
                                writer.WriteString(result.IdProcessor.ToString());
                                writer.WriteEndElement();
                                //Date
                                writer.WriteStartElement("Date");
                                writer.WriteString(DateTime.Now.ToString(Constants.FORMAT_MM_DD_YYYY));
                                writer.WriteEndElement();
                                //Time
                                writer.WriteStartElement("Time");
                                writer.WriteString(DateTime.Now.ToString(Constants.FORMAT_TIME_HH_MM));
                                writer.WriteEndElement();
                                //IsSuccess
                                writer.WriteStartElement("IsSuccess");
                                writer.WriteString("0");
                                writer.WriteEndElement();
                                writer.WriteEndElement();
                            }
                            //}
                            //catch
                            //{
                            //    CreateStatusResponse(writer, terminalUpdated.Id, clientId, ApiLabelXmlConstants.MerchantProcessor, 0);
                            //}
                            break;
                            #endregion
                    }

                    #region Parse Sales Data

                    if (ApiLabelXmlConstants.Batch.Equals(currentNode.Name.LocalName))
                    {
                        #region Parse Batch
                        var batchStr = currentNode.ToString();
                        var serializer = new XmlSerializer(typeof(BatchApiModel));
                        BatchApiModel result;

                        using (TextReader reader = new StringReader(batchStr))
                        {
                            result = (BatchApiModel)serializer.Deserialize(reader);
                        }
                        if (result != null)
                        {
                            // Insert Batch
                            TerminalReportBatch batch;
                            if (
                                _reportService.GetBatches()
                                    .Where(x => x.Status != MMS.Core.CoreUTI.Constants.DELETE_RECORD)
                                    .SingleOrDefault(
                                        x => x.ClientId == result.IdBatch && x.TerminalId == terminalUpdated.Id) !=
                                null)
                            {
                                batch = _reportService.GetBatches()
                                    .SingleOrDefault(
                                        x => x.ClientId == result.IdBatch && x.TerminalId == terminalUpdated.Id);
                                batch = result.GetMaster(batch);
                                _reportService.Update(batch);
                                CreateStatusResponse(writer, batch.Id, batch.ClientId, "Batch", 1);
                            }
                            else
                            {
                                CreateStatusResponse(writer, result.IdBatch, result.IdBatch, "Batch", 0);
                            }
                        }
                        #endregion
                    }
                    else if (ApiLabelXmlConstants.Order.Equals(currentNode.Name.LocalName))
                    {
                        #region Parse Order
                        string orderXml = currentNode.ToString();
                        var serializer = new XmlSerializer(typeof(OrderApiModel));
                        OrderApiModel order = null;

                        using (TextReader reader = new StringReader(orderXml))
                        {
                            order = (OrderApiModel)serializer.Deserialize(reader);
                        }
                        if (order != null)
                        {
                            var orderServer =
                                _reportService.GetOrders()
                                    .Where(x => x.Batch.Status != MMS.Core.CoreUTI.Constants.DELETE_RECORD)
                                    .SingleOrDefault(x => x.ClientId == order.IdOrder && x.Batch.TerminalId == terminalUpdated.Id);
                            var batch =
                                _reportService.GetBatches()
                                    .Where(x => x.Status != MMS.Core.CoreUTI.Constants.DELETE_RECORD)
                                    .SingleOrDefault(
                                        x => x.ClientId == order.IdBatch && x.TerminalId == terminalUpdated.Id);
                            if (batch != null && orderServer != null)
                            {
                                orderServer = order.GetMaster(orderServer);
                                orderServer.BatchId = batch.Id;
                                _reportService.Update(orderServer);
                                CreateStatusResponse(writer, orderServer.Id, orderServer.ClientId, "Order", 1);
                            }
                            else
                            {
                                CreateStatusResponse(writer, order.IdOrder, order.IdOrder, "Order", 0);
                            }
                        }
                        #endregion
                    }
                    else if (ApiLabelXmlConstants.Payment.Equals(currentNode.Name.LocalName))
                    {
                        #region Parse Payment
                        string paymentXml = currentNode.ToString();
                        var serializer = new XmlSerializer(typeof(PaymentsApiModel));
                        PaymentsApiModel payment = null;

                        using (TextReader reader = new StringReader(paymentXml))
                        {
                            payment = (PaymentsApiModel)serializer.Deserialize(reader);
                        }
                        if (payment != null)
                        {
                            var paymentServer = payment.GetMaster(new TerminalReportPayment());
                            var batch =
                                _reportService.GetBatches()
                                    .Where(x => x.Status != MMS.Core.CoreUTI.Constants.DELETE_RECORD)
                                    .SingleOrDefault(
                                        x => x.ClientId == payment.IdBatch && x.TerminalId == terminalUpdated.Id);
                            var order =
                                _reportService.GetOrders()
                                    .SingleOrDefault(x => x.ClientId == payment.IdOrder && x.BatchId == batch.Id);
                            if (batch != null && order != null)
                            {
                                paymentServer.BatchId = batch.Id;
                                paymentServer.TerminalId = terminalUpdated.Id;
                                paymentServer.OrderId = order.Id;
                                if (payment.CT != null)
                                {
                                    paymentServer.Ct = payment.CT.GetMaster(new TerminalReportPaymentCt());
                                }
                                if (payment.GIVEX != null)
                                {
                                    paymentServer.Givex = payment.GIVEX.GetMaster(new TerminalReportPaymentGivex());
                                }
                                if (payment.GLOBALONE != null)
                                {
                                    paymentServer.GlobalOne =
                                        payment.GLOBALONE.GetMaster(new TerminalReportPaymentGlobalOne());
                                }
                                if (payment.POS != null)
                                {
                                    paymentServer.Pos = payment.POS.GetMaster(new TerminalReportPaymentPos());
                                }
                                if (payment.TSYS != null)
                                {
                                    paymentServer.Tsys = payment.TSYS.GetMaster(new TerminalReportPaymentTsys());
                                }
                                _reportService.Insert(paymentServer);
                                CreateStatusResponse(writer, paymentServer.Id, paymentServer.ClientId, "Payment", 1);
                            }
                            else
                            {
                                CreateStatusResponse(writer, payment.IdPayment, payment.IdPayment, "Payment", 0);
                            }

                        } // end if payment!=null
                        else
                        {
                            CreateStatusResponse(writer, 0, 0, "Payment", 0);
                        }
                        #endregion
                    }
                    #endregion

                    currentNode = Parent(currentNode);
                    //end xml response
                    writer.WriteEndElement();
                }
                else if (currentNode.Name.LocalName == ApiLabelXmlConstants.Add)
                {
                    //Start Xml Response
                    writer.WriteStartElement(ApiLabelXmlConstants.Add);

                    currentNode = MoveToFirstChild(currentNode);
                    //check Add Processor
                    switch (currentNode.Name.LocalName)
                    {
                        case ApiLabelXmlConstants.Processor:
                            #region Parse Processor
                            var client = ParseProcessor(MoveToFirstChild(currentNode));
                            //Add new Processor Setup 
                            var processorType = 0;
                            if (client.ClientId != 0)
                            {
                                if (terminalUpdated.ProcessorTerminals != null)
                                {
                                    if (terminalUpdated.ProcessorTerminals.Select(x => x.ClientId)
                                        .Contains(client.ClientId))
                                    {
                                        CreateStatusResponse(writer, 0, client.ClientId,
                                            ApiLabelXmlConstants.Processor, 0);
                                        //currentNode = Parent(currentNode);
                                        break;
                                    }
                                }

                                if (client.iLibrary != 0 && client.iLibrary != null)
                                {
                                    processorType = _processorMasterService.GetLibraryTypeIdByValue(client.iLibrary.Value);
                                }
                                var processor = new ProcessorMaster()
                                {
                                    ClientId = client.ClientId,
                                    lszProcessorName = client.lszProcessorName,
                                    lszProcessorDLL = client.lszProcessorDLL,
                                    lszURL = client.wszMainAddress,
                                    LibraryId = processorType == 0 ? (int?)null : processorType
                                };
                                _processorMasterService.Insert(processor);
                                var processorTerminal = client.GetProcessorTerminal(new ProcessorTerminal()
                                {
                                    ClientId = client.ClientId,
                                    ProcessorId = processor.Id,
                                    TerminalId = terminalUpdated.Id,
                                });
                                _processorTerminalService.Insert(processorTerminal);
                                client.UpdateProcessorTSYS(processorTerminal.Id, _processorMasterService);
                                client.UpdateOtherType(processorTerminal.Id, _processorMasterService);
                                CreateStatusResponse(writer, processorTerminal.Id, client.ClientId,
                                    ApiLabelXmlConstants.Processor, 1);
                            }
                            else
                            {
                                CreateStatusResponse(writer, terminalUpdated.Id, client.ClientId,
                                    ApiLabelXmlConstants.Processor, 0);
                            }
                            #endregion
                            break;
                        case ApiLabelXmlConstants.Merchant:
                            #region parse Merchant
                            //Insert new Merchant Master 
                            var clientIdNode = MoveToFirstChild(currentNode);
                            var clientId = Convert.ToInt32(clientIdNode.Value);
                            if (clientId != 0)
                            {

                                var merchantTerminals = _merchantTerminalService.GetMerchantByTerminal(terminalUpdated.Id, true);
                                if (merchantTerminals != null)
                                {
                                    if (merchantTerminals.Select(x => x.ClientId).Contains(clientId))
                                    {
                                        CreateStatusResponse(writer, 0, clientId,
                                            ApiLabelXmlConstants.Merchant, 0);
                                        //currentNode = Parent(currentNode);
                                        break;
                                    }
                                }
                                var merchantMaster = ParseMerchantMaster(MoveToFirstChild(currentNode),
                                    new MerchantMaster());
                                _merchantMasterService.Insert(merchantMaster);
                                //insert merchant terminal 
                                var merchantTerminal = new MerchantTerminal()
                                {
                                    MerchantId = merchantMaster.Id,
                                    TerminalId = terminalUpdated.Id,
                                    ClientId = clientId,
                                    IsStatus = MMS.Core.CoreUTI.Constants.ADD_RECORD,
                                    PrintMessage = new MessageMerchant(""),
                                    MerchantReport = new MerchantReport()
                                };
                                _merchantTerminalService.Insert(merchantTerminal);
                                var merchantNew = _merchantTerminalService.GetMerchantTerminalById(merchantTerminal.Id, true);
                                ParseMerchant(MoveToFirstChild(currentNode), merchantNew, writer,
                                    _processorTerminalService.GetProcessorTerminals(), terminalUpdated.Id, Constants.ActionAdd);
                                CreateStatusResponse(writer, terminalUpdated.Id, clientId,
                                    ApiLabelXmlConstants.Merchant, 1);
                            }
                            else
                            {
                                CreateStatusResponse(writer, terminalUpdated.Id, clientId,
                                    ApiLabelXmlConstants.Merchant, 0);
                            }
                            #endregion
                            break;
                        case ApiLabelXmlConstants.User:
                            #region parse User
                            var clientUserIdNode = MoveToFirstChild(currentNode);
                            var clientUserId = Convert.ToInt32(clientUserIdNode.Value);
                            if (clientUserId != 0)
                            {
                                if (terminalUpdated.UserTerminals != null)
                                {
                                    if (terminalUpdated.UserTerminals.Select(x => x.ClientId).Contains(clientUserId))
                                    {
                                        CreateStatusResponse(writer, 0, clientUserId,
                                            ApiLabelXmlConstants.User, 0);
                                        //currentNode = Parent(currentNode);
                                        break;
                                    }
                                }

                                var user = (User)new User().ParseToObject(MoveToFirstChild(currentNode));
                                var userMaster = user.GetUserMaster(new UserMaster());
                                _userMasterService.Insert(userMaster);


                                var userTerminal = user.GetUserTerminal(new UserTerminal()
                                {
                                    UserMasterId = userMaster.Id,
                                    TerminalId = terminalUpdated.Id,
                                    ClientId = clientUserId
                                });
                                _userTerminalService.Insert(userTerminal);
                                CreateStatusResponse(writer, terminalUpdated.Id, clientUserId,
                                    ApiLabelXmlConstants.User, 1);
                            }
                            else
                            {
                                CreateStatusResponse(writer, terminalUpdated.Id, clientUserId,
                                    ApiLabelXmlConstants.User, 0);
                            }
                            #endregion
                            break;
                        case ApiLabelXmlConstants.MerchantProcessor:
                            #region Parse Merchant Processor
                            //try
                            //{

                            //}
                            //catch
                            //{

                            //}
                            var result = ParseMerchantProcessor(MoveToFirstChild(currentNode), ApiLabelXmlConstants.Update, terminalUpdated.Id);
                            if (result.Id != 0)
                            {
                                writer.WriteStartElement(ApiLabelXmlConstants.MerchantProcessor);
                                writer.WriteStartElement("CloudId");
                                writer.WriteString(result.Id.ToString());
                                writer.WriteEndElement();
                                //Client Id
                                writer.WriteStartElement("ClientId");
                                writer.WriteString(result.IdMerchant.ToString());
                                writer.WriteEndElement();

                                writer.WriteStartElement("ClientId2");
                                writer.WriteString(result.IdProcessor.ToString());
                                writer.WriteEndElement();
                                //Date
                                writer.WriteStartElement("Date");
                                writer.WriteString(DateTime.Now.ToString(Constants.FORMAT_MM_DD_YYYY));
                                writer.WriteEndElement();
                                //Time
                                writer.WriteStartElement("Time");
                                writer.WriteString(DateTime.Now.ToString(Constants.FORMAT_TIME_HH_MM));
                                writer.WriteEndElement();
                                //IsSuccess
                                writer.WriteStartElement("IsSuccess");
                                writer.WriteString("1");
                                writer.WriteEndElement();
                                writer.WriteEndElement();
                            }
                            else
                            {
                                writer.WriteStartElement(ApiLabelXmlConstants.MerchantProcessor);
                                writer.WriteStartElement("CloudId");
                                writer.WriteString(result.Id.ToString());
                                writer.WriteEndElement();
                                //Client Id
                                writer.WriteStartElement("ClientId");
                                writer.WriteString(result.IdMerchant.ToString());
                                writer.WriteEndElement();

                                writer.WriteStartElement("ClientId2");
                                writer.WriteString(result.IdProcessor.ToString());
                                writer.WriteEndElement();
                                //Date
                                writer.WriteStartElement("Date");
                                writer.WriteString(DateTime.Now.ToString(Constants.FORMAT_MM_DD_YYYY));
                                writer.WriteEndElement();
                                //Time
                                writer.WriteStartElement("Time");
                                writer.WriteString(DateTime.Now.ToString(Constants.FORMAT_TIME_HH_MM));
                                writer.WriteEndElement();
                                //IsSuccess
                                writer.WriteStartElement("IsSuccess");
                                writer.WriteString("0");
                                writer.WriteEndElement();
                                writer.WriteEndElement();
                            }
                            #endregion
                            break;
                    }
                    #region Parse Sales Data
                    if (ApiLabelXmlConstants.Batch.Equals(currentNode.Name.LocalName))
                    {
                        #region Parse Batch
                        var batchStr = currentNode.ToString();
                        var serializer = new XmlSerializer(typeof(BatchApiModel));
                        BatchApiModel result;

                        using (TextReader reader = new StringReader(batchStr))
                        {
                            result = (BatchApiModel)serializer.Deserialize(reader);
                        }
                        if (result != null)
                        {
                            // Insert Batch
                            TerminalReportBatch batch;
                            if (
                                _reportService.GetBatches()
                                    .Where(x => x.Status != MMS.Core.CoreUTI.Constants.DELETE_RECORD)
                                    .SingleOrDefault(
                                        x => x.ClientId == result.IdBatch && x.TerminalId == terminalUpdated.Id) !=
                                null)
                            {
                                batch = _reportService.GetBatches()
                                    .SingleOrDefault(
                                        x => x.ClientId == result.IdBatch && x.TerminalId == terminalUpdated.Id);
                                batch = result.GetMaster(batch);
                                _reportService.Update(batch);
                            }
                            else
                            {
                                batch = new TerminalReportBatch();
                                batch = result.GetMaster(batch);
                                batch.TerminalId = terminalUpdated.Id;
                                _reportService.Insert(batch);
                            }
                            if (result.CT != null)
                            {
                                var ctError = result.CT.GetMaster(new TerminalReportCtError());
                                ctError.BatchId = batch.Id;
                                _reportService.Insert(ctError);
                            }
                            if (result.TSYS != null)
                            {
                                var tsysError = result.TSYS.GetMaster(new TerminalReportTsysError());
                                tsysError.BatchId = batch.Id;
                                _reportService.Insert(tsysError);
                            }
                            CreateStatusResponse(writer, batch.Id, batch.ClientId, "Batch", 1);
                        }
                        #endregion
                    } //end if name == Batches 
                    else if (ApiLabelXmlConstants.Order.Equals(currentNode.Name.LocalName))
                    {
                        #region Parse Order
                        string orderXml = currentNode.ToString();
                        var serializer = new XmlSerializer(typeof(OrderApiModel));
                        OrderApiModel order = null;

                        using (TextReader reader = new StringReader(orderXml))
                        {
                            order = (OrderApiModel)serializer.Deserialize(reader);
                        }
                        if (order != null)
                        {
                            var orderServer = order.GetMaster(new TerminalReportOrder());
                            if (
                                _reportService.GetOrders()
                                    .Where(x => x.Batch.Status != MMS.Core.CoreUTI.Constants.DELETE_RECORD)
                                    .SingleOrDefault(
                                        x => x.Batch.TerminalId == terminalUpdated.Id && x.ClientId == order.IdOrder) ==
                                null)
                            {
                                var batch =
                                    _reportService.GetBatches()
                                        .Where(x => x.Status != MMS.Core.CoreUTI.Constants.DELETE_RECORD)
                                        .SingleOrDefault(
                                            x => x.ClientId == order.IdBatch && x.TerminalId == terminalUpdated.Id);
                                if (batch != null)
                                {
                                    orderServer.BatchId = batch.Id;
                                    _reportService.Insert(orderServer);
                                    CreateStatusResponse(writer, orderServer.Id, orderServer.ClientId, "Order", 1);
                                }
                                else
                                {
                                    CreateStatusResponse(writer, orderServer.Id, orderServer.ClientId, "Order", 0);
                                }
                            }
                            else
                            {
                                CreateStatusResponse(writer, orderServer.Id, orderServer.ClientId, "Order", 0);
                            }
                        }
                        #endregion
                    }
                    else if (ApiLabelXmlConstants.Payment.Equals(currentNode.Name.LocalName))
                    {
                        #region Parse Payment
                        string paymentXml = currentNode.ToString();
                        var serializer = new XmlSerializer(typeof(PaymentsApiModel));
                        PaymentsApiModel payment = null;

                        using (TextReader reader = new StringReader(paymentXml))
                        {
                            payment = (PaymentsApiModel)serializer.Deserialize(reader);
                        }
                        if (payment != null)
                        {
                            var paymentServer = payment.GetMaster(new TerminalReportPayment());
                            var batch =
                                _reportService.GetBatches()
                                    .Where(x => x.Status != MMS.Core.CoreUTI.Constants.DELETE_RECORD)
                                    .SingleOrDefault(
                                        x => x.ClientId == payment.IdBatch && x.TerminalId == terminalUpdated.Id);
                            var order =
                                _reportService.GetOrders()
                                    .Where(x => x.Batch.Status != MMS.Core.CoreUTI.Constants.DELETE_RECORD)
                                    .SingleOrDefault(x => x.ClientId == payment.IdOrder && x.BatchId == batch.Id);
                            if (batch != null && order != null)
                            {
                                paymentServer.BatchId = batch.Id;
                                paymentServer.TerminalId = terminalUpdated.Id;
                                paymentServer.OrderId = order.Id;
                                if (payment.CT != null)
                                {
                                    paymentServer.Ct = payment.CT.GetMaster(new TerminalReportPaymentCt());
                                }
                                if (payment.GIVEX != null)
                                {
                                    paymentServer.Givex = payment.GIVEX.GetMaster(new TerminalReportPaymentGivex());
                                }
                                if (payment.GLOBALONE != null)
                                {
                                    paymentServer.GlobalOne =
                                        payment.GLOBALONE.GetMaster(new TerminalReportPaymentGlobalOne());
                                }
                                if (payment.POS != null)
                                {
                                    paymentServer.Pos = payment.POS.GetMaster(new TerminalReportPaymentPos());
                                }
                                if (payment.TSYS != null)
                                {
                                    paymentServer.Tsys = payment.TSYS.GetMaster(new TerminalReportPaymentTsys());
                                }
                                _reportService.Insert(paymentServer);
                                CreateStatusResponse(writer, paymentServer.Id, paymentServer.ClientId, "Payment", 1);
                            }
                            else
                            {
                                CreateStatusResponse(writer, payment.IdPayment, payment.IdPayment, "Payment", 0);
                            }
                        } // end if payment!=null
                        else
                        {
                            CreateStatusResponse(writer, 0, 0, "Payment", 0);
                        }
                        #endregion
                    }
                    currentNode = Parent(currentNode);
                    writer.WriteEndElement();//end Update Tag Xml
                    #endregion
                }
                else if (currentNode.Name.LocalName == ApiLabelXmlConstants.Delete)
                {
                    //Start Xml Response
                    writer.WriteStartElement(ApiLabelXmlConstants.Delete);

                    currentNode = MoveToFirstChild(currentNode);
                    //check Add Processor
                    switch (currentNode.Name.LocalName)
                    {
                        case ApiLabelXmlConstants.Processor:
                            #region Parse Processor
                            var clientProcessorDeleteIdNode = MoveToFirstChild(currentNode);
                            var clientProcessorDeleteId = Convert.ToInt32(clientProcessorDeleteIdNode.Value);
                            var processors = terminalUpdated.ProcessorTerminals;
                            if (processors != null && clientProcessorDeleteId != 0)
                            {
                                var deleleProcessor =
                                    processors.SingleOrDefault(
                                        x =>
                                            x.ClientId == clientProcessorDeleteId && x.ClientId != 0 &&
                                            x.IsStatus != MMS.Core.CoreUTI.Constants.NOTCHANGE_RECORD);
                                if (deleleProcessor != null)
                                {
                                    deleleProcessor.IsStatus = MMS.Core.CoreUTI.Constants.DELETE_RECORD;
                                    _processorTerminalService.Update(deleleProcessor);
                                    CreateStatusResponse(writer, deleleProcessor.Id, clientProcessorDeleteId,
                                        ApiLabelXmlConstants.Processor, 1);
                                }
                                else
                                {
                                    CreateStatusResponse(writer, terminalUpdated.Id, clientProcessorDeleteId,
                                        ApiLabelXmlConstants.Processor, 0);
                                }
                            }
                            else
                            {
                                CreateStatusResponse(writer, terminalUpdated.Id, clientProcessorDeleteId,
                                        ApiLabelXmlConstants.Processor, 0);
                            }
                            #endregion
                            break;
                        case ApiLabelXmlConstants.Merchant:
                            #region Parse Merchant
                            var clientIdNode = MoveToFirstChild(currentNode);
                            var clientId = Convert.ToInt32(clientIdNode.Value);
                            var merchants = _merchantTerminalService.GetMerchantByTerminal(terminalUpdated.Id, true);

                            if (merchants != null && clientId != 0)
                            {
                                var deleteMerchant =
                                    merchants.SingleOrDefault(
                                        x =>
                                            x.ClientId == clientId && x.ClientId != 0 &&
                                            x.IsStatus != MMS.Core.CoreUTI.Constants.NOTCHANGE_RECORD);
                                if (deleteMerchant != null)
                                {
                                    deleteMerchant.IsStatus = MMS.Core.CoreUTI.Constants.DELETE_RECORD;
                                    _merchantTerminalService.Update(deleteMerchant);
                                    CreateStatusResponse(writer, deleteMerchant.Id, clientId,
                                       ApiLabelXmlConstants.Merchant, 1);
                                }
                                else
                                {
                                    CreateStatusResponse(writer, clientId, clientId,
                                       ApiLabelXmlConstants.Merchant, 0);
                                }
                            }
                            else
                            {
                                CreateStatusResponse(writer, clientId, clientId,
                                   ApiLabelXmlConstants.Merchant, 0);
                            }
                            #endregion
                            break;
                        case ApiLabelXmlConstants.User:
                            #region User
                            var clientUserIdNode = MoveToFirstChild(currentNode);
                            var clientUserId = Convert.ToInt32(clientUserIdNode.Value);
                            var users = terminalUpdated.UserTerminals;
                            if (users != null && clientUserId != 0)
                            {
                                var deleteUser =
                                    users.SingleOrDefault(
                                        x =>
                                            x.ClientId == clientUserId && x.ClientId != 0 &&
                                            x.IsStatus != MMS.Core.CoreUTI.Constants.NOTCHANGE_RECORD);
                                if (deleteUser != null)
                                {
                                    deleteUser.IsStatus = MMS.Core.CoreUTI.Constants.DELETE_RECORD;
                                    _userTerminalService.Update(deleteUser);
                                    CreateStatusResponse(writer, deleteUser.Id, clientUserId,
                                       ApiLabelXmlConstants.User, 1);
                                }
                                else
                                {
                                    CreateStatusResponse(writer, clientUserId, clientUserId,
                                       ApiLabelXmlConstants.User, 0);
                                }
                            }
                            else
                            {
                                CreateStatusResponse(writer, clientUserId, clientUserId,
                                    ApiLabelXmlConstants.User, 0);
                            }
                            #endregion
                            break;
                    }

                    currentNode = Parent(currentNode);

                    writer.WriteEndElement();
                }
                if (currentNode.NextNode == null)
                {
                    lastNode = true;
                }
                if (!lastNode)
                {
                    currentNode = Next(currentNode);
                }
            }
            writer.WriteEndElement();
            writer.WriteEndDocument();
            writer.Close();

            return GetResponseMessageSuccess(builder.ToString());
        }

        [Route("api/synchronize/resync/up/prepare")]
        [HttpPost]
        public ActionResult<ResponseMessage> PrepareSyncAllData([FromForm] RequestTerminalModel model, string type = "json")
        {
            // TODO: Currently we don't support Sync Up.
            return GetResponseMessageSuccess(ResponseYes);

            var terminal = _terminalMasterService.GetTerminalBySerialNumber(model.TerminalSerialNumber,
                model.TerminalMacAddress, model.TerminalModelId);
            if (terminal == null)
            {
                return GetResponseMessageSuccess(ResponseNo);
            }
            var whereTerminal = "where terminalId = " + terminal.Id;

            _terminalMasterService.SetIsStatus(MMS.Core.CoreUTI.Constants.MerchantTerminal, whereTerminal,
                MMS.Core.CoreUTI.Constants.DELETE_RECORD);

            _terminalMasterService.SetIsStatus(MMS.Core.CoreUTI.Constants.ProcessorTerminal, whereTerminal,
                MMS.Core.CoreUTI.Constants.DELETE_RECORD);

            _terminalMasterService.SetIsStatus(MMS.Core.CoreUTI.Constants.UserTerminal, whereTerminal,
                MMS.Core.CoreUTI.Constants.DELETE_RECORD);

            _terminalMasterService.SetIsStatus(MMS.Core.CoreUTI.Constants.TerminalReportBatches, whereTerminal,
                MMS.Core.CoreUTI.Constants.DELETE_RECORD);

            _terminalMasterService.Update(terminal);

            return GetResponseMessageSuccess(ResponseYes);
        }

        [Route("api/synchronize/resync/up/restore")]
        [HttpPost]
        public async Task<ActionResult<ResponseMessage>> RestoreSyncAllData([FromForm] RequestTerminalModel model, string type = "json")
        {
            var terminal = _terminalMasterService.GetTerminalBySerialNumber(model.TerminalSerialNumber,
                model.TerminalMacAddress, model.TerminalModelId);
            if (terminal == null)
            {
                return GetResponseMessageSuccess(ResponseNo);
            }
            var whereTerminal = "where terminalId = " + terminal.Id;

            await _terminalMasterService.SetIsStatusWithColumnGroupAsync(MMS.Core.CoreUTI.Constants.MerchantTerminal, whereTerminal,
                MMS.Core.CoreUTI.Constants.NOTCHANGE_RECORD, "MerchantId");

            await _terminalMasterService.SetIsStatusWithColumnGroupAsync(MMS.Core.CoreUTI.Constants.ProcessorTerminal, whereTerminal,
                MMS.Core.CoreUTI.Constants.NOTCHANGE_RECORD, "ProcessorId");

            await _terminalMasterService.SetIsStatusWithColumnGroupAsync(MMS.Core.CoreUTI.Constants.UserTerminal, whereTerminal,
                MMS.Core.CoreUTI.Constants.NOTCHANGE_RECORD, "UserMasterId");

            _terminalMasterService.SetIsStatus(MMS.Core.CoreUTI.Constants.TerminalReportBatches, whereTerminal,
                MMS.Core.CoreUTI.Constants.NOTCHANGE_RECORD);

            _terminalMasterService.Update(terminal);

            return GetResponseMessageSuccess(ResponseYes);
        }

        [Route("api/synchronize/resync/up")]
        [HttpPost]
        public ActionResult<ResponseMessage> SyncAllData(string type = "json")
        {
            var text = Request.GetRawBodyStringAsync().Result;
            var xdoc = XDocument.Parse(text);
            var currentNode = (XElement)xdoc.DescendantNodes().ElementAt(1);

            var mac = currentNode.Value;
            currentNode = Next(currentNode);
            var serial = currentNode.Value;
            currentNode = Next(currentNode);
            var model = Convert.ToInt32(currentNode.Value);
            currentNode = Next(currentNode);
            var app = currentNode.Value;
            currentNode = Next(currentNode);
            var firmware = currentNode.Value;
            currentNode = Next(currentNode);
            var terminalUpdated = _terminalMasterService.GetTerminalBySerialNumber(serial,
                mac, model);
            if (terminalUpdated == null)
                return GetResponseMessageError(HttpStatusCode.NoContent);
            var builder = new StringBuilder();
            var writer = XmlWriter.Create(builder);

            //write xml response 
            writer.WriteStartDocument();
            writer.WriteStartElement(ApiLabelXmlConstants.Root);
            //terminal mac address
            writer.WriteStartElement(ApiLabelXmlConstants.TerminalMacAddress);
            writer.WriteString(mac);
            writer.WriteEndElement();
            //terminal serial Number
            writer.WriteStartElement(ApiLabelXmlConstants.TerminalSerialNumber);
            writer.WriteString(serial);
            writer.WriteEndElement();
            //terminal ModelId
            writer.WriteStartElement(ApiLabelXmlConstants.TerminalModelId);
            writer.WriteString(model.ToString());
            writer.WriteEndElement();
            var lastNode = false;
            while (!lastNode)
            {
                if (currentNode.Name.LocalName == ApiLabelXmlConstants.Add)
                {
                    //Start Xml Response
                    writer.WriteStartElement(ApiLabelXmlConstants.Add);

                    currentNode = MoveToFirstChild(currentNode);
                    //check Add Processor
                    switch (currentNode.Name.LocalName)
                    {
                        case ApiLabelXmlConstants.Processor:
                            #region Parse Processor
                            var client = ParseProcessor(MoveToFirstChild(currentNode));
                            //Add new Processor Setup 
                            var processorType = 0;
                            if (client.ClientId != 0)
                            {
                                if (terminalUpdated.ProcessorTerminals != null)
                                {
                                    if (terminalUpdated.ProcessorTerminals
                                        .Where(x => x.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD)
                                        .Select(x => x.ClientId)
                                        .Contains(client.ClientId))
                                    {
                                        CreateStatusResponse(writer, 0, client.ClientId,
                                            ApiLabelXmlConstants.Processor, 0);
                                        //currentNode = Parent(currentNode);
                                        break;
                                    }
                                }

                                if (client.iLibrary != 0 && client.iLibrary != null)
                                {
                                    processorType = _processorMasterService.GetLibraryTypeIdByValue(client.iLibrary.Value);
                                }
                                var processor = new ProcessorMaster()
                                {
                                    ClientId = client.ClientId,
                                    lszProcessorName = client.lszProcessorName,
                                    lszProcessorDLL = client.lszProcessorDLL,
                                    lszURL = client.wszMainAddress,
                                    LibraryId = processorType == 0 ? (int?)null : processorType
                                };
                                _processorMasterService.Insert(processor);
                                var processorTerminal = client.GetProcessorTerminal(new ProcessorTerminal()
                                {
                                    ClientId = client.ClientId,
                                    ProcessorId = processor.Id,
                                    TerminalId = terminalUpdated.Id,
                                });
                                _processorTerminalService.Insert(processorTerminal);
                                client.UpdateProcessorTSYS(processorTerminal.Id, _processorMasterService);
                                client.UpdateOtherType(processorTerminal.Id, _processorMasterService);
                                CreateStatusResponse(writer, processorTerminal.Id, client.ClientId,
                                    ApiLabelXmlConstants.Processor, 1);
                            }
                            else
                            {
                                CreateStatusResponse(writer, terminalUpdated.Id, client.ClientId,
                                    ApiLabelXmlConstants.Processor, 0);
                            }
                            #endregion
                            break;
                        case ApiLabelXmlConstants.Merchant:
                            #region Parse Merchant
                            //Insert new Merchant Master 
                            var clientIdNode = MoveToFirstChild(currentNode);
                            var clientId = Convert.ToInt32(clientIdNode.Value);
                            if (clientId != 0)
                            {
                                var merchantTerminals = _merchantTerminalService.GetMerchantByTerminal(terminalUpdated.Id, true);
                                if (merchantTerminals != null)
                                {
                                    if (merchantTerminals
                                        .Where(x => x.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD)
                                        .Select(x => x.ClientId).Contains(clientId))
                                    {
                                        CreateStatusResponse(writer, 0, clientId,
                                            ApiLabelXmlConstants.Merchant, 0);
                                        //currentNode = Parent(currentNode);
                                        break;
                                    }
                                }
                                var merchantMaster = ParseMerchantMaster(MoveToFirstChild(currentNode),
                                    new MerchantMaster());
                                _merchantMasterService.Insert(merchantMaster);
                                //insert merchant terminal 
                                var merchantTerminal = new MerchantTerminal()
                                {
                                    MerchantId = merchantMaster.Id,
                                    TerminalId = terminalUpdated.Id,
                                    ClientId = clientId,
                                    IsStatus = MMS.Core.CoreUTI.Constants.ADD_RECORD,
                                    PrintMessage = new MessageMerchant(""),
                                    MerchantReport = new MerchantReport()
                                };
                                _merchantTerminalService.Insert(merchantTerminal);
                                var merchantNew = _merchantTerminalService.GetMerchantTerminalById(merchantTerminal.Id, true);
                                ParseMerchant(MoveToFirstChild(currentNode), merchantNew, writer,
                                    _processorTerminalService.GetProcessorTerminals(x => x.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD && x.ClientId != 0)
                                    , terminalUpdated.Id, Constants.ActionAdd);
                                CreateStatusResponse(writer, terminalUpdated.Id, clientId,
                                    ApiLabelXmlConstants.Merchant, 1);
                            }
                            else
                            {
                                CreateStatusResponse(writer, terminalUpdated.Id, clientId,
                                    ApiLabelXmlConstants.Merchant, 0);
                            }
                            #endregion
                            break;
                        case ApiLabelXmlConstants.Support:
                            #region Parse Support
                            try
                            {
                                var sp = ParseSupport(MoveToFirstChild(currentNode), true);
                                //go outside Update
                                //Update Support
                                var support = sp.GetEntityMaster(terminalUpdated.Support);
                                support.IsStatus = MMS.Core.CoreUTI.Constants.NOTCHANGE_RECORD;
                                _supportTerminalService.Update(support);
                                CreateStatusResponse(writer, support.Id, sp.Id, ApiLabelXmlConstants.Support, 1);
                            }
                            catch
                            {
                                //Handle later
                            }
                            #endregion
                            break;

                        case ApiLabelXmlConstants.Terminal:
                            #region Parse Terminal
                            try
                            {
                                var clientTerminal = ParseTerminalModel(MoveToFirstChild(currentNode), true);
                                terminalUpdated = clientTerminal.GetEntityMaster(terminalUpdated, Constants.FORMAT_DATE_MM_DD_YYYY,
                                    Constants.FORMAT_TIME_HH_MM);
                                var setupTerminal =
                                    _terminalMasterService.GetTerminalSetupByTerminalId(terminalUpdated.Id);
                                _terminalMasterService.Update(terminalUpdated);
                                if (setupTerminal == null)
                                {
                                    setupTerminal = new TerminalSetup()
                                    {
                                        TerminalName = terminalUpdated.lszTerminalName
                                    };
                                    _terminalSetupService.Insert(setupTerminal);
                                }
                                else
                                {
                                    setupTerminal.TerminalName = terminalUpdated.lszTerminalName;
                                    _terminalSetupService.Update(setupTerminal);
                                }
                                CreateStatusResponse(writer, terminalUpdated.Id, clientTerminal.Id, ApiLabelXmlConstants.Terminal, 1);
                            }
                            catch
                            {

                            }
                            #endregion
                            break;

                        case ApiLabelXmlConstants.System:
                            #region Parse System
                            try
                            {
                                ParseSystem(MoveToFirstChild(currentNode), terminalUpdated, true);
                                CreateStatusResponse(writer, terminalUpdated.Id, 0, ApiLabelXmlConstants.System, 1);
                            }
                            catch
                            {

                            }
                            #endregion
                            break;
                        case ApiLabelXmlConstants.User:
                            #region Parse User
                            var clientUserIdNode = MoveToFirstChild(currentNode);
                            var clientUserId = Convert.ToInt32(clientUserIdNode.Value);
                            if (clientUserId != 0)
                            {
                                if (terminalUpdated.UserTerminals != null)
                                {
                                    if (terminalUpdated.UserTerminals
                                        .Where(x => x.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD)
                                        .Select(x => x.ClientId).Contains(clientUserId))
                                    {
                                        CreateStatusResponse(writer, 0, clientUserId,
                                            ApiLabelXmlConstants.User, 0);
                                        //currentNode = Parent(currentNode);
                                        break;
                                    }
                                }

                                var user = (User)new User().ParseToObject(MoveToFirstChild(currentNode));
                                var userMaster = user.GetUserMaster(new UserMaster());
                                _userMasterService.Insert(userMaster);


                                var userTerminal = user.GetUserTerminal(new UserTerminal()
                                {
                                    UserMasterId = userMaster.Id,
                                    TerminalId = terminalUpdated.Id,
                                    ClientId = clientUserId
                                });
                                _userTerminalService.Insert(userTerminal);
                                CreateStatusResponse(writer, terminalUpdated.Id, clientUserId,
                                    ApiLabelXmlConstants.User, 1);
                            }
                            else
                            {
                                CreateStatusResponse(writer, terminalUpdated.Id, clientUserId,
                                    ApiLabelXmlConstants.User, 0);
                            }
                            #endregion
                            break;
                        case ApiLabelXmlConstants.MerchantProcessor:
                            #region Parse Merchant Processor
                            try
                            {
                                var result = ParseMerchantProcessor(MoveToFirstChild(currentNode), ApiLabelXmlConstants.Update, terminalUpdated.Id);
                                if (result.Id != 0)
                                {
                                    writer.WriteStartElement(ApiLabelXmlConstants.MerchantProcessor);
                                    writer.WriteStartElement("CloudId");
                                    writer.WriteString(result.Id.ToString());
                                    writer.WriteEndElement();
                                    //Client Id
                                    writer.WriteStartElement("ClientId");
                                    writer.WriteString(result.IdMerchant.ToString());
                                    writer.WriteEndElement();

                                    writer.WriteStartElement("ClientId2");
                                    writer.WriteString(result.IdProcessor.ToString());
                                    writer.WriteEndElement();
                                    //Date
                                    writer.WriteStartElement("Date");
                                    writer.WriteString(DateTime.Now.ToString(Constants.FORMAT_MM_DD_YYYY));
                                    writer.WriteEndElement();
                                    //Time
                                    writer.WriteStartElement("Time");
                                    writer.WriteString(DateTime.Now.ToString(Constants.FORMAT_TIME_HH_MM));
                                    writer.WriteEndElement();
                                    //IsSuccess
                                    writer.WriteStartElement("IsSuccess");
                                    writer.WriteString("1");
                                    writer.WriteEndElement();
                                    writer.WriteEndElement();
                                }
                                else
                                {
                                    writer.WriteStartElement(ApiLabelXmlConstants.MerchantProcessor);
                                    writer.WriteStartElement("CloudId");
                                    writer.WriteString(result.Id.ToString());
                                    writer.WriteEndElement();
                                    //Client Id
                                    writer.WriteStartElement("ClientId");
                                    writer.WriteString(result.IdMerchant.ToString());
                                    writer.WriteEndElement();

                                    writer.WriteStartElement("ClientId2");
                                    writer.WriteString(result.IdProcessor.ToString());
                                    writer.WriteEndElement();
                                    //Date
                                    writer.WriteStartElement("Date");
                                    writer.WriteString(DateTime.Now.ToString(Constants.FORMAT_MM_DD_YYYY));
                                    writer.WriteEndElement();
                                    //Time
                                    writer.WriteStartElement("Time");
                                    writer.WriteString(DateTime.Now.ToString(Constants.FORMAT_TIME_HH_MM));
                                    writer.WriteEndElement();
                                    //IsSuccess
                                    writer.WriteStartElement("IsSuccess");
                                    writer.WriteString("0");
                                    writer.WriteEndElement();
                                    writer.WriteEndElement();
                                }
                            }
                            catch
                            {
                                CreateStatusResponse(writer, terminalUpdated.Id, 0,
                                   ApiLabelXmlConstants.MerchantProcessor, 0);
                            }
                            #endregion
                            break;
                    }

                    if (ApiLabelXmlConstants.Batch.Equals(currentNode.Name.LocalName))
                    {
                        #region Parse Batch
                        var batchStr = currentNode.ToString();
                        var serializer = new XmlSerializer(typeof(BatchApiModel));
                        BatchApiModel result;

                        using (TextReader reader = new StringReader(batchStr))
                        {
                            result = (BatchApiModel)serializer.Deserialize(reader);
                        }
                        if (result != null)
                        {
                            // Insert Batch
                            TerminalReportBatch batch;
                            if (
                                _reportService.GetBatches()
                                    .Where(x => x.Status != MMS.Core.CoreUTI.Constants.DELETE_RECORD)
                                    .SingleOrDefault(
                                        x => x.ClientId == result.IdBatch && x.TerminalId == terminalUpdated.Id) !=
                                null)
                            {
                                batch = _reportService.GetBatches()
                                    .SingleOrDefault(
                                        x => x.ClientId == result.IdBatch && x.TerminalId == terminalUpdated.Id);
                                batch = result.GetMaster(batch);
                                _reportService.Update(batch);
                            }
                            else
                            {
                                batch = new TerminalReportBatch();
                                batch = result.GetMaster(batch);
                                batch.TerminalId = terminalUpdated.Id;
                                _reportService.Insert(batch);
                            }
                            if (result.CT != null)
                            {
                                var ctError = result.CT.GetMaster(new TerminalReportCtError());
                                ctError.BatchId = batch.Id;
                                _reportService.Insert(ctError);
                            }
                            if (result.TSYS != null)
                            {
                                var tsysError = result.TSYS.GetMaster(new TerminalReportTsysError());
                                tsysError.BatchId = batch.Id;
                                _reportService.Insert(tsysError);
                            }
                            CreateStatusResponse(writer, batch.Id, batch.ClientId, "Batch", 1);
                        }
                        #endregion
                    } //end if name == Batches 
                    else if (ApiLabelXmlConstants.Order.Equals(currentNode.Name.LocalName))
                    {
                        #region Parse Order
                        string orderXml = currentNode.ToString();
                        var serializer = new XmlSerializer(typeof(OrderApiModel));
                        OrderApiModel order = null;

                        using (TextReader reader = new StringReader(orderXml))
                        {
                            order = (OrderApiModel)serializer.Deserialize(reader);
                        }
                        if (order != null)
                        {
                            var orderServer = order.GetMaster(new TerminalReportOrder());
                            if (
                                _reportService.GetOrders()
                                    .Where(x => x.Batch.Status != MMS.Core.CoreUTI.Constants.DELETE_RECORD)
                                    .SingleOrDefault(
                                        x => x.Batch.TerminalId == terminalUpdated.Id && x.ClientId == order.IdOrder) ==
                                null)
                            {
                                var batch =
                                    _reportService.GetBatches()
                                        .Where(x => x.Status != MMS.Core.CoreUTI.Constants.DELETE_RECORD)
                                        .SingleOrDefault(
                                            x => x.ClientId == order.IdBatch && x.TerminalId == terminalUpdated.Id);
                                if (batch != null)
                                {
                                    orderServer.BatchId = batch.Id;
                                    _reportService.Insert(orderServer);
                                    CreateStatusResponse(writer, orderServer.Id, orderServer.ClientId, "Order", 1);
                                }
                                else
                                {
                                    CreateStatusResponse(writer, orderServer.Id, orderServer.ClientId, "Order", 0);
                                }
                            }
                            else
                            {
                                CreateStatusResponse(writer, orderServer.Id, orderServer.ClientId, "Order", 0);
                            }
                        }
                        #endregion
                    }
                    else if (ApiLabelXmlConstants.Payment.Equals(currentNode.Name.LocalName))
                    {
                        #region Parse Payment
                        string paymentXml = currentNode.ToString();
                        var serializer = new XmlSerializer(typeof(PaymentsApiModel));
                        PaymentsApiModel payment = null;

                        using (TextReader reader = new StringReader(paymentXml))
                        {
                            payment = (PaymentsApiModel)serializer.Deserialize(reader);
                        }
                        if (payment != null)
                        {
                            var paymentServer = payment.GetMaster(new TerminalReportPayment());
                            var batch =
                                _reportService.GetBatches()
                                    .Where(x => x.Status != MMS.Core.CoreUTI.Constants.DELETE_RECORD)
                                    .SingleOrDefault(
                                        x => x.ClientId == payment.IdBatch && x.TerminalId == terminalUpdated.Id);
                            var order =
                                _reportService.GetOrders()
                                    .SingleOrDefault(x => x.ClientId == payment.IdOrder && x.BatchId == batch.Id);
                            if (batch != null && order != null)
                            {
                                paymentServer.BatchId = batch.Id;
                                paymentServer.TerminalId = terminalUpdated.Id;
                                paymentServer.OrderId = order.Id;
                                if (payment.CT != null)
                                {
                                    paymentServer.Ct = payment.CT.GetMaster(new TerminalReportPaymentCt());
                                }
                                if (payment.GIVEX != null)
                                {
                                    paymentServer.Givex = payment.GIVEX.GetMaster(new TerminalReportPaymentGivex());
                                }
                                if (payment.GLOBALONE != null)
                                {
                                    paymentServer.GlobalOne =
                                        payment.GLOBALONE.GetMaster(new TerminalReportPaymentGlobalOne());
                                }
                                if (payment.POS != null)
                                {
                                    paymentServer.Pos = payment.POS.GetMaster(new TerminalReportPaymentPos());
                                }
                                if (payment.TSYS != null)
                                {
                                    paymentServer.Tsys = payment.TSYS.GetMaster(new TerminalReportPaymentTsys());
                                }
                                _reportService.Insert(paymentServer);
                                CreateStatusResponse(writer, paymentServer.Id, paymentServer.ClientId, "Payment", 1);
                            }
                            else
                            {
                                CreateStatusResponse(writer, payment.IdPayment, payment.IdPayment, "Payment", 0);
                            }
                        } // end if payment!=null
                        else
                        {
                            CreateStatusResponse(writer, 0, 0, "Payment", 0);
                        }
                        #endregion
                    }

                    currentNode = Parent(currentNode);

                    writer.WriteEndElement();
                }
                if (currentNode.NextNode == null)
                {
                    lastNode = true;
                }
                if (!lastNode)
                {
                    currentNode = Next(currentNode);
                }
            }
            writer.WriteEndElement();
            writer.WriteEndDocument();
            writer.Close();

            return GetResponseMessageSuccess(builder.ToString());
        }


        #endregion

        #region Application Upgrade Version

        //application upgrade
        [Route("api/synchronize/upgrade/application")]
        [HttpPost]
        public ActionResult<ResponseMessage> ApplicationUpgradeVersion([FromBody] RequestTerminalModel model, string type = "json")
        {
            // param invalid
            if (model == null || !ModelState.IsValid)
                return GetResponseMessageError(HttpStatusCode.BadRequest);

            var terminal = _terminalMasterService.GetTerminalBySerialNumber(model.TerminalSerialNumber,
                model.TerminalMacAddress, model.TerminalModelId);

            //terminal object not found
            if (terminal == null || terminal.ApplicationUpgradeTerminal == null ||
                terminal.ApplicationUpgradeTerminal.ApplicationVersion == null)
                return GetResponseMessageError(HttpStatusCode.NoContent);
            var versionUpdateName = terminal.ApplicationUpgradeTerminal.ApplicationVersion.VersionNumber;
            var currentVersion = terminal.ApplicationVersion.VersionNumber;
            if (currentVersion != null && versionUpdateName != null && currentVersion.Equals(versionUpdateName))
            {
                return GetResponseMessageError(HttpStatusCode.NoContent);
            }
            //not upgrade because not change version application
            //var terminalApplicationVersion = new Version(model.TerminalApplicationVersion);
            //var cloudApplicationVersion = new Version(terminal.ApplicationUpgradeTerminal.ApplicationVersion.VersionNumber);
            //var compareVersion = terminalApplicationVersion.CompareTo(cloudApplicationVersion);
            //if (compareVersion != 0)
            //{
            //upgrade version on cloud or on show information for terminal
            //var applicationVersion =
            //    _terminalMasterService.GetApplicationVersionByModelAndVersionNumber(model.TerminalModelId,
            //        model.TerminalApplicationVersion);
            //var terminalUpdateApplication = terminal.ApplicationUpgradeTerminal;
            if (terminal.ApplicationUpgradeTerminal == null)
            {
                return GetResponseMessageError(HttpStatusCode.NoContent);
            }
            //if (applicationVersion != null)
            //{
            //    //check date upgrade
            //if date-time upgrade more than current datetime - it will be not upgrade
            //if date-time upgrade less than current datetime - it will upgrade version for server
            var upgradeDate =
                terminal.ApplicationUpgradeTerminal.NextUpgradeDate.GetValueOrDefault()
                    .ToString(Constants.FORMAT_MM_DD_YYYY);
            var upgradeTime =
                terminal.ApplicationUpgradeTerminal.NextUpgradeTime.GetValueOrDefault()
                    .ToString(Constants.FORMAT_TIME_HH_MM_SS);
            DateTime upgradeDateTime;
            //if (DateTime.TryParse(upgradeDate + " " + upgradeTime, out upgradeDateTime) &&
            //    upgradeDateTime <= DateTime.Now)
            //{
            //check is outsite or during business time
            var businessStartTime =
                terminal.BusinessStartTime.GetValueOrDefault().ToString(Constants.FORMAT_MM_DD_YYYY);
            var businessFinishTime =
                terminal.BusinessFinishTime.GetValueOrDefault().ToString(Constants.FORMAT_TIME_HH_MM_SS);
            DateTime startUpgradeDateTime;
            var tryParseStartUpgrade = DateTime.TryParseExact(upgradeDate + " " + upgradeTime,
                Constants.FORMAT_MM_DD_YYYY + " " + Constants.FORMAT_TIME_HH_MM_SS, CultureInfo.InvariantCulture,
                DateTimeStyles.None, out startUpgradeDateTime);

            DateTime finishUpgradeDateTime;
            var tryParseFinishUpgrade = DateTime.TryParseExact(upgradeDate + " " + businessFinishTime,
                Constants.FORMAT_MM_DD_YYYY + " " + Constants.FORMAT_TIME_HH_MM_SS, CultureInfo.InvariantCulture,
                DateTimeStyles.None, out finishUpgradeDateTime);

            //terminal.ApplicationUpgradeTerminal.ApplicationVersion = applicationVersion;
            terminal.ApplicationUpgradeTerminal.LastUpgradeDate = DateTime.Now;
            terminal.ApplicationUpgradeTerminal.LastUpgradeTime = DateTime.Now;
            //terminal.ApplicationVersion = applicationVersion;
            var filePath = Utils.WebUtils.PathApplicationFolder(terminal.ApplicationUpgradeTerminal.ApplicationVersion.FileSelected);

            //file application not found
            if (!System.IO.File.Exists(filePath))
                return GetResponseMessageError(HttpStatusCode.NoContent);

            var oFileInfo = new FileInfo(filePath);
            var fileName = terminal.ApplicationUpgradeTerminal.ApplicationVersion.RealFileNameSelected;
            var extension = "";
            var splitFileName = fileName.Split('.');
            if (splitFileName.Count() > 0)
            {
                extension = splitFileName.Last();
            }
            var fileType = extension;
            var fileLength = oFileInfo.Length.ToString();
            _terminalMasterService.Update(terminal);
            //}
            var response = new VersionNumberUpgradeInfo(terminal.ApplicationUpgradeTerminal)
            {
                VersionNumber = terminal.ApplicationUpgradeTerminal.ApplicationVersion.VersionNumber,
                Name = fileName,
                Length = fileLength,
                Type = fileType
            };
            return GetResponseMessageSuccess(response);
        }

        #endregion

        #region Application Upgrade File Version

        [Route("api/synchronize/upgrade/application/file")]
        [HttpPost]
        public ActionResult<ResponseMessage> ApplicationUpgradeFileVersion([FromBody] RequestTerminalModel model, string type = "json")
        {
            // param invalid
            if (model == null || !ModelState.IsValid) return GetResponseMessageError(HttpStatusCode.BadRequest);

            var terminal = _terminalMasterService.GetTerminalBySerialNumber(model.TerminalSerialNumber,
                model.TerminalMacAddress, model.TerminalModelId);

            //terminal object not found
            if (terminal == null || terminal.ApplicationUpgradeTerminal == null || terminal.ApplicationUpgradeTerminal.ApplicationVersion == null)
                return GetResponseMessageError(HttpStatusCode.NotFound);

            //not donwload because not change version application or version more than with cloud
            //var terminalApplicationVersion = new Version(model.TerminalApplicationVersion);
            //var cloudApplicationVersion = new Version(terminal.ApplicationUpgradeTerminal.ApplicationVersion.VersionNumber);
            var compareVersion = -1;
            if (compareVersion < 0)
            {
                //var donwloadDate = terminal.ApplicationUpgradeTerminal.NextDownloadDate.GetValueOrDefault().ToString("yyyy-m-d dddd");
                //var donwloadTime = terminal.ApplicationUpgradeTerminal.NextDownloadTime.GetValueOrDefault().ToString("HH:m:s tt zzz");
                //DateTime downloadDateTime;
                //if (DateTime.TryParse(donwloadDate + " " + donwloadTime, out downloadDateTime) && downloadDateTime <= DateTime.Now)
                //{
                var fileName = terminal.ApplicationUpgradeTerminal.ApplicationVersion.FileSelected;
                var filePath = Utils.WebUtils.PathApplicationFolder(terminal.ApplicationUpgradeTerminal.ApplicationVersion.FileSelected);
                terminal.ApplicationVersionId = terminal.ApplicationUpgradeTerminal.ApplicationVersionId;
                _terminalMasterService.Update(terminal);
                //file application not found
                if (!System.IO.File.Exists(filePath))
                    return GetResponseMessageError(HttpStatusCode.NoContent);

                var fileStream = System.IO.File.Open(filePath, FileMode.Open, FileAccess.Read);
                return File(fileStream, "application/octet-stream", fileName);
                //}
            }
            return GetResponseMessageError(HttpStatusCode.NotFound);
        }

        #endregion

        #region Application Upgrade Version Response

        [Route("api/synchronize/upgrade/application/response")]
        [HttpPost]
        public ActionResult<ResponseMessage> ApplicationUpgradeVersionResponse([FromBody] RequestTerminalModel model, string type = "json")
        {
            //param  invaild
            if (model == null || !ModelState.IsValid) return GetResponseMessageError(HttpStatusCode.BadRequest);

            var terminal = _terminalMasterService.GetTerminalBySerialNumber(model.TerminalSerialNumber,
                model.TerminalMacAddress, model.TerminalModelId);

            //not found terminal object
            if (terminal == null || terminal.ApplicationUpgradeTerminal == null)
                return GetResponseMessageError(HttpStatusCode.NotFound);

            //not upgrade because not change version firmware
            var terminalApplicationVersion = new Version(model.TerminalApplicationVersion);
            var cloudApplicationVersion = new Version(terminal.ApplicationUpgradeTerminal.ApplicationVersion.VersionNumber);
            var compareVersion = terminalApplicationVersion.CompareTo(cloudApplicationVersion);

            if (compareVersion != 0)
            {
                //upgrade version on cloud or on show information for terminal
                var applicationVersion =
                    _terminalMasterService.GetApplicationVersionByModelAndVersionNumber(model.TerminalModelId,
                        model.TerminalApplicationVersion);
                if (applicationVersion != null)
                {
                    //check date upgrade
                    //if date-time upgrade more than current datetime - it will be not upgrade
                    //if date-time upgrade less than current datetime - it will upgrade version for server
                    var upgradeDate = terminal.ApplicationUpgradeTerminal.NextUpgradeDate.GetValueOrDefault().ToString("yyyy-m-d dddd");
                    var upgradeTime = terminal.ApplicationUpgradeTerminal.NextUpgradeTime.GetValueOrDefault().ToString("HH:m:s tt zzz");
                    DateTime upgradeDateTime;
                    if (DateTime.TryParse(upgradeDate + " " + upgradeTime, out upgradeDateTime) &&
                        upgradeDateTime <= DateTime.Now)
                    {
                        //check is outsite or during business time
                        var businessStartTime = terminal.BusinessStartTime.GetValueOrDefault().ToString("HH:m:s tt zzz");
                        var businessFinishTime = terminal.BusinessFinishTime.GetValueOrDefault().ToString("HH:m:s tt zzz");
                        DateTime startUpgradeDateTime;
                        var tryParseStartUpgrade = DateTime.TryParse(upgradeDate + " " + businessStartTime,
                            out startUpgradeDateTime);
                        DateTime finishUpgradeDateTime;
                        var tryParseFinishUpgrade = DateTime.TryParse(upgradeDate + " " + businessFinishTime,
                            out finishUpgradeDateTime);

                        if (terminal.ApplicationUpgradeTerminal.IsOutsideBusinessHours)
                        {
                            //outside 8.30am - 8.30pm
                            if (tryParseStartUpgrade && tryParseFinishUpgrade &&
                                (startUpgradeDateTime >= DateTime.Now ||
                                 finishUpgradeDateTime <= DateTime.Now))
                            {
                                if (compareVersion > 0)
                                {
                                    terminal.ApplicationUpgradeTerminal.ApplicationVersion = applicationVersion;
                                }
                                terminal.ApplicationUpgradeTerminal.LastUpgradeDate = DateTime.Now;
                                terminal.ApplicationUpgradeTerminal.LastUpgradeTime = DateTime.Now;
                                terminal.ApplicationVersion = applicationVersion;
                                _terminalMasterService.Update(terminal);
                                return GetResponseMessageSuccess(
                                    new VersionNumberUpgradeInfo(terminal.ApplicationUpgradeTerminal)
                                    {
                                        VersionNumber = terminal.ApplicationUpgradeTerminal.ApplicationVersion.VersionNumber
                                    });
                            }
                        }
                        if (terminal.ApplicationUpgradeTerminal.IsDuringBusinessHours)
                        {
                            //during 8.30am - 8.30pm
                            if (tryParseStartUpgrade && tryParseFinishUpgrade && startUpgradeDateTime <= DateTime.Now &&
                                finishUpgradeDateTime >= DateTime.Now)
                            {
                                if (compareVersion > 0)
                                {
                                    terminal.ApplicationUpgradeTerminal.ApplicationVersion = applicationVersion;
                                }
                                terminal.ApplicationVersion = applicationVersion;
                                terminal.ApplicationUpgradeTerminal.LastUpgradeDate = DateTime.Now;
                                terminal.ApplicationUpgradeTerminal.LastUpgradeTime = DateTime.Now;
                                _terminalMasterService.Update(terminal);

                                return GetResponseMessageSuccess(
                                     new VersionNumberUpgradeInfo(terminal.ApplicationUpgradeTerminal)
                                    {
                                        VersionNumber = terminal.ApplicationUpgradeTerminal.ApplicationVersion.VersionNumber
                                    });
                            }
                        }
                    }
                }
                return GetResponseMessageError(HttpStatusCode.NotFound);
            }
            else
            {
                //nothing to upgrade 
                return GetResponseMessageError(HttpStatusCode.NotFound);
            }
        }

        #endregion

        #region Firmware Update Version

        //firmware upgrade
        [Route("api/synchronize/upgrade/firmware")]
        [HttpPost]
        public ActionResult<ResponseMessage> FirmwareUpgradeVersion([FromBody] RequestTerminalModel model, string type = "json")
        {
            // param invalid
            if (model == null || !ModelState.IsValid)
                return GetResponseMessageError(HttpStatusCode.BadRequest);

            var terminal = _terminalMasterService.GetTerminalBySerialNumber(model.TerminalSerialNumber,
                model.TerminalMacAddress, model.TerminalModelId);

            // terminal object not found
            if (terminal == null || terminal.FirmwareUpgradeTerminal == null || terminal.FirmwareUpgradeTerminal.FirmwareVersion == null)
                return GetResponseMessageError(HttpStatusCode.NotFound);

            //not upgrade because not change version firmware
            var terminalFirmwareVersion = new Version(model.TerminalFirmwareVersion);
            var cloudFirmwareVersion = new Version(terminal.FirmwareUpgradeTerminal.FirmwareVersion.VersionNumber);
            var compareVersion = terminalFirmwareVersion.CompareTo(cloudFirmwareVersion);

            if (compareVersion != 0)
            {
                //upgrade version on cloud or on show information for terminal
                var firmwareVersion =
                    _terminalMasterService.GetFirmwareVersionByModelAndVersionNumber(model.TerminalModelId,
                        model.TerminalFirmwareVersion);
                if (firmwareVersion != null)
                {
                    //check date upgrade
                    //if date-time upgrade more than current datetime - it will be not upgrade
                    //if date-time upgrade less than current datetime - it will upgrade version for server
                    var upgradeDate = terminal.FirmwareUpgradeTerminal.NextUpgradeDate.GetValueOrDefault().ToString("yyyy-m-d dddd");
                    var upgradeTime = terminal.FirmwareUpgradeTerminal.NextUpgradeTime.GetValueOrDefault().ToString("HH:m:s tt zzz");
                    DateTime upgradeDateTime;
                    if (DateTime.TryParse(upgradeDate + " " + upgradeTime, out upgradeDateTime) &&
                        upgradeDateTime <= DateTime.Now)
                    {
                        //check is outsite or during business time
                        var businessStartTime = terminal.BusinessStartTime.GetValueOrDefault().ToString("HH:m:s tt zzz");
                        var businessFinishTime = terminal.BusinessFinishTime.GetValueOrDefault().ToString("HH:m:s tt zzz");
                        DateTime startUpgradeDateTime;
                        var tryParseStartUpgrade = DateTime.TryParse(upgradeDate + " " + businessStartTime,
                            out startUpgradeDateTime);
                        DateTime finishUpgradeDateTime;
                        var tryParseFinishUpgrade = DateTime.TryParse(upgradeDate + " " + businessFinishTime,
                            out finishUpgradeDateTime);

                        if (terminal.FirmwareUpgradeTerminal.IsOutsideBusinessHours)
                        {
                            //outside 8.30am - 8.30pm
                            if (tryParseStartUpgrade && tryParseFinishUpgrade &&
                                (startUpgradeDateTime >= DateTime.Now ||
                                 finishUpgradeDateTime <= DateTime.Now))
                            {
                                if (compareVersion > 0)
                                {
                                    terminal.FirmwareUpgradeTerminal.FirmwareVersion = firmwareVersion;
                                    terminal.FirmwareUpgradeTerminal.LastUpgradeDate = DateTime.Now;
                                    terminal.FirmwareUpgradeTerminal.LastUpgradeTime = DateTime.Now;
                                    terminal.FirmwareVersion = firmwareVersion;
                                    _terminalMasterService.Update(terminal);
                                }

                                return GetResponseMessageSuccess(
                                    new VersionNumberUpgradeInfo(terminal.FirmwareUpgradeTerminal)
                                    {
                                        VersionNumber = terminal.FirmwareUpgradeTerminal.FirmwareVersion.VersionNumber
                                    });
                            }
                        }
                        if (terminal.FirmwareUpgradeTerminal.IsDuringBusinessHours)
                        {
                            //during 8.30am - 8.30pm
                            if (tryParseStartUpgrade && tryParseFinishUpgrade && startUpgradeDateTime <= DateTime.Now &&
                                finishUpgradeDateTime >= DateTime.Now)
                            {
                                if (compareVersion > 0)
                                {
                                    terminal.FirmwareUpgradeTerminal.FirmwareVersion = firmwareVersion;
                                    terminal.FirmwareUpgradeTerminal.LastUpgradeDate = DateTime.Now;
                                    terminal.FirmwareUpgradeTerminal.LastUpgradeTime = DateTime.Now;
                                    terminal.FirmwareVersion = firmwareVersion;
                                    _terminalMasterService.Update(terminal);
                                }
                                return GetResponseMessageSuccess(
                                    new VersionNumberUpgradeInfo(terminal.FirmwareUpgradeTerminal)
                                    {
                                        VersionNumber = terminal.FirmwareUpgradeTerminal.FirmwareVersion.VersionNumber
                                    });
                            }
                        }

                    }
                }
                return GetResponseMessageError(HttpStatusCode.NotFound);

            }
            else
            {
                //nothing to upgrade 
                return GetResponseMessageError(HttpStatusCode.NotFound);
            }
        }

        #endregion

        #region Firmware Update File Version

        [Route("api/synchronize/upgrade/firmware/file")]
        [HttpPost]
        public ActionResult<ResponseMessage> FirmwareUpgradeFileVersion([FromBody] RequestTerminalModel model, string type = "json")
        {
            // param invalid
            if (model == null || !ModelState.IsValid)
                return GetResponseMessageError(HttpStatusCode.BadRequest);

            var terminal = _terminalMasterService.GetTerminalBySerialNumber(model.TerminalSerialNumber,
                model.TerminalMacAddress, model.TerminalModelId);

            // terminal object not found
            if (terminal == null || terminal.FirmwareUpgradeTerminal == null || terminal.FirmwareUpgradeTerminal.FirmwareVersion == null)
                return GetResponseMessageError(HttpStatusCode.NotFound);

            //not donwload because not change version firmware or version more than with cloud
            var terminalFirmwareVersion = new Version(model.TerminalFirmwareVersion);
            var cloudFirmwareVersion = new Version(terminal.FirmwareUpgradeTerminal.FirmwareVersion.VersionNumber);
            var compareVersion = terminalFirmwareVersion.CompareTo(cloudFirmwareVersion);
            if (compareVersion < 0)
            {
                var donwloadDate = terminal.FirmwareUpgradeTerminal.NextDownloadDate.GetValueOrDefault().ToString("yyyy-m-d dddd");
                var donwloadTime = terminal.FirmwareUpgradeTerminal.NextDownloadTime.GetValueOrDefault().ToString("HH:m:s tt zzz");
                DateTime downloadDateTime;
                if (DateTime.TryParse(donwloadDate + " " + donwloadTime, out downloadDateTime) && downloadDateTime <= DateTime.Now)
                {
                    var fileName = terminal.FirmwareUpgradeTerminal.FirmwareVersion.FileSelected;
                    var filePath = Utils.WebUtils.PathFirmwareFolder(terminal.FirmwareUpgradeTerminal.FirmwareVersion.FileSelected);

                    //file application not found
                    if (!System.IO.File.Exists(filePath))
                        return GetResponseMessageError(HttpStatusCode.NotFound);

                    var fileStream = System.IO.File.Open(filePath, FileMode.Open, FileAccess.Read);
                    return File(fileStream, "application/octet-stream", fileName);
                }
            }
            return GetResponseMessageError(HttpStatusCode.NotFound);
        }

        #endregion

        #region Firmware Update Version Response API

        [Route("api/synchronize/upgrade/firmware/response")]
        [HttpPost]
        public ActionResult<ResponseMessage> FirmwareUpgradeVersionResponse([FromBody] RequestTerminalModel model, string type = "json")
        {
            // param invalid
            if (model == null || !ModelState.IsValid)
                return GetResponseMessageError(HttpStatusCode.BadRequest);

            var terminal = _terminalMasterService.GetTerminalBySerialNumber(model.TerminalSerialNumber,
                model.TerminalMacAddress, model.TerminalModelId);

            // terminal object not found
            if (terminal == null || terminal.FirmwareUpgradeTerminal == null || terminal.FirmwareUpgradeTerminal.FirmwareVersion == null)
                return GetResponseMessageError(HttpStatusCode.NotFound);

            //not upgrade because not change version firmware
            var terminalFirmwareVersion = new Version(model.TerminalFirmwareVersion);
            var cloudFirmwareVersion = new Version(terminal.FirmwareUpgradeTerminal.FirmwareVersion.VersionNumber);
            var compareVersion = terminalFirmwareVersion.CompareTo(cloudFirmwareVersion);

            if (compareVersion != 0)
            {
                //upgrade version on cloud or on show information for terminal
                var firmwareVersion =
                    _terminalMasterService.GetFirmwareVersionByModelAndVersionNumber(model.TerminalModelId,
                        model.TerminalFirmwareVersion);
                if (firmwareVersion != null)
                {
                    //check date upgrade
                    //if date-time upgrade more than current datetime - it will be not upgrade
                    //if date-time upgrade less than current datetime - it will upgrade version for server
                    var upgradeDate = terminal.FirmwareUpgradeTerminal.NextUpgradeDate.GetValueOrDefault().ToString("yyyy-m-d dddd");
                    var upgradeTime = terminal.FirmwareUpgradeTerminal.NextUpgradeTime.GetValueOrDefault().ToString("HH:m:s tt zzz");
                    DateTime upgradeDateTime;
                    if (DateTime.TryParse(upgradeDate + " " + upgradeTime, out upgradeDateTime) &&
                        upgradeDateTime <= DateTime.Now)
                    {
                        //check is outsite or during business time
                        var businessStartTime = terminal.BusinessStartTime.GetValueOrDefault().ToString("HH:m:s tt zzz");
                        var businessFinishTime = terminal.BusinessFinishTime.GetValueOrDefault().ToString("HH:m:s tt zzz");
                        DateTime startUpgradeDateTime;
                        var tryParseStartUpgrade = DateTime.TryParse(upgradeDate + " " + businessStartTime,
                            out startUpgradeDateTime);
                        DateTime finishUpgradeDateTime;
                        var tryParseFinishUpgrade = DateTime.TryParse(upgradeDate + " " + businessFinishTime,
                            out finishUpgradeDateTime);

                        if (terminal.FirmwareUpgradeTerminal.IsOutsideBusinessHours)
                        {
                            //outside 8.30am - 8.30pm
                            if (tryParseStartUpgrade && tryParseFinishUpgrade &&
                                (startUpgradeDateTime >= DateTime.Now ||
                                 finishUpgradeDateTime <= DateTime.Now))
                            {
                                if (compareVersion > 0)
                                {
                                    terminal.FirmwareUpgradeTerminal.FirmwareVersion = firmwareVersion;
                                }
                                terminal.FirmwareUpgradeTerminal.LastUpgradeDate = DateTime.Now;
                                terminal.FirmwareUpgradeTerminal.LastUpgradeTime = DateTime.Now;
                                terminal.FirmwareVersion = firmwareVersion;
                                _terminalMasterService.Update(terminal);

                                return GetResponseMessageSuccess(new VersionNumberUpgradeInfo(terminal.FirmwareUpgradeTerminal)
                                {
                                    VersionNumber = terminal.FirmwareUpgradeTerminal.FirmwareVersion.VersionNumber
                                }); 
                            }
                        }
                        if (terminal.FirmwareUpgradeTerminal.IsDuringBusinessHours)
                        {
                            //during 8.30am - 8.30pm
                            if (tryParseStartUpgrade && tryParseFinishUpgrade && startUpgradeDateTime <= DateTime.Now &&
                                finishUpgradeDateTime >= DateTime.Now)
                            {
                                if (compareVersion > 0)
                                {
                                    terminal.FirmwareUpgradeTerminal.FirmwareVersion = firmwareVersion;
                                }
                                terminal.FirmwareVersion = firmwareVersion;
                                terminal.FirmwareUpgradeTerminal.LastUpgradeDate = DateTime.Now;
                                terminal.FirmwareUpgradeTerminal.LastUpgradeTime = DateTime.Now;
                                _terminalMasterService.Update(terminal);

                                return GetResponseMessageSuccess(new VersionNumberUpgradeInfo(terminal.FirmwareUpgradeTerminal)
                                {
                                    VersionNumber = terminal.FirmwareUpgradeTerminal.FirmwareVersion.VersionNumber
                                });
                            }
                        }

                    }
                }
                return GetResponseMessageError(HttpStatusCode.NotFound);
            }
            else
            {
                //nothing to upgrade 
                return GetResponseMessageError(HttpStatusCode.NotFound);
            }
        }

        #endregion

        //TODO demo TMS server - by Dung Pham 18/02/2021
        public async Task<string> GetResponseURI(Uri u)
        {
            var response = string.Empty;
            HttpClientHandler clientHandler = new HttpClientHandler();
            clientHandler.ServerCertificateCustomValidationCallback = (sender, cert, chain, sslPolicyErrors) => { return true; };//NOSONAR

            using (var client = new HttpClient(clientHandler))
            {
                HttpResponseMessage result = await client.GetAsync(u);
                if (result.IsSuccessStatusCode)
                {
                    response = await result.Content.ReadAsStringAsync();
                }
            }

            return response;
        }


        #region Synchronize
        [Route("api/synchronize/resyncfile/prepare")]
        [HttpPost]
        public async Task<ActionResult<ResponseMessage>> PrepareFile([FromForm] RequestTerminalModel model, bool isSetupUpgrade = false, string type = "json", bool isRawData = false, bool isPaymentRequest = true, bool isValidate = true)
        {
            ComTerminal comip = null;
            RS232Comm rs232 = null;
            PosHttpServer httpServer = null;
            // Allow the type parameter to determine the response format (JSON or XML)
            //param invaild
            if (model == null || !ModelState.IsValid)
                return GetResponseMessageError(HttpStatusCode.BadRequest);

            var originalTerminalSerialNumber = model.TerminalSerialNumber;

            //get terminal template for download
            if (isPaymentRequest)
            {
                var terminalOrg = _terminalMasterService.GetTerminalBySerialNumber(model.TerminalSerialNumber);
                int appVersion = int.TryParse(model.TerminalApplicationVersion, out var version) ? version : 1; // cheat for mutiple app in terminal
                var downloadableTerminalTemplate = await _terminalMasterService.GetTerminalTemplateForApiDownload(model.TerminalSerialNumber, appVersion);
                model.TerminalSerialNumber = downloadableTerminalTemplate.TerminalSerialNumber;

                var setupUpgrade = await _deviceSetupUpgradeService.GetLastVersionByTerminalSerialNumber(originalTerminalSerialNumber, _registeredAppService.GetAppIDById(appVersion));
                var isSuccess = await _merchantTerminalService.MismatchMerchantValidationAsync(terminalOrg.Id, setupUpgrade.DeviceTemplateId);

                if (!isSuccess) return GetResponseMessageError(HttpStatusCode.BadRequest, "Merchant in template not match with Merchant Terminal.");
            }

            TerminalMaster terminal;

            if (model.Id != null)
            {
                terminal = _terminalMasterService.GetTerminalIncludedById(int.Parse(model.Id));
            }
            else
            {
                terminal = _terminalMasterService.GetTerminalBySerialNumber(model.TerminalSerialNumber);
            }

            if (terminal == null)
                return GetResponseMessageError(HttpStatusCode.BadRequest, "Terminal not found.");

            //TODO demo TMS server - by Dung Pham 18/02/2021
            // #region Download TEM 
            //
            // var terminalId = terminal.Id;      
            // var messageObj = new MessageObj();
            // var requestUrl = $"{Request.Scheme}://{Request.Host.Value}/";
            //
            // //Call get version template
            // string urlGetversion = requestUrl + "TheEstateManager/GetTemplateVersionLatest?terminalServiceNumber=" + terminal.TerminalServiceNumber + "&templateId=XCRTemplate&tmsSetupId=1&byPass=true";
            // var responseGetVersionTemplate = Task.Run(() => GetResponseURI(new Uri(urlGetversion)));
            // responseGetVersionTemplate.Wait();
            //
            // if (string.IsNullOrEmpty(responseGetVersionTemplate.Result))
            // {
            //     return GetResponseMessageError(HttpStatusCode.BadRequest, "Connection to get version in MMS server failed");
            // }
            // string version = responseGetVersionTemplate.Result;
            //
            //
            // //Call validate
            // string urlValidate = requestUrl + "TheEstateManager/ValidateTemplate?TMSId=1&terminalMasterId=" + terminal.Id + "&templateId=XCRTemplate&templatesVersion=" + version + "&byPass=true";
            // var responseValidate = Task.Run(() => GetResponseURI(new Uri(urlValidate)));
            // responseValidate.Wait();
            //
            // if (string.IsNullOrEmpty(responseValidate.Result))
            // {
            //     return GetResponseMessageError(HttpStatusCode.BadRequest, "Connection to validate in MMS server failed");
            // }
            //
            // messageObj = JsonConvert.DeserializeObject<MessageObj>(responseValidate.Result);
            // if (messageObj.MessageResult.Status != "200")
            // {
            //     return GetResponseMessageError(HttpStatusCode.BadRequest, "Validate template failed " + messageObj.MessageResult.Message);
            // }
            //
            // //call download
            // var urlDownload = requestUrl + "TheEstateManager/DownloadTemplate?TMSId=1&terminalMasterId=" + terminal.Id + "&templateId=XCRTemplate&templatesVersion=" + version + "&isAutoOverride=true&byPass=true";
            // var responseDownload = Task.Run(() => GetResponseURI(new Uri(urlDownload)));
            // responseDownload.Wait();
            //
            // if (string.IsNullOrEmpty(responseDownload.Result))
            // {
            //     return GetResponseMessageError(HttpStatusCode.BadRequest, "Connection to download template in MMS server failed");
            // }
            //
            // messageObj = JsonConvert.DeserializeObject<MessageObj>(responseDownload.Result);
            // if (messageObj.MessageResult.Status != "200")
            // {
            //     return GetResponseMessageError(HttpStatusCode.BadRequest, "Download template failed " + messageObj.MessageResult.Message);
            // }
            // #endregion download TEM 


            if (terminal.PosRequest != null)
            {
                comip = _comTerminalService.FirstOrDefault(x => x.PosRequestId == terminal.PosRequest.Id);
                rs232 = _rs232CommService.FirstOrDefault(x => x.PosRequestId == terminal.PosRequest.Id);
                httpServer = _posHttpServerService.FirstOrDefault(x => x.PosRequestId == terminal.PosRequest.Id);
            }
            var merchantsMasters = _merchantTerminalService.GetMerchantByTerminal(terminal.Id, false, p=>p.MerchantMaster);
            var merchantTerminalIds = merchantsMasters?.Select(x => x.Id).ToList();

            var responseModel = new Resync();

            responseModel.TerminalMacAddress = model.TerminalMacAddress;
            responseModel.TerminalSerialNumber = model.TerminalSerialNumber;
            if (terminal.DeviceSetupType == DeviceSetupType.VirtualTemplateDevice)
            {
                responseModel.TerminalSerialNumber = Utils.WebUtils.GetTerminalSerialNumber(model.TerminalSerialNumber);
            }
            
            #region Processor
            var processors = terminal.ProcessorTerminals.Where(x => x.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD)
                    .ToList();
            var payplusProcessor = terminal.PayplusProcessor;

            if (processors != null && processors.Any())
            {
                var processorList = new List<Processor>();

                foreach (var each in processors)
                {
                    processorList.Add(new Processor(each));
                }
                if (processorList.Any())
                {
                    processorList.FirstOrDefault().Id = 1;

                    if (payplusProcessor != null)
                        processorList.Add(new Processor(payplusProcessor));

                    responseModel.Processors = processorList;
                }
            }
            else
            {
                var processorList = new List<Processor>();

                processorList.Add(new Processor());

                responseModel.Processors = processorList;
            }
            #endregion

            #region Merchant Terminal
            var merchants = _merchantTerminalService.GetMerchantTerminalsByTerminalId(terminal.Id, includeDeactivated: false);

            IList<MerchantTerminal> originalTerminalMasterMerchants = [];
            var templateMerchantIds = merchants.Select(m => m.MerchantId).ToHashSet();
            var originalTerminalMasterMerchantIds = new HashSet<int>();
            var parentTemplateMerchantIds = new HashSet<int>();

            if (terminal.CopyDeviceTemplateId is not null && terminal.DeviceSetupType == DeviceSetupType.VirtualTemplateDevice) // terminal is a template device
            {
                int lastDashIndex = terminal.TerminalSerialNumber.LastIndexOf('-');
                var originalTerminalMasterSerialNumber = lastDashIndex >= 0 ? terminal.TerminalSerialNumber[..lastDashIndex] : terminal.TerminalSerialNumber;
                if (!string.IsNullOrEmpty(originalTerminalMasterSerialNumber))
                {
                    var originalTerminalMaster = _terminalMasterService.GetTerminalBySerialNumber(originalTerminalMasterSerialNumber);
                    if (originalTerminalMaster is not null)
                    {
                        originalTerminalMasterMerchants = _merchantTerminalService.GetOnlyMerchantTerminalsByTerminalId(originalTerminalMaster.Id, false);
                        originalTerminalMasterMerchantIds = originalTerminalMasterMerchants.Select(m => m.MerchantId).ToHashSet();
                    }
                }

                var deviceTemplate = _deviceTemplatesService.GetById(terminal.CopyDeviceTemplateId.Value); // find existing template
                if (deviceTemplate is not null)
                {
                    var parentTemplateTerminalMaster = await _terminalMasterService.GetTerminalByDeviceTemplateId(deviceTemplate.CopyTemplateId.Value, terminal.DeviceSetupType); // get terminal master of existing template
                    if (parentTemplateTerminalMaster is not null)
                    {
                        var parentTemplateMerchants = _merchantTerminalService.GetOnlyMerchantTerminalsByTerminalId(parentTemplateTerminalMaster.Id, false);
                        parentTemplateMerchantIds = parentTemplateMerchants.Select(m => m.MerchantId).ToHashSet();
                    }
                }
            }

            if (merchants != null && merchants.Any())
            {
                var merchantList = new List<Merchant>();

                //load notification hard code
                //****************************
                var dropdownText = _hardCodeService.GetListHardCodeByCode(MMS.Core.CoreUTI.Constants.Notifications)
                                  .Select(x => new HardCodeModel
                                  {
                                      Id = x.Id,
                                      Code = x.Code,
                                      Name = x.Name,
                                      Value = x.Value
                                  }).ToList();

                var paymentModeFirst = _terminalMasterService.PaymentModeOptionGet(terminal.Id, merchants);
                foreach (var each in merchants)
                {
                    //update xml missing
                    //-------------------------------------------------------------------------------------------------------------------
                    var alert = _merchantAlertNotificationService.GetAlertNotificationByMerchantId(each.Id);
                    var langby = _languageByTerminalService.FirstOrDefault(x => x.MerchantTerminalId == each.Id);
                    var langOption = _languageOptionTerminalService.GetAll(x=> x.MerchantTerminalId == each.Id);
                    var merchantCharity = _charityService.GetMerchantCharitiesByMerchantId(each.Id);
                    //-------------------------------------------------------------------------------------------------------------------

                    if (originalTerminalMasterMerchantIds is not null && originalTerminalMasterMerchantIds.Count > 0)
                    {
                        each.fEnabled = originalTerminalMasterMerchantIds.Contains(each.MerchantId); // check diff between template merchant list and original terminal master merchant list

                        if (parentTemplateMerchantIds is not null && parentTemplateMerchantIds.Count > 0)
                        {
                            each.fEnabled = parentTemplateMerchantIds.Contains(each.MerchantId); // check diff between template merchant list and parent template merchant list
                        }
                    }

                    var eachMerchantLoadPaymentModes = _merchantTerminalService.GetPaymentModes(each.Id);
                    each.PaymentModes = eachMerchantLoadPaymentModes.PaymentModes;//update navigation props

                    var eachMerchantLoadMerchantCashs = _merchantTerminalService.GetMerchantCash(each.Id);
                    each.MerchantCash = eachMerchantLoadMerchantCashs.MerchantCash;

                    var eachMerchantLoadMOTOCard= _merchantTerminalService.GetMOTOCard(each.Id);
                    each.MOTOCards = eachMerchantLoadMOTOCard.MOTOCards;

                    var eachEOVMerchantLimits = _merchantTerminalService.GetEOVMerchantLimit(each.Id);
                    each.EOVCardLimits = eachEOVMerchantLimits.EOVCardLimits;

                    var eachEOVMerchantCardCredit = _merchantTerminalService.GetEOVMerchantCreditCard(each.Id);
                    each.EOVMerchantCreditCard = eachEOVMerchantCardCredit.EOVMerchantCreditCard;

                    var eachEOVMerchantCardDebit = _merchantTerminalService.GetEOVMerchantDebitCard(each.Id);
                    each.EOVMerchantDebitCard = eachEOVMerchantCardDebit.EOVMerchantDebitCard;

                    var eachCardLimits = _merchantTerminalService.GetCardLimits(each.Id);
                    each.CardLimits = eachCardLimits.CardLimits;

                    var eachMerchantLoadCashOutFees = _merchantTerminalService.GetCashOutFees(each.Id);
                    each.CashOutFees = eachMerchantLoadCashOutFees.CashOutFees;

                    var eachMerchantMerchantReport = _merchantTerminalService.GetMerchantReport(each.Id);
                    each.MerchantReport = eachMerchantMerchantReport.MerchantReport;

                    var eachMerchantGetPaymentTenderType = _merchantTerminalService.GetPaymentTenderTypes(each.Id);
                    each.PaymentTenderTypes = eachMerchantGetPaymentTenderType.PaymentTenderTypes;
                    
                    var eachMerchantSalesTypes = _merchantTerminalService.GetSalesTypes(each.Id);
                    each.SalesTypes = eachMerchantSalesTypes.SalesTypes;

                    var eachMerchantUsers = _merchantTerminalService.GetMerchantUsers(each.Id);
                    each.MerchantUsers = eachMerchantUsers.MerchantUsers;
                    
                    var eachMerchantSetups = _merchantTerminalService.GetSetups(each.Id);
                    each.Setups = eachMerchantSetups.Setups;
                    each.EmailReceipt = eachMerchantSetups.EmailReceipt;
                    each.SmsReceipt = eachMerchantSetups.SmsReceipt;

                    var eachFunctionTypes = _merchantTerminalService.GetMerchantFunctionTypes(each.Id);
                    each.FunctionTypes = eachFunctionTypes.FunctionTypes;
                    
                    var eachEftTenderTypes = _merchantTerminalService.GetMerchantTenderTypes(each.Id);
                    each.TenderTypes = eachEftTenderTypes.TenderTypes;
                    
                    var eachMerchantTips = _merchantTerminalService.GetTips(each.Id);
                    each.Tips = eachMerchantTips.Tips;

                    var eachPreEmvSurcharge = _merchantTerminalService.GetPreEmvSurcharge(each.Id);
                    each.PreEmvSurcharge = eachPreEmvSurcharge.PreEmvSurcharge;

                    var eachMerchantSecurityLevels = _merchantTerminalService.GetSecurityLevels(each.Id);
                    each.SecurityLevels = eachMerchantSecurityLevels.SecurityLevels;

                    var merchantUserLogon = _merchantUserLogonService.GetByMerchanTerminalId(each.Id);

                    var messageMerchant = _merchantTerminalService.GetMessageMerchant(each.Id);
                    each.PrintMessage = messageMerchant;

                    var processorTerminals = _processorTerminalService.GetProcessorTerminals(x => x.TerminalId == terminal.Id);

                    var merchantCreditCards = _merchantCreditCardsAcceptedService.GetAll(p => p.MerchantTerminalId == each.Id).ToList();

                    var merchantDebitCards = _merchantDebitCardsAcceptedService.GetAll(p => p.MerchantTerminalId == each.Id).ToList();

                    var merchantCharityList = _charityService.GetCharitiesByMerchantTerminalIdApi(each.Id);

                    var surchargeRuleList = _surchargeRuleService.GetAll(p => p.MerchantTerminalId == each.Id, includes: p => p.AmountRanges).ToList();

                    var serviceItems = _serviceItemService.GetAll(p => p.MerchanTerminalId == each.Id).ToList();

                    merchantList.Add(new Merchant(each, langOption ?? null, langby ?? null, processorTerminals, alert, dropdownText, paymentModeFirst, merchantUserLogon, merchantCreditCards, merchantDebitCards, merchantCharityList, surchargeRuleList, serviceItems));
                }

                // create extra elements of setting up device and mark these "Merchant" nodes as fEnabled=false in template xml
                foreach (var masterMerchant in originalTerminalMasterMerchants.Where(m => !templateMerchantIds.Contains(m.MerchantId)))
                {
                    masterMerchant.fEnabled = false;
                    masterMerchant.InitializeMerchantTerminal();
                    merchantList.Add(new Merchant(masterMerchant, new List<LanguageOptionTerminal>()));
                }

                if (merchantList.Count != 0)
                {
                    responseModel.Merchants = merchantList;
                }
            }
            #endregion

            #region User Terminal
            var users = terminal.UserTerminals.Where(x => x.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD).ToList();
            if (users != null && users.Any())
            {
                var userList = new List<User>();
                foreach (var user in users)
                {
                    userList.Add(new User(user));
                }
                if (userList.Any())
                {
                    var terminalSetupByTerminal = _terminalMasterService.GetTerminalSetupByTerminalId(terminal.Id);

                    responseModel.Users = userList;
                }
            }
            #endregion

            #region User Merchant Terminal
            var userMerchants = _userMerchantService.GetUserMerchantByTerminalId(terminal.Id);
            if (userMerchants != null && userMerchants.Any())
            {
                var userMerchantList = new List<UserMerchant>();
                foreach (var userMerchant in userMerchants)
                {
                    userMerchantList.Add(new UserMerchant(userMerchant));
                }
                if (userMerchantList.Any())
                {
                    responseModel.UserMerchants = userMerchantList;
                }
            }
            #endregion

            #region System & Terminal Setup
            var terminalSetup = _terminalMasterService.GetTerminalSetupByTerminalId(terminal.Id);

            var terminalMasterFallback = _terminalMasterService.GetMerchantCardFallback(terminal.Id);
            terminal.MerchantCardFallbacks = terminalMasterFallback?.MerchantCardFallbacks;

            var languageBy = _terminalMasterService.GetLanguageByByTerminalId(terminal.Id);

            // for view incopmplete template
            if (terminalSetup == null && !isValidate)
            {
                terminalSetup = new TerminalSetup();
            }

            if (terminalSetup is null || terminalSetup?.Status == MMS.Core.CoreUTI.Constants.DELETE_RECORD)
            {
                throw new NullReferenceException("Terminal Setup is required");
            }
            else
            {
                var cloud = _terminalMasterService.GetTerminalCloudByTerminalId(terminal.Id);
                //if (terminal.PosRequest != null)
                //{
                //    terminal.PosRequest.Sale = _terminalMasterService.GetSaleTypeById(terminal.PosRequest.SaleId.Value);
                //    terminal.PosRequest.Bar = _terminalMasterService.GetSaleTypeById(terminal.PosRequest.BarId.Value);
                //    terminal.PosRequest.Customer = _terminalMasterService.GetSaleTypeById(terminal.PosRequest.CustomerId.Value);
                //    terminal.PosRequest.Room = _terminalMasterService.GetSaleTypeById(terminal.PosRequest.RoomId.Value);
                //    terminal.PosRequest.Table = _terminalMasterService.GetSaleTypeById(terminal.PosRequest.TableId.Value);
                //}
                
                var terminalConfiguration = new TerminalConfigParams()
                {
                    TerminalMaster = terminal,
                    TerminalSetup = terminalSetup,
                    TerminalCloud = cloud,
                    PosHttpServer = httpServer,
                    ComTerminal = comip,
                    RS232Comm = rs232,
                    LanguageBy = languageBy
                };
                
                var system = new SystemModel(terminalConfiguration, Constants.FORMAT_DATE_MM_DD_YYYY, Constants.FORMAT_TIME_HH__MM);
                responseModel.System = system;

                // Terminal Setup
                terminal.TerminalSetup = terminalSetup; // update Terminal Setup to Terminal Master

                if (terminal.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD)
                {
                    var terminalSetting = new TerminalModelApi(terminal, terminalSetup, Constants.FORMAT_DATE_MM_DD_YYYY, Constants.FORMAT_TIME_HH__MM);
                    responseModel.Terminal = terminalSetting;
                }
            }
            #endregion

            #region Support
            if (terminal.Support != null && terminal?.Support.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD)
            {
                var support = new SupportTerminalApi(terminal.Support);
                responseModel.Support = support;
            }
            #endregion

            #region Terminal Setup

            #region Reboot Schedule
            var entity = _terminalRebootScheduleService.FirstOrDefault(r => r.TerminalMasterId == terminal.Id);

            var rebootscheduleApiModel = new RebootScheduleApiModel();

            if (entity is not null)
                rebootscheduleApiModel = RebootScheduleApiModel.ToApiModel(entity);

            responseModel.RebootSchedule = rebootscheduleApiModel;
            #endregion Reboot Schedule

            #endregion Terminal Setup

            #region Merchant Processor
            var merchantProcessors = _processorMerchantService.GetMerchantProcessorsByTerminalId(terminal.Id);
            var merchantProcessorList = new List<MerchantProcessorApi>();
            if (merchantProcessors != null && merchantProcessors.Any())
            {
                foreach (var each in merchantProcessors.Where(each => each.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD))
                {
                    MerchantProcessorFunctionType function = null;
                    MerchantProcessorTypePaymentsSetup payments = null;
                    MerchantProcessorTypeGivexSetup givex = null;
                    MerchantProcessorTypeGlobalOneSetup global = null;
                    var lib = each.ProcessorTerminal.Proccessor.Library;
                    if (lib != null)
                    {
                        switch (lib.Value)
                        {
                            case Constants.TSYSValue:
                                function =
                                    _merchantProcessorFunctionTypeService.FirstOrDefault(x=> x.MerchantProcessorId == each.Id);
                                break;
                            case Constants.CTPaymentsValue:
                                payments = _merchantProcessorTypePaymentsSetupService.FirstOrDefault(x => x.MerchantProcessorId == each.Id);
                                break;
                            case Constants.GivexValue:
                                givex = _merchantProcessorTypeGivexSetupService.FirstOrDefault(x => x.MerchantProcessorId == each.Id);
                                break;
                            case Constants.GlobalOneValue:
                                global = _merchantProcessorTypeGlobalOneSetupService.FirstOrDefault(x => x.MerchantProcessorId == each.Id);
                                break;
                        }
                    }
                    merchantProcessorList.Add(new MerchantProcessorApi(each, function, payments, givex, global));
                }
                if (merchantProcessorList.Any())
                {
                    responseModel.MerchantProcessors = merchantProcessorList;
                }
            }
            #endregion

            #region Charity
            var charities = _charityService.GetGlobalCharitiesApi(terminal.Id);

            if (charities != null && charities.Count != 0)
            {
                var charityList = new List<CharityApiModel>();

                foreach (var each in charities)
                {
                    each.Amounts = each.Amounts
                        .Where(a => a.IsStatus != Core.CoreUTI.Constants.DELETE_RECORD && a.IsStatus != Core.CoreUTI.Constants.PRE_DELETE_RECORD)
                        .ToList();
                    charityList.Add(each.ToApiModel());
                }
                if (charityList.Count != 0)
                {
                    responseModel.Charities = charityList;
                }
            }
            #endregion

            #region Bin Table
            var binTable = _binTableService.GetAll(x=> x.TerminalId == terminal.Id).OrderByDescending(x=> x.Created).FirstOrDefault();
            if (binTable != null)
            {
                var binTableApi = new BinTableApi(binTable);
                if (binTableApi.Id != 0)
                {
                    binTableApi.lszFilePath = binTableApi.lszFileName;
                    //binTableApi.lszFilePath = new Uri(Request.GetDisplayUrl()).Authority + Constants.PATH_BINTABLE + binTableApi.lszFileName;
                    responseModel.BinTable = binTableApi;
                }
            }
            #endregion

            #region Currencies
            var currencies = _terminalCurrencyService.GetAll(x
                => x.TerminalId == terminal.Id, 
                false, true, null, false, 
                x=> x.Currency,
                x=> x.Currency.HardCode
                ).Where(x => x.Currency != null)
                .Select(x => x.Currency).ToList();
            if (currencies != null && currencies.Any())
            {
                var currenciesModel = new List<CurrencyApiModel>();

                foreach (var each in currencies)
                {
                    currenciesModel.Add(new CurrencyApiModel(each));
                }
                if (currenciesModel.Any())
                {
                    responseModel.Currencies = currenciesModel;
                }
            }
            #endregion

            #region PaymentMode
            var paymentModes = _terminalMasterService.GetPaymentModes(merchants);

            if (paymentModes != null && paymentModes.Any())
            {
                var paymentModeList = new List<PaymentModeApi>();
                paymentModeList.AddRange(paymentModes.Select(pm => new PaymentModeApi(pm, pm.PaymentModeOption)));
                responseModel.PaymentModes = paymentModeList;
            }

            #endregion PaymentMode

            if (isValidate)
            {
                var validatorResults = _resyncValidator.Validate(responseModel);
                if (!validatorResults.IsValid)
                {
                    var errors = string.Join(Environment.NewLine, validatorResults.Errors.Select(x => $"{x.ErrorMessage}"));

                    Log.Error(errors);
                    throw new FluentValidation.ValidationException(errors);
                }
            }

            if (isPaymentRequest)
            {
                var appId = _registeredAppService.GetAppIDById(int.TryParse(model.TerminalApplicationVersion, out var version) ? version : 1);
                var setupUpgrade = await _deviceSetupUpgradeService.GetLastVersionByTerminalSerialNumber(originalTerminalSerialNumber, appId)
                    ?? throw new KeyNotFoundException($"Setup upgrade not found for terminal serial number {originalTerminalSerialNumber}");

                await _deviceSetupUpgradeService.UnassignAppTemplateFromTerminalAsync(setupUpgrade.TerminalMasterId);
                setupUpgrade.IsAssignedAppTemplate = true;
                setupUpgrade.Status = DeviceSetupUpgradeStatuses.Upgraded;
                await _deviceSetupUpgradeService.UpdateWithRealTimeAsync(setupUpgrade);

                var applicationDeviceMapping = _applicationDeviceMappingService.GetById(setupUpgrade.ApplicationDeviceMappingId);
                ArgumentNullException.ThrowIfNull(applicationDeviceMapping);

                applicationDeviceMapping.IsInstalled = true;
                _applicationDeviceMappingService.Update(applicationDeviceMapping);
            }

            string stringContent;

            // Serialize the model based on the requested format
            if (type?.ToLower() == "json")
            {
                // Serialize to JSON using Newtonsoft.Json
                stringContent = JsonConvert.SerializeObject(responseModel, Newtonsoft.Json.Formatting.Indented,
                    new JsonSerializerSettings
                    {
                        NullValueHandling = NullValueHandling.Ignore
                    });
            }
            else
            {
                // Default to XML serialization
            var ser = new XmlSerializer(typeof(Resync));
            var sw = new StringWriter();
            ser.Serialize(sw, responseModel);
                stringContent = sw.ToString();
            }

            if (isRawData)
            {
                return GetResponseMessageSuccess(stringContent, model.Id);
            }

            // Process content based on the format
            if (type?.ToLower() == "json")
            {
                // For JSON format, we need to get the processor settings and add them to the model
                // before re-serializing to JSON

                // First, deserialize the JSON back to the model
                var jsonModel = JsonConvert.DeserializeObject<Resync>(stringContent);

                // Get processor settings
                var hostInterface = _hostInterfacesService.GetAll(p => p.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD).FirstOrDefault();

                if (hostInterface != null)
                {
                    IGatewaySetting gatewaySettingInstance = DllInstanceFactory.CreateInstance<IGatewaySetting>("Paymark.dll");

                    if (gatewaySettingInstance == null)
                    {
                        return GetResponseMessageError(HttpStatusCode.NoContent);
                    }

                    var gatewaySettings = gatewaySettingInstance.GatewaySettings();
                    var settingProcessors = GatewaySettingEmm.GetData(hostInterface?.SettingProcessor, gatewaySettings);

                    // Add processor settings to the model
                    if (settingProcessors != null && settingProcessors.Any() &&
                        jsonModel?.Processors != null && jsonModel.Processors.Any())
                    {
                        var processor = jsonModel.Processors.FirstOrDefault();
                        if (processor != null)
                        {
                            // Add the settings as dynamic properties to the processor
                            foreach (var item in settingProcessors)
                            {
                                // Add the property dynamically using reflection or another approach
                                // This is a simplified example - you may need to adjust based on your model structure
                                var propertyInfo = processor.GetType().GetProperty(item.FieldName);
                                if (propertyInfo != null && propertyInfo.CanWrite)
                                {
                                    propertyInfo.SetValue(processor, item.FieldVariable);
                                }
                            }
                        }
                    }

                    // Re-serialize the updated model to JSON
                    stringContent = JsonConvert.SerializeObject(jsonModel, Newtonsoft.Json.Formatting.Indented,
                        new JsonSerializerSettings
                        {
                            NullValueHandling = NullValueHandling.Ignore
                        });
                }
            }
            else
            {
                // For XML format, process as before
                XDocument xmlDoc = XDocument.Parse(stringContent);

                #region Processor using dynamic node

                XElement processorElement = xmlDoc.Root.Elements("Processors").Elements("Processor").FirstOrDefault();

                if (processorElement != null)
                {
                    var hostInterface = _hostInterfacesService.GetAll(p => p.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD).FirstOrDefault();

                    IGatewaySetting gatewaySettingInstance = DllInstanceFactory.CreateInstance<IGatewaySetting>("Paymark.dll");

                    if (gatewaySettingInstance == null)
                    {
                        return GetResponseMessageError(HttpStatusCode.NoContent);
                    }

                    var gatewaySettings = gatewaySettingInstance.GatewaySettings();

                    var settingProcessors = GatewaySettingEmm.GetData(hostInterface?.SettingProcessor, gatewaySettings);

                    if (settingProcessors != null && settingProcessors.Any())
                    {
                        foreach (var item in settingProcessors)
                        {
                            XElement addressElement = new XElement(item.FieldName, item.FieldVariable);

                            processorElement.Add(addressElement);
                        }
                    }
                }

                stringContent = "<?xml version=\"1.0\" encoding=\"utf-8\"?>" + xmlDoc.ToStr();
            }
            #endregion

            var reponseModel = new ResponseFile();

            if (string.IsNullOrWhiteSpace(stringContent))
            {
                return GetResponseMessageError(HttpStatusCode.NoContent);
            }

            var currentDirectory = Directory.GetCurrentDirectory();
            
            var fileName = DateTime.Now.Ticks;
            // Set the file extension based on the requested format
            string fileExtension = (type?.ToLower() == "json") ? ".json" : ".xml";
            var fileFullName = fileName + fileExtension;
            var pathParam = string.Empty;
            
            if (isSetupUpgrade)
            {
                pathParam = Path.Combine(_rootPath, Constants.PATH_TERMINAL_MASTER_SETUP_UPGRADE_RECORD);
            }
            else
            {
                pathParam = Path.Combine(_rootPath, Constants.PATH_PARAMETER);
            }
            
            var pathFile = Utils.WebUtils.CreateDirectory(Path.Combine(currentDirectory,pathParam, model.TerminalSerialNumber));
            var file = Path.Combine(pathFile, fileFullName);

            using (FileStream fs = System.IO.File.Create(file))
            {
                byte[] content = new UTF8Encoding(true).GetBytes(stringContent);
                fs.Write(content, 0, content.Length);
            }

            if(model.IsCompressed)
            {
                var terminalFileChangesUrl = _integrationPaxstoreAndMMSService.TerminalDownloadGetListFiles(terminal, file);
                var zipPath = await WebUtils.CompressZip(_webHostEnvironment.WebRootPath, terminalFileChangesUrl, Constants.PATH_Paxstore_Server);

                string pattern = @"(\d+)\.zip";

                // Use Regex to find matches
                Match match = Regex.Match(zipPath, pattern);

                if (match.Success)
                {
                    // Extract the numeric part and the full XML file name
                    reponseModel.FullName = match.Value;
                    reponseModel.Name = match.Groups[1].Value;
                    var host = new Uri(Request.GetDisplayUrl()).Authority;

                    reponseModel.Url = $"{host}/{Constants.PATH_USERDATA}/{Constants.PATH_Paxstore_Server}/{reponseModel.Name}.zip";
                }
                else
                {
                    throw new InvalidOperationException($"Can not find the xml name in: {zipPath}");
                }

                return GetResponseMessageSuccess(reponseModel, model.Id);
            }

            if (model.IsNoEncrypt)
            {
                reponseModel.FullName = fileFullName;
                reponseModel.Name = fileName.ToString();

                if (isSetupUpgrade)
                {
                    reponseModel.Url = new Uri(Request.GetDisplayUrl()).Authority + "/" + Constants.PATH_TERMINAL_MASTER_SETUP_UPGRADE_RECORD + "/" + model.TerminalSerialNumber + "/" + fileFullName;
                }
                else
                {
                    reponseModel.Url = new Uri(Request.GetDisplayUrl()).Authority + "/" +  Constants.PATH_PARAMETER + "/" + model.TerminalSerialNumber + "/" + fileFullName;
                }

                return GetResponseMessageSuccess(reponseModel, model.Id);
            }

            #region MyRegion
            try
            {
                var pathParamKey = PathHelper.GetFullPath(Constants.PATH_PARAMETER_KEY);
                var keyfolderpath = Utils.WebUtils.CreateDirectory(Path.Combine(currentDirectory, pathParamKey, model.TerminalSerialNumber));

                var outputfolderpath = Utils.WebUtils.PathParameterDownload(model.TerminalSerialNumber);
                var outputfilepath = Path.Combine(outputfolderpath, fileFullName);

                Package package = new Package();

                Package.ProgressCallback callback = new Package.ProgressCallback(Progress);

                package.SetCallback(callback);

                var server_publickeypath = outputfolderpath + "\\server_publickey";

                var server_privatekeypath = keyfolderpath + "\\server_privatekey";

                //write terminal public key from client to file
                var terminal_publickeypath = outputfolderpath + "\\terminal_publickey";

                int ret = package.GenerateKey(server_publickeypath, server_privatekeypath);

                System.IO.File.WriteAllBytes(terminal_publickeypath, Convert.FromBase64String(model.TerminalPublicKey));

                if (ret == 0)
                {
                    //Encrypt file
                    package.PackageInit("", outputfilepath, outputfolderpath, terminal_publickeypath, server_privatekeypath, Package.PACKAGE_COMPRESS_ENCRYPT);

                    package.Add(file, "/data/data/com.cctech.launchpadapp/files/", fileFullName);

                    ret = package.Run();

                    if (ret == 0)
                    {

                        var folderResult = outputfolderpath + "\\" + model.TerminalSerialNumber;

                        Directory.CreateDirectory(folderResult);

                        var keyResult = Path.Combine(folderResult, "server_publickey");
                        System.IO.File.Move(server_publickeypath, keyResult);

                        var xmlResult = Path.Combine(folderResult, fileFullName);
                        System.IO.File.Move(outputfilepath, xmlResult);

                        var terminalKey = Path.Combine(folderResult, "terminal_publickey");
                        System.IO.File.Move(terminal_publickeypath, terminalKey);

                        var zipPath = Path.Combine(outputfolderpath, fileName + ".zip");

                        ZipFile.CreateFromDirectory(folderResult, zipPath);

                    }
                    else
                    {
                        return GetResponseMessageError(HttpStatusCode.NotFound, "Application version is can not be encrypted with code " + ret);
                    }
                }

            }
            catch (Exception ex)
            {
                // return CreateResponseFromStatusCode(HttpStatusCode.InternalServerError,
                //  ex.ToString(), type);

                 return new ResponseMessage()
                    {
                        StatusCode = (int)HttpStatusCode.InternalServerError,
                        Message = ex.ToString(),
                    };
            }


            #endregion

            // Set the Extension property in the response model based on the file extension
            reponseModel.Extension = (type?.ToLower() == "json") ? "json" : "xml";

            reponseModel.FullName = fileFullName;
            reponseModel.Name = fileName.ToString();
            reponseModel.Url = new Uri(Request.GetDisplayUrl()).Authority + Constants.PATH_PARAMETER_DOWNLOAD + model.TerminalSerialNumber + "//" + fileName + ".zip";

            return CreateResponseFromStatusCode(HttpStatusCode.OK, reponseModel, type);
        }

        /// <summary>
        /// Client Call this api to add Client Id and change status after resync file
        /// </summary>
        /// <returns> if got full data rerutn recallFalse else return recall </returns>
        [Route("api/synchronize/resyncfile/response")]
        [HttpPost]
        public ActionResult<ResponseMessage> ReSynchronizeFileResponse([FromBody]ResponseModel model, string type = "json")
        {
            if (model == null || !ModelState.IsValid)
                return GetResponseMessageError(HttpStatusCode.BadRequest);

            var terminalUpdated = _terminalMasterService.GetTerminalBySerialNumber(model.TerminalSerialNumber,
                model.TerminalMacAddress, model.TerminalModelId);

            if (terminalUpdated == null)
                return GetResponseMessageError(HttpStatusCode.NoContent);

            try
            {
                #region Merchant
                if (model.Merchants != null && model.Merchants.Any())
                {
                    var i = 0;
                    foreach (var item in model.Merchants)
                    {
                        if (item.isSuccess != 1) continue;

                        var merchantTerminal = _merchantTerminalService.GetMerchantTerminalById(item.CloudId);
                        if (merchantTerminal == null) continue;
                        merchantTerminal.ClientStatusUpdate = true;
                        merchantTerminal.ClientId = item.ClientId;
                        merchantTerminal.ClientTimeUpdate = Utils.WebUtils.ConvertStringToDateTime($"{item.Date} {item.Time}", $"{Constants.FORMAT_DATE_MM_DD_YYYY} {Constants.FORMAT_TIME_HH__MM}");
                        _merchantTerminalService.Update(merchantTerminal);
                        model.Merchants[i].CloudId = merchantTerminal.Id;
                        model.Merchants[i].IsCloudUpdated = 1;
                        model.Merchants[i].Date = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_DATE_MM_DD_YYYY);
                        model.Merchants[i].Time = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_TIME_HH__MM);
                        i++;
                    }

                }
                #endregion

                #region Processor
                if (model.Processors != null && model.Processors.Any())
                {
                    var i = 0;
                    foreach (var item in model.Processors)
                    {
                        if (item.isSuccess != 1) continue;
                        var proccessorTerminal = _processorTerminalService.GetById(item.CloudId);
                        if (proccessorTerminal == null) continue;
                        proccessorTerminal.ClientStatusUpdate = true;
                        proccessorTerminal.ClientId = item.ClientId;
                        proccessorTerminal.ClientTimeUpdate = Utils.WebUtils.ConvertStringToDateTime($"{item.Date} {item.Time}", $"{Constants.FORMAT_DATE_MM_DD_YYYY} {Constants.FORMAT_TIME_HH__MM}");
                        _processorTerminalService.Update(proccessorTerminal);
                        model.Processors[i].CloudId = proccessorTerminal.Id;
                        model.Processors[i].IsCloudUpdated = 1;
                        model.Processors[i].Date = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_DATE_MM_DD_YYYY);
                        model.Processors[i].Time = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_TIME_HH__MM);
                        i++;
                    }
                }
                #endregion

                #region User Terminal
                if (model.Users != null && model.Users.Any())
                {
                    var i = 0;
                    foreach (var item in model.Users)
                    {
                        if (item.isSuccess != 1) continue;
                        var userTerminal = _userTerminalService.GetById(item.CloudId);
                        if (userTerminal == null) continue;
                        userTerminal.ClientStatusUpdate = true;
                        userTerminal.ClientId = item.ClientId;
                        userTerminal.ClientTimeUpdate = Utils.WebUtils.ConvertStringToDateTime($"{item.Date} {item.Time}", $"{Constants.FORMAT_DATE_MM_DD_YYYY} {Constants.FORMAT_TIME_HH__MM}");
                        _userTerminalService.Update(userTerminal);
                        model.Users[i].CloudId = userTerminal.Id;
                        model.Users[i].IsCloudUpdated = 1;
                        model.Users[i].Date = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_DATE_MM_DD_YYYY);
                        model.Users[i].Time = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_TIME_HH__MM);
                        i++;
                    }
                }
                #endregion

                #region User Mechant
                if (model.UserMerchants != null && model.UserMerchants.Any())
                {
                    var i = 0;
                    foreach (var item in model.UserMerchants)
                    {
                        if (item.isSuccess != 1) continue;
                        var userMerchant = _userMerchantService.GetById(item.CloudId);
                        if (userMerchant == null) continue;
                        userMerchant.ClientStatusUpdate = true;
                        userMerchant.ClientId = item.ClientId;
                        userMerchant.ClientTimeUpdate = Utils.WebUtils.ConvertStringToDateTime($"{item.Date} {item.Time}", $"{Constants.FORMAT_DATE_MM_DD_YYYY} {Constants.FORMAT_TIME_HH__MM}");
                        _userMerchantService.Update(userMerchant);
                        model.UserMerchants[i].CloudId = userMerchant.Id;
                        model.UserMerchants[i].IsCloudUpdated = 1;
                        model.UserMerchants[i].Date = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_DATE_MM_DD_YYYY);
                        model.UserMerchants[i].Time = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_TIME_HH__MM);
                        i++;
                    }
                }
                #endregion

                #region System
                if (model.System != null && model.System.isSuccess == 1)
                {

                    var setup = _terminalMasterService.GetTerminalSetupByTerminalId(terminalUpdated.Id);

                    setup.ClientStatusUpdate = true;
                    setup.ClientId = model.System.ClientId;
                    setup.ClientTimeUpdate = Utils.WebUtils.ConvertStringToDateTime($"{model.System.Date} {model.System.Time}", $"{Constants.FORMAT_DATE_MM_DD_YYYY} {Constants.FORMAT_TIME_HH__MM}");
                    _terminalSetupService.Update(setup);

                    model.System.CloudId = terminalUpdated.Id;
                    model.System.IsCloudUpdated = 1;
                    model.System.Date = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                        Utils.Constants.FORMAT_DATE_MM_DD_YYYY);
                    model.System.Time = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                        Utils.Constants.FORMAT_TIME_HH__MM);

                }
                #endregion

                #region Support
                if (model.Support != null && model.Support.isSuccess == 1)
                {
                    var support = terminalUpdated.Support;
                    support.ClientStatusUpdate = true;
                    support.ClientId = model.Support.ClientId;
                    support.ClientTimeUpdate = Utils.WebUtils.ConvertStringToDateTime($"{model.Support.Date} {model.Support.Time}", $"{Constants.FORMAT_DATE_MM_DD_YYYY} {Constants.FORMAT_TIME_HH__MM}");
                    _supportTerminalService.Update(support);
                    model.Support.CloudId = support.Id;
                    model.Support.IsCloudUpdated = 1;
                    model.Support.Date = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                        Utils.Constants.FORMAT_DATE_MM_DD_YYYY);
                    model.Support.Time = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                        Utils.Constants.FORMAT_TIME_HH__MM);
                }
                #endregion

                #region Terminal
                if (model.Terminal != null && model.Terminal.isSuccess == 1)
                {
                    terminalUpdated.ClientStatusUpdate = true;
                    terminalUpdated.ClientId = model.Terminal.ClientId;
                    terminalUpdated.ClientTimeUpdate = Utils.WebUtils.ConvertStringToDateTime($"{model.Terminal.Date} {model.Terminal.Time}", $"{Constants.FORMAT_DATE_MM_DD_YYYY} {Constants.FORMAT_TIME_HH__MM}");
                    _terminalMasterService.Update(terminalUpdated);
                    model.Terminal.CloudId = terminalUpdated.Id;
                    model.Terminal.IsCloudUpdated = 1;
                    model.Terminal.Date = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                        Utils.Constants.FORMAT_DATE_MM_DD_YYYY);
                    model.Terminal.Time = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                        Utils.Constants.FORMAT_TIME_HH__MM);
                }
                #endregion

                #region Merchant Processor
                if (model.MerchantProcessors != null && model.MerchantProcessors.Any())
                {
                    var i = 0;
                    foreach (var each in model.MerchantProcessors)
                    {
                        if (each.isSuccess != 1) continue;

                        var merchantProcesor = _processorMerchantService.GetMerchantProcessorById(each.CloudId);
                        if (merchantProcesor == null) continue;
                        merchantProcesor.ClientStatusUpdate = true;
                        merchantProcesor.ClientId = each.ClientId;
                        merchantProcesor.ClientTimeUpdate = Utils.WebUtils.ConvertStringToDateTime($"{each.Date} {each.Time}", $"{Constants.FORMAT_DATE_MM_DD_YYYY} {Constants.FORMAT_TIME_HH__MM}");

                        _processorMerchantService.Update(merchantProcesor);
                        model.MerchantProcessors[i].CloudId = merchantProcesor.Id;
                        model.MerchantProcessors[i].IsCloudUpdated = 1;
                        model.MerchantProcessors[i].Date = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_DATE_MM_DD_YYYY);
                        model.MerchantProcessors[i].Time = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_TIME_HH__MM);
                        i++;

                    }
                }
                #endregion

                #region Charity
                if (model.Charities != null && model.Charities.Any())
                {
                    var i = 0;
                    foreach (var item in model.Charities)
                    {
                        if (item.isSuccess != 1) continue;

                        var charity = _charityService.GetById(item.CloudId);
                        charity.ClientStatusUpdate = true;
                        charity.ClientId = item.ClientId;
                        charity.ClientTimeUpdate = Utils.WebUtils.ConvertStringToDateTime($"{item.Date} {item.Time}", $"{Constants.FORMAT_DATE_MM_DD_YYYY} {Constants.FORMAT_TIME_HH__MM}");
                        _charityService.Update(charity);

                        model.Charities[i].CloudId = charity.Id;
                        model.Charities[i].IsCloudUpdated = 1;
                        model.Charities[i].Date = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_DATE_MM_DD_YYYY);
                        model.Charities[i].Time = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_TIME_HH__MM);


                        i++;
                    }
                }
                #endregion

                #region Merchant Charity
                if (model.MerchantCharities != null && model.MerchantCharities.Any())
                {
                    var i = 0;
                    foreach (var item in model.MerchantCharities)
                    {
                        if (item.isSuccess != 1) continue;

                        var merchantCharity = _merchantCharityService.GetById(item.CloudId);
                        merchantCharity.ClientStatusUpdate = true;
                        merchantCharity.ClientId = item.ClientId;
                        merchantCharity.ClientTimeUpdate = Utils.WebUtils.ConvertStringToDateTime($"{item.Date} {item.Time}", $"{Constants.FORMAT_DATE_MM_DD_YYYY} {Constants.FORMAT_TIME_HH__MM}");
                        _merchantCharityService.Update(merchantCharity);

                        model.MerchantCharities[i].CloudId = merchantCharity.Id;
                        model.MerchantCharities[i].IsCloudUpdated = 1;
                        model.MerchantCharities[i].Date = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_DATE_MM_DD_YYYY);
                        model.MerchantCharities[i].Time = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_TIME_HH__MM);
                        i++;
                    }
                }
                #endregion

                #region Merchant Message
                if (model.MerchantPrintMessages != null && model.MerchantPrintMessages.Any())
                {
                    var i = 0;
                    foreach (var item in model.MerchantPrintMessages)
                    {
                        if (item.isSuccess != 1) continue;

                        var merchantPrintMessages = _messageMerchantService.GetMessageById(item.CloudId);
                        merchantPrintMessages.ClientStatusUpdate = true;
                        merchantPrintMessages.ClientId = item.ClientId;
                        merchantPrintMessages.ClientTimeUpdate = Utils.WebUtils.ConvertStringToDateTime($"{item.Date} {item.Time}", $"{Constants.FORMAT_DATE_MM_DD_YYYY} {Constants.FORMAT_TIME_HH__MM}");
                        _messageMerchantService.Update(merchantPrintMessages);

                        model.MerchantPrintMessages[i].CloudId = merchantPrintMessages.Id;
                        model.MerchantPrintMessages[i].IsCloudUpdated = 1;
                        model.MerchantPrintMessages[i].Date = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_DATE_MM_DD_YYYY);
                        model.MerchantPrintMessages[i].Time = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_TIME_HH__MM);
                        i++;
                    }
                }
                #endregion Message

                #region Bin Table
                if (model.BinTables != null && model.BinTables.Any())
                {
                    var i = 0;
                    foreach (var item in model.BinTables)
                    {
                        if (item.isSuccess != 1) continue;

                        var binTable = _binTableService.GetById(item.CloudId);
                        binTable.ClientStatusUpdate = true;
                        binTable.ClientId = item.ClientId;
                        binTable.ClientTimeUpdate = Utils.WebUtils.ConvertStringToDateTime($"{item.Date} {item.Time}", $"{Constants.FORMAT_DATE_MM_DD_YYYY} {Constants.FORMAT_TIME_HH__MM}");
                        _binTableService.Update(binTable);

                        model.BinTables[i].CloudId = binTable.Id;
                        model.BinTables[i].IsCloudUpdated = 1;
                        model.BinTables[i].Date = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_DATE_MM_DD_YYYY);
                        model.BinTables[i].Time = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_TIME_HH__MM);

                        i++;
                    }
                }
                #endregion

                #region Currencies
                if (model.Currencies != null && model.Currencies.Any())
                {
                    var i = 0;
                    foreach (var item in model.Currencies)
                    {
                        if (item.isSuccess != 1) continue;

                        var currency = _currencyService.GetById(item.CloudId);
                        currency.ClientStatusUpdate = true;
                        currency.ClientId = item.ClientId;
                        currency.ClientTimeUpdate = Utils.WebUtils.ConvertStringToDateTime($"{item.Date} {item.Time}", $"{Constants.FORMAT_DATE_MM_DD_YYYY} {Constants.FORMAT_TIME_HH__MM}");
                        _currencyService.Update(currency);

                        model.Currencies[i].CloudId = currency.Id;
                        model.Currencies[i].IsCloudUpdated = 1;
                        model.Currencies[i].Date = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_DATE_MM_DD_YYYY);
                        model.Currencies[i].Time = Utils.WebUtils.ConvertDateTimeToString(DateTime.Now,
                            Utils.Constants.FORMAT_TIME_HH__MM);
                        i++;
                    }
                }
                #endregion

                return CreateResponseFromStatusCode(HttpStatusCode.OK, model, type);

            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
                return GetResponseMessageError(HttpStatusCode.InternalServerError);
            }
        }

        #endregion

        #region HELPER

        private string CreateXmlForUserMerchantTerminal(int clientId, int cloudId, int merchantId)
        {
            var builder = new StringBuilder();
            var setting = new XmlWriterSettings() { OmitXmlDeclaration = true };
            using (var writer = XmlWriter.Create(builder, setting))
            {
                writer.WriteStartDocument();
                writer.WriteStartElement(ApiLabelXmlConstants.Update);
                writer.WriteStartElement(ApiLabelXmlConstants.User);

                WriteXmlAttr(writer, ApiLabelXmlConstants.CloudId, cloudId.ToString("D"));
                WriteXmlAttr(writer, ApiLabelXmlConstants.ClientId, clientId.ToString("D"));
                WriteXmlAttr(writer, ApiLabelXmlConstants.IdMerchant, merchantId.ToString("D"));

                writer.WriteEndElement();
                writer.WriteEndElement();
                writer.WriteEndDocument();
            }
            return builder.ToString();
        }

        private string CreateXmlForUserTrainingAndAutoLogon(int trainingId, int logonId)
        {
            var builder = new StringBuilder();
            var setting = new XmlWriterSettings() { OmitXmlDeclaration = true };
            using (var writer = XmlWriter.Create(builder, setting))
            {
                writer.WriteStartDocument();
                writer.WriteStartElement(ApiLabelXmlConstants.Update);
                writer.WriteStartElement(ApiLabelXmlConstants.System);
                writer.WriteStartElement(ApiLabelXmlConstants.Setup);

                if (trainingId != 0)
                {
                    WriteXmlAttr(writer, ApiLabelXmlConstants.IdTrainingUser, trainingId.ToString("D"));
                }
                if (trainingId != 0)
                {
                    WriteXmlAttr(writer, ApiLabelXmlConstants.IdAutoLogonUser, logonId.ToString("D"));
                }

                writer.WriteEndElement();
                writer.WriteEndElement();
                writer.WriteEndElement();
                writer.WriteEndDocument();
            }
            return builder.ToString();
        }

        private string CreateXmlForMerchantProcessor(ProcessorMerchant master)
        {
            var builder = new StringBuilder();
            var setting = new XmlWriterSettings() { OmitXmlDeclaration = true };
            using (var writer = XmlWriter.Create(builder, setting))
            {
                writer.WriteStartDocument();
                writer.WriteStartElement(ApiLabelXmlConstants.Add);
                writer.WriteStartElement(ApiLabelXmlConstants.MerchantProcessor);

                WriteXmlAttr(writer, ApiLabelXmlConstants.IdProcessor, master.ProcessorTerminal.ClientId.ToString("D"));
                WriteXmlAttr(writer, ApiLabelXmlConstants.IdMerchant, master.MerchantTerminal.ClientId.ToString("D"));
                //WriteXmlAttr(writer, ApiLabelXmlConstants.fCreditRefund,
                //        WebUtils.ConvertBoolToInt(master.fCreditRefund).ToString("D"));
                //WriteXmlAttr(writer, ApiLabelXmlConstants.fCreditAdjust,
                //        WebUtils.ConvertBoolToInt(master.fCreditAdjust).ToString("D"));
                //WriteXmlAttr(writer, ApiLabelXmlConstants.fCreditVoid,
                //        WebUtils.ConvertBoolToInt(master.fCreditVoid).ToString("D"));
                //WriteXmlAttr(writer, ApiLabelXmlConstants.fCreditPartialVoid,
                //        WebUtils.ConvertBoolToInt(master.fCreditPartialVoid).ToString("D"));
                //WriteXmlAttr(writer, ApiLabelXmlConstants.fDebitRefund,
                //        WebUtils.ConvertBoolToInt(master.fCreditRefund).ToString("D"));
                //WriteXmlAttr(writer, ApiLabelXmlConstants.fCreditAdjust,
                //        WebUtils.ConvertBoolToInt(master.fCreditAdjust).ToString("D"));
                //WriteXmlAttr(writer, ApiLabelXmlConstants.fCreditVoid,
                //        WebUtils.ConvertBoolToInt(master.fCreditVoid).ToString("D"));
                //WriteXmlAttr(writer, ApiLabelXmlConstants.fCreditPartialVoid,
                //        WebUtils.ConvertBoolToInt(master.fCreditPartialVoid).ToString("D"));
                if (master.ProcessorTerminal.Proccessor.Library != null)
                {
                    if (master.ProcessorTerminal.Proccessor.Library.Value == Constants.TSYSValue)
                    {
                        if (master.ProcessorMerchantSetup != null)
                        {
                            writer.WriteStartElement(ApiLabelXmlConstants.TSYS);
                            WriteXmlAttr(writer, ApiLabelXmlConstants.wszAcquirerBin,
                                master.ProcessorMerchantSetup.AcquirerBin);
                            WriteXmlAttr(writer, ApiLabelXmlConstants.wszStoreNumber,
                                master.ProcessorMerchantSetup.StoreNumber.ToString("D"));
                            WriteXmlAttr(writer, ApiLabelXmlConstants.wszCategoryCode,
                                master.ProcessorMerchantSetup.CategoryCode);
                            WriteXmlAttr(writer, ApiLabelXmlConstants.wsAgentBankNumber,
                                master.ProcessorMerchantSetup.AgentBankNumber);
                            WriteXmlAttr(writer, ApiLabelXmlConstants.wsAgentChainNumber,
                                master.ProcessorMerchantSetup.AgentChainNumber);
                            WriteXmlAttr(writer, ApiLabelXmlConstants.wsSharingGroup,
                                master.ProcessorMerchantSetup.SharingGroup);
                            WriteXmlAttr(writer, ApiLabelXmlConstants.wsABANumber, master.ProcessorMerchantSetup.ABANumber);
                            WriteXmlAttr(writer, ApiLabelXmlConstants.wsSettlementAgentNumber,
                                master.ProcessorMerchantSetup.SettlementAgentNumber);
                            WriteXmlAttr(writer, ApiLabelXmlConstants.wsSettlementAgentNumber,
                                master.ProcessorMerchantSetup.SettlementAgentNumber);
                            WriteXmlAttr(writer, ApiLabelXmlConstants.wsReinbursementAtribute,
                                master.ProcessorMerchantSetup.ReinbursementAtribute);
                            //WriteXmlAttr(writer, ApiLabelXmlConstants.wsReceivingInstitutionIdentification,
                            //    master.ProcessorMerchantSetup.ReceivingInstitutionID);
                            writer.WriteEndElement();
                        }
                    }
                }

                if (master.ProcessorMerchantSettleBatchSetup != null)
                {
                    writer.WriteStartElement(ApiLabelXmlConstants.SettleBatch);

                    //WriteXmlAttr(writer, ApiLabelXmlConstants.fPrint,
                    //    WebUtils.ConvertBoolToInt(master.ProcessorMerchantSettleBatchSetup.fPrint).ToString("D"));


                    WriteXmlAttr(writer, ApiLabelXmlConstants.MaxBatches,
                        master.ProcessorMerchantSettleBatchSetup.MaxBatches.ToString("D"));
                    //if (master.ProcessorMerchantSettleBatchSetup.BatchCutOffTime != null)
                    //{
                    //    WriteXmlAttr(writer, ApiLabelXmlConstants.MaxBatches,
                    //        master.ProcessorMerchantSettleBatchSetup.BatchCutOffTime.Value.ToString("HHmmss"));
                    //}
                    if (master.ProcessorMerchantSettleBatchSetup.TimeLine1 != null)
                    {
                        writer.WriteStartElement(ApiLabelXmlConstants.Times + "1");
                        WriteXmlAttr(writer, ApiLabelXmlConstants.fAuto,
                            Utils.WebUtils.ConvertBoolToInt(master.ProcessorMerchantSettleBatchSetup.fTimeLine1)
                                .ToString("D"));
                        WriteXmlAttr(writer, ApiLabelXmlConstants.Hours,
                            master.ProcessorMerchantSettleBatchSetup.TimeLine1.Value.ToString("HH"));
                        WriteXmlAttr(writer, ApiLabelXmlConstants.Minutes,
                            master.ProcessorMerchantSettleBatchSetup.TimeLine1.Value.ToString("mm"));
                        WriteXmlAttr(writer, ApiLabelXmlConstants.Seconds,
                            master.ProcessorMerchantSettleBatchSetup.TimeLine1.Value.ToString("ss"));

                        writer.WriteEndElement();
                    }
                    if (master.ProcessorMerchantSettleBatchSetup.TimeLine2 != null)
                    {
                        writer.WriteStartElement(ApiLabelXmlConstants.Times + "2");
                        WriteXmlAttr(writer, ApiLabelXmlConstants.fAuto,
                            Utils.WebUtils.ConvertBoolToInt(master.ProcessorMerchantSettleBatchSetup.fTimeLine2)
                                .ToString("D"));
                        WriteXmlAttr(writer, ApiLabelXmlConstants.Hours,
                            master.ProcessorMerchantSettleBatchSetup.TimeLine2.Value.ToString("HH"));
                        WriteXmlAttr(writer, ApiLabelXmlConstants.Minutes,
                            master.ProcessorMerchantSettleBatchSetup.TimeLine2.Value.ToString("mm"));
                        WriteXmlAttr(writer, ApiLabelXmlConstants.Seconds,
                            master.ProcessorMerchantSettleBatchSetup.TimeLine2.Value.ToString("ss"));

                        writer.WriteEndElement();
                    }
                    if (master.ProcessorMerchantSettleBatchSetup.TimeLine3 != null)
                    {
                        writer.WriteStartElement(ApiLabelXmlConstants.Times + "3");
                        WriteXmlAttr(writer, ApiLabelXmlConstants.fAuto,
                            Utils.WebUtils.ConvertBoolToInt(master.ProcessorMerchantSettleBatchSetup.fTimeLine3)
                                .ToString("D"));
                        WriteXmlAttr(writer, ApiLabelXmlConstants.Hours,
                            master.ProcessorMerchantSettleBatchSetup.TimeLine3.Value.ToString("HH"));
                        WriteXmlAttr(writer, ApiLabelXmlConstants.Minutes,
                            master.ProcessorMerchantSettleBatchSetup.TimeLine3.Value.ToString("mm"));
                        WriteXmlAttr(writer, ApiLabelXmlConstants.Seconds,
                            master.ProcessorMerchantSettleBatchSetup.TimeLine3.Value.ToString("ss"));

                        writer.WriteEndElement();
                    }
                    if (master.ProcessorMerchantSettleBatchSetup.TimeLine4 != null)
                    {
                        writer.WriteStartElement(ApiLabelXmlConstants.Times + "4");
                        WriteXmlAttr(writer, ApiLabelXmlConstants.fAuto,
                            Utils.WebUtils.ConvertBoolToInt(master.ProcessorMerchantSettleBatchSetup.fTimeLine4)
                                .ToString("D"));
                        WriteXmlAttr(writer, ApiLabelXmlConstants.Hours,
                            master.ProcessorMerchantSettleBatchSetup.TimeLine4.Value.ToString("HH"));
                        WriteXmlAttr(writer, ApiLabelXmlConstants.Minutes,
                            master.ProcessorMerchantSettleBatchSetup.TimeLine4.Value.ToString("mm"));
                        WriteXmlAttr(writer, ApiLabelXmlConstants.Seconds,
                            master.ProcessorMerchantSettleBatchSetup.TimeLine4.Value.ToString("ss"));

                        writer.WriteEndElement();
                    }
                    if (master.ProcessorMerchantSettleBatchSetup.TimeLine5 != null)
                    {
                        writer.WriteStartElement(ApiLabelXmlConstants.Times + "5");
                        WriteXmlAttr(writer, ApiLabelXmlConstants.fAuto,
                            Utils.WebUtils.ConvertBoolToInt(master.ProcessorMerchantSettleBatchSetup.fTimeLine5)
                                .ToString("D"));
                        WriteXmlAttr(writer, ApiLabelXmlConstants.Hours,
                            master.ProcessorMerchantSettleBatchSetup.TimeLine5.Value.ToString("HH"));
                        WriteXmlAttr(writer, ApiLabelXmlConstants.Minutes,
                            master.ProcessorMerchantSettleBatchSetup.TimeLine5.Value.ToString("mm"));
                        WriteXmlAttr(writer, ApiLabelXmlConstants.Seconds,
                            master.ProcessorMerchantSettleBatchSetup.TimeLine5.Value.ToString("ss"));

                        writer.WriteEndElement();
                    }
                    if (master.ProcessorMerchantSettleBatchSetup.TimeLine6 != null)
                    {
                        writer.WriteStartElement(ApiLabelXmlConstants.Times + "6");
                        WriteXmlAttr(writer, ApiLabelXmlConstants.fAuto,
                            Utils.WebUtils.ConvertBoolToInt(master.ProcessorMerchantSettleBatchSetup.fTimeLine6)
                                .ToString("D"));
                        WriteXmlAttr(writer, ApiLabelXmlConstants.Hours,
                            master.ProcessorMerchantSettleBatchSetup.TimeLine6.Value.ToString("HH"));
                        WriteXmlAttr(writer, ApiLabelXmlConstants.Minutes,
                            master.ProcessorMerchantSettleBatchSetup.TimeLine6.Value.ToString("mm"));
                        WriteXmlAttr(writer, ApiLabelXmlConstants.Seconds,
                            master.ProcessorMerchantSettleBatchSetup.TimeLine6.Value.ToString("ss"));

                        writer.WriteEndElement();
                    }

                    writer.WriteEndElement();//end Settle Batch
                }
                writer.WriteEndElement();
                writer.WriteEndElement();
                writer.WriteEndDocument();
            }
            return builder.ToString();
        }

        private void WriteXmlAttr(XmlWriter writer, string element, string value)
        {
            writer.WriteStartElement(element);
            writer.WriteString(value);
            writer.WriteEndElement();
        }

        #endregion

        #region TestAPI

        //---------------------------------------------------------------------------------------
        // TEST API
        //---------------------------------------------------------------------------------------
        [Route("api/synchronize/test/xml")]
        [HttpGet]
        public ActionResult<ResponseMessage> ResponseModel(int id = 0)
        {
            var modelTemp = new ResponseModel();
            var users = new List<ResponseStatus>()
            {
                new ResponseStatus()
                {
                    ClientId = 1,
                    CloudId = 2,
                    isSuccess = 1,
                    Date = DateTime.Now.ToString("yyy-MM-dd"),
                    Time = DateTime.Now.ToString("HH:mm:ss")
                },
                new ResponseStatus()
                {
                    ClientId = 1,
                    CloudId = 2,
                    isSuccess = 1,
                    Date = DateTime.Now.ToString("yyy-MM-dd"),
                    Time = DateTime.Now.ToString("HH:mm:ss")
                },
                new ResponseStatus()
                {
                    ClientId = 1,
                    CloudId = 2,
                    isSuccess = 1,
                    Date = DateTime.Now.ToString("yyy-MM-dd"),
                    Time = DateTime.Now.ToString("HH:mm:ss")
                }
            };
            modelTemp.Users = users;

            var processor = new List<ResponseStatus>()
            {
                 new ResponseStatus()
                {
                    ClientId = 1,
                    CloudId = 2,
                    isSuccess = 1,
                    Date = DateTime.Now.ToString("yyy-MM-dd"),
                    Time = DateTime.Now.ToString("HH:mm:ss")
                },
                new ResponseStatus()
                {
                    ClientId = 1,
                    CloudId = 2,
                    isSuccess = 1,
                    Date = DateTime.Now.ToString("yyy-MM-dd"),
                    Time = DateTime.Now.ToString("HH:mm:ss")
                },
                new ResponseStatus()
                {
                    ClientId = 1,
                    CloudId = 2,
                    isSuccess = 1,
                    Date = DateTime.Now.ToString("yyy-MM-dd"),
                    Time = DateTime.Now.ToString("HH:mm:ss")
                }
            };
            modelTemp.Processors = processor;

            var merchants = new List<ResponseStatus>()
            {
                 new ResponseStatus()
                {
                    ClientId = 1,
                    CloudId = 2,
                    isSuccess = 1,
                    Date = DateTime.Now.ToString("yyy-MM-dd"),
                    Time = DateTime.Now.ToString("HH:mm:ss")
                },
                new ResponseStatus()
                {
                   ClientId = 1,
                    CloudId = 2,
                    isSuccess = 1,
                    Date = DateTime.Now.ToString("yyy-MM-dd"),
                    Time = DateTime.Now.ToString("HH:mm:ss")
                },
                new ResponseStatus()
                {
                    ClientId = 1,
                    CloudId = 2,
                    isSuccess = 1,
                    Date = DateTime.Now.ToString("yyy-MM-dd"),
                    Time = DateTime.Now.ToString("HH:mm:ss")
                }
            };
            modelTemp.Merchants = merchants;

            var merchantProcessors = new List<ResponseStatus>()
            {
                 new ResponseStatus()
                {
                    ClientId = 1,
                    CloudId = 2,
                    isSuccess = 1,
                    Date = DateTime.Now.ToString("yyy-MM-dd"),
                    Time = DateTime.Now.ToString("HH:mm:ss")
                },
                new ResponseStatus()
                {
                   ClientId = 1,
                    CloudId = 2,
                    isSuccess = 1,
                    Date = DateTime.Now.ToString("yyy-MM-dd"),
                    Time = DateTime.Now.ToString("HH:mm:ss")
                },
                new ResponseStatus()
                {
                   ClientId = 1,
                    CloudId = 2,
                    isSuccess = 1,
                    Date = DateTime.Now.ToString("yyy-MM-dd"),
                    Time = DateTime.Now.ToString("HH:mm:ss")
                }
            };

            modelTemp.MerchantProcessors = merchantProcessors;
            var tmp = new ResponseStatus();
            modelTemp.Support = tmp;

            if (id == 1)
            {
                return CreateResponseFromStatusCode(HttpStatusCode.NotFound, modelTemp, Constants.XmlMediaTypeFormatter);
            }
            else
            {
                return CreateResponseFromStatusCode(HttpStatusCode.OK, modelTemp, Constants.JsonMediaTypeFormatter);
            }
        }

        #endregion

        #region XML node Moving Function

        private XElement Next(XElement a)
        {
            if (a.NextNode != null)
                return (XElement)a.NextNode;
            return null;
        }

        private XElement Parent(XElement a)
        {
            return a.Parent;
        }

        private XElement MoveToFirstChild(XElement a)
        {
            return (XElement)a.FirstNode;
        }

        #endregion

        #region XML to Object Parser
        private SupportTerminalApi ParseSupport(XElement a, bool defaultValue)
        {
            var result = new SupportTerminalApi(defaultValue);
            var props = result.GetType().GetProperties();
            while (a != null)
            {
                foreach (var prop in props.Where(prop => prop.Name.Equals(a.Name.LocalName)))
                {
                    TypeConverter conv = TypeDescriptor.GetConverter(prop.PropertyType);
                    prop.SetValue(result, conv.ConvertFrom(a.Value));
                    break;
                }
                a = Next(a);
            }
            return result;
        }

        private TerminalModelApi ParseTerminalModel(XElement a, bool defaultValue)
        {
            var result = new TerminalModelApi(defaultValue);
            var props = result.GetType().GetProperties();
            while (a != null)
            {
                foreach (var prop in props.Where(prop => prop.Name.Equals(a.Name.LocalName)))
                {
                    TypeConverter conv = TypeDescriptor.GetConverter(prop.PropertyType);
                    prop.SetValue(result, conv.ConvertFrom(a.Value));
                    break;
                }
                a = Next(a);
            }
            return result;
        }

        private Processor ParseProcessor(XElement a)
        {
            var result = new Processor();
            var props = result.GetType().GetProperties();
            while (a != null)
            {
                if (!a.HasElements)
                {
                    foreach (var prop in props)
                    {
                        if (prop.Name.Equals(a.Name.LocalName))
                        {
                            TypeConverter conv = TypeDescriptor.GetConverter(prop.PropertyType);
                            prop.SetValue(result, conv.ConvertFrom(a.Value));
                            break;
                        }
                    }
                    a = Next(a);
                }
                else
                {
                    if (a.Name.LocalName.Equals(ApiLabelXmlConstants.TSYS))
                    {
                        result.TSYS = ParseTsysType(MoveToFirstChild(a));
                        result.iLibrary = 1;
                    }
                    switch (a.Name.LocalName.ToUpper())
                    {
                        case Constants.PAYMENTS:
                            result.Payments = ParseOtherProcessorType(MoveToFirstChild(a));
                            break;
                        case Constants.POSPROCESSOR:
                            result.PosProcessor = ParseOtherProcessorType(MoveToFirstChild(a));
                            break;
                        case Constants.GIVEX:
                            result.Givex = ParseOtherProcessorType(MoveToFirstChild(a));
                            break;
                        case Constants.GLOBALONE:
                            result.GlobalOne = ParseOtherProcessorType(MoveToFirstChild(a));
                            break;
                    }
                    a = Next(a);
                }

            }
            return result;
        }

        private PosRequestTerminalApi ParsePosRequests(XElement a)
        {
            var result = new PosRequestTerminalApi();
            var props = result.GetType().GetProperties();
            while (a != null)
            {
                if (!a.HasElements)
                {
                    foreach (var prop in props)
                    {
                        if (prop.Name.Equals(a.Name.LocalName))
                        {
                            TypeConverter conv = TypeDescriptor.GetConverter(prop.PropertyType);
                            prop.SetValue(result, conv.ConvertFrom(a.Value));
                            break;
                        }
                    }
                    a = Next(a);
                }
                else
                {
                    switch (a.Name.LocalName)
                    {
                        case ApiLabelXmlConstants.Sale:
                            result.Sale = ParseSystemPosSaleType(MoveToFirstChild(a));
                            break;
                        case ApiLabelXmlConstants.Bar:
                            result.Bar = ParseSystemPosSaleType(MoveToFirstChild(a));
                            break;
                        case ApiLabelXmlConstants.Room:
                            result.Room = ParseSystemPosSaleType(MoveToFirstChild(a));
                            break;
                        case ApiLabelXmlConstants.Table:
                            result.Table = ParseSystemPosSaleType(MoveToFirstChild(a));
                            break;
                        case ApiLabelXmlConstants.Customer:
                            result.Customer = ParseSystemPosSaleType(MoveToFirstChild(a));
                            break;
                        default:
                            break;
                    }
                    a = Next(a);
                }
            }
            return result;
        }

        private MerchantProcessorApi ParseMerchantProcessor(XElement a, string action, int terminalId)
        {
            var result = new MerchantProcessorApi();
            var props = result.GetType().GetProperties();
            while (a != null)
            {
                if (!a.HasElements)
                {
                    foreach (var prop in props)
                    {
                        if (prop.Name.ToLower().Equals(a.Name.LocalName.ToLower()))
                        {
                            TypeConverter conv = TypeDescriptor.GetConverter(prop.PropertyType);
                            prop.SetValue(result, conv.ConvertFrom(a.Value));
                            break;
                        }
                    }
                    a = Next(a);
                }
                else
                {
                    switch (a.Name.LocalName)
                    {
                        case ApiLabelXmlConstants.SettleBatch:
                            result.SettleBatch = ParseMerchantProcessorSettleBatch(MoveToFirstChild(a));
                            break;
                        case ApiLabelXmlConstants.CTPayments:
                            result.CtPayments =
                                (MerchantProcessorPayments)
                                    new MerchantProcessorPayments().ParseToObject(MoveToFirstChild(a));
                            break;
                        case ApiLabelXmlConstants.Givex:
                            result.Givex =
                                (MerchantProcessorGivex)new MerchantProcessorGivex().ParseToObject(MoveToFirstChild(a));
                            break;
                        case ApiLabelXmlConstants.GlobalOne:
                            result.Global =
                                (MerchantProcessorGlobal)new MerchantProcessorGlobal().ParseToObject(MoveToFirstChild(a));
                            break;
                        case ApiLabelXmlConstants.TSYS:
                            result.TSYS =
                                (ProcessorMerchantSetupModelApi)
                                    new ProcessorMerchantSetupModelApi().ParseToObject(MoveToFirstChild(a));
                            break;
                        default:
                            break;
                    }
                    a = Next(a);
                }
            }
            if (result.IdMerchant != 0 && result.IdProcessor != 0)
            {
                var merchantProcessorServer =
                    _processorMerchantService.GetMerchantProcessorsQueryable()
                        .SingleOrDefault(
                            x =>
                                x.MerchantTerminal.ClientId == result.IdMerchant &&
                                x.MerchantTerminal.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD &&
                                x.ProcessorTerminal.ClientId == result.IdProcessor &&
                                x.ProcessorTerminal.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD &&
                                x.MerchantTerminal.TerminalId == terminalId &&
                                x.ProcessorTerminal.TerminalId == terminalId &&
                                x.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD);
                if (merchantProcessorServer != null)
                {
                    var setupBatch =
                        _processorMerchantService.GetProcessorSetupSettleByMerchantProcessorId(merchantProcessorServer.Id);
                    setupBatch = result.SettleBatch.GetEntityMaster(setupBatch);
                    var functionType =
                        _merchantProcessorFunctionTypeService.FirstOrDefault(x=> x.MerchantProcessorId == merchantProcessorServer.Id);
                    if (functionType == null)
                    {
                        functionType = new MerchantProcessorFunctionType()
                        {
                            MerchantProcessorId = merchantProcessorServer.Id
                        };
                        _merchantProcessorFunctionTypeService.Insert(functionType);
                    }
                    merchantProcessorServer = result.GetEntityMaster(merchantProcessorServer);
                    functionType = result.GetMasterEntity(functionType);
                    _processorMerchantService.Update(merchantProcessorServer);
                    _processorMerchantSettleBatchSetupService.Update(setupBatch);
                    _merchantProcessorFunctionTypeService.Update(functionType);
                    result.Id = setupBatch.Id;
                    MerchantProcessorFunctionType function = null;
                    MerchantProcessorTypePaymentsSetup payments = null;
                    MerchantProcessorTypeGivexSetup givex = null;
                    MerchantProcessorTypeGlobalOneSetup global = null;
                    ProcessorMerchantSetup tsys = null;

                    var lib = merchantProcessorServer.ProcessorTerminal.Proccessor.Library;
                    if (lib != null)
                    {
                        switch (lib.Value)
                        {
                            case Constants.TSYSValue:
                                if (result.TSYS != null)
                                {
                                    tsys =
                                        _processorMerchantService.GetProcessorSetupByMerchantId(
                                            merchantProcessorServer.MerchantTerminalId,
                                            merchantProcessorServer.ProcessorTerminalId);
                                    if (tsys != null)
                                    {
                                        tsys = result.TSYS.GetEntityMaster(tsys);
                                        _processorMerchantSetupService.Update(tsys);
                                    }
                                }
                                break;
                            case Constants.CTPaymentsValue:
                                if (result.CtPayments != null)
                                {
                                    payments =
                                        _merchantProcessorTypePaymentsSetupService.FirstOrDefault(x=> x.MerchantProcessorId == merchantProcessorServer.Id);
                                    if (payments == null)
                                    {
                                        payments = new MerchantProcessorTypePaymentsSetup()
                                        {
                                            MerchantProcessorId = merchantProcessorServer.Id
                                        };
                                        _merchantProcessorTypePaymentsSetupService.Insert(payments);
                                    }
                                    payments = result.CtPayments.GetEntityMaster(payments);
                                    _merchantProcessorTypePaymentsSetupService.Insert(payments);
                                }
                                break;
                            case Constants.GivexValue:
                                if (result.Givex != null)
                                {
                                    givex =
                                        _merchantProcessorTypeGivexSetupService.FirstOrDefault(x => x.MerchantProcessorId ==
                                            merchantProcessorServer.Id);
                                    if (givex == null)
                                    {
                                        givex = new MerchantProcessorTypeGivexSetup()
                                        {
                                            MerchantProcessorId = merchantProcessorServer.Id
                                        };
                                        _merchantProcessorTypeGivexSetupService.Insert(givex);
                                    }
                                    givex = result.Givex.GetEntityMaster(givex);
                                    _merchantProcessorTypeGivexSetupService.Update(givex);
                                }
                                break;
                            case Constants.GlobalOneValue:
                                if (result.Global != null)
                                {
                                    global =
                                        _merchantProcessorTypeGlobalOneSetupService.FirstOrDefault(x => x.MerchantProcessorId ==
                                            merchantProcessorServer.Id);
                                    if (global == null)
                                    {
                                        global = new MerchantProcessorTypeGlobalOneSetup()
                                        {
                                            MerchantProcessorId = merchantProcessorServer.Id
                                        };
                                        _merchantProcessorTypeGlobalOneSetupService.Insert(global);
                                    }
                                    global = result.Global.GetEntityMaster(global);
                                    _merchantProcessorTypeGlobalOneSetupService.Update(global);
                                }
                                break;
                        }
                    }
                }
                else
                {
                    var processorId = 0;
                    var merchantId = 0;
                    try
                    {
                        processorId =
                            _processorTerminalService.GetProcessorTerminals()
                                .Where(x => x.ClientId == result.IdProcessor)
                                .Where(x => x.TerminalId == terminalId)
                                .Where(x => x.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD)
                                .Select(x => x.Id).First();
                        merchantId =
                            _merchantTerminalService.GetMerchantTerminals()
                                .Where(x => x.ClientId == result.IdMerchant)
                                .Where(x => x.TerminalId == terminalId)
                                .Where(x => x.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD)
                                .Select(x => x.Id).First();
                    }
                    catch
                    {
                        return result;
                    }
                    if (merchantId != 0 && processorId != 0)
                    {
                        merchantProcessorServer = new ProcessorMerchant()
                        {
                            MerchantTerminalId = merchantId,
                            ProcessorTerminalId = processorId,
                            ProcessorMerchantSettleBatchSetup = new ProcessorMerchantSettleBatchSetup(),
                            ProcessorMerchantSetup = new ProcessorMerchantSetup()
                        };
                        merchantProcessorServer = result.GetEntityMaster(merchantProcessorServer);
                        _processorMerchantService.Insert(merchantProcessorServer);
                        result.Id = merchantProcessorServer.Id;
                        var setupBatch =
                            _processorMerchantService.GetProcessorSetupSettleByMerchantProcessorId(
                                merchantProcessorServer.Id);
                        setupBatch = result.SettleBatch.GetEntityMaster(setupBatch);
                        var functionType =
                            _merchantProcessorFunctionTypeService.FirstOrDefault(x=> x.MerchantProcessorId == merchantProcessorServer.Id);
                        if (functionType == null)
                        {
                            functionType = new MerchantProcessorFunctionType()
                            {
                                MerchantProcessorId = merchantProcessorServer.Id
                            };
                            _merchantProcessorFunctionTypeService.Insert(functionType);
                        }
                        functionType = result.GetMasterEntity(functionType);
                        _processorMerchantSettleBatchSetupService.Update(setupBatch);
                        _merchantProcessorFunctionTypeService.Update(functionType);
                        MerchantProcessorFunctionType function = null;
                        MerchantProcessorTypePaymentsSetup payments = null;
                        MerchantProcessorTypeGivexSetup givex = null;
                        MerchantProcessorTypeGlobalOneSetup global = null;
                        ProcessorMerchantSetup tsys = null;

                        var lib = _processorMasterService.GetLibraryByProcessor(merchantProcessorServer.ProcessorTerminal.ProcessorId);
                               
                        if (lib != null)
                        {
                            switch (lib.Value)
                            {
                                case Constants.TSYSValue:
                                    if (result.TSYS != null)
                                    {
                                        tsys =
                                            _processorMerchantService.GetProcessorSetupByMerchantId(
                                                merchantProcessorServer.MerchantTerminalId,
                                                merchantProcessorServer.ProcessorTerminalId);
                                        if (tsys != null)
                                        {
                                            tsys = result.TSYS.GetEntityMaster(tsys);
                                            _processorMerchantSetupService.Update(tsys);
                                        }
                                    }

                                    break;
                                case Constants.CTPaymentsValue:
                                    if (result.CtPayments != null)
                                    {
                                        payments =
                                            _merchantProcessorTypePaymentsSetupService.FirstOrDefault(x=> x.MerchantProcessorId ==
                                                merchantProcessorServer.Id);
                                        if (payments == null)
                                        {
                                            payments = new MerchantProcessorTypePaymentsSetup()
                                            {
                                                MerchantProcessorId = merchantProcessorServer.Id
                                            };
                                            _merchantProcessorTypePaymentsSetupService.Insert(payments);
                                        }
                                        payments = result.CtPayments.GetEntityMaster(payments);
                                        _merchantProcessorTypePaymentsSetupService.Insert(payments);
                                    }
                                    break;
                                case Constants.GivexValue:
                                    if (result.Givex != null)
                                    {
                                        givex =
                                            _merchantProcessorTypeGivexSetupService.FirstOrDefault(x=> x.MerchantProcessorId ==
                                                merchantProcessorServer.Id);
                                        if (givex == null)
                                        {
                                            givex = new MerchantProcessorTypeGivexSetup()
                                            {
                                                MerchantProcessorId = merchantProcessorServer.Id
                                            };
                                            _merchantProcessorTypeGivexSetupService.Insert(givex);
                                        }
                                        givex = result.Givex.GetEntityMaster(givex);
                                        _merchantProcessorTypeGivexSetupService.Update(givex);
                                    }
                                    break;
                                case Constants.GlobalOneValue:
                                    if (result.Global != null)
                                    {
                                        global =
                                            _merchantProcessorTypeGlobalOneSetupService.FirstOrDefault(x=> x.MerchantProcessorId
                                                == merchantProcessorServer.Id);
                                        if (global == null)
                                        {
                                            global = new MerchantProcessorTypeGlobalOneSetup()
                                            {
                                                MerchantProcessorId = merchantProcessorServer.Id
                                            };
                                            _merchantProcessorTypeGlobalOneSetupService.Insert(global);
                                        }
                                        global = result.Global.GetEntityMaster(global);
                                        _merchantProcessorTypeGlobalOneSetupService.Update(global);
                                    }
                                    break;
                            }
                        }
                    }
                }
            }
            return result;
        }

        private ProcessorMerchantSettleBatchModelApi ParseMerchantProcessorSettleBatch(XElement a)
        {
            var result = new ProcessorMerchantSettleBatchModelApi();
            var lastNode = false;
            while (!lastNode)
            {
                //check if It Has Element

                var updateItemName = a.Name.LocalName;
                switch (updateItemName)
                {
                    case "cMaxBatches":
                        result.cMaxBatches = Int32.Parse(a.Value);
                        break;
                    case "fPrint":
                        result.fPrint = a.Value == "" ? false : Int32.Parse(a.Value) == 0 ? false : true;
                        break;
                    case "fWarnAdjustToSettle":
                        result.fWarnAdjustToSettle = a.Value == "" ? false : Int32.Parse(a.Value) == 0 ? false : true;
                        break;
                    case "fMustAdjustToSettle":
                        result.fMustAdjustToSettle = a.Value == "" ? false : Int32.Parse(a.Value) == 0 ? false : true;
                        break;
                    case "fPrintUnadjustedTips":
                        result.fPrintUnadjustedTips = a.Value == "" ? false : Int32.Parse(a.Value) == 0 ? false : true;
                        break;
                    case "fPrintDetailedReport":
                        result.fPrintDetailedReport = a.Value == "" ? false : Int32.Parse(a.Value) == 0 ? false : true;
                        break;
                    case "fPrintSummaryReport":
                        result.fPrintSummaryReport = a.Value == "" ? false : Int32.Parse(a.Value) == 0 ? false : true;
                        break;
                    case "fDeleteBatchPasscode":
                        result.fDeleteBatchPasscode = a.Value == "" ? false : Int32.Parse(a.Value) == 0 ? false : true;
                        break;
                    case "fPrintSettlement":
                        result.fPrintSettlement = a.Value == "" ? false : Int32.Parse(a.Value) == 0 ? false : true;
                        break;
                    case "fConfirmSettlement":
                        result.fConfirmSettlement = a.Value == "" ? false : Int32.Parse(a.Value) == 0 ? false : true;
                        break;
                }
                if (updateItemName.StartsWith(ApiLabelXmlConstants.Times))
                {
                    switch (updateItemName)
                    {
                        case ApiLabelXmlConstants.Times + "1":
                            result.Times1 = ParseMerchantProcessorSettleBatchTime(MoveToFirstChild(a));
                            break;
                        case ApiLabelXmlConstants.Times + "2":
                            result.Times2 = ParseMerchantProcessorSettleBatchTime(MoveToFirstChild(a));
                            break;
                        case ApiLabelXmlConstants.Times + "3":
                            result.Times3 = ParseMerchantProcessorSettleBatchTime(MoveToFirstChild(a));
                            break;
                        case ApiLabelXmlConstants.Times + "4":
                            result.Times4 = ParseMerchantProcessorSettleBatchTime(MoveToFirstChild(a));
                            break;
                        case ApiLabelXmlConstants.Times + "5":
                            result.Times5 = ParseMerchantProcessorSettleBatchTime(MoveToFirstChild(a));
                            break;
                        case ApiLabelXmlConstants.Times + "6":
                            result.Times6 = ParseMerchantProcessorSettleBatchTime(MoveToFirstChild(a));
                            break;
                    }
                }
                //end if current has elements
                if (Next(a) == null)
                {
                    lastNode = true;
                }
                if (!lastNode)
                {
                    a = Next(a);
                }
            } // end while
            return result;
        }

        private SettleBatchTime ParseMerchantProcessorSettleBatchTime(XElement a)
        {
            var lastNode = false;
            var result = new SettleBatchTime();
            while (!lastNode)
            {

                var updateItemName = a.Name.LocalName;
                switch (updateItemName)
                {
                    case ApiLabelXmlConstants.fAuto:
                        result.fAuto = a.Value == "" ? false : Int32.Parse(a.Value) == 0 ? false : true; ;
                        break;
                    case "time":
                        result.Time = (TimeApiModel)new TimeApiModel().ParseToObject(MoveToFirstChild(a));
                        break;
                }

                if (Next(a) == null)
                {
                    lastNode = true;
                }
                if (!lastNode)
                {
                    a = Next(a);
                }
            } // end while
            return result;
        }

        private SystemPosSaleTypeApi ParseSystemPosSaleType(XElement a)
        {
            var result = new SystemPosSaleTypeApi();
            var props = result.GetType().GetProperties();
            while (a != null)
            {
                foreach (var prop in props)
                {
                    if (prop.Name.Equals(a.Name.LocalName))
                    {
                        TypeConverter conv = TypeDescriptor.GetConverter(prop.PropertyType);
                        prop.SetValue(result, conv.ConvertFrom(a.Value));
                        break;
                    }
                }
                a = Next(a);
            }
            return result;
        }

        private TSYSType ParseTsysType(XElement a)
        {
            var result = new TSYSType();
            var props = result.GetType().GetProperties();
            while (a != null)
            {
                foreach (var prop in props)
                {
                    if (prop.Name.Equals(a.Name.LocalName))
                    {
                        TypeConverter conv = TypeDescriptor.GetConverter(prop.PropertyType);
                        prop.SetValue(result, conv.ConvertFrom(a.Value));
                        break;
                    }
                }
                a = Next(a);
            }
            return result;
        }

        private LibrarySetting ParseOtherProcessorType(XElement a)
        {
            var result = new LibrarySetting();
            var props = result.GetType().GetProperties();
            while (a != null)
            {
                foreach (var prop in props)
                {
                    if (prop.Name.Equals(a.Name.LocalName))
                    {
                        TypeConverter conv = TypeDescriptor.GetConverter(prop.PropertyType);
                        prop.SetValue(result, conv.ConvertFrom(a.Value));
                        break;
                    }
                }
                a = Next(a);
            }
            return result;
        }

        private void CreateStatusResponse(XmlWriter writer, int cloudId, int clientId, string label, int status)
        {
            writer.WriteStartElement(label);
            //Cloud Id
            writer.WriteStartElement("CloudId");
            writer.WriteString(cloudId.ToString());
            writer.WriteEndElement();
            //Client Id
            writer.WriteStartElement("ClientId");
            writer.WriteString(clientId.ToString());
            writer.WriteEndElement();
            //Date
            writer.WriteStartElement("Date");
            writer.WriteString(DateTime.Now.ToString(Constants.FORMAT_MM_DD_YYYY));
            writer.WriteEndElement();
            //Time
            writer.WriteStartElement("Time");
            writer.WriteString(DateTime.Now.ToString(Constants.FORMAT_TIME_HH_MM));
            writer.WriteEndElement();
            //IsSuccess
            writer.WriteStartElement("IsSuccess");
            writer.WriteString(status.ToString());
            writer.WriteEndElement();

            writer.WriteEndElement();
        }

        private void TestResult(XmlWriter writer, string label)
        {
            writer.WriteStartElement(label);
            //Cloud Id
            writer.WriteStartElement("Run");
            writer.WriteString(label);
            writer.WriteEndElement();

            writer.WriteEndElement();
        }

        private MessageMerchant ParsePrintMessageMerchant(XElement currentNode, MessageMerchant printMessage)
        {
            if (currentNode == null)
            {
                return printMessage;
            }
            var lastNode = false;
            while (!lastNode)
            {
                if (!currentNode.HasElements)
                // if current hasn't element it is Logo
                {
                    //update message print 
                    //this is a file So waiting client. Update Later
                }
                else
                {
                    var localName = currentNode.Name.LocalName;
                    switch (localName)
                    {
                        case ApiLabelXmlConstants.PrintMessagesHeaderLine + "1":
                            var header1 = new PrinterMessagesLine().ParseToObject(MoveToFirstChild(currentNode));
                            var header1Server = header1.GetEntityMaster(printMessage.HeaderLine1);
                            printMessage.HeaderLine1 = (LineMessageMerchant)header1Server;
                            break;
                        case ApiLabelXmlConstants.PrintMessagesHeaderLine + "2":
                            var header2 = new PrinterMessagesLine().ParseToObject(MoveToFirstChild(currentNode));
                            var header2Server = header2.GetEntityMaster(printMessage.HeaderLine2);
                            printMessage.HeaderLine2 = (LineMessageMerchant)header2Server;
                            break;
                        case ApiLabelXmlConstants.PrintMessagesHeaderLine + "3":
                            var header3 = new PrinterMessagesLine().ParseToObject(MoveToFirstChild(currentNode));
                            var header3Server = header3.GetEntityMaster(printMessage.HeaderLine3);
                            printMessage.HeaderLine3 = (LineMessageMerchant)header3Server;
                            break;
                        case ApiLabelXmlConstants.PrintMessagesHeaderLine + "4":
                            var header4 = new PrinterMessagesLine().ParseToObject(MoveToFirstChild(currentNode));
                            var header4Server = header4.GetEntityMaster(printMessage.HeaderLine4);
                            printMessage.HeaderLine4 = (LineMessageMerchant)header4Server;
                            break;
                        case ApiLabelXmlConstants.PrintMessagesHeaderLine + "5":
                            var header5 = new PrinterMessagesLine().ParseToObject(MoveToFirstChild(currentNode));
                            var header5Server = header5.GetEntityMaster(printMessage.HeaderLine5);
                            printMessage.HeaderLine5 = (LineMessageMerchant)header5Server;
                            break;
                        case ApiLabelXmlConstants.PrintMessagesHeaderLine + "6":
                            var header6 = new PrinterMessagesLine().ParseToObject(MoveToFirstChild(currentNode));
                            var header6Server = header6.GetEntityMaster(printMessage.HeaderLine6);
                            printMessage.HeaderLine6 = (LineMessageMerchant)header6Server;
                            break;
                        case ApiLabelXmlConstants.PrintMessagesFooterLine + "1":
                            var footer1 = new PrinterMessagesLine().ParseToObject(MoveToFirstChild(currentNode));
                            var footer1Server = footer1.GetEntityMaster(printMessage.FooterLine1);
                            printMessage.FooterLine1 = (LineMessageMerchant)footer1Server;
                            break;
                        case ApiLabelXmlConstants.PrintMessagesFooterLine + "2":
                            var footer2 = new PrinterMessagesLine().ParseToObject(MoveToFirstChild(currentNode));
                            var footer2Server = footer2.GetEntityMaster(printMessage.FooterLine2);
                            printMessage.FooterLine2 = (LineMessageMerchant)footer2Server;
                            break;
                        case ApiLabelXmlConstants.PrintMessagesFooterLine + "3":
                            var footer3 = new PrinterMessagesLine().ParseToObject(MoveToFirstChild(currentNode));
                            var footer3Server = footer3.GetEntityMaster(printMessage.FooterLine3);
                            printMessage.FooterLine3 = (LineMessageMerchant)footer3Server;
                            break;
                        case ApiLabelXmlConstants.PrintMessagesFooterLine + "4":
                            var footer4 = new PrinterMessagesLine().ParseToObject(MoveToFirstChild(currentNode));
                            var footer4Server = footer4.GetEntityMaster(printMessage.FooterLine4);
                            printMessage.FooterLine4 = (LineMessageMerchant)footer4Server;
                            break;
                        case ApiLabelXmlConstants.PrintMessagesFooterLine + "5":
                            var footer5 = new PrinterMessagesLine().ParseToObject(MoveToFirstChild(currentNode));
                            var footer5Server = footer5.GetEntityMaster(printMessage.FooterLine5);
                            printMessage.FooterLine5 = (LineMessageMerchant)footer5Server;
                            break;
                        case ApiLabelXmlConstants.PrintMessagesFooterLine + "6":
                            var footer6 = new PrinterMessagesLine().ParseToObject(MoveToFirstChild(currentNode));
                            var footer6Server = footer6.GetEntityMaster(printMessage.FooterLine6);
                            printMessage.FooterLine6 = (LineMessageMerchant)footer6Server;
                            break;
                        case ApiLabelXmlConstants.PrintMessagesReturnLine + "1":
                            var return1 = new PrinterMessagesLine().ParseToObject(MoveToFirstChild(currentNode));
                            var return1Server = return1.GetEntityMaster(printMessage.ReturnLine1);
                            printMessage.ReturnLine1 = (LineMessageMerchant)return1Server;
                            break;
                        case ApiLabelXmlConstants.PrintMessagesReturnLine + "2":
                            var return2 = new PrinterMessagesLine().ParseToObject(MoveToFirstChild(currentNode));
                            var return2Server = return2.GetEntityMaster(printMessage.ReturnLine2);
                            printMessage.ReturnLine2 = (LineMessageMerchant)return2Server;
                            break;
                        case ApiLabelXmlConstants.PrintMessagesReturnLine + "3":
                            var return3 = new PrinterMessagesLine().ParseToObject(MoveToFirstChild(currentNode));
                            var return3Server = return3.GetEntityMaster(printMessage.ReturnLine3);
                            printMessage.ReturnLine3 = (LineMessageMerchant)return3Server;
                            break;
                        case ApiLabelXmlConstants.PrintMessagesReturnLine + "4":
                            var return4 = new PrinterMessagesLine().ParseToObject(MoveToFirstChild(currentNode));
                            var return4Server = return4.GetEntityMaster(printMessage.ReturnLine4);
                            printMessage.ReturnLine4 = (LineMessageMerchant)return4Server;
                            break;
                        case ApiLabelXmlConstants.PrintMessagesReturnLine + "5":
                            var return5 = new PrinterMessagesLine().ParseToObject(MoveToFirstChild(currentNode));
                            var return5Server = return5.GetEntityMaster(printMessage.ReturnLine5);
                            printMessage.ReturnLine5 = (LineMessageMerchant)return5Server;
                            break;
                        case ApiLabelXmlConstants.PrintMessagesReturnLine + "6":
                            var return6 = new PrinterMessagesLine().ParseToObject(MoveToFirstChild(currentNode));
                            var return6Server = return6.GetEntityMaster(printMessage.ReturnLine6);
                            printMessage.ReturnLine6 = (LineMessageMerchant)return6Server;
                            break;
                    }
                }
                if (Next(currentNode) == null)
                {
                    lastNode = true;
                }
                currentNode = Next(currentNode);
            }
            return printMessage;
        }

        private MerchantMaster ParseMerchantMaster(XElement currentNode, MerchantMaster merchant)
        {
            var lastNode = false;
            while (!lastNode)
            {
                if (!currentNode.HasElements)
                {
                    //get value of merchant master is being Update
                    switch (currentNode.Name.LocalName)
                    {
                        case ApiLabelXmlConstants.MerchantName:
                            merchant.lszMerchantName = currentNode.Value;
                            break;
                        case ApiLabelXmlConstants.MerchantEmailAddress:
                            merchant.lszEmailAddress = currentNode.Value;
                            break;
                        case ApiLabelXmlConstants.MerchantPhone:
                            merchant.lszPhone = currentNode.Value;
                            break;
                        case ApiLabelXmlConstants.MerchantMobilePhone:
                            merchant.lszMobilePhone = currentNode.Value;
                            break;
                    }
                }
                if (Next(currentNode) == null || Next(currentNode).HasElements)
                {
                    lastNode = true;
                }
                currentNode = Next(currentNode);
            }
            return merchant;
        }
        private void ParseMerchant(XElement currentNode, MerchantTerminal merchant, XmlWriter writer, IList<ProcessorTerminal> processors, int terminalId, string action = Constants.ActionEdit)
        {
            var lastNode = false;
            var merchantMaster = merchant.MerchantMaster;
            while (!lastNode)
            {
                if (!currentNode.HasElements)
                {
                    //get value of merchant master is being Update

                    if (merchantMaster != null)
                    {
                        switch (currentNode.Name.LocalName)
                        {
                            case ApiLabelXmlConstants.MerchantName:
                                merchantMaster.lszMerchantName = currentNode.Value;
                                break;
                            case ApiLabelXmlConstants.MerchantEmailAddress:
                                merchantMaster.lszEmailAddress = currentNode.Value;
                                break;
                            case ApiLabelXmlConstants.MerchantPhone:
                                merchantMaster.lszPhone = currentNode.Value;
                                break;
                            case ApiLabelXmlConstants.MerchantMobilePhone:
                                merchantMaster.lszMobilePhone = currentNode.Value;
                                break;
                        }
                    }
                } //end if check current has elements
                else
                {
                    switch (currentNode.Name.LocalName)
                    {
                        case ApiLabelXmlConstants.OtherTenderTypes:
                            // add handle logic here when we need sync up
                            break;
                        case ApiLabelXmlConstants.TenderTypes:
                            // add handle logic here when we need sync up
                            break;
                        case ApiLabelXmlConstants.FunctionTypes:
                                // add handle logic here when we need sync up
                            break;
                        case ApiLabelXmlConstants.PrintMessages:
                            var printMessage = _messageMerchantService.GetMessageByMerchantId(merchant.Id, true);
                            printMessage = ParsePrintMessageMerchant(MoveToFirstChild(currentNode), printMessage);
                            _messageMerchantService.Update(printMessage);
                            break;
                        case ApiLabelXmlConstants.GiftTenderTypes:
                            // add handle logic here when we need sync up
                            break;
                        case ApiLabelXmlConstants.LoyaltyTenderTypes:
                            // add handle logic here when we need sync up
                            break;
                        case ApiLabelXmlConstants.Setup:
                            var setup = new SetupSystemMerchantApi().ParseToObject(MoveToFirstChild(currentNode));
                            if (merchant.Setups == null || !merchant.Setups.Any())
                            {
                                var server = new MerchantGeneralSetup()
                                {
                                    MerchantTerminalId = merchant.Id
                                };
                                server = (MerchantGeneralSetup)setup.GetEntityMaster(server);
                                _merchantGeneralSetupService.Insert(server);
                            }
                            else
                            {
                                var server = setup.GetEntityMaster(merchant.Setups.First());
                                _merchantGeneralSetupService.Update((MerchantGeneralSetup)server);
                            }
                            break;
                        case ApiLabelXmlConstants.SaleType:
                            var saleType = new DefaultSaleTypeTerminalApi().ParseToObject(MoveToFirstChild(currentNode));
                            if (merchant.SalesTypes == null || !merchant.SalesTypes.Any())
                            {
                                var saleTypes = new DefaultSaleTypeTerminal() { MerchantTerminalId = merchant.Id };
                                saleTypes = (DefaultSaleTypeTerminal)saleType.GetEntityMaster(saleTypes);
                                _defaultSaleTypeTerminalService.Insert(saleTypes);
                            }
                            else
                            {
                                var saleTypeServer = saleType.GetEntityMaster(merchant.SalesTypes.First());
                                _defaultSaleTypeTerminalService.Update((DefaultSaleTypeTerminal)saleTypeServer);
                            }
                            break;
                        case ApiLabelXmlConstants.StoreForward:
                            var storeForward = new StoreForwardTerminalApi().ParseToObject(MoveToFirstChild(currentNode));
                            if (merchant.StoreForwards == null || !merchant.StoreForwards.Any())
                            {
                                var tmp = new StoreForwardTerminal() { MerchantTerminalId = merchant.Id };
                                tmp = (StoreForwardTerminal)storeForward.GetEntityMaster(tmp);
                                _storeForwardTerminalService.Insert(tmp);
                            }
                            else
                            {
                                var store =
                                    (StoreForwardTerminal)storeForward.GetEntityMaster(merchant.StoreForwards.First());
                                _storeForwardTerminalService.Update(store);
                            }
                            break;
                        case ApiLabelXmlConstants.Security1:
                            var security1 =
                                (MerchantSecurity)new MerchantSecurity().ParseToObject(MoveToFirstChild(currentNode));
                            var securityServer1 = GetMerchantSecurityLevel(merchant.Id, Constants.NONE);
                            var security1Server =
                                security1.GetSecurityMaster(securityServer1);
                            _merchantSecurityLevelService.Update(security1Server);

                            break;
                        case ApiLabelXmlConstants.Security2:
                            var security2 =
                                (MerchantSecurity)new MerchantSecurity().ParseToObject(MoveToFirstChild(currentNode));
                            var securityServer2 = GetMerchantSecurityLevel(merchant.Id, Constants.NONE);
                            var security2Server =
                                security2.GetSecurityMaster(securityServer2);
                            _merchantSecurityLevelService.Update(security2Server);
                            break;
                        case ApiLabelXmlConstants.Security3:
                            var security3 =
                                (MerchantSecurity)new MerchantSecurity().ParseToObject(MoveToFirstChild(currentNode));
                            var securityServer3 = GetMerchantSecurityLevel(merchant.Id, Constants.NONE);
                            var security3Server =
                                security3.GetSecurityMaster(securityServer3);
                            _merchantSecurityLevelService.Update(security3Server);
                            break;
                        case ApiLabelXmlConstants.Security4:
                            var security4 =
                                (MerchantSecurity)new MerchantSecurity().ParseToObject(MoveToFirstChild(currentNode));
                            var securityServer4 = GetMerchantSecurityLevel(merchant.Id, Constants.NONE);
                            var security4Server =
                                security4.GetSecurityMaster(securityServer4);
                            _merchantSecurityLevelService.Update(security4Server);
                            break;
                        case ApiLabelXmlConstants.Tips:
                            var tip = _tipsTerminalService.FirstOrDefault(x=> x.MerchantTerminalId == merchant.Id);
                            var tipClient =
                                (TipsTerminalApi)new TipsTerminalApi().ParseToObject(MoveToFirstChild(currentNode));
                            if (tip == null)
                            {
                                tip = new TipsTerminal() { MerchantTerminalId = merchant.Id };
                                _tipsTerminalService.Insert(tip);
                            }
                            tip = (TipsTerminal)tipClient.GetEntityMaster(tip);
                            _tipsTerminalService.Update(tip);
                            break;
                        case ApiLabelXmlConstants.CardsAccepted:
                            var cardAccepted = (CardAcceptedApiModel)new CardAcceptedApiModel().ParseToObject(MoveToFirstChild(currentNode));
                            var credit = _cardAcceptTerminalService.FirstOrDefault( x=> x.MerchantTerminalId == merchant.Id &&
                                x.Type == Constants.TypeCredit && x.CardType == Constants.CreditAccepted);
                            var debit = _debitCardsAcceptedService.FirstOrDefault(x=> x.MerchantTerminalId == merchant.Id);
                            if (credit == null)
                            {
                                credit = new CardAcceptTerminal() { MerchantTerminalId = merchant.Id, CardType = Constants.CreditAccepted };
                                _cardAcceptTerminalService.Insert(credit);
                            }
                            if (debit == null)
                            {
                                debit = new DebitCardsAccepted() { MerchantTerminalId = merchant.Id };
                                _debitCardsAcceptedService.Insert(debit);
                            }
                            credit = cardAccepted.GetEntityMaster(credit);
                            _cardAcceptTerminalService.Update(credit);
                            debit = cardAccepted.GetEntityMaster(debit);
                            _debitCardsAcceptedService.Update(debit);
                            break;
                        case ApiLabelXmlConstants.CreditCards:
                            ParseCreditCards(MoveToFirstChild(currentNode), merchant);
                            break;
                        case ApiLabelXmlConstants.DebitCards:
                            ParseDebitCards(MoveToFirstChild(currentNode), merchant);
                            break;
                        case ApiLabelXmlConstants.GiftCards:
                            ParseGiftCards(MoveToFirstChild(currentNode), merchant);
                            break;
                        case ApiLabelXmlConstants.Checks:
                            ParseChecks(MoveToFirstChild(currentNode), merchant);
                            break;
                        case ApiLabelXmlConstants.AlertMerchant:
                            var alertobject = new AlertMerchantApi().ParseToObject(MoveToFirstChild(currentNode));

                            //load notification hard code
                            //****************************
                            var dropdowText = _hardCodeService.GetListHardCodeByCode("Notifications")
                                              .Select(x => new HardCodeModel
                                              {
                                                  Id = x.Id,
                                                  Code = x.Code,
                                                  Name = x.Name,
                                                  Value = x.Value
                                              }).ToList();

                            var alerts = ((AlertMerchantApi)alertobject).GetMaster(merchant, dropdowText);

                            var notificationExisted = _merchantAlertNotificationService.GetAlertNotificationByMerchantId(merchant.Id);

                            if (alerts != null)
                            {
                                foreach (var each in alerts)
                                {
                                    var check = _merchantAlertNotificationService.GetByMerchantIdAndHardCodeId(merchant.Id, each.HardcodeId);
                                    if (check != null)
                                    {
                                        switch (each.DataType)
                                        {
                                            case Constants.BOOLEAN_FIELD:
                                                if (each.Value == 0)
                                                {
                                                    _merchantAlertNotificationService.Delete(check.Id);
                                                }
                                                break;
                                            case Constants.ENTER_FIELD:
                                                //update
                                                check.Value = each.Value;
                                                _merchantAlertNotificationService.Update(check);
                                                break;
                                        }
                                    }
                                    else
                                    {
                                        //except bool type and value = 0
                                        if (each.DataType != Constants.BOOLEAN_FIELD || each.Value != 0)
                                        {
                                            _merchantAlertNotificationService.Insert(each);
                                        }
                                    }
                                }
                            }

                            break;
                    }
                }
                if (currentNode.NextNode == null)
                {
                    lastNode = true;
                }
                if (!lastNode)
                {
                    currentNode = Next(currentNode);
                }
            }
            if (action.Equals(Constants.ActionAdd))
            {
                //_terminalMasterService.Insert(merchant);
            }
            else
            {
                _merchantTerminalService.Update(merchant);
            }

        }

        public MerchantSecurityLevel GetMerchantSecurityLevel(int merchantId, int level)
        {
            var securities = _terminalMasterService.GetMerchantSecurityLevelByMerchantId(merchantId);
            if (!securities.Any())
            {
                var security1 = new MerchantSecurityLevel
                {
                    MerchantTerminalId = merchantId,
                    Level = Constants.NONE,
                };
                _merchantSecurityLevelService.Insert(security1);
                securities.Add(security1);

                var security2 = new MerchantSecurityLevel
                {
                    MerchantTerminalId = merchantId,
                    Level = Constants.SERVER,
                };
                _merchantSecurityLevelService.Insert(security2);
                securities.Add(security2);

                var security3 = new MerchantSecurityLevel
                {
                    MerchantTerminalId = merchantId,
                    Level = Constants.MANAGER,
                };
                securities.Add(security3);
                _merchantSecurityLevelService.Insert(security3);

                var security4 = new MerchantSecurityLevel
                {
                    MerchantTerminalId = merchantId,
                    Level = Constants.ADMIN,
                };
                _merchantSecurityLevelService.Insert(security4);
                securities.Add(security4);
            }
            return securities.SingleOrDefault(x => x.Level == level);
        }


        private void ParseCreditCards(XElement currentNode, MerchantTerminal merchant)
        {
            if (currentNode == null) return;
            var lastNode = false;
            while (!lastNode)
            {
                //check if It Has Element
                if (currentNode.HasElements)
                {
                    var updateItemName = currentNode.Name.LocalName;
                    switch (updateItemName)
                    {
                        case ApiLabelXmlConstants.Limits:
                            var client = new CardLimitsApiModel().ParseToObject(MoveToFirstChild(currentNode));
                            var creditLimit =
                                _cardLimitService.FirstOrDefault(x=> x.LimitType == Constants.TypeCredit && x.MerchantTerminalId == merchant.Id);
                            if (creditLimit == null)
                            {
                                creditLimit = new CardLimit() { MerchantTerminalId = merchant.Id };
                                _cardLimitService.Insert(creditLimit);
                            }
                            creditLimit = (CardLimit)client.GetEntityMaster(creditLimit);
                            _cardLimitService.Update(creditLimit);
                            break;
                        case ApiLabelXmlConstants.Manual:
                            var manual =
                                (CardManualApiModel)
                                    new CardManualApiModel().ParseToObject(MoveToFirstChild(currentNode));
                            var server = _manualCardTerminalService.FirstOrDefault(x => x.MerchantTerminalId == merchant.Id && x.Type == Constants.TypeCredit);
                            if (server == null)
                            {
                                server = new ManualCardTerminal() { MerchantTerminalId = merchant.Id };
                                _manualCardTerminalService.Insert(server);
                            }
                            server = manual.GetEntityMaster(server);
                            _manualCardTerminalService.Update(server);
                            break;
                    }
                    if (updateItemName.StartsWith(ApiLabelXmlConstants.CreditCard))
                    {
                        var card = -1;
                        switch (updateItemName)
                        {
                            case ApiLabelXmlConstants.CreditCard + "1":
                                card = Constants.ApiCreditCard1;
                                break;
                            case ApiLabelXmlConstants.CreditCard + "2":
                                card = Constants.ApiCreditCard2;
                                break;
                            case ApiLabelXmlConstants.CreditCard + "3":
                                card = Constants.ApiCreditCard3;
                                break;
                            case ApiLabelXmlConstants.CreditCard + "4":
                                card = Constants.ApiCreditCard4;
                                break;
                            case ApiLabelXmlConstants.CreditCard + "5":
                                card = Constants.ApiCreditCard5;
                                break;
                            case ApiLabelXmlConstants.CreditCard + "6":
                                card = Constants.ApiCreditCard6;
                                break;
                            case ApiLabelXmlConstants.CreditCard + "7":
                                card = Constants.ApiCreditCard7;
                                break;
                        }
                        ParseCreditCard(MoveToFirstChild(currentNode), merchant, card);
                    }
                } //end if current has elements
                if (Next(currentNode) == null)
                {
                    lastNode = true;
                }
                if (!lastNode)
                {
                    currentNode = Next(currentNode);
                }
            } // end while
        }
        private void ParseDebitCards(XElement currentNode, MerchantTerminal merchant)
        {
            if (currentNode == null) return;
            var lastNode = false;
            while (!lastNode)
            {
                //check if It Has Element
                if (currentNode.HasElements)
                {
                    var updateItemName = currentNode.Name.LocalName;
                    switch (updateItemName)
                    {
                        case ApiLabelXmlConstants.Limits:
                            var client = new CardLimitsApiModel().ParseToObject(MoveToFirstChild(currentNode));
                            var creditLimit =
                                _cardLimitService.FirstOrDefault( x=> x.LimitType == Constants.TypeDebit && x.MerchantTerminalId == merchant.Id);
                            if (creditLimit == null)
                            {
                                creditLimit = new CardLimit()
                                {
                                    MerchantTerminalId = merchant.Id,
                                    LimitType = Constants.TypeDebit,
                                };
                                _cardLimitService.Insert(creditLimit);
                            }
                            creditLimit = (CardLimit)client.GetEntityMaster(creditLimit);
                            _cardLimitService.Update(creditLimit);
                            break;

                    }
                    if (updateItemName.StartsWith(ApiLabelXmlConstants.DebitCard))
                    {
                        var card = -1;
                        switch (updateItemName)
                        {
                            case ApiLabelXmlConstants.DebitCard + "1":
                                card = Constants.ApiDebitCard1;
                                break;
                            case ApiLabelXmlConstants.DebitCard + "2":
                                card = Constants.ApiDebitCard2;
                                break;
                            case ApiLabelXmlConstants.DebitCard + "3":
                                card = Constants.ApiDebitCard3;
                                break;
                            case ApiLabelXmlConstants.DebitCard + "4":
                                card = Constants.ApiDebitCard4;
                                break;
                        }
                        ParseDebitCard(MoveToFirstChild(currentNode), merchant, card);
                    }
                } //end if current has elements
                if (Next(currentNode) == null)
                {
                    lastNode = true;
                }
                if (!lastNode)
                {
                    currentNode = Next(currentNode);
                }
            } // end while
        }
        private void ParseDebitCard(XElement currentNode, MerchantTerminal merchant, int card)
        {
            if (currentNode == null) return;
            var lastNode = false;
            while (!lastNode)
            {
                //check if It Has Element
                if (currentNode.HasElements)
                {
                    var updateItemName = currentNode.Name.LocalName;
                    switch (updateItemName)
                    {
                        case ApiLabelXmlConstants.Surcharge:
                            var client =
                                (CardSurchargeApiModel)
                                    new CardSurchargeApiModel().ParseToObject(MoveToFirstChild(currentNode));
                            var surcharge =
                                _surchargeTerminalService.FirstOrDefault( x=> x.MerchantTerminalId == merchant.Id && 
                                x.SurchargeType == Constants.TypeDebit && x.CardType == card);
                            if (surcharge == null)
                            {
                                surcharge = new SurchargeTerminal()
                                {
                                    MerchantTerminalId = merchant.Id,
                                    CardType = card,
                                    SurchargeType = Constants.TypeDebit
                                };
                                _surchargeTerminalService.Insert(surcharge);
                            }
                            surcharge = client.GetEntityMaster(surcharge);
                            _surchargeTerminalService.Update(surcharge);
                            break;
                        case ApiLabelXmlConstants.CashOut:
                            var cashClient =
                                (CardCashOutApiModel)
                                    new CardCashOutApiModel().ParseToObject(MoveToFirstChild(currentNode));
                            var cash = _creditCashOutTerminalService.FirstOrDefault( x=> x.MerchantTerminalId == merchant.Id &&
                            x.Type == Constants.TypeCashBack && x.CardType == card);
                            if (cash == null)
                            {
                                cash = new CreditCashOutTerminal()
                                {
                                    MerchantTerminalId = merchant.Id,
                                    CardType = card,
                                    Type = Constants.TypeCashBack,
                                };
                                _creditCashOutTerminalService.Insert(cash);
                            }
                            cash = cashClient.GetEntityMaster(cash);
                            _creditCashOutTerminalService.Update(cash);
                            break;

                    }
                } //end if current has elements
                if (Next(currentNode) == null)
                {
                    lastNode = true;
                }
                if (!lastNode)
                {
                    currentNode = Next(currentNode);
                }
            } // end while
        }
        private void ParseCreditCard(XElement currentNode, MerchantTerminal merchant, int card)
        {
            if (currentNode == null) return;
            var lastNode = false;
            while (!lastNode)
            {
                //check if It Has Element
                if (currentNode.HasElements)
                {
                    var updateItemName = currentNode.Name.LocalName;
                    switch (updateItemName)
                    {
                        case ApiLabelXmlConstants.Surcharge:
                            var client =
                                (CardSurchargeApiModel)
                                    new CardSurchargeApiModel().ParseToObject(MoveToFirstChild(currentNode));
                            var surcharge =
                                 _surchargeTerminalService.FirstOrDefault(x => x.MerchantTerminalId == merchant.Id &&
                               x.SurchargeType == Constants.TypeDebit && x.CardType == card);
                            if (surcharge == null)
                            {
                                surcharge = new SurchargeTerminal()
                                {
                                    MerchantTerminalId = merchant.Id,
                                    CardType = card,
                                    SurchargeType = Constants.TypeCredit
                                };
                                _surchargeTerminalService.Insert(surcharge);
                            }
                            surcharge = client.GetEntityMaster(surcharge);
                            _surchargeTerminalService.Update(surcharge);
                            break;
                        case ApiLabelXmlConstants.CashOut:
                            var cashClient =
                                (CardCashOutApiModel)
                                    new CardCashOutApiModel().ParseToObject(MoveToFirstChild(currentNode));
                            var cash =
                                _creditCashOutTerminalService.FirstOrDefault(x => x.MerchantTerminalId == merchant.Id &&
                               x.Type == Constants.TypeCashOver && x.CardType == card);
                            if (cash == null)
                            {
                                cash = new CreditCashOutTerminal()
                                {
                                    MerchantTerminalId = merchant.Id,
                                    CardType = card,
                                    Type = Constants.TypeCashOver,
                                };
                                _creditCashOutTerminalService.Insert(cash);
                            }
                            cash = cashClient.GetEntityMaster(cash);
                            _creditCashOutTerminalService.Update(cash);
                            break;
                        case ApiLabelXmlConstants.BankCashOut:
                            var bankCash = _creditCashOutTerminalService.FirstOrDefault(x => x.MerchantTerminalId == merchant.Id &&
                               x.Type == Constants.TypeCashAdvance && x.CardType == card); 

                            var bankCashClient = (CardCashOutApiModel)
                                    new CardCashOutApiModel().ParseToObject(MoveToFirstChild(currentNode));
                            if (bankCash == null)
                            {
                                bankCash = new CreditCashOutTerminal()
                                {
                                    MerchantTerminalId = merchant.Id,
                                    CardType = card,
                                    Type = Constants.TypeCashAdvance,
                                };
                                _creditCashOutTerminalService.Insert(bankCash);
                            }
                            bankCash = bankCashClient.GetEntityMaster(bankCash);
                            _creditCashOutTerminalService.Update(bankCash);
                            break;
                        case ApiLabelXmlConstants.DCC:
                            //call Parse Dcc
                            ParseDccCard(MoveToFirstChild(currentNode), merchant, card);
                            break;
                        case ApiLabelXmlConstants.SaleID:
                            var saleId = _creditCashOutTerminalService.FirstOrDefault(x => x.MerchantTerminalId == merchant.Id &&
                               x.Type == Constants.TypeSale && x.CardType == card);

                            var saleClient =
                                (CardSaleIdApiModel)
                                    new CardSaleIdApiModel().ParseToObject(MoveToFirstChild(currentNode));
                            if (saleId == null)
                            {
                                saleId = new CreditCashOutTerminal()
                                {
                                    MerchantTerminalId = merchant.Id,
                                    CardType = card,
                                    Type = Constants.TypeSale,
                                };
                                _creditCashOutTerminalService.Insert(saleId);
                            }
                            saleId = saleClient.GetEntityMaster(saleId);
                            _creditCashOutTerminalService.Update(saleId);
                            break;
                    }
                } //end if current has elements
                if (Next(currentNode) == null)
                {
                    lastNode = true;
                }
                if (!lastNode)
                {
                    currentNode = Next(currentNode);
                }
            } // end while
        }
        private void ParseDccCard(XElement currentNode, MerchantTerminal merchant, int card)
        {
            var lastNode = false;
            var dccCard = _dccCardService.FirstOrDefault(x=> x.MerchantTerminalId == merchant.Id && x.CardName == card);
            if (dccCard == null)
            {
                dccCard = new DccCard()
                {
                    MerchantTerminalId = merchant.Id,
                    CardName = card,
                    Type = Constants.TypeDcc,
                };
                _dccCardService.Insert(dccCard);
            }
            while (!lastNode)
            {
                //check if It Has Element
                if (currentNode.HasElements)
                {
                    var updateItemName = currentNode.Name.LocalName;
                    switch (updateItemName)
                    {
                        case ApiLabelXmlConstants.Surcharge:
                            var client =
                                (CardSurchargeApiModel)
                                    new CardSurchargeApiModel().ParseToObject(MoveToFirstChild(currentNode));
                            var surcharge = _surchargeTerminalService.FirstOrDefault(x => x.MerchantTerminalId == merchant.Id &&
                               x.SurchargeType == Constants.TypeDcc && x.CardType == card);
                            if (surcharge == null)
                            {
                                surcharge = new SurchargeTerminal()
                                {
                                    MerchantTerminalId = merchant.Id,
                                    CardType = card,
                                    SurchargeType = Constants.TypeDcc
                                };
                                _surchargeTerminalService.Insert(surcharge);
                            }
                            surcharge = client.GetEntityMaster(surcharge);
                            _surchargeTerminalService.Update(surcharge);
                            break;
                        case ApiLabelXmlConstants.SaleID:
                            var saleId = _creditCashOutTerminalService.FirstOrDefault(x => x.MerchantTerminalId == merchant.Id &&
                               x.Type == Constants.TypeSaleDcc && x.CardType == card);

                            var saleClient =
                                (CardSaleIdApiModel)
                                    new CardSaleIdApiModel().ParseToObject(MoveToFirstChild(currentNode));
                            if (saleId == null)
                            {
                                saleId = new CreditCashOutTerminal()
                                {
                                    MerchantTerminalId = merchant.Id,
                                    CardType = card,
                                    Type = Constants.TypeSaleDcc,
                                };
                                _creditCashOutTerminalService.Insert(saleId);
                            }
                            saleId = saleClient.GetEntityMaster(saleId);
                            _creditCashOutTerminalService.Update(saleId);
                            break;
                    }
                } //end if current has elements
                else
                {
                    var updateItemName = currentNode.Name.LocalName;
                    switch (updateItemName)
                    {
                        case "fEnabled":
                            dccCard.fEnable = currentNode.Value == "1";
                            break;
                        case "fRateApproval":
                            dccCard.fRateApproval = currentNode.Value == "1";
                            break;
                        case "fPrintQuote":
                            dccCard.fPrintQuote = currentNode.Value == "1";
                            break;
                        case "IdDCCProcessor":
                            dccCard.DccProcessorInterface = currentNode.Value;
                            var processorId = string.IsNullOrWhiteSpace(currentNode.Value)
                                ? 0
                                : Int32.Parse(currentNode.Value);
                            //update processor interface. 
                            var processor = _processorTerminalService.GetProcessorTerminals()
                                 .Where(
                                     x =>
                                         x.TerminalId == merchant.TerminalId &&
                                         x.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD)
                                 .SingleOrDefault(x => x.ClientId == processorId);
                            if (processor != null)
                            {
                                dccCard.DccProcessorId = processor.Id;
                            }
                            break;
                    }
                }
                if (Next(currentNode) == null)
                {
                    lastNode = true;
                }
                if (!lastNode)
                {
                    currentNode = Next(currentNode);
                }
            } // end while
            _dccCardService.Update(dccCard);
        }
        private void ParseChecks(XElement currentNode, MerchantTerminal merchant)
        {
            if (currentNode == null) return;
            var lastNode = false;
            while (!lastNode)
            {
                //check if It Has Element
                if (currentNode.HasElements)
                {
                    var updateItemName = currentNode.Name.LocalName;
                    switch (updateItemName)
                    {
                        case ApiLabelXmlConstants.Limits:
                            var client = new CardLimitsApiModel().ParseToObject(MoveToFirstChild(currentNode));
                            var giftLimit =
                                _cardLimitService.FirstOrDefault( x=> x.LimitType == Constants.TypeCheck && x.MerchantTerminalId == merchant.Id);
                            if (giftLimit == null)
                            {
                                giftLimit = new CardLimit()
                                {
                                    MerchantTerminalId = merchant.Id,
                                    LimitType = Constants.TypeCheck,
                                };
                                _cardLimitService.Insert(giftLimit);
                            }
                            giftLimit = (CardLimit)client.GetEntityMaster(giftLimit);
                            _cardLimitService.Update(giftLimit);
                            break;

                        case ApiLabelXmlConstants.Surcharge:
                            var surchargeClient =
                                (CardSurchargeApiModel)
                                    new CardSurchargeApiModel().ParseToObject(MoveToFirstChild(currentNode));
                            var surcharge = _surchargeTerminalService.FirstOrDefault(x => x.MerchantTerminalId == merchant.Id &&
                               x.SurchargeType == Constants.TypeCheck && x.CardType == 0);

                            if (surcharge == null)
                            {
                                surcharge = new SurchargeTerminal()
                                {
                                    MerchantTerminalId = merchant.Id,
                                    SurchargeType = Constants.TypeCheck
                                };
                                _surchargeTerminalService.Insert(surcharge);
                            }
                            surcharge = surchargeClient.GetEntityMaster(surcharge);
                            _surchargeTerminalService.Update(surcharge);
                            break;

                        case ApiLabelXmlConstants.CashOut:
                            var cashClient =
                                (CardCashOutApiModel)
                                    new CardCashOutApiModel().ParseToObject(MoveToFirstChild(currentNode));
                            var cash = _creditCashOutTerminalService.FirstOrDefault(x => x.MerchantTerminalId == merchant.Id &&
                               x.Type == Constants.TypeCashAdvance && x.CardType == Constants.TypeCheck);
                            if (cash == null)
                            {
                                cash = new CreditCashOutTerminal()
                                {
                                    MerchantTerminalId = merchant.Id,
                                    Type = Constants.TypeCashAdvance,
                                    CardType = Constants.TypeCheck
                                };
                                _creditCashOutTerminalService.Insert(cash);
                            }
                            cash = cashClient.GetEntityMaster(cash);
                            _creditCashOutTerminalService.Update(cash);
                            break;

                        case ApiLabelXmlConstants.SaleID:
                            var saleId = _creditCashOutTerminalService.FirstOrDefault(x => x.MerchantTerminalId == merchant.Id &&
                               x.Type == Constants.TypeCheckSaleId && x.CardType == 0);
                            var saleClient =
                                (CardSaleIdApiModel)
                                    new CardSaleIdApiModel().ParseToObject(MoveToFirstChild(currentNode));
                            if (saleId == null)
                            {
                                saleId = new CreditCashOutTerminal()
                                {
                                    MerchantTerminalId = merchant.Id,
                                    Type = Constants.TypeCheckSaleId,
                                };
                                _creditCashOutTerminalService.Insert(saleId);
                            }
                            saleId = saleClient.GetEntityMaster(saleId);
                            _creditCashOutTerminalService.Update(saleId);
                            break;
                    }

                } //end if current has elements
                if (Next(currentNode) == null)
                {
                    lastNode = true;
                }
                if (!lastNode)
                {
                    currentNode = Next(currentNode);
                }
            } // end while
        }
        private void ParseGiftCards(XElement currentNode, MerchantTerminal merchant)
        {
            if (currentNode == null) return;
            var lastNode = false;
            while (!lastNode)
            {
                //check if It Has Element
                if (currentNode.HasElements)
                {
                    var updateItemName = currentNode.Name.LocalName;
                    switch (updateItemName)
                    {
                        case ApiLabelXmlConstants.Limits:
                            var client = new CardLimitsApiModel().ParseToObject(MoveToFirstChild(currentNode));
                            var giftLimit = _cardLimitService.FirstOrDefault(x => x.MerchantTerminalId == merchant.Id &&
                               x.LimitType == Constants.TypeGift);

                            if (giftLimit == null)
                            {
                                giftLimit = new CardLimit()
                                {
                                    MerchantTerminalId = merchant.Id,
                                    LimitType = Constants.TypeGift,
                                };
                                _cardLimitService.Insert(giftLimit);
                            }
                            giftLimit = (CardLimit)client.GetEntityMaster(giftLimit);
                            _cardLimitService.Update(giftLimit);
                            break;
                        case ApiLabelXmlConstants.Manual:
                            var manual =
                                (CardManualApiModel)
                                    new CardManualApiModel().ParseToObject(MoveToFirstChild(currentNode));
                            var server = _manualCardTerminalService.FirstOrDefault(x=> x.MerchantTerminalId == merchant.Id && x.Type == Constants.TypeGift);
                            if (server == null)
                            {
                                server = new ManualCardTerminal()
                                {
                                    MerchantTerminalId = merchant.Id,
                                    Type = Constants.TypeGift,
                                };
                                _manualCardTerminalService.Insert(server);
                            }
                            server = manual.GetEntityMaster(server);
                            _manualCardTerminalService.Update(server);
                            break;
                        case ApiLabelXmlConstants.Surcharge:
                            var surchargeClient =
                                (CardSurchargeApiModel)
                                    new CardSurchargeApiModel().ParseToObject(MoveToFirstChild(currentNode));
                            var surcharge =
                                _surchargeTerminalService.FirstOrDefault(x=> x.MerchantTerminalId == merchant.Id &&
                                    x.SurchargeType == Constants.TypeGift && x.CardType == 0);
                            if (surcharge == null)
                            {
                                surcharge = new SurchargeTerminal()
                                {
                                    MerchantTerminalId = merchant.Id,
                                    SurchargeType = Constants.TypeGift
                                };
                                _surchargeTerminalService.Insert(surcharge);
                            }
                            surcharge = surchargeClient.GetEntityMaster(surcharge);
                            _surchargeTerminalService.Update(surcharge);
                            break;
                        case ApiLabelXmlConstants.CashOut:
                            var cashClient =
                                (CardCashOutApiModel)
                                    new CardCashOutApiModel().ParseToObject(MoveToFirstChild(currentNode));
                            var cash = _creditCashOutTerminalService.FirstOrDefault(x => x.MerchantTerminalId == merchant.Id &&
                                    x.Type == Constants.TypeGiftCashAdvance && x.CardType == 0);

                            if (cash == null)
                            {
                                cash = new CreditCashOutTerminal()
                                {
                                    MerchantTerminalId = merchant.Id,
                                    Type = Constants.TypeGiftCashAdvance,
                                };
                                _creditCashOutTerminalService.Insert(cash);
                            }
                            cash = cashClient.GetEntityMaster(cash);
                            _creditCashOutTerminalService.Update(cash);
                            break;
                    }

                } //end if current has elements
                if (Next(currentNode) == null)
                {
                    lastNode = true;
                }
                if (!lastNode)
                {
                    currentNode = Next(currentNode);
                }
            } // end while
        }
        private void ParseSystem(XElement currentNode, TerminalMaster terminalUpdated, bool defaultValue)
        {
            if (currentNode == null) return;
            var lastNode = false;
            while (!lastNode)
            {
                //check if It Has Element
                if (currentNode.HasElements)
                {
                    var updateItemName = currentNode.Name.LocalName;
                    switch (updateItemName)
                    {
                        case ApiLabelXmlConstants.Setup:
                            var systemClient =
                                (SetupSystemTerminalApi)
                                    new SetupSystemTerminalApi(defaultValue).ParseToObject(MoveToFirstChild(currentNode));
                            var terminalSetup = _terminalMasterService.GetTerminalSetupByTerminalId(terminalUpdated.Id);
                            var terminalLang = _terminalMasterService.GetLanguageByByTerminalId(terminalUpdated.Id);
                            if (terminalLang == null)
                            {
                                terminalLang = new LanguageBy();
                                _languageByService.Insert(terminalLang);
                            }
                            if (terminalSetup == null)
                            {
                                terminalSetup = new TerminalSetup();
                                _terminalSetupService.Insert(terminalSetup);
                            }
                            terminalSetup = systemClient.GetEntityMaster(terminalSetup);
                            _terminalSetupService.Update(terminalSetup);
                            terminalLang = systemClient.GetEntityMaster(terminalLang);
                            _languageByService.Update(terminalLang);
                            break;
                        case ApiLabelXmlConstants.Date:
                            var date = new DateTimeZoneTerminalApi(defaultValue).ParseToObject(MoveToFirstChild(currentNode));
                            var dateServer = date.GetEntityMaster(terminalUpdated.Date);
                            _dateTimeZoneTerminalService.Update((DateTimeZoneTerminal)dateServer);
                            break;
                        case ApiLabelXmlConstants.Sound:
                            var sound = new SoundTerminalApi(true).ParseToObject(MoveToFirstChild(currentNode));
                            var soundServer = sound.GetEntityMaster(terminalUpdated.Sound);
                            _soundTerminalService.Update((SoundTerminal)soundServer);
                            break;
                        case ApiLabelXmlConstants.Security1:
                            var security1 = (Security)new Security(defaultValue).ParseToObject(MoveToFirstChild(currentNode));
                            var security1Server = terminalUpdated.TerminalSecurityAccesses.SingleOrDefault(
                                x => x.Level == Constants.NONE);
                            security1Server = security1.GetEntityMaster(security1Server);
                            _terminalUserSecurityAccessService.Update(security1Server);
                            break;
                        case ApiLabelXmlConstants.Security2:
                            var security2 = (Security)new Security(defaultValue).ParseToObject(MoveToFirstChild(currentNode));
                            var security2Server =
                                security2.GetEntityMaster(
                                    terminalUpdated.TerminalSecurityAccesses.Single(
                                        x => x.Level == Constants.SERVER));
                            _terminalUserSecurityAccessService.Update(security2Server);
                            break;
                        case ApiLabelXmlConstants.Security3:
                            var security3 = (Security)new Security(defaultValue).ParseToObject(MoveToFirstChild(currentNode));

                            var security3Server =
                                security3.GetEntityMaster(
                                    terminalUpdated.TerminalSecurityAccesses.Single(
                                        x => x.Level == Constants.MANAGER));
                            _terminalUserSecurityAccessService.Update(security3Server);
                            break;
                        case ApiLabelXmlConstants.Security4:
                            var security4 = (Security)new Security(defaultValue).ParseToObject(MoveToFirstChild(currentNode));
                            var security4Server =
                                security4.GetEntityMaster(
                                    terminalUpdated.TerminalSecurityAccesses.Single(
                                        x => x.Level == Constants.ADMIN));
                            _terminalUserSecurityAccessService.Update(security4Server);
                            break;
                        case ApiLabelXmlConstants.PosRequests:
                            var posrquest = new PosRequestTerminalApi().ParseToObject(MoveToFirstChild(currentNode));
                            var posrquestServer = posrquest.GetEntityMaster(terminalUpdated.PosRequest);
                            _posRequestTerminalService.Update((PosRequestTerminal)posrquestServer);
                            break;
                        default:
                            break;
                    }
                } //end if current has elements
                if (Next(currentNode) == null)
                {
                    lastNode = true;
                }
                if (!lastNode)
                {
                    currentNode = Next(currentNode);
                }
            } // end while
        }

        #endregion

        #region XML from Object Model

        private void CreateUserXml(UserTerminal user, XmlWriter writer)
        {
            writer.WriteStartElement(ApiLabelXmlConstants.User);
            WriteXmlAttr(writer, ApiLabelXmlConstants.iLanguage, user.iLanguage.ToString("D"));
            //WriteXmlAttr(writer, ApiLabelXmlConstants.usAutoLogOff, user.usAutoLogOff.ToString("F"));
            //WriteXmlAttr(writer, ApiLabelXmlConstants.usVoids, user.usVoids.ToString("F"));
            //WriteXmlAttr(writer, ApiLabelXmlConstants.usRefunds, user.usRefunds.ToString("F"));
            //WriteXmlAttr(writer, ApiLabelXmlConstants.usGiftReloads, user.usGiftReloads.ToString("F"));
            //WriteXmlAttr(writer, ApiLabelXmlConstants.lszEmailAddress, user.lszEmailAddress);
            //WriteXmlAttr(writer, ApiLabelXmlConstants.lszMobilePhone, user.lszMobilePhone);
            //WriteXmlAttr(writer, ApiLabelXmlConstants.fTipSelection,
            //    WebUtils.ConvertBoolToInt(user.fTipSelection).ToString("D"));
            //WriteXmlAttr(writer, ApiLabelXmlConstants.fAddUsertoTip,
            //    WebUtils.ConvertBoolToInt(user.fAddUsertoTip).ToString("D"));
            //WriteXmlAttr(writer, ApiLabelXmlConstants.fManualTimeID,
            //    WebUtils.ConvertBoolToInt(user.fManualTimeID).ToString("D"));
            //WriteXmlAttr(writer, ApiLabelXmlConstants.fCloseBatch,
            //    WebUtils.ConvertBoolToInt(user.fCloseBatch).ToString("D"));
            //WriteXmlAttr(writer, ApiLabelXmlConstants.fVoidsAndReturns,
            //    WebUtils.ConvertBoolToInt(user.fVoidsAndReturns).ToString("D"));
            //WriteXmlAttr(writer, ApiLabelXmlConstants.fCancelTrans,
            //    WebUtils.ConvertBoolToInt(user.fCancelTrans).ToString("D"));
            //WriteXmlAttr(writer, ApiLabelXmlConstants.fOfflineTrans,
            //    WebUtils.ConvertBoolToInt(user.fOfflineTrans).ToString("D"));
            WriteXmlAttr(writer, ApiLabelXmlConstants.fTrainingUsed,
                Utils.WebUtils.ConvertBoolToInt(user.fTrainingUsed).ToString("D"));
            //WriteXmlAttr(writer, ApiLabelXmlConstants.fSetupAccessed,
            //    WebUtils.ConvertBoolToInt(user.fSetupAccessed).ToString("D"));
            //WriteXmlAttr(writer, ApiLabelXmlConstants.fTextAlerts,
            //    WebUtils.ConvertBoolToInt(user.fTextAlerts).ToString("D"));
            //WriteXmlAttr(writer, ApiLabelXmlConstants.fTextBatchClose,
            //    WebUtils.ConvertBoolToInt(user.fTextBatchClose).ToString("D"));
            //WriteXmlAttr(writer, ApiLabelXmlConstants.fEmailReports,
            //    WebUtils.ConvertBoolToInt(user.fEmailReports).ToString("D"));
            //WriteXmlAttr(writer, ApiLabelXmlConstants.fTextReports,
            //    WebUtils.ConvertBoolToInt(user.fTextReports).ToString("D"));
            //WriteXmlAttr(writer, ApiLabelXmlConstants.fBatchSettle,
            //    WebUtils.ConvertBoolToInt(user.fBatchSettle).ToString("D"));
            //WriteXmlAttr(writer, ApiLabelXmlConstants.fGiftReloads,
            //    WebUtils.ConvertBoolToInt(user.fGiftReloads).ToString("D"));
            //WriteXmlAttr(writer, ApiLabelXmlConstants.fTipsByEmployee,
            //    WebUtils.ConvertBoolToInt(user.fTipsByEmployee).ToString("D"));
            //WriteXmlAttr(writer, ApiLabelXmlConstants.fSalesByEmployee,
            //    WebUtils.ConvertBoolToInt(user.fSalesByEmployee).ToString("D"));
            //WriteXmlAttr(writer, ApiLabelXmlConstants.fSetupChanges,
            //    WebUtils.ConvertBoolToInt(user.fSetupChanges).ToString("D"));
            //WriteXmlAttr(writer, ApiLabelXmlConstants.fUserChange,
            //    WebUtils.ConvertBoolToInt(user.fUserChange).ToString("D"));
            //WriteXmlAttr(writer, ApiLabelXmlConstants.fEmailAlerts,
            //    WebUtils.ConvertBoolToInt(user.fEmailAlerts).ToString("D"));
            //WriteXmlAttr(writer, ApiLabelXmlConstants.fSecurityChange,
            //    WebUtils.ConvertBoolToInt(user.fSecurityChange).ToString("D"));
            //if (user.Merchant != null)  //Todo Fix later
            //{
            //    if (user.Merchant.ClientId != 0)
            //    {
            //        WriteXmlAttr(writer, ApiLabelXmlConstants.IdMerchant, user.Merchant.ClientId.ToString("D"));
            //    }
            //}

            WriteXmlAttr(writer, ApiLabelXmlConstants.lszCardNumber, user.lszCardNumber);

            writer.WriteEndElement();
        }

        private void CreateMerchantProcessorXml(ProcessorMerchant master, XmlWriter writer)
        {
            writer.WriteStartElement(ApiLabelXmlConstants.MerchantProcessor);

            WriteXmlAttr(writer, ApiLabelXmlConstants.IdProcessor, master.ProcessorTerminal.ClientId.ToString("D"));
            WriteXmlAttr(writer, ApiLabelXmlConstants.IdMerchant, master.MerchantTerminal.ClientId.ToString("D"));
            if (master.ProcessorTerminal.Proccessor.Library != null)
            {
                if (master.ProcessorTerminal.Proccessor.Library.Value == Constants.TSYSValue)
                {
                    if (master.ProcessorMerchantSetup != null)
                    {
                        writer.WriteStartElement(ApiLabelXmlConstants.TSYS);
                        WriteXmlAttr(writer, ApiLabelXmlConstants.wszAcquirerBin,
                            master.ProcessorMerchantSetup.AcquirerBin);
                        WriteXmlAttr(writer, ApiLabelXmlConstants.wszStoreNumber,
                            master.ProcessorMerchantSetup.StoreNumber.ToString("D"));
                        WriteXmlAttr(writer, ApiLabelXmlConstants.wszCategoryCode,
                            master.ProcessorMerchantSetup.CategoryCode);
                        WriteXmlAttr(writer, ApiLabelXmlConstants.wsAgentBankNumber,
                            master.ProcessorMerchantSetup.AgentBankNumber);
                        WriteXmlAttr(writer, ApiLabelXmlConstants.wsAgentChainNumber,
                            master.ProcessorMerchantSetup.AgentChainNumber);
                        WriteXmlAttr(writer, ApiLabelXmlConstants.wsSharingGroup,
                            master.ProcessorMerchantSetup.SharingGroup);
                        WriteXmlAttr(writer, ApiLabelXmlConstants.wsABANumber, master.ProcessorMerchantSetup.ABANumber);
                        WriteXmlAttr(writer, ApiLabelXmlConstants.wsSettlementAgentNumber,
                            master.ProcessorMerchantSetup.SettlementAgentNumber);
                        WriteXmlAttr(writer, ApiLabelXmlConstants.wsSettlementAgentNumber,
                            master.ProcessorMerchantSetup.SettlementAgentNumber);
                        WriteXmlAttr(writer, ApiLabelXmlConstants.wsReinbursementAtribute,
                            master.ProcessorMerchantSetup.ReinbursementAtribute);
                        //WriteXmlAttr(writer, ApiLabelXmlConstants.wsReceivingInstitutionIdentification,
                        //    master.ProcessorMerchantSetup.ReceivingInstitutionID);
                        writer.WriteEndElement();
                    }
                }
            }

            if (master.ProcessorMerchantSettleBatchSetup != null)
            {
                writer.WriteStartElement(ApiLabelXmlConstants.SettleBatch);

                //WriteXmlAttr(writer, ApiLabelXmlConstants.fPrint,
                //    WebUtils.ConvertBoolToInt(master.ProcessorMerchantSettleBatchSetup.fPrint).ToString("D"));


                WriteXmlAttr(writer, ApiLabelXmlConstants.MaxBatches,
                    master.ProcessorMerchantSettleBatchSetup.MaxBatches.ToString("D"));
                //if (master.ProcessorMerchantSettleBatchSetup.BatchCutOffTime != null)
                //{
                //    WriteXmlAttr(writer, ApiLabelXmlConstants.MaxBatches,
                //        master.ProcessorMerchantSettleBatchSetup.BatchCutOffTime.Value.ToString("HHmmss"));
                //}
                if (master.ProcessorMerchantSettleBatchSetup.TimeLine1 != null)
                {
                    writer.WriteStartElement(ApiLabelXmlConstants.Times + "1");
                    WriteXmlAttr(writer, ApiLabelXmlConstants.fAuto,
                        Utils.WebUtils.ConvertBoolToInt(master.ProcessorMerchantSettleBatchSetup.fTimeLine1)
                            .ToString("D"));
                    WriteXmlAttr(writer, ApiLabelXmlConstants.Hours,
                        master.ProcessorMerchantSettleBatchSetup.TimeLine1.Value.ToString("HH"));
                    WriteXmlAttr(writer, ApiLabelXmlConstants.Minutes,
                        master.ProcessorMerchantSettleBatchSetup.TimeLine1.Value.ToString("mm"));
                    WriteXmlAttr(writer, ApiLabelXmlConstants.Seconds,
                        master.ProcessorMerchantSettleBatchSetup.TimeLine1.Value.ToString("ss"));

                    writer.WriteEndElement();
                }
                if (master.ProcessorMerchantSettleBatchSetup.TimeLine2 != null)
                {
                    writer.WriteStartElement(ApiLabelXmlConstants.Times + "2");
                    WriteXmlAttr(writer, ApiLabelXmlConstants.fAuto,
                        Utils.WebUtils.ConvertBoolToInt(master.ProcessorMerchantSettleBatchSetup.fTimeLine2)
                            .ToString("D"));
                    WriteXmlAttr(writer, ApiLabelXmlConstants.Hours,
                        master.ProcessorMerchantSettleBatchSetup.TimeLine2.Value.ToString("HH"));
                    WriteXmlAttr(writer, ApiLabelXmlConstants.Minutes,
                        master.ProcessorMerchantSettleBatchSetup.TimeLine2.Value.ToString("mm"));
                    WriteXmlAttr(writer, ApiLabelXmlConstants.Seconds,
                        master.ProcessorMerchantSettleBatchSetup.TimeLine2.Value.ToString("ss"));

                    writer.WriteEndElement();
                }
                if (master.ProcessorMerchantSettleBatchSetup.TimeLine3 != null)
                {
                    writer.WriteStartElement(ApiLabelXmlConstants.Times + "3");
                    WriteXmlAttr(writer, ApiLabelXmlConstants.fAuto,
                        Utils.WebUtils.ConvertBoolToInt(master.ProcessorMerchantSettleBatchSetup.fTimeLine3)
                            .ToString("D"));
                    WriteXmlAttr(writer, ApiLabelXmlConstants.Hours,
                        master.ProcessorMerchantSettleBatchSetup.TimeLine3.Value.ToString("HH"));
                    WriteXmlAttr(writer, ApiLabelXmlConstants.Minutes,
                        master.ProcessorMerchantSettleBatchSetup.TimeLine3.Value.ToString("mm"));
                    WriteXmlAttr(writer, ApiLabelXmlConstants.Seconds,
                        master.ProcessorMerchantSettleBatchSetup.TimeLine3.Value.ToString("ss"));

                    writer.WriteEndElement();
                }
                if (master.ProcessorMerchantSettleBatchSetup.TimeLine4 != null)
                {
                    writer.WriteStartElement(ApiLabelXmlConstants.Times + "4");
                    WriteXmlAttr(writer, ApiLabelXmlConstants.fAuto,
                        Utils.WebUtils.ConvertBoolToInt(master.ProcessorMerchantSettleBatchSetup.fTimeLine4)
                            .ToString("D"));
                    WriteXmlAttr(writer, ApiLabelXmlConstants.Hours,
                        master.ProcessorMerchantSettleBatchSetup.TimeLine4.Value.ToString("HH"));
                    WriteXmlAttr(writer, ApiLabelXmlConstants.Minutes,
                        master.ProcessorMerchantSettleBatchSetup.TimeLine4.Value.ToString("mm"));
                    WriteXmlAttr(writer, ApiLabelXmlConstants.Seconds,
                        master.ProcessorMerchantSettleBatchSetup.TimeLine4.Value.ToString("ss"));

                    writer.WriteEndElement();
                }
                if (master.ProcessorMerchantSettleBatchSetup.TimeLine5 != null)
                {
                    writer.WriteStartElement(ApiLabelXmlConstants.Times + "5");
                    WriteXmlAttr(writer, ApiLabelXmlConstants.fAuto,
                        Utils.WebUtils.ConvertBoolToInt(master.ProcessorMerchantSettleBatchSetup.fTimeLine5)
                            .ToString("D"));
                    WriteXmlAttr(writer, ApiLabelXmlConstants.Hours,
                        master.ProcessorMerchantSettleBatchSetup.TimeLine5.Value.ToString("HH"));
                    WriteXmlAttr(writer, ApiLabelXmlConstants.Minutes,
                        master.ProcessorMerchantSettleBatchSetup.TimeLine5.Value.ToString("mm"));
                    WriteXmlAttr(writer, ApiLabelXmlConstants.Seconds,
                        master.ProcessorMerchantSettleBatchSetup.TimeLine5.Value.ToString("ss"));

                    writer.WriteEndElement();
                }
                if (master.ProcessorMerchantSettleBatchSetup.TimeLine6 != null)
                {
                    writer.WriteStartElement(ApiLabelXmlConstants.Times + "6");
                    WriteXmlAttr(writer, ApiLabelXmlConstants.fAuto,
                        Utils.WebUtils.ConvertBoolToInt(master.ProcessorMerchantSettleBatchSetup.fTimeLine6)
                            .ToString("D"));
                    WriteXmlAttr(writer, ApiLabelXmlConstants.Hours,
                        master.ProcessorMerchantSettleBatchSetup.TimeLine6.Value.ToString("HH"));
                    WriteXmlAttr(writer, ApiLabelXmlConstants.Minutes,
                        master.ProcessorMerchantSettleBatchSetup.TimeLine6.Value.ToString("mm"));
                    WriteXmlAttr(writer, ApiLabelXmlConstants.Seconds,
                        master.ProcessorMerchantSettleBatchSetup.TimeLine6.Value.ToString("ss"));

                    writer.WriteEndElement();
                }

                writer.WriteEndElement();//end Settle Batch
            }

            writer.WriteEndElement();
        }

        #endregion

        #region  Login Api
        [Route("api/login")]
        [HttpPost]
        public ActionResult<ResponseMessage> Login([FromBody] LoginApiModel model)
        {
            try
            {
                if (!string.IsNullOrEmpty(model.Username) && !string.IsNullOrEmpty(model.Passcode))
                {
                    var user = _userMasterService.GetAll(includeDeactivated: true).Where(m => m.CloudUserName == model.Username)?.FirstOrDefault();
                    if (user != null)
                    {
                        if (!string.IsNullOrEmpty(user.CloudPasscode) && Utils.WebUtils.IsMatchHash(user.CloudPasscode, model.Passcode))
                        {
                            return GetResponseMessageSuccess(
                                new ResponseLoginApiModel()
                                {
                                    Id = user.Id,
                                    FirstName = user.FirstName,
                                    MiddleName = user.MiddleName,
                                    SurName = user.SurName
                                });
                        }
                    }
                }
                return GetResponseMessageError(HttpStatusCode.Unauthorized);
            }
            catch
            {
                return GetResponseMessageError(HttpStatusCode.InternalServerError);
            }
        }
        #endregion

        private string[] GetChangeCodeByXml(XElement e)
        {
            var code = "";
            var value = "0";
            var isSystem = false;
            while (true)
            {
                if (e.HasElements)
                {
                    if (!e.Name.LocalName.Equals("ResponseStatus") && !isSystem)
                    {
                        if (e.Name.LocalName.Equals(ApiLabelXmlConstants.System))
                        {
                            isSystem = true;
                        }
                        code += e.Name.LocalName + ".";
                        e = (XElement)e.FirstNode;
                    }
                    else
                    {
                        e = (XElement)e.FirstNode;
                    }
                }
                else
                {
                    if (e.Name.LocalName.Equals(ApiLabelXmlConstants.CloudId))
                    {
                        if (!isSystem)
                        {
                            if (e.Value != "0")
                            {
                                code += e.Value;
                            }
                        }
                    }
                    if (e.Name.LocalName.Equals("isSuccess"))
                    {
                        value = e.Value;
                        break;
                    }
                    if (e.NextNode != null)
                    {
                        e = Next(e);
                    }
                    else
                    {
                        break;
                    }
                }
                if (e == null)
                {
                    return null;
                }
            }
            return new string[] { code, value };
        }

        private Resync GetStringContent(TerminalMaster terminal)
        {
            var responseModel = new Resync();

            var merchants = _merchantTerminalService.GetMerchantByTerminal(terminal.Id, true);

            var resyncOther = true;
            responseModel.MerchantProcessors = null;
            responseModel.Users = null;

            if (merchants != null && merchants.Any())
            {
                resyncOther = false;
                // reponseModel.
                var merchantList = new List<Merchant>();

                foreach (var each in merchants)
                {
                    if (each.ClientId == 0)
                    {
                        var alert = _merchantAlertNotificationService.GetAlertNotificationByMerchantId(each.Id);
                        resyncOther = false;
                        merchantList.Add(new Merchant(each,
                        _processorTerminalService.GetProcessorTerminals(x => x.TerminalId == terminal.Id), alert));
                    }
                }
                if (merchantList.Any())
                {
                    responseModel.Merchants = merchantList;
                    return responseModel;
                }
            }
            return null;
        }

        [Route("api/devicehelthcheck")]
        [HttpPost]
        public ActionResult<ResponseMessage> ReceiveHelthcheckDevice(string terminalSerialNumber, string terminalMacAddress, int terminalModelId, string data, string type = "json")
        {
            var terminal = _terminalMasterService.GetTerminalBySerialNumber(terminalSerialNumber,
                terminalMacAddress, terminalModelId);

            if (terminal == null)
            {
                return GetResponseMessageSuccess(ResponseNo);
            }

            var listInsert = new List<DeviceHealthCheck>();
            var listUpdate = new List<DeviceHealthCheck>();
            var deviceHealthChecks = new List<DeviceHealthCheck>();
            var entities = _deviceHealthCheckService.GetAll(p => p.TerminaMasterlId == terminal.Id);

            if (type == "json")
            {
                deviceHealthChecks = JsonConvert.DeserializeObject<List<DeviceHealthCheck>>(data);
            }

            if (type == "xml")
            {
                XDocument doc = new XDocument();
                if (!string.IsNullOrEmpty(data))
                {
                    doc = XDocument.Parse(data);
                }
                //Check if xml has any elements 
                if (!string.IsNullOrEmpty(data) && doc.Root.Elements().Any())
                {
                    deviceHealthChecks = doc.Descendants("HealthCheck").Select(d =>
                    new DeviceHealthCheck
                    {
                        Name = d.Element("Name").Value,
                        Value = d.Element("Value").Value,
                        GroupType = (d.Element("GroupType").Value).ToEnum<GroupHealthCheckType>(),
                        Status = bool.Parse(d.Element("Status").Value),
                        Version = d.Element("Version").Value,
                    }).ToList();
                }
            }

            if (deviceHealthChecks != null)
            {
                foreach (var item in deviceHealthChecks)
                {
                    var entitty = entities.FirstOrDefault(p => p.Name == item.Name);
                    if (entitty != null)
                    {
                        entitty.SetValueEntity(item);
                        listUpdate.Add(entitty);
                    }
                    else
                    {
                        item.TerminaMasterlId = terminal.Id;
                        listInsert.Add(item);
                    }

                }
            }

            _deviceHealthCheckService.InsertAll(listInsert);

            _deviceHealthCheckService.UpdateAll(listUpdate);

            return GetResponseMessageSuccess(ResponseYes);
        }


        [Route("api/log-upload/{name?}")]
        [HttpPost]
        public async Task<ActionResult<ResponseMessage>> LogUpload([FromForm] LogUploadModel logUploadModel, [FromForm] IFormFile logFile, string? name = null)
        {
            if (logUploadModel == null) return GetResponseMessageError(HttpStatusCode.BadRequest, "Terminal's Data is null");

            var terminal = _terminalMasterService.GetTerminalBySerialNumber(logUploadModel.TerminalSerialNumber, logUploadModel.TerminalMacAddress, logUploadModel.TerminalModelId);
            if (terminal == null) return GetResponseMessageError(HttpStatusCode.BadRequest, "Terminal's Data invalid");

            if (logFile == null) return GetResponseMessageError(HttpStatusCode.BadRequest, "Log File is null");

            if (!string.IsNullOrWhiteSpace(logUploadModel.FileHashed))
            {
                using (var memoryStream = new MemoryStream())
                {
                    await logFile.CopyToAsync(memoryStream);
                    var logFileByteArr = memoryStream.ToArray();
                    var logFileHex = MMS.Core.CoreUTI.CoreUtils.ToHexadecimalString(logFileByteArr);

                    var fileOk = MMS.Core.CoreUTI.CoreUtils.VerifyHashSHA256(logFileHex, logUploadModel.FileHashed);
                    if (!fileOk) return GetResponseMessageError(HttpStatusCode.BadRequest, "File Log isvalid");
                }
            }

            WebUtils.SaveFileAt(Constants.PATH_LogFile, logFile, newName: logFile.FileName);
            return GetResponseMessageSuccess("Uploaded");
        }

        [Route("api/log-upload-simple/{name?}")]
        [HttpPost]
        public async Task<ActionResult<ResponseMessage>> LogUpload([FromForm] IFormFile logFile, string? name = null)
        {
            WebUtils.SaveFileAt(Constants.PATH_LogFile, logFile, newName: logFile.FileName);
            return GetResponseMessageSuccess("Uploaded");
        }

        [Route("api/log-upload-json/{name?}")]
        [HttpPost]
        public async Task<ActionResult<ResponseMessage>> LogUpload(string name = null)
        {
            //WebUtils.SaveFileAt(Constants.PATH_LogFile, logFile, newName: logFile.FileName);
            return GetResponseMessageSuccess("Uploaded");
        }

        [Route("api/setup-upgrade/status")]
        [HttpPost]
        public async Task<ActionResult<ResponseMessage>> DownloadStatus([FromBody] SetupUpgradeStatusApiModel model)
        {
            var setupUpgrade = await _deviceSetupUpgradeService.GetLastVersionByTerminalSerialNumber(model.SerialNumber, model.AppId);
            if (setupUpgrade == null || !setupUpgrade.VersionName.Equals(model.VersionName))
            {
                return GetResponseMessageError(HttpStatusCode.BadRequest, "Version Name is invalid!");
            }

            if (!Enum.TryParse<ClientSetupUpgradeStatus>(model.Status, out var status))
            {
                return GetResponseMessageError(HttpStatusCode.BadRequest, "Status is invalid!");
            }

            bool isValid;

            switch (status)
            {
                case ClientSetupUpgradeStatus.Received:
                    isValid = setupUpgrade.Status == DeviceSetupUpgradeStatuses.Sent;
                    setupUpgrade.Status = DeviceSetupUpgradeStatuses.Delivered;
                    break;
                
                case ClientSetupUpgradeStatus.Downloaded:
                    isValid = setupUpgrade.Status == DeviceSetupUpgradeStatuses.Delivered;
                    setupUpgrade.Status = DeviceSetupUpgradeStatuses.Downloaded;
                    break;

                case ClientSetupUpgradeStatus.DownloadFailed:
                    isValid = setupUpgrade.Status == DeviceSetupUpgradeStatuses.Delivered;
                    setupUpgrade.Status = DeviceSetupUpgradeStatuses.DownloadFailed;
                    break;

                case ClientSetupUpgradeStatus.Upgraded:
                    isValid = setupUpgrade.Status == DeviceSetupUpgradeStatuses.Downloaded && setupUpgrade.DateUpgrade < DateTime.UtcNow;
                    setupUpgrade.Status = DeviceSetupUpgradeStatuses.Upgraded;
                    break;

                case ClientSetupUpgradeStatus.UpgradeFailed:
                    isValid = setupUpgrade.Status == DeviceSetupUpgradeStatuses.Downloaded && setupUpgrade.DateUpgrade < DateTime.UtcNow;
                    setupUpgrade.Status = DeviceSetupUpgradeStatuses.UpgradeFailed;
                    break;

                default:
                    isValid = false;
                    break;
            }

            if (!isValid) return GetResponseMessageError(HttpStatusCode.BadRequest, "Status is not valid with flow!");

            if (status == ClientSetupUpgradeStatus.Received)
                BackgroundJob.Delete(setupUpgrade.CheckStatusJobId);

            await _deviceSetupUpgradeService.UpdateWithRealTimeAsync(setupUpgrade);

            return GetResponseMessageSuccess("Update status successfully!");
        }
    }
}
