import { CompanyItemType } from "../constants/app.enums";
import { ApiBaseModel } from "./common.model";

export interface TreeNode {
  title: React.ReactNode;
  key: string;
  icon: React.ReactNode | null;
  children?: TreeNode[];
  isLeaf?: boolean;
  hasChild: boolean
  item: CompanyItem;
}
  
export interface CompanyItem {
  orgId: number;
  label: string;
  hasChild: boolean;
  itemType: CompanyItemType;
  parentId: number | null;
  id: number;
  created: string;
  modified: string;
  isStatus: number;
  isActive: boolean;
  child: CompanyItem[];
}
  
export interface CompanyResponse {
  parentId: number;
  items: CompanyItem[];
}

export interface CompanyApiModel extends ApiBaseModel {
  name: string;
}

export interface CompanyGroupResponse {
  id: number;
  name: string;
}

export interface CompanyGroupRequest {
  name: string;
  parentId?: number | null;
}