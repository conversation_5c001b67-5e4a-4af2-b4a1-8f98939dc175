﻿var scopeJsTerminalSetupGUIListGlobalHostInterfaces = function (scopeId) {

    let $scope = terminalGlobalJs.getScope(scopeId);


    let toEdit = function () {
        var hostInterfaceId = $(this).closest(".terminal-list-item").find("input[type='hidden']").val();
        var terminalMasterId = $scope.find("#teminalMasterId").val();
        var params = {
            "terminalMasterId": terminalMasterId,
            "hostInterfaceId": hostInterfaceId
        }
        const url = "/TerminalSetupGUI/EditHostInterface?" + $.param(params);

        terminalGlobalJs.terLoadForm(this, url);
        return false;
    };

    let toAdd = function () {
        var terminalMasterId = $scope.find("#teminalMasterId").val();
        const url = "/TerminalSetupGUI/SelectGlobalHostInterfaces?terminalMasterId=" + terminalMasterId;

        terminalGlobalJs.terLoadForm(this, url);
        return false;
    };

    return {
        toAdd: toAdd,
        toEdit: toEdit
    };
};

$(document).ready(function () {
    let scopeId = "HostInterfacesList";
    let scopeJs = scopeJsTerminalSetupGUIListGlobalHostInterfaces(scopeId);

    terminalGlobalJs.bindTerminalRemoveEventScope(scopeId, ".terminal-icon-close", "/TerminalSetupGUI/DeleteHostInterface");

    terminalGlobalJs.partialViewNormalEventBindScope(scopeId, "click", ".toEditAction", scopeJs.toEdit);

    terminalGlobalJs.partialViewNormalEventBindScope(scopeId, "click", ".terminal-add-area", scopeJs.toAdd);
});
