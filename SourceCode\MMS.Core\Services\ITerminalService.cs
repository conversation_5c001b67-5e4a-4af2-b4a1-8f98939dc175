using MMS.Core.CoreUTI.Enum;
using MMS.Core.Entities;
using MMS.Core.Entities.Merchants;
using MMS.Core.Services.Base;
using MMS.Infrastructure.Commons;
using MMS.Model.ApiModelRequest;
using MMS.Model.ApiModelResponse;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace MMS.Core.Services
{
    public interface ITerminalService : IBaseService<TerminalMaster>
    {
        IList<TerminalMaster> GetAllTerminalMaster(Expression<Func<TerminalMaster, bool>> whereClauses = null, bool includeDeleted = false, bool includeDeactivated = true, Expression<Func<TerminalMaster, object>> orderBy = null, bool isDescendingOrder = false, params Expression<Func<TerminalMaster, object>>[] includes);
        //==================================================================
        // Terminal
        //==================================================================
        //void SetContext(Context context);
        new ITerminalService UsingSessionContext();
        void Insert(TerminalChange entity, int id, bool commit = true);
        void Update(TerminalChange entity);
        void UndoAll();

        /// <summary>
        /// Synchronizes terminal data from Linkly to the MMS database
        /// </summary>
        /// <param name="linklyTerminals">List of terminals from Linkly API</param>
        /// <param name="companyId">Company ID to associate with the terminals</param>
        /// <returns>Summary of sync operation (inserted, updated, deleted counts)</returns>
        Task<(int inserted, int updated, int deleted)> SyncTerminalsFromLinklyAsync(List<MMS.Model.LinklyFake.LinklyTerminalDto> linklyTerminals, int companyId);
        void SaveChanges();
        int LastMaster();
        TerminalMaster GetTerminalByTemplateId(int templateId);
        TerminalMaster GetTerminalByTerminalId(int terminalId);
        IList<TerminalMaster> GetAllTerminals();
        IList<TerminalMaster> GetAllTerminalQueryable();
        IList<TerminalMaster> GetAllTerminalsByApplicationTemplateId(int id);
        IList<TerminalMaster> GetAllTerminalsByApplicationTemplate(int id);
        IList<TerminalMaster> GetAllTerminalsByFirmwareTemplateId(int id);
        IList<TerminalMaster> GetAllTerminalsByFirmwareTemplate(int id);
        IList<TerminalMaster> GetAllTerminalsByUpdateTemplateId(int id);
        List<int> GetAllTerminalsIdByUpdateTemplateId(int id);
        TerminalMaster GetTerminalBySerialNumber(string serialNumber, string macAddress, int modelId);

        /// <summary>
        /// Support API api/synchronize/resyncfile/prepare without MacAddress and TerminalModelId
        /// </summary>
        /// <param name="serialNumber"></param>
        /// <returns></returns>
        TerminalMaster GetTerminalBySerialNumber(string serialNumber);

        TerminalMaster GetTerminalBySerialNumberAndBrand(string serialNumber, int brandId);
        IList<TerminalMaster> GetTerminalsByApplicationId(int id);
        IList<TerminalMaster> GetTerminalsByParameterLocationId(int id);
        bool SetSetupTemplate(int templateId, List<int> terminalids);

        bool SetIsUpdate(string table, List<int> ids, bool isUpdated);

        IList<MerchantTerminalHistory> GetMerchantTerminalHistories(int terminalId);
        //==================================================================
        // Configuration for terminal
        //==================================================================

        //support
        SupportTerminal GetSupportByTerminalId(int id);
        List<int> GetAllSupportIdByTemplateId(int id);

        //Date Time Zone
        DateTimeZoneTerminal GetDateTimeZoneByTerminalId(int id);
        List<int> GetAllDateTimeZoneIdByTemplateId(int id);

        //Country
        CountryTerminal GetCountryByTerminalId(int id);


        //Pos Request
        PosRequestTerminal GetPosRequestByTerminalId(int id);
        List<int> GetAllPosRequestIdByTemplateId(int id);

        //PayAtTable
        PayAtTableTerminal GetPayAtTableByPosRequestId(int id);

        //Sound
        SoundTerminal GetSoundByTerminalId(int id);
        List<int> GetAllSoundTerminalIdByTemplateId(int id);

        //Fallback
        FallBackTerminal GetFallBackByTerminalId(int id);
        List<int> GetAllFallBackIdByTemplateId(int id);

        //Debit Cash Back
        List<int> GetAllDeditCardBackIdByTemplateId(int id);

        //Terminal Backup
        //TerminalBackup GetTerminalBackupById(int id);

        //User security Level
        IList<TerminalUserSecurityAccess> GetTerminalSecurityAccessByTerminalId(int id);

        //Application Upgrade Terminal
        ApplicationVersion GetApplicationVersionByModelAndVersionNumber(int modelId, string versionNumber);

        //Firmware Upgrade Terminal
        FirmwareVersion GetFirmwareVersionByModelAndVersionNumber(int modelId, string versionNumber);

        //Setup Update for terminal
        TerminalSetupUpdate GetTerminalSetupUpdateByTerminalId(int id, out int isAssign);
        List<int> GetAllTerminalSetupUpdateByTemplateId(int id);
        TrainingTerminal GetTrainingByTerminalId(int id);
        AutoLoginTerminal GetAutoLoginByTerminalId(int id);
        IList<TerminalChange> GetTerminalChanges(int terminalId, int status);
        bool SetIsStatus(string table, List<int> ids, int isStatus);
        bool SetIsStatus(string table, string whereStatus, int isStatus);

        /// <summary>
        /// Update IsStatus for api sync up restore
        /// </summary>
        /// <param name="table"></param>
        /// <param name="whereStatus"></param>
        /// <param name="isStatus"></param>
        /// <param name="columnName"></param>
        /// <returns></returns>
        Task<bool> SetIsStatusWithColumnGroupAsync(string table, string whereStatus, int isStatus, string columnName);
        bool SetIsClientId(string table, List<int> ids, int clientId);

        IList<MerchantSecurityLevel> GetMerchantSecurityLevelByMerchantId(int id);

        TerminalCloud GetTerminalCloudByTerminalId(int id);

        TerminalSetup GetTerminalSetupByTerminalId(int id);

        LanguageBy GetLanguageByByTerminalId(int id);
        IList<TerminalUpdateSchedule> GetTerminalUpdateSchedulesByTerminalId(int id, int status);
        TerminalMaster GetTerminalDownload(string serialNumber, string macAddress, string ipAddress);

        PaymentMode PaymentModeOptionGet(int terminalMasterId, IList<MerchantTerminal> merchantTerminals);

        TerminalMaster GetMerchantCardFallback(int terminalMasterId);

        bool PaymentModeOptionUpdate(PaymentModeOption entity);

        bool SoundTerminalUpdate(SoundTerminal entity);
        bool SoundTerminalInsert(int terminalMasterId, SoundTerminal entity);
        bool TerminalSetupUpdate(TerminalSetup entity);
        bool TerminalSetupInsert(int terminalMasterId, TerminalSetup entity);
        bool TerminalCloudUpdate(TerminalCloud entity);
        bool TerminalCloudInsert(int terminalMasterId, TerminalCloud entity);
        bool CountryTerminalUpdate(CountryTerminal entity);
        bool CountryTerminalInsert(int terminalMasterId, CountryTerminal entity);
        bool UserMasterUpdate(UserMaster entity);
        bool FallBackTerminalUpdate(FallBackTerminal entity);
        bool FallBackTerminalInsert(int terminalMasterId, FallBackTerminal entity);
        bool ExtraModudleUpdate(ExtraModudle entity);
        bool ExtraModudleInsert(int terminalMasterId, ExtraModudle entity);
        bool PayplusProcessorUpdate(PayplusProcessor entity);
        bool PayplusProcessorInsert(int terminalMasterId, PayplusProcessor entity);
        bool MerchantCardFallbacUpdateInsert(int terminalMasterId, int merchantCardFallbackType, IList<TemMerchantCardFallback> apiEntities, IList<MerchantCardFallback> sources);

        bool TEMUpdateTerminalMaster(TerminalMaster terminalMaster);

        bool apiICSSetupByTerminalTypeUpdate(ICSSetupByTerminalType entity);
        bool apiICSSetupByTerminalTypeInsert(ICSSetupByTerminalType entity, int terminalMasterId);

        bool apiICSSetupByCardDataInputUpdate(ICSSetupByCardDataInput entity);
        bool apiICSSetupByCardDataInputInsert(ICSSetupByCardDataInput entity, int terminalMasterId);

        bool apiICSSetupByCVMCapabilityUpdate(ICSSetupByCVMCapability entity);
        bool apiICSSetupByCVMCapabilityInsert(ICSSetupByCVMCapability entity, int terminalMasterId);

        bool apiICSSetupBySecurityCapabilityUpdate(ICSSetupBySecurityCapability entity);
        bool apiICSSetupBySecurityCapabilityInsert(ICSSetupBySecurityCapability entity, int terminalMasterId);

        bool apiICSSetupByTmDataInputUpdate(ICSSetupByTmDataInput entity);
        bool apiICSSetupByTmDataInputInsert(ICSSetupByTmDataInput entity, int terminalMasterId);

        bool apiICSSetupByTmDataOutputUpdate(ICSSetupByTmDataOutput entity);
        bool apiICSSetupByTmDataOutputInsert(ICSSetupByTmDataOutput entity, int terminalMasterId);

        bool apiICSSetupByCDAModeUpdate(ICSSetupByCDAMode entity);
        bool apiICSSetupByCDAModeInsert(ICSSetupByCDAMode entity, int terminalMasterId);

        bool apiPOSConnectionUpdate(POSConnection entity);
        bool apiPOSConnectionInsert(POSConnection entity, int terminalMasterId);

        bool apiPOSInterfaceUpdate(POSInterface entity);
        bool apiPOSInterfaceInsert(POSInterface entity, int terminalMasterId);

        // IOT Device
        IList<TerminalMaster> GetAllTerminalByIoTDevice(int IoTDeviceTypeId, int IoTDeviceCateId, int IoTDeviceId, bool isAssignedDevice = false, string searchKey = "");
        IList<TerminalMaster> GetAllTerminalByCompanyAndAccessStatus(int companyDetailId, int brandId, int modelId, bool isAssignedDevice = false, string searchKey = "", int companyId = 0, int accessStatusId = 0);
        IList<TerminalMaster> GetAllTerminalByIoTDeviceView(int? IoTDeviceTypeId, int? IoTDeviceCateId, int? IoTDeviceId, string searchKey = "");
        IList<TerminalMaster> GetAllTerminalForIoTDeviceManager(IList<int> ioTDeviceTypeIds = null, IList<int> ioTDeviceCateIds = null, IList<int> ioTDeviceIds = null);

        string GetAllXmlChanges(int terminalMasterId);

        TerminalMaster GetForDeviceSetupEdit(int terminalMasterId);

        /// <summary>
        /// Retrieves a list of selected registered applications associated with a specific terminal master.
        /// </summary>
        /// <param name="terminalMaterId">The unique identifier of the terminal master.</param>
        /// <returns>
        /// A task that represents the asynchronous operation.
        /// The task result contains a list of <see cref="ApplicationDeviceMapping"/> objects associated with the specified terminal master.
        /// </returns>
        Task<List<ApplicationDeviceMapping>> GetSelectedApplicationsListByTerminalMasterIdAsync(int terminalMaterId);

        List<TerminalMaster> GetTerminalForPaxStore(Expression<Func<TerminalMaster, bool>> whereClause = null);

        bool Copy(int terminalMasterIdFrom, int terminalMasterIdTo);

        List<PaymentMode> GetPaymentModes(IList<MerchantTerminal> merchantTerminals);

        Task<List<TerminalMaster>> GetTerminalByCompanyAndAccessStatus(int companyId);

        Task<List<TerminalMaster>> GetTerminalByCompany(int companyId);

        Task<List<TerminalMaster>> GetTerminalAssigneds();

        Task<int> GetTerminalIdBySerialNumber(string serialNumber);

        Task<TerminalMaster> GetTerminalMasterForCopy(int terminalMasterId, bool includeMerchantTerminal = true, bool isBaseToBase = false);

        /// <summary>
        /// Get merchant terminal template include related entity
        /// </summary>
        /// <param name="merchantTerminalId"></param>
        /// <returns></returns>
        Task<MerchantTerminal> GetMerchantTerminalForCopy(int merchantTerminalId);

        /// <summary>
        /// copy data from merchant terminal template to merchant terminal
        /// </summary>
        /// <param name="merchantTerminalId"></param>
        /// <param name="merchantTerminalTemplate"></param>
        /// <returns></returns>
        Task<MerchantTerminal> CopyMerchantTerminal(int merchantTerminalId, MerchantTerminal merchantTerminalTemplate);

        Task<TerminalMaster> CopyTemplate(int terminalMasterId, TerminalMaster template, bool includeMerchantTerminal = true);
        Task<List<TerminalMaster>> GetAllTerminalUnAssignedDeviceAsync(int companyIdOrg, int brandIdOrg, int modelIdOrg, bool isAssignedDevice = false, string searchKey = "", int companyId = 0, int accessStatusId = 0);


        /// <summary>
        /// Get all available devices with pagination for api
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="brandId"></param>
        /// <param name="modelId"></param>
        /// <param name="pagingParameter"></param>
        /// <returns></returns>
        Task<PagingResponse<AvailableDevicesApiModel>> GetAllTerminalUnAssignedDeviceForApiAsync(int companyId, List<int> brandId, List<int> modelId, string keySearch, PagingParameter pagingParameter);

        /// <summary>
        /// Assign Device to Location Area and Device Industry
        /// </summary>
        /// <param name="terminalId"></param>
        /// <param name="locationAreaId"></param>
        /// <param name="deviceIndustryId"></param>
        /// <param name="companyId"></param>
        /// <param name="groupId"></param>
        /// <returns></returns>
        Task AssignDeviceToLocationAsync(int terminalId, int locationAreaId, int deviceIndustryId, int groupId);

        /// <summary>
        /// Asynchronously assigns a device industry to a terminal based on their respective IDs.
        /// </summary>
        /// <param name="terminalId">The ID of the terminal to which the device industry will be assigned.</param>
        /// <param name="deviceIndustryId">The ID of the device industry that will be assigned to the terminal.</param>
        /// <returns>A task that represents the asynchronous operation. The task completes when the device industry is successfully assigned.</returns>
        /// <exception cref="ArgumentException">Thrown when either the terminalId or deviceIndustryId is invalid or does not exist.</exception>
        /// <exception cref="InvalidOperationException">Thrown when the operation cannot be completed due to a system error or invalid state.</exception>
        Task AssignDeviceIndustryAsync(int terminalId, int deviceIndustryId);

        /// <summary>
        /// Get Assigned Location Device
        /// </summary>
        /// <param name="paramsModel">
        /// An instance of <see cref="AssignedDeviceParamsModel"/> containing the filtering parameters, such as group ID, category ID, type ID, industry ID, brand ID, model ID, company ID, and location string hierarchy IDs.
        /// </param>
        /// <returns></returns>
        Task<IList<TerminalMaster>> GetAssignedDeviceAsync(AssignedDeviceParamsModel paramsModel);

        /// <summary>
        /// Asynchronously retrieves a list of terminals by a list of terminal IDs.
        /// </summary>
        /// <param name="ids">A list of terminal IDs to query the terminals.</param>
        /// <returns>
        /// A task that represents the asynchronous operation. The task result contains a list of
        /// <see cref="TerminalMaster"/> objects that match the provided IDs.
        /// </returns>
        Task<IList<TerminalMaster>> GetListTerminalByListIdAsync(IList<int> ids);

        /// <summary>
        /// Get Brand Master in Location tree
        /// </summary>
        /// <param name="locationStringHierarchyIds"></param>
        /// <param name="deviceCategoryId"></param>
        /// <param name="deviceTypeId"></param>
        /// <param name="deviceIndustryId"></param>
        /// <param name="companyId"></param>
        /// <param name="groupId"></param>
        /// <returns></returns>
        Task<IList<TerminalBrandMaster>> GetTerminalBrandMasterAsync(string locationStringHierarchyIds, int deviceCategoryId, int deviceTypeId, int deviceIndustryId, int companyId, int groupId);

        /// <summary>
        /// Get Model Master in Location tree
        /// </summary>
        /// <param name="locationStringHierarchyIds"></param>
        /// <param name="deviceCategoryId"></param>
        /// <param name="deviceTypeId"></param>
        /// <param name="deviceIndustryId"></param>
        /// <param name="deviceBrandId"></param>
        /// <param name="companyId"></param>
        /// <param name="groupId"></param>
        /// <returns></returns>
        Task<IList<TerminalModelMaster>> GetTerminalModelMasterAsync(string locationStringHierarchyIds, int deviceCategoryId, int deviceTypeId, int deviceIndustryId, int deviceBrandId, int companyId, int groupId);

        /// <summary>
        /// Unassign Location Device
        /// </summary>
        /// <param name="terminalId"></param>
        void UnassignDevice(int terminalId);

        /// <summary>
        /// Support API api/synchronize/resyncfile/prepare with Terminal Master id
        /// </summary>
        /// <param name="serialNumber"></param>
        /// <returns></returns>
        TerminalMaster GetTerminalIncludedById(int terminalMasterId);

        /// <summary>
        /// Get Terminal by DeviceTemplateId (not base template)
        /// </summary>
        /// <param name="templateId"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        Task<TerminalMaster> GetTerminalByDeviceTemplateId(int templateId, DeviceSetupType type);

        /// <summary>
        /// Asynchronously retrieves a list of <see cref="TerminalMaster"/> objects with deleted template status by calling the corresponding method in the <see cref="_terminalMasterRepository"/>.
        /// </summary>
        /// <returns>
        /// A task that represents the asynchronous operation. The task result contains a list of <see cref="TerminalMaster"/> objects matching the criteria for deleted templates.
        /// </returns>
        Task<List<TerminalMaster>> GetDeletedTemplateTerminalsAsync();

        /// <summary>
        /// Save mapping id (charityId, hostInterfaceId,...) when copy terminal template
        /// </summary>
        /// <param name="terminalMasterTemplate"></param>
        /// <param name="copiedTerminal"></param>
        void SaveMappingIdAfterCopy(TerminalMaster terminalMasterTemplate, TerminalMaster copiedTerminal);

        /// <summary>
        /// Get last terminal template
        /// </summary>
        /// <param name="serialNumber"></param>
        /// <param name="applicationId"></param>
        /// <returns></returns>
        Task<TerminalMaster> GetTerminalTemplateForApiDownload(string serialNumber, int applicationId);

        /// <summary>
        /// Get Terminal by device template id
        /// </summary>
        /// <param name="templateId"></param>
        /// <returns></returns>
        Task<TerminalMaster> GetTerminalByDeviceTemplateId(int templateId);

        /// <summary>
        /// Discards all pending changes for a MerchantTerminal and its related entities asynchronously.
        /// </summary>
        /// <param name="merchantTerminalId">The ID of the MerchantTerminal to reset.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        Task DiscardMerchantAsync(int merchantTerminalId);

        /// <summary>
        /// Get assigned device ids by location
        /// </summary>
        /// <param name="locationItemId"></param>
        /// <returns></returns>
        Task<List<int>> GetAssignedDeviceIdsByLocationAsync(int locationItemId, int groupId);

        /// <summary>
        /// Assign Device to Location Area and Group Id
        /// </summary>
        /// <param name="terminalId"></param>
        /// <param name="locationAreaId"></param>
        /// <param name="deviceIndustryId"></param>
        /// <param name="groupId"></param>
        /// <returns></returns>
        Task AssignDeviceToLocationForApiAsync(int terminalId, int locationAreaId, int groupId);

        /// <summary>
        /// Assign multiple Device to Location Area and Group Id
        /// </summary>
        /// <param name="terminalIds"></param>
        /// <param name="locationAreaId"></param>
        /// <param name="groupId"></param>
        /// <returns></returns>
        Task AssignDevicesToLocationForApiAsync(IList<int> terminalIds, int locationAreaId, int groupId);

        /// <summary>
        ///
        /// </summary>
        /// <param name="paramsModel"></param>
        /// <param name="pagingParameter"></param>
        /// <returns></returns>
        Task<PagingResponse<DeviceAssociatedResponseApiModel>> GetAssignedDeviceAsync(AssignedDeviceParamsApiModel paramsModel, PagingParameter pagingParameter, string searchValue, SearchAssignDeviceType searchType);

        /// <summary>
        /// Unassigns the specified devices from their associated location asynchronously.
        /// </summary>
        /// <param name="terminalIds"></param>
        /// <returns></returns>
        Task UnassignDevicesToLocationForApiAsync(IList<int> terminalIds);

        /// <summary>
        /// Checks if any device is assigned to a specific group.
        /// </summary>
        /// <param name="groupId"></param>
        /// <returns></returns>
        Task<bool> HasAnyDeviceAssignedToGroup(int groupId);
    }
}
