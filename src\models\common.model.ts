import { ReactNode } from 'react';

export interface ApiBaseModel {
  id: number;
  created: string;
  modified: string;
  isStatus: number;
  isActive: boolean;
  name: string;
}

export interface PagingResponse<T> {
  data: T[];
  totalRecords: number;
}

/**
 * Interface for select remove form
 */

export interface SelectionFilterRequest extends PaginationParams {
  parentId?: number | null;
  type: string;
  selectedFilterType: string,
}

export interface SelectionDataResponse<T> {
  source: PagingResponse<T>;
  selectedIds: number[];
}

export interface SelectionUpdateRequest {
  selectedIds: number[];
}

/**
 * Interface for select option
 */
export interface SelectOption {
  label: ReactNode;
  value: string;
}

/**
 * Interface for select option with number value
 */
export interface SelectOptionNumberValue {
  label: ReactNode;
  value: number;
}

/**
 * Interface for data item response
 */
export interface DataItemResponse {
  id: number;
  name: string;
  isActive: boolean;
}

/**
 * Interface for pagination params
 */
export interface PaginationParams {
  pageNumber: number;
  pageSize: number;
  searchString?: string;
}

/**
 * Interface for select list item
 */
export interface SelectListItem {
  text: string;
  value: string;
  selected?: boolean;
  disabled?: boolean;
  group?: string;
}

export interface ApiError {
  response?: {
    data?: {
      message?: string;
    };
  };
}