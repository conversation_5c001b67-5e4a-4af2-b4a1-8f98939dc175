using Microsoft.AspNetCore.Http;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;

namespace MMS.Infrastructure.Commons;

public class TokenClaimReader : ITokenClaimReader
{
    private readonly IHttpContextAccessor _httpContextAccessor;

    public TokenClaimReader(IHttpContextAccessor httpContextAccessor)
    {
        _httpContextAccessor = httpContextAccessor ?? throw new ArgumentNullException(nameof(httpContextAccessor));
    }

    public IEnumerable<Claim> GetClaims()
    {
        var token = GetTokenFromHeader();
        if (string.IsNullOrEmpty(token))
        {
            return Array.Empty<Claim>();
        }

        var handler = new JwtSecurityTokenHandler();
        var jwtToken = handler.ReadJwtToken(token);
        return jwtToken.Claims;
    }

    public Claim? GetClaim(string claimType)
    {
        if (string.IsNullOrEmpty(claimType))
        {
            throw new ArgumentNullException(nameof(claimType));
        }

        return GetClaims().FirstOrDefault(c => c.Type == claimType);
    }

    public int GetUserIdClaim()
    {
        return GetIntegerClaim(ClaimConstants.USER_ID_CLAIM);
    }

    public bool GetIsAdminClaim()
    {
        var isAdminClaimValue = GetClaim(ClaimConstants.IS_ADMIN_CLAIM)?.Value;
        return string.IsNullOrEmpty(isAdminClaimValue)
            ? throw new KeyNotFoundException("IsAdmin not found in token")
            : bool.Parse(isAdminClaimValue);
    }

    public int GetCompanyIdClaim()
    {
        return GetIntegerClaim(ClaimConstants.COMPANY_ID_CLAIM);
    }

    public int GetAccessStatusIdClaim()
    {
        return GetIntegerClaim(ClaimConstants.ACCESS_STATUS_ID_CLAIM);
    }

    public int GetCompanyGroupMappingIdClaim()
    {
        return GetIntegerClaim(ClaimConstants.COMPANY_GROUP_MAPPING_ID_CLAIM);
    }

    public string GetJtiClaim()
    {
        var jtiClaimValue = GetClaim(JwtRegisteredClaimNames.Jti)?.Value;
        return string.IsNullOrEmpty(jtiClaimValue)
            ? throw new KeyNotFoundException("JTI not found in token")
            : jtiClaimValue;
    }

    private int GetIntegerClaim(string claimType)
    {
        var claimValue = GetClaim(claimType)?.Value;
        if (string.IsNullOrEmpty(claimValue))
        {
            throw new KeyNotFoundException($"Claim not found in token");
        }

        if (!int.TryParse(claimValue, out var result))
        {
            throw new InvalidOperationException($"Claim with ID={claimValue} is invalid");
        }

        return result;
    }

    private string? GetTokenFromHeader()
    {
        const string BearerPrefix = "Bearer ";
        var authorizationHeader = _httpContextAccessor.HttpContext?.Request.Headers.Authorization.FirstOrDefault();

        return string.IsNullOrEmpty(authorizationHeader) || !authorizationHeader.StartsWith(BearerPrefix)
            ? null
            : authorizationHeader[BearerPrefix.Length..];
    }
}
