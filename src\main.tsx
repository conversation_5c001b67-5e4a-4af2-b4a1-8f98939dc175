import 'antd/dist/reset.css';
import { createRoot } from 'react-dom/client';
import './index.css';
import App from './App.tsx';
import './scss/index.scss';
import './assets/font-icon/css/fontello.css';
import './utils/i18n';
import { RecoilRoot } from 'recoil';
import { ConfigProvider, App as AntApp } from 'antd';
import { useTheme } from './hooks/useTheme';

const Root = () => {
  const { currentTheme } = useTheme();
  return (
    <ConfigProvider theme={currentTheme}>
      <AntApp>
        <App />
      </AntApp>
    </ConfigProvider>
  );
};

createRoot(document.getElementById('root')!).render(
  <RecoilRoot>
    <Root />
  </RecoilRoot>,
);
