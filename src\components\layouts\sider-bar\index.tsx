import { Layout, Menu } from 'antd';
import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useRecoilState } from 'recoil';
import { AppIcons } from '../../shared/icons';
import { layoutCollapseState } from '../../../states/layout';
import './index.scss';
import { getThemeAsset } from '../../../utils/assets';
import { ROUTE_PATHS } from '../../../constants/router.constants';

const { Sider } = Layout;

const Sidebar: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [layoutCollapse, setLayoutCollapse] =
    useRecoilState(layoutCollapseState);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [openKeys, setOpenKeys] = useState<string[]>([]);

  // Map paths to menu keys, including parent key for highlighting
  const pathToKeyMap: {
    [key: string]: { selectedKeys: string[]; openKey?: string };
  } = {
    '/applications': { selectedKeys: ['1'] },
    '/app-base-template': { selectedKeys: ['2'] },
    '/organizations': { selectedKeys: ['3'] },
    '/cloud-security': { selectedKeys: ['4'] },
    '/management': { selectedKeys: ['5'] },
    '/persons': { selectedKeys: ['5', '5-1'], openKey: '5' },
    '/companies': { selectedKeys: ['5', '5-2'], openKey: '5' },
    // '/device-view': { selectedKeys: ['6', '6-1'], openKey: '6' },
    // '/unassigned-device': { selectedKeys: ['6', '6-2'], openKey: '6' },
    '/merchant-locations': { selectedKeys: ['6'] },
    '/partner-access': { selectedKeys: ['6', '6-4'], openKey: '6' },
    '/settings': { selectedKeys: ['7'] },
  };

  useEffect(() => {
    // Set selected and open keys based on the current path
    const currentPath = location.pathname;

    // Find a matching path prefix instead of exact path match
    const matchingPath = Object.keys(pathToKeyMap).find(
      (path) => currentPath === path || currentPath.startsWith(`${path}/`),
    );

    const menuItem = matchingPath
      ? pathToKeyMap[matchingPath]
      : { selectedKeys: ['1'] };

    setSelectedKeys(menuItem.selectedKeys);
  }, [location.pathname]);

  const handleMenuClick = (key: string) => {
    switch (key) {
      case '1':
        navigate(ROUTE_PATHS.Home);
        break;
      case '2':
        navigate('/app-base-template');
        break;
      case '3':
        // navigate('/organizations');
        break;
      case '4':
        // navigate('/cloud-security');
        break;
      case '5':
        // navigate('/management');
        break;
      case '5-1':
        navigate(ROUTE_PATHS.Persons);
        break;
      case '5-2':
        navigate(ROUTE_PATHS.Companies);
        break;
      case '6':
        navigate(ROUTE_PATHS.MerchantLocations);

        break;
      case '6-1':
        // navigate('/device-view');
        break;
      case '6-2':
        // navigate('/unassigned-device');
        break;
      case '6-3':
        navigate(ROUTE_PATHS.MerchantLocations);
        break;
      case '6-4':
        // navigate('/partner-access');
        break;
      case '7':
        // navigate('/settings');
        break;
      case '8':
        // Handle logout
        break;
    }
  };

  const baseMenuItems: any[] = [
    {
      type: 'divider',
    },
    {
      key: '1',
      label: 'Dashboard',
      icon: <AppIcons.TaskIcon />,
    },
    {
      type: 'divider',
    },
    {
      key: 'CloudSetup',
      label: 'Cloud Setup',
      className: 'menu-group',
      disabled: true,
      icon: <></>,
    },
    { key: '2', label: 'App Base Template', icon: <AppIcons.PartialIcon /> },
    { key: '3', label: 'Organizations', icon: <AppIcons.FinalIcon /> },
    { key: '4', label: 'Cloud Security', icon: <AppIcons.CloudIcon /> },
    {
      key: '5',
      label: 'Management',
      icon: <AppIcons.SoftwareIcon />,
      children: [
        { key: '5-1', label: 'Person' },
        { key: '5-2', label: 'Company' },
        { key: '5-3', label: 'Merchant' },
      ],
    },
    {
      type: 'divider',
    },
    {
      key: '6',
      label: 'Device Manager',
      icon: <AppIcons.CoreValuesIcon />,
      // children: [
      //   // { key: '6-1', label: 'Device View' },
      //   // { key: '6-2', label: 'Unassigned Device' },
      //   { key: '6-3', label: 'Merchant Locations' },
      //   { key: '6-4', label: 'Partner Access' },
      // ],
    },
    { key: '7', label: 'Settings', icon: <AppIcons.SettingIcon /> },
    { key: '8', label: 'Logout', icon: <AppIcons.LogoutIcon /> },
  ];

  const siderMenuItems = layoutCollapse.sidebarCollapsed
    ? baseMenuItems.filter((item) => !item.disabled)
    : baseMenuItems;

  const toggleSidebar = () => {
    setLayoutCollapse({
      ...layoutCollapse,
      sidebarCollapsed: !layoutCollapse.sidebarCollapsed,
    });
  };

  return (
    <Sider
      width={200}
      className="sider"
      collapsible
      collapsed={layoutCollapse.sidebarCollapsed}
      onCollapse={(value) =>
        setLayoutCollapse({
          ...layoutCollapse,
          sidebarCollapsed: value,
        })
      }
      trigger={null}
    >
      <div className="logo">
        {layoutCollapse.sidebarCollapsed ? (
          <h2>M</h2>
        ) : (
          <img
            src={getThemeAsset('images', 'logo.svg')}
            alt="Logo"
            className="logo-image"
          />
        )}
      </div>
      <Menu
        mode="inline"
        selectedKeys={selectedKeys}
        openKeys={openKeys}
        onOpenChange={(keys) => setOpenKeys(keys)}
        style={{ height: 'calc(100% - 112px)', borderRight: 0 }}
        className="sider-menu"
        items={siderMenuItems}
        onSelect={({ key }) => handleMenuClick(key)}
      />
      <div className="collapse-btn" onClick={toggleSidebar}>
        <AppIcons.CollapseIcon
          className={layoutCollapse.sidebarCollapsed ? 'collapsed' : ''}
        />
        {layoutCollapse.sidebarCollapsed ? '' : '  Collapse'}
      </div>
    </Sider>
  );
};

export default Sidebar;
