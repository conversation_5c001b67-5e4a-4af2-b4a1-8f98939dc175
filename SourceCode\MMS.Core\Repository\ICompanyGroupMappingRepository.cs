﻿using MMS.Core.CoreUTI.Enum;
using MMS.Core.Entities;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MMS.Core.Repository
{
    public interface ICompanyGroupMappingRepository : IBaseRepository<CompanyGroupMapping>
    {
        /// <summary>
        /// Get item by id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<CompanyGroupMapping> GetByIdAsync(int id);

        /// <summary>
        /// Get item by ids
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        Task<List<CompanyGroupMapping>> GetByIdsAsync(IList<int> ids);

        /// <summary>
        /// Get Hierarchy Ids
        /// </summary>
        /// <param name="parentId"></param>
        /// <returns></returns>
        Task<List<int>> GetHierarchyIdsAsync(int parentId);

        /// <summary>
        /// Get Selected items
        /// </summary>
        /// <param name="type"></param>
        /// <param name="parentId"></param>
        /// <param name="screenType"></param>
        /// <param name="includeDeactivatedOrg"></param>
        /// <returns></returns>
        Task<List<CompanyGroupMapping>> GetSelectedAsync(CompanyGroupMappingType type, int? parentId, bool includeDeactivatedOrg = false);

        /// <summary>
        /// Get children items of a mapping with parent id. If parent ID is not null, type is Subsidiary or Group otherwise type is Company.
        /// </summary>
        /// <param name="parentId"></param>
        /// <param name="isIncludeGroup"></param>
        /// <returns></returns>
        Task<List<CompanyGroupMapping>> GetCompanyTreeChildrenMappingsAync(int? parentId, bool isIncludeGroup = true);

        /// <summary>
        /// Get children count for multiple parent IDs to check if they have children
        /// </summary>
        /// <param name="parentIds"></param>
        /// <param name="isIncludeGroup"></param>
        /// <returns>Dictionary where key is parent ID and value is boolean indicating if it has children</returns>
        Task<Dictionary<int, bool>> GetCompanyTreeChildrenMappingsByIdsAync(List<int> parentIds, bool isIncludeGroup);

        /// <summary>
        /// Get children items of a mapping with parent id. If parent ID is not null, type is Subsidiary, Partner, Sub Partner otherwise type is Company.
        /// </summary>
        /// <param name="parentId"></param>
        /// <returns></returns>
        Task<List<CompanyGroupMapping>> GetPartnerAccessTreeChildrenMappingsAync(int? parentId);

        /// <summary>
        /// Get Selected Items By Parent Ids
        /// </summary>
        /// <param name="type"></param>
        /// <param name="parentIds"></param>
        /// <param name="includeDeactivatedOrg"></param>
        /// <returns></returns>
        Task<List<CompanyGroupMapping>> GetSelectedByParentIdsAsync(CompanyGroupMappingType type, IList<int> parentIds, bool includeDeactivatedOrg = false);

        /// <summary>
        /// Retrieves a <see cref="CompanyGroupMapping"/> entity based on the specified company ID and screen type.
        /// </summary>
        /// <param name="parentId">The ID of the company to filter by. Can be null.</param>
        /// <param name="screenType">The screen type to filter by.</param>
        /// <returns>
        /// An instance of <see cref="CompanyGroupMapping"/> if a match is found; otherwise, null.
        /// </returns>
        Task<CompanyGroupMapping> GetCompanyByCompanyIdAndScreenType(int? parentId, ScreenType screenType);

        /// <summary>
        /// Get By Type
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        Task<List<CompanyGroupMapping>> GetByTypeAsync(CompanyGroupMappingType type);

        /// <summary>
        /// Update Selected items
        /// </summary>
        /// <param name="selectedIds"></param>
        /// <param name="parentId"></param>
        /// <param name="type"></param>
        /// <param name="useSelectSort"></param>
        /// <param name="screenType"></param>
        /// <returns></returns>
        Task UpdateSelectedAsync(IList<int> selectedIds, int? parentId, CompanyGroupMappingType type, ScreenType screenType, bool useSelectSort = false);

        /// <summary>
        /// Update IsHideDeactivatedByParentId
        /// </summary>
        /// <param name="parentId"></param>
        Task UpdateIsHideDeactivatedByParentIdAsync(int parentId);

        /// <summary>
        /// Get IsHideDeactivated by Parent id
        /// </summary>
        /// <param name="parentId"></param>
        /// <returns></returns>
        bool GetIsHideDeactivatedByParentId(int parentId);

        /// <summary>
        /// Get group sources for sub partner
        /// </summary>
        /// <param name="accessPartnerId"></param>
        /// <returns></returns>
        Task<List<CompanyGroup>> GetGroupSourcesForSubPartnerAsync(int accessPartnerId);

        /// <summary>
        /// Get selected groups by company or subsidiary
        /// </summary>
        /// <param name="parentId"></param>
        /// <returns></returns>
        Task<List<CompanyGroupMapping>> GetSelectedGroupsByCompanyOrSubsidiaryAsync(int parentId);

        /// <summary>
        /// Get by Hierarchy Key
        /// </summary>
        /// <param name="hierarchyKey"></param>
        /// <returns></returns>
        Task<CompanyGroupMapping> GetByHierarchyKey(string hierarchyKey);

        /// <summary>
        /// Get selected Application by Company id or Subsidiary id
        /// </summary>
        /// <param name="companyMasterId"></param>
        /// <param name="groupId"></param>
        /// <returns></returns>
        Task<List<CompanyGroupMapping>> GetSelectedAppByCompanyOrSubsidiaryAsync(int companyMasterId, int groupId);

        /// <summary>
        /// Get list selected App Version ids
        /// </summary>
        /// <returns></returns>
        Task<List<int>> GetSelectedAppVersionIdsAsync();

        /// <summary>
        /// validate app version deleteable
        /// </summary>
        /// <param name="removeVersionIds"></param>
        /// <returns></returns>
        Task ValidateAppVersionDeletableAsync(List<CompanyGroupMapping> removeVersionIds);

        /// <summary>
        /// Get all parents by hierarchy
        /// </summary>
        /// <param name="hierarchy"></param>
        /// <returns></returns>
        Task<List<int>> GetAllParentIdsByHierarchy(string hierarchy);

        /// <summary>
        /// Check deleted parent in the entire parent chain of list of companyGroupMappingIds
        /// </summary>
        /// <param name="companyGroupMappingId"></param>
        /// <returns></returns>
        Task<Dictionary<int, bool>> HasDeletedParents(List<int> companyGroupMappingIds);

        /// <summary>
        /// Get all company tree mappings at once instead of by parentId
        /// </summary>
        /// <returns>Complete list of partner access mappings</returns>
        Task<List<CompanyGroupMapping>> GetAllCompanyForSearchTreeMappingsAsync();

        /// <summary>
        /// Get all partner access tree mappings at once instead of by parentId
        /// </summary>
        /// <returns>Complete list of partner access mappings</returns>
        Task<List<CompanyGroupMapping>> GetAllPartnerAccessTreeMappingsAsync();

        /// <summary>
        /// Get all company id selected in company group mapping
        /// </summary>
        /// <returns>Complete list of partner access mappings</returns>
        Task<List<int>> GetAllCompanyIdSelected(int? parentId);

        /// <summary>
        /// Insert company group in company group mapping
        /// </summary>
        Task InsertGroupAsync(int groupId, int parentId);

        /// <summary>
        /// Get list company id childs
        /// </summary>
        /// <param name="hierarchy"></param>
        /// <returns></returns>
        Task<List<int>> GetAllChildIdsByHierarchyAsync(string hierarchy);
    }
}
