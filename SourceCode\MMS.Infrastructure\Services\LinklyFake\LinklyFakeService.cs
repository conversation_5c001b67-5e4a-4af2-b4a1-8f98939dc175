using Microsoft.Extensions.Options;
using MMS.Model.LinklyFake;
using Newtonsoft.Json;
using Serilog;
using System.Net.Http;
using System.Text;

namespace MMS.Infrastructure.Services.LinklyFake
{
    /// <summary>
    /// Service for interacting with the LinklyFake API
    /// </summary>
    public class LinklyFakeService : ILinklyFakeService
    {
        private readonly HttpClient _httpClient;
        private readonly LinklyFakeOptions _options;
        private const string _unknownError = "Unknown error";

        /// <summary>
        /// Constructor for LinklyFakeService
        /// </summary>
        /// <param name="httpClient">HTTP client</param>
        /// <param name="options">LinklyFake options</param>
        public LinklyFakeService(
            HttpClient httpClient,
            IOptions<LinklyFakeOptions> options)
        {
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));

            // Configure HTTP client
            _httpClient.BaseAddress = new Uri(_options.BaseUrl);
            _httpClient.Timeout = TimeSpan.FromSeconds(_options.TimeoutSeconds);


        }

        /// <inheritdoc />
        public async Task<List<LinklyTerminalDto>> GetAllTerminalsAsync()
        {
            try
            {
                Log.Information("Fetching all terminals from LinklyFake API");

                var httpResponse = await _httpClient.GetAsync(_options.TerminalsEndpoint);
                httpResponse.EnsureSuccessStatusCode();

                var jsonContent = await httpResponse.Content.ReadAsStringAsync();
                var response = JsonConvert.DeserializeObject<LinklyTerminalListResponseDto>(jsonContent);

                if (response == null || !response.Success)
                {
                    Log.Warning("Failed to fetch terminals from LinklyFake API: {Message}",
                        response?.Message ?? _unknownError);
                    return new List<LinklyTerminalDto>();
                }

                Log.Information("Successfully fetched {Count} terminals from LinklyFake API",
                    response.Count);
                return response.Data ?? new List<LinklyTerminalDto>();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error fetching terminals from LinklyFake API");
                // Return empty list instead of throwing to make the API more resilient
                return new List<LinklyTerminalDto>();
            }
        }

        /// <inheritdoc />
        public async Task<List<LinklyCompanyDto>> GetAllCompaniesAsync()
        {
            try
            {
                Log.Information("Fetching all companies from LinklyFake API");

                var httpResponse = await _httpClient.GetAsync(_options.CompaniesEndpoint);
                httpResponse.EnsureSuccessStatusCode();

                var jsonContent = await httpResponse.Content.ReadAsStringAsync();
                var response = JsonConvert.DeserializeObject<LinklyCompanyListResponseDto>(jsonContent);

                if (response == null || !response.Success)
                {
                    Log.Warning("Failed to fetch companies from LinklyFake API: {Message}",
                        response?.Message ?? _unknownError);
                    return new List<LinklyCompanyDto>();
                }

                Log.Information("Successfully fetched {Count} companies from LinklyFake API",
                    response.Count);
                return response.Data ?? new List<LinklyCompanyDto>();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error fetching companies from LinklyFake API");
                // Return empty list instead of throwing to make the API more resilient
                return new List<LinklyCompanyDto>();
            }
        }

        /// <inheritdoc />
        public async Task<List<LinklyMerchantMasterDto>> GetAllMerchantMastersAsync()
        {
            try
            {
                Log.Information("Fetching all merchant masters from LinklyFake API");

                var httpResponse = await _httpClient.GetAsync(_options.MerchantMastersEndpoint);
                httpResponse.EnsureSuccessStatusCode();

                var jsonContent = await httpResponse.Content.ReadAsStringAsync();
                var response = JsonConvert.DeserializeObject<LinklyMerchantMasterListResponseDto>(jsonContent);

                if (response == null || !response.Success)
                {
                    Log.Warning("Failed to fetch merchant masters from LinklyFake API: {Message}",
                        response?.Message ?? _unknownError);
                    return new List<LinklyMerchantMasterDto>();
                }

                Log.Information("Successfully fetched {Count} merchant masters from LinklyFake API",
                    response.Count);
                return response.Data ?? new List<LinklyMerchantMasterDto>();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error fetching merchant masters from LinklyFake API");
                // Return empty list instead of throwing to make the API more resilient
                return new List<LinklyMerchantMasterDto>();
            }
        }
    }
}
