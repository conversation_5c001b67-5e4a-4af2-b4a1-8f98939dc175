using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MMS.Api.Filters;
using MMS.Api.Security;
using MMS.Core.CoreUTI.Enum;
using MMS.Core.Services;
using MMS.Infrastructure.Services.LinklyFake;

namespace MMS.Api.Controllers
{
    /// <summary>
    /// Controller for company synchronization operations
    /// </summary>
    [ApiController]
    [Route("company-sync")]
    public class CompanySyncController : ControllerBase
    {
        private readonly ILinklyFakeService _linklyFakeService;
        private readonly ICompanySyncService _companySyncService;

        /// <summary>
        /// Constructor for CompanySyncController
        /// </summary>
        /// <param name="linklyFakeService">Service for interacting with the LinklyFake API</param>
        /// <param name="companySyncService">Service for synchronizing company data</param>
        public CompanySyncController(
            ILinklyFakeService linklyFakeService,
            ICompanySyncService companySyncService)
        {
            _linklyFakeService = linklyFakeService ?? throw new ArgumentNullException(nameof(linklyFakeService));
            _companySyncService = companySyncService ?? throw new ArgumentNullException(nameof(companySyncService));
        }

        /// <summary>
        /// Synchronizes companies from the LinklyFake API to the MMS database
        /// </summary>
        /// <returns>Summary of sync operation</returns>
#if DEBUG
        [AllowAnonymous] // Allow anonymous access in debug mode for testing
#endif
        [HttpGet("sync-linkly-companies")]
        [ApiAuthority(
            Function = ApiSecure.ApiCompanyDetails,
            Action = ApiSecure.EditAction)]
        public async Task<IActionResult> SyncLinklyCompanies()
        {
            // Fetch companies from Linkly API
            var companies = await _linklyFakeService.GetAllCompaniesAsync();

            // Sync companies with our database
            var (inserted, updated, deleted) = await _companySyncService.SyncCompaniesFromLinklyAsync(companies);

            // Return summary of sync operation
            return Ok(new
            {
                message = "Companies synchronized successfully",
                companies_count = companies.Count,
                inserted,
                updated,
                deleted
            });
        }
    }
}
