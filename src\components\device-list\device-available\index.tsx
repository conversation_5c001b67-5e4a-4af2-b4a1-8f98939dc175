import { Button, Select, Table, Input, SelectProps, Space } from 'antd';
import CommonModal from '../../shared/modal';
import {
  DeviceAssociatedResponseApiModel,
  DeviceBrand,
  DeviceModel,
  AvailableDeviceParams,
} from '../../../models/device-list.model';
import FontIcon from '../../shared/icons/font-icon';
import PaginationComponent from '../../shared/pagination';
import { getDeviceAvailablColumns } from '../columns/device-columns';
import { useTranslation } from 'react-i18next';
import { useCallback, useEffect, useState } from 'react';
import { DeviceListApi } from '../../../apis/device-list.api';
import { DEFAULT_PAGE_SIZE } from '../../../constants/device-list.constants';
import showNotification from '../../shared/notification';

interface DeviceAvailableModalProps {
  visible: boolean;
  onCancel: () => void;
  deviceRowSelection: any;
  groupId?: number;
  locationAreaId?: number;
  companyId?: number;
  onSuccess?: () => void; // Add callback to refresh device list
}

const DeviceAvailableModal: React.FC<DeviceAvailableModalProps> = ({
  visible,
  onCancel,
  deviceRowSelection,
  groupId,
  locationAreaId,
  companyId,
  onSuccess,
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [devices, setDevices] = useState<DeviceAssociatedResponseApiModel[]>(
    [],
  );
  const [totalRecord, setTotalRecord] = useState(0);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
  const [pageNumber, setPageNumber] = useState(1);
  const [searchModel, setSearchModel] = useState<string[]>([]);
  const [searchBrand, setSearchBrand] = useState<string[]>([]);
  const [brands, setBrands] = useState<DeviceBrand[]>([]);
  const [models, setModels] = useState<DeviceModel[]>([]);
  const [loadingBrands, setLoadingBrands] = useState(false);
  const [loadingModels, setLoadingModels] = useState(false);
  const [searchValue, setSearchValue] = useState<string>('');
  const [searchKey, setSearchKey] = useState<string>('');
  const [isSearchVisible, setIsSearchVisible] = useState(false);

  // Fetch brands from API
  const fetchBrands = useCallback(async () => {
    setLoadingBrands(true);
    try {
      const response = await DeviceListApi.getDeviceBrands();
      if (response.data) {
        setBrands(response.data);
      }
    } catch (error) {
      console.error('Error fetching brands:', error);
    } finally {
      setLoadingBrands(false);
    }
  }, []); // Remove visible

  const fetchModels = useCallback(async (brandIds: number[]) => {
    if (!brandIds.length) {
      setModels([]);
      return;
    }

    setLoadingModels(true);
    try {
      const response = await DeviceListApi.getDeviceModels(brandIds);
      if (response.data) {
        setModels(response.data);
      }
    } catch (error) {
      console.error('Error fetching models:', error);
    } finally {
      setLoadingModels(false);
    }
  }, []);

  // Fetch device data function
  const fetchDeviceData = useCallback(async () => {
    setLoading(true);
    try {
      const requestParams: AvailableDeviceParams = {
        pageNumber,
        pageSize,
        companyDetailId: companyId,
        keySearch: searchKey,
        deviceBrandIds: searchBrand.map(Number),
        deviceModelIds: searchModel.map(Number),
      };

      const { data } = await DeviceListApi.syncLinklyDevices(companyId);

      const { data: responseData } =
        await DeviceListApi.getAvailableDevices(requestParams);

      if (responseData) {
        setDevices(responseData.data);
        setTotalRecord(responseData.totalRecords);
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  }, [pageNumber, pageSize, searchKey, searchBrand, searchModel, companyId]);

  // Combine useEffect for fetching brands and devices
  useEffect(() => {
    if (visible) {
      // Only fetch brands the first time when the modal opens
      if (brands.length === 0) {
        fetchBrands();
      }
      // Fetch devices when filters change
      fetchDeviceData();
    }
  }, [visible, pageNumber, pageSize, searchKey, searchBrand, searchModel]);

  const handleSearch = () => {
    if (!isSearchVisible) {
      setIsSearchVisible(true);
      return;
    }
    setSearchKey(searchValue);
    setIsSearchVisible(false);
    setPageNumber(1);
  };

  const handleClearAll = () => {
    setSearchBrand([]);
    setSearchModel([]);
    setSearchValue('');
    setSearchKey('');
    setPageNumber(1);
  };

  const handleSave = async () => {
    try {
      setLoading(true);

      const selectedDeviceIds = deviceRowSelection.selectedRowKeys as number[];

      if (!selectedDeviceIds || selectedDeviceIds.length === 0) {
        showNotification(
          'warning',
          t('notification.warning'),
          t('device.noDevicesSelected'),
        );
        return;
      }

      await DeviceListApi.assignDevicesToLocation({
        locationAreaId: locationAreaId ?? 0,
        terminalMasterIds: selectedDeviceIds,
        groupId: groupId ?? 0,
      });

      showNotification(
        'success',
        t('notification.success'),
        t('device.devicesAssignedSuccess'),
      );
      onCancel();

      // Call onSuccess callback to refresh device list in parent component
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      showNotification(
        'error',
        t('notification.error'),
        t('device.devicesAssignedError'),
      );
    } finally {
      setLoading(false);
    }
  };

  const handleBrandChange = (brandIds: string[]) => {
    setSearchBrand(brandIds);
    setSearchModel([]); // Reset model when brand changes
    if (brandIds.length) {
      fetchModels(brandIds.map(Number));
    } else {
      setModels([]); // Clear models if no brands selected
    }
    setPageNumber(1);
  };

  const handleModelChange = (modelIds: string[]) => {
    setSearchModel(modelIds);
    setPageNumber(1);
  };

  const sharedProps: SelectProps = {
    mode: 'multiple',
    style: { width: '100%' },
    placeholder: 'Select Item...',
    maxTagCount: 'responsive',
  };

  return (
    <CommonModal
      visible={visible}
      onCancel={onCancel}
      onSave={handleSave}
      title={t('device.availableDevices')}
      width={560}
    >
      <div className="filter-section">
        <div className="filter-row">
          <div className="filter-item">
            <span>Brand</span>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Select
                className="filter-select"
                value={searchBrand}
                onChange={handleBrandChange}
                loading={loadingBrands}
                mode="multiple"
                placeholder="Select brands"
                style={{ width: '100%' }}
                maxTagCount="responsive"
              >
                {brands.map((brand) => (
                  <Select.Option key={brand.id} value={brand.id.toString()}>
                    {brand.name}
                  </Select.Option>
                ))}
              </Select>
            </Space>
          </div>
          <div className="filter-item">
            <span>Model</span>
            <Select
              className="filter-select"
              value={searchModel}
              onChange={handleModelChange}
              loading={loadingModels}
              mode="multiple"
              placeholder="Select models"
              style={{ width: '100%' }}
              maxTagCount="responsive"
            >
              {models.map((model) => (
                <Select.Option key={model.id} value={model.id.toString()}>
                  {model.name}
                </Select.Option>
              ))}
            </Select>
          </div>
        </div>
      </div>

      <div className="selected-devices-section">
        <div className="selected-devices-header">
          <span className="section-title">{t('device.selectedDevices')}</span>
          <div className="section-actions">
            <div className="search-wrapper">
              <Button
                type="text"
                onClick={handleSearch}
                size="small"
                icon={<FontIcon size={16} className="icon-search main-color" />}
              >
                {t('common.search')}
              </Button>
              {isSearchVisible && (
                <div className="search-input">
                  <Input
                    autoFocus
                    placeholder={t('device.searchBySN')}
                    value={searchValue}
                    onChange={(e) => setSearchValue(e.target.value)}
                    onPressEnter={() => {
                      setSearchKey(searchValue);
                      setPageNumber(1);
                    }}
                    onBlur={() => setIsSearchVisible(false)}
                    style={{ width: 180 }}
                  />
                </div>
              )}
            </div>
            <Button
              type="text"
              icon={<FontIcon size={16} className="icon-close main-color" />}
              size="small"
              onClick={handleClearAll}
            >
              Clear All
            </Button>
          </div>
        </div>
        <div className="selected-devices-table">
          <Table
            rowSelection={{ type: 'checkbox', ...deviceRowSelection }}
            className="device-assigned"
            columns={getDeviceAvailablColumns()}
            dataSource={devices}
            rowKey="id"
            loading={loading}
            pagination={false}
            scroll={{ y: 230 }}
          />
          <PaginationComponent
            totalRecords={totalRecord}
            pageSize={pageSize}
            setPageSize={setPageSize}
            pageNumber={pageNumber}
            setPageNumber={setPageNumber}
          />
        </div>
      </div>
    </CommonModal>
  );
};

export default DeviceAvailableModal;
