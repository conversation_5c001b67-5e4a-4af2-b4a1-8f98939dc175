{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Hangfire": "Error"}}, "LinklyFake": {"TerminalsEndpoint": "api/terminals", "CompaniesEndpoint": "api/companies", "MerchantMastersEndpoint": "api/merchantmasters", "TimeoutSeconds": 30}, "Serilog": {"Using": ["Serilog.Sinks.Async", "Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Verbose", "Override": {"Microsoft": "Information", "Hangfire": "Error"}}, "Enrich": ["FromLogContext", "WithMachineName"], "WriteTo": [{"Name": "Async", "Args": {"configure": [{"Name": "<PERSON><PERSON><PERSON>"}]}}, {"Name": "Async", "Args": {"configure": [{"Name": "File", "Args": {"path": "Logs/log-.txt", "rollingInterval": "Day", "rollOnFileSizeLimit": true, "fileSizeLimitBytes": 10000000}}]}}]}, "JwtSettings": {"Issuer": "MMS2.0", "Audience": "MMS2.0", "ExpiryInMinutes": 60, "ExpiryInMinutesForTemp": 5000000}, "TerminalSourceConfig": {"Source": "<PERSON><PERSON>"}, "HostDomainApiSetting": {"HostDomain": "localhost:7098"}}