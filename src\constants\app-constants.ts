import i18n from '../utils/i18n';

export const PAGE_SIZE_DEFAULT = 1000;

export const LOCAL_COOKIE_KEY = {
  ID_TOKEN: 'ID_TOKEN',
  IS_LOGGED_IN: 'IS_LOGGED_IN',
};

export const LOCAL_STORAGE_KEY = {
  LANGUAGE: 'lang',
  USER_INFO: 'USER_INFO',
  IS_REMEMBER_ME: 'IS_REMEMBER_ME',
  REFRESH_TOKEN: 'REFRESH_TOKEN',
  ACCESS_TOKEN: 'ACCESS_TOKEN',
  GUEST_USER_INFO: 'GUEST_USER_INFO',
  LOCATION_SELECTIONS: 'LOCATION_SELECTIONS',
  COMPANY_TREE_SELECTION: 'COMPANY_TREE_SELECTION',
  SELECTED_TEMPLATE_ID: 'SELECTED_TEMPLATE_ID',
  IS_LOCATION_TREE_LOADED: 'IS_LOCATION_TREE_LOADED',
  BASE_TEMPLATE_SELECTIONS: 'BASE_TEMPLATE_SELECTIONS',
  ACTIVE_BASE_TEMPLATE_SELECTED_TAB: 'ACTIVE_BASE_TEMPLATE_SELECTED_TAB',
  COPY_TEMPLATE_SELECTIONS: 'COPY_TEMPLATE_SELECTIONS',
};

export const PASSWORD_REGEX = /^(?=.*[a-z])(?=.*[A-Z])[A-Za-z0-9_@./#&+-]{8,}$/;

export const PASSWORD_REGEX_ONE_LOWER_CASE = `(?=.*[a-z])`; // should contain at least one lower case
export const PASSWORD_REGEX_ONE_UPPER_CASE = `(?=.*[A-Z])`; // should contain at least one upper case
export const PASSWORD_REGEX_ONE_NUMBER = `(?=.*[0-9])`; // should contain at least one upper case
export const PASSWORD_REGEX_MINIMUM_8_CHARACTERS = `(?=.{8,})`; // should contain at least 8 characters
export const PASSWROD_REGEX_SPECIAL_CHARACTER = `(?=.*[!@#$%^&*])`; // should contain a special character

export const VALIDATION_MESSAGE_CONFIG = {
  required: i18n.t('common.requireValidation'),
  types: {
    email: i18n.t('common.emailValidation'),
    url: i18n.t('common.urlValidation'),
  },
};

export const DATE_TIME_FORMAT = {
  DateFormat_DD_MM_YYYY: 'DD/MM/YYYY', // ex: May 2nd, 2025 => 02/05/2025
  TimeFormat_hh_mm_A: 'hh:mm A', // ex: 20:30 => 08:30 PM | 9:30 => 09:30 AM
};

export const DEFAULT_COMPANY_URL =
  'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQ5hTFrSwvXzQxeuWygMRqpsEgMqufqo7b1fQ&s';

export const LAYOUT = {
  MARGIN: 16,
  WIDTH_MENU_COLLAPSED: 80,
  WIDTH_MENU: 200,
  WIDTH_COMPANY_TREE_COLLAPSED: 0,
  WIDTH_COMPANY_TREE: 240,
  HEADER_FOOTER_HEIGHT: 390,
  LOCATION_FILTER_HEIGHT: 300,
};

export const MAP_CONFIG = {
  projection: 'geoMercator' as const,
  projectionConfig: {
    scale: 300,
    center: [0, 0] as [number, number],
  },
  defaultZoom: 1,
  defaultCoordinates: [0, 0] as [number, number],
};
