namespace MMS.Infrastructure.Services.LinklyFake
{
    /// <summary>
    /// Configuration options for the LinklyFake service
    /// </summary>
    public class LinklyFakeOptions
    {
        private const string ENV_LINKLY_FAKE_URL = "LINKLY_FAKE_URL";

        /// <summary>
        /// The base URL of the LinklyFake API
        /// </summary>
        public string BaseUrl
        {
            get
            {
                var url = Environment.GetEnvironmentVariable(ENV_LINKLY_FAKE_URL);
                if (string.IsNullOrEmpty(url))
                {
                    throw new InvalidOperationException($"Environment variable {ENV_LINKLY_FAKE_URL} is not set");
                }
                return url;
            }
        }

        /// <summary>
        /// The API endpoint for terminals
        /// </summary>
        public string TerminalsEndpoint { get; set; } = "api/terminals";

        /// <summary>
        /// The API endpoint for companies
        /// </summary>
        public string CompaniesEndpoint { get; set; } = "api/companies";

        /// <summary>
        /// The API endpoint for merchant masters
        /// </summary>
        public string MerchantMastersEndpoint { get; set; } = "api/merchantmasters";

        /// <summary>
        /// Timeout in seconds for HTTP requests
        /// </summary>
        public int TimeoutSeconds { get; set; } = 30;
    }
}
