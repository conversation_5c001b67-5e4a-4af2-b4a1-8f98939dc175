﻿using Microsoft.EntityFrameworkCore;
using MMS.Core.CoreUTI;
using MMS.Core.dbContext;
using MMS.Core.Entities;
using MMS.Core.Utils;
using MMS.Model.ApiModelResponse;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MMS.Core.Repository.Impl
{
    public class TerminalModelMasterRepository : BaseRepository<TerminalModelMaster>, ITerminalModelMasterRepository
    {
        public TerminalModelMasterRepository(DbContextFactory contextFactory, MMSContext scopedContext) : base(contextFactory)
        {
            this.SetScopeMMSContext(scopedContext);
        }

        public async Task<IList<DeviceModelsApiModel>> GetModelsByBrandForApiAsync(List<int> brandIds)
        {
            var query = ScopedEntities.AsNoTracking().Where(p => p.IsStatus != Constants.DELETE_RECORD);

            if (brandIds != null && brandIds.Count > 0)
            {
                query = query.Where(p => brandIds.Contains(p.TerminalBrandId)).OrderBy(p => p.OrderIndex);

            }

            var result = query.Select(b => new DeviceModelsApiModel
            {
                Name = b.TerminalModelName
            }.MapBaseProperties(b));

            return await result.ToListAsync();
        }
    }
}
