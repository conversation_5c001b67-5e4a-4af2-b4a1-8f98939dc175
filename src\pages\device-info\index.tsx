import { useEffect, useState } from 'react';
import { useParams, useSearchParams } from 'react-router-dom';
import { DeviceListApi } from '../../apis/device-info.api';
import { AppTemplateApi } from '../../apis/app-template.api';
import { DeviceInfoModel } from '../../models/device-info.model';
import { DeviceTemplate } from '../../models/device-template.model';
import './index.scss';
import {
  Card,
  Table,
  Tabs,
  Button,
  Input,
  Space,
  Form,
  Tag,
  Spin,
  message,
} from 'antd';
import deviceImage from '../../assets/images/Rectangle 8.png';
import FontIcon from '../../components/shared/icons/font-icon';
import AppTemplate from '../../components/app-template';
import { useBreadcrumb } from '../../hooks/useBreadcrumb';
import { ROUTE_PATHS } from '../../constants/router.constants';

import CommonModal from '../../components/shared/modal';
import MerchantSettings from '../../components/template-data';
import { useTranslation } from 'react-i18next';
import showModal from '../../components/shared/notification-modal';

const DeviceInfoContent = ({
  deviceInfo,
  tasks,
  onDeleteTask,
}: {
  deviceInfo: DeviceInfoModel;
  tasks: DeviceTemplate[];
  onDeleteTask: (taskId: number) => void;
}) => {
  useEffect(() => {
    // Listen to the reviewTemplate event from the child component
    const handleReview = (e: Event) => {
      const customEvent = e as CustomEvent;
      const record = customEvent.detail;
      window.dispatchEvent(
        new CustomEvent('showTemplateModal', { detail: record }),
      );
    };

    window.addEventListener('reviewTemplate', handleReview);

    return () => {
      window.removeEventListener('reviewTemplate', handleReview);
    };
  }, []);

  const { t } = useTranslation();

  const columns = [
    {
      title: t('deviceInfo.taskName'),
      dataIndex: 'deviceTemplateName',
      key: 'deviceTemplateName',
      render: (text: string, record: DeviceTemplate) => (
        <div>
          <div className="body-1-bold">{text}</div>
          <div className="body-2-regular">
            {t('deviceInfo.version')}: {record.version}
          </div>
        </div>
      ),
    },
    {
      title: 'Type',
      dataIndex: 'scheduleStringType',
      key: 'scheduleStringType',
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status: number) => {
        const className = getStatusColor(status);
        return (
          <Tag className={`status-tag ${className}`}>
            {getStatusText(status)}
          </Tag>
        );
      },
    },
    {
      title: 'Push Time',
      dataIndex: 'pushTime',
      key: 'pushTime',
    },
    {
      title: 'Action',
      key: 'action',
      render: (_: DeviceTemplate, record: DeviceTemplate) => (
        <Space>
          {record.status === 3 && (
            <Button
              className="action-btn update-btn"
              size="small"
              icon={<FontIcon size={16} className="icon-update" />}
            ></Button>
          )}
          <Button
            onClick={() => {
              // Dispatch the reviewTemplate event to the child component
              window.dispatchEvent(
                new CustomEvent('reviewTemplate', { detail: record }),
              );
            }}
            className="action-btn review-btn"
            size="small"
            icon={<FontIcon size={16} className="icon-review" />}
          ></Button>
          <Button
            onClick={() => onDeleteTask(record.id)}
            className="action-btn delete-btn"
            danger
            size="small"
            icon={<FontIcon size={16} className="icon-delete" />}
          ></Button>
        </Space>
      ),
    },
  ];

  return (
    <div className="device-info-page">
      <div className="content-wrapper">
        <div className="device-image-section">
          <label>Device Image</label>
          <img className="device-image" alt="Device" src={deviceImage} />
        </div>

        <div className="device-info-section">
          <Form layout="vertical" disabled>
            <div className="form-row">
              <Form.Item
                label="Device Name"
                className="body-2-regular form-item"
              >
                <Input value={deviceInfo.deviceName} />
              </Form.Item>
              <Form.Item
                label="Serial Number (SN)"
                className="body-2-regular form-item"
              >
                <Input value={deviceInfo.serialNumber} />
              </Form.Item>
              <Form.Item label="Brand" className="body-2-regular form-item">
                <Input value={deviceInfo.brand} />
              </Form.Item>
            </div>

            <div className="form-row">
              <Form.Item label="Model" className="body-2-regular form-item">
                <Input value={deviceInfo.model} />
              </Form.Item>
              <Form.Item label="Location" className="body-2-regular form-item">
                <Input value={deviceInfo.location} />
              </Form.Item>
              <Form.Item
                label="Location Area"
                className="body-2-regular form-item"
              >
                <Input value={deviceInfo.locationArea} />
              </Form.Item>
            </div>
          </Form>

          <Card title="Tasks" className="tasks-card">
            <Table
              columns={columns}
              dataSource={tasks || []}
              pagination={false}
              rowKey={(record) => record?.id?.toString() || ''}
              bordered={false}
            />
          </Card>
        </div>
      </div>
    </div>
  );
};

const getStatusColor = (status: number): string => {
  switch (status) {
    case 1:
      return 'pending';
    case 2:
      return 'processing';
    case 3:
      return 'error';
    case 4:
      return 'completed';
    default:
      return 'default';
  }
};

const getStatusText = (status: number): string => {
  switch (status) {
    case 1:
      return 'Pending';
    case 2:
      return 'In Progress';
    case 3:
      return 'Failed';
    case 4:
      return 'Completed';
    default:
      return 'Unknown';
  }
};

function DeviceInfoPage() {
  const { terminalMasterId } = useParams<{ terminalMasterId: string }>();
  const [searchParams, setSearchParams] = useSearchParams();
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfoModel | null>(null);
  const [tasks, setTasks] = useState<DeviceTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { updateBreadcrumb } = useBreadcrumb();

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isModalLoading, setIsModalLoading] = useState(false);
  const [templateData, setTemplateData] = useState<Record<string, JsonValue>>(
    {},
  );
  const [currentTask, setCurrentTask] = useState<DeviceTemplate | null>(null);
  const [tabSettings, setTabSettings] = useState<Record<string, JsonValue>>({});
  const [activeModalTab, setActiveModalTab] = useState<string>(() => {
    return localStorage.getItem('deviceInfo-selectedTemplateTab') || 'Terminal';
  });

  const [activeTab, setActiveTab] = useState(
    searchParams.get('tab') || 'deviceInfo',
  );

  const [forceUpdate, setForceUpdate] = useState<number>(0);

  const { t } = useTranslation();

  const fetchData = async () => {
    try {
      if (!terminalMasterId) {
        throw new Error('Terminal Master ID is required');
      }

      const deviceInfoResponse = await DeviceListApi.getDeviceInfo(
        Number(terminalMasterId),
      );
      setDeviceInfo(deviceInfoResponse.data);

      const templateResponse = await DeviceListApi.getDeviceTemplate(
        Number(terminalMasterId),
      );
      setTasks(templateResponse.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [terminalMasterId, searchParams]);

  useEffect(() => {
    if (deviceInfo?.deviceName && deviceInfo?.serialNumber) {
      updateBreadcrumb({
        title: t('breadcrumbs.deviceSetup', {
          deviceName: deviceInfo.deviceName,
          serialNumber: deviceInfo.serialNumber,
        }),
        path: `${ROUTE_PATHS.DeviceInfo}/${terminalMasterId}`,
      }, false);
    }
  }, [
    deviceInfo?.deviceName,
    deviceInfo?.serialNumber,
    terminalMasterId,
    t,
    updateBreadcrumb,
  ]);

  const handleTabChange = (key: string) => {
    setActiveTab(key);
    setSearchParams({ tab: key });
  };

  const handleReviewTemplate = async (record?: DeviceTemplate) => {
    if (!record || !record.id) {
      message.error('Template task information is missing');
      return;
    }

    setCurrentTask(record);
    setIsModalVisible(true);
    setIsModalLoading(true);

    try {
      // Load template data from task
      const { data } = await AppTemplateApi.showDeviceTemplateDataByTask(
        record.id,
      );

      let parsedData = {};
      if (typeof data === 'string') {
        parsedData = JSON.parse(data);
      } else {
        parsedData = data;
      }

      setTemplateData(parsedData as Record<string, JsonValue>);

      // Handle active tab
      if (parsedData && Object.keys(parsedData).length > 0) {
        // Get the saved tab from localStorage
        const savedTab =
          localStorage.getItem('deviceInfo-selectedTemplateTab') || 'Terminal';

        // Check if savedTab exists in data
        if ((parsedData as Record<string, JsonValue>)[savedTab]) {
          setActiveModalTab(savedTab);
        }
        // Check if there is a Terminal tab
        else if ((parsedData as Record<string, JsonValue>)['Terminal']) {
          setActiveModalTab('Terminal');
          localStorage.setItem('deviceInfo-selectedTemplateTab', 'Terminal');
        } else {
          // If there is no tab in localStorage and no Terminal tab, select the first tab
          const firstTabKey = Object.keys(parsedData)[0];
          setActiveModalTab(firstTabKey);
          localStorage.setItem('deviceInfo-selectedTemplateTab', firstTabKey);
        }
      }
    } catch (error) {
      console.error('Error loading template data:', error);
      message.error('Failed to load template data');
    } finally {
      setIsModalLoading(false);
    }
  };

  const handleTabChangeModal = (tabKey: string) => {
    setActiveModalTab(tabKey);
    // Save the selected tab to localStorage
    localStorage.setItem('deviceInfo-selectedTemplateTab', tabKey);

    if (templateData && templateData[tabKey]) {
      setTabSettings(templateData[tabKey] as Record<string, JsonValue>);
    }
  };

  const handleCloseModal = () => {
    setIsModalVisible(false);
  };

  useEffect(() => {
    const handleShowModal = (e: Event) => {
      const customEvent = e as CustomEvent;
      handleReviewTemplate(customEvent.detail);
    };

    window.addEventListener('showTemplateModal', handleShowModal);

    return () => {
      window.removeEventListener('showTemplateModal', handleShowModal);
    };
  }, []);

  useEffect(() => {
    if (templateData && activeModalTab && templateData[activeModalTab]) {
      setTabSettings(templateData[activeModalTab] as Record<string, JsonValue>);
      // Force update to trigger re-render
      setForceUpdate((prev) => prev + 1);
    }
  }, [templateData, activeModalTab]);

  const handleDeleteTask = async (taskId: number) => {
    showModal(
      'delete',
      t('deviceInfo.deleteTask.title'),
      t('deviceInfo.deleteTask.content'),
      {
        okText: t('deviceInfo.deleteTask.okText'),
        cancelText: t('deviceInfo.deleteTask.cancelText'),
        onOk: () => confirmDeleteTask(taskId),
      },
    );
  };

  const confirmDeleteTask = async (taskId: number) => {
    try {
      await AppTemplateApi.cancelAction(taskId);
      message.success(t('deviceInfo.deleteTask.success'));

      // Refresh the task list after deletion
      if (terminalMasterId) {
        const templateResponse = await DeviceListApi.getDeviceTemplate(
          Number(terminalMasterId),
        );
        setTasks(templateResponse.data);
      }
    } catch (error) {
      console.error('Error deleting task:', error);
      message.error(t('deviceInfo.deleteTask.error'));
    }
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  if (!deviceInfo) {
    return <div>No device information found</div>;
  }

  const items = [
    {
      key: 'deviceInfo',
      label: 'Device Info',
      forceRender: false,
      children: (
        <DeviceInfoContent
          deviceInfo={deviceInfo}
          tasks={tasks}
          onDeleteTask={handleDeleteTask}
        />
      ),
    },
    {
      key: 'appTemplate',
      forceRender: false,
      label: 'App Template',
      children: <AppTemplate deviceInfo={deviceInfo} />,
    },
  ];

  return (
    <div className="device-setup">
      <Tabs
        type="card"
        destroyInactiveTabPane
        activeKey={activeTab}
        onChange={handleTabChange}
        items={items}
      />

      <CommonModal
        visible={isModalVisible}
        onCancel={handleCloseModal}
        title={`${t('deviceInfo.taskName')}: ${currentTask?.deviceTemplateName || ''}`}
        width={800}
        footer={null}
        className="template-modal"
      >
        <Spin spinning={isModalLoading}>
          <MerchantSettings
            key={`template-${currentTask?.id}-tab-${activeModalTab}-update-${forceUpdate}`}
            initialTabKey={activeModalTab}
            tabSettings={tabSettings}
            onTabChange={handleTabChangeModal}
            templateData={templateData}
            arrayItemDisplayConfigs={{
              Merchants: 'lszMerchantName',
              Charities: 'lszCharityName',
            }}
            arrayItemLabels={{
              Processors: 'Processor',
              PaymentModes: 'Payment Mode',
              CharityAmounts: 'Charity Amount',
              'CharityAmounts/Amounts': 'Amount',
            }}
          />
        </Spin>
      </CommonModal>
    </div>
  );
}

type JsonValue =
  | string
  | number
  | boolean
  | null
  | { [key: string]: JsonValue }
  | JsonValue[];

export default DeviceInfoPage;
