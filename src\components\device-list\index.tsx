import React, { useEffect, useState, useCallback, useMemo } from 'react';
import { Table, Button, Input, Space, Select, message } from 'antd';
import {
  AssignedDeviceParamsApi,
  DeviceAssociatedResponseApiModel,
  DeviceWithStatus,
  MenuActionDevice,
} from '../../models/device-list.model';
import './index.scss';
import PaginationComponent from '../shared/pagination';
import { getDeviceColumns } from './columns/device-columns';
import {
  DEFAULT_PAGE_SIZE,
  SEARCH_OPTIONS,
  TOTAL_RECORDS,
} from '../../constants/device-list.constants';
import FontIcon from '../shared/icons/font-icon';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { CompanyItemType, LocationLevelId } from '../../constants/app.enums';
import { DeviceListApi } from '../../apis/device-list.api';
import useCompanyTree from '../../hooks/useCompanyTree';
import useStoredLocations from '../../hooks/useStoredLocations';
import MerchantAssignModal from './merchant-assign';
import { MerchantListApi } from '../../apis/merchant.api';
import DeviceAvailableModal from './device-available';
import showNotification from '../shared/notification';
import { ROUTE_PATHS } from '../../constants/router.constants';
import { useRecoilState } from 'recoil';
import { layoutCollapseState } from '../../states/layout';
import { LAYOUT } from '../../constants/app-constants';
import showModal from '../shared/notification-modal';
import FilterDrawer from './filter-drawer';

interface FilterValues {
  merchantIds: number[];
  brandIds: number[];
  modelIds: number[];
  status: boolean | null;
}

interface FilterFormValues {
  merchant: string[];
  brand: string[];
  model: string[];
  status: string[];
}

const DeviceList: React.FC = () => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedDeviceKeys, setSelectedDeviceKeys] = useState<React.Key[]>([]);
  const [selectedMerchantKeys, setSelectedMerchantKeys] = useState<React.Key[]>(
    [],
  );
  const [loading, setLoading] = useState(false);
  const [devices, setDevices] = useState<DeviceAssociatedResponseApiModel[]>(
    [],
  );
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isMerchantModalVisible, setIsMerchantModalVisible] = useState(false);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
  const [totalRecord, setTotalRecord] = useState(TOTAL_RECORDS);
  const [pageNumber, setPageNumber] = useState(1);
  const [selectedDevice, setSelectedDevice] =
    useState<DeviceAssociatedResponseApiModel | null>(null);
  const [searchType, setSearchType] = useState('sn');
  const [searchValue, setSearchValue] = useState('');
  const [tempSearchValue, setTempSearchValue] = useState('');
  const navigate = useNavigate();
  const { lastSelectedItem, parentItem } = useCompanyTree();

  const { storedLocations, getLocationIdsString } = useStoredLocations();
  const locationArea = storedLocations.find(
    (item) => item.level === LocationLevelId.LocationAreas,
  );
  const [layoutCollapse] = useRecoilState(layoutCollapseState);

  const { t } = useTranslation();

  const [isFilterDrawerOpen, setIsFilterDrawerOpen] = useState(false);
  const [filterValues, setFilterValues] = useState<FilterValues>({
    merchantIds: [],
    brandIds: [],
    modelIds: [],
    status: null,
  });

  const transformFilterFormValues = (
    values: FilterFormValues,
  ): FilterValues => {
    const { merchant, brand, model, status } = values;

    // Transform status
    let transformedStatus: boolean | null = null;
    if (status && status.length > 0) {
      if (status.length === 2) {
        transformedStatus = null; // Both active and inactive selected
      } else if (status.includes('active')) {
        transformedStatus = true;
      } else if (status.includes('inactive')) {
        transformedStatus = false;
      }
    }

    return {
      merchantIds: merchant.map(Number),
      brandIds: brand.map(Number),
      modelIds: model.map(Number),
      status: transformedStatus,
    };
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  const openAssginDevice = () => {
    let locationArea = storedLocations.find(
      (item) => item.level === LocationLevelId.LocationAreas,
    );

    if (!parentItem) {
      if (!parentItem) {
        showModal(
          'warning',
          t('notification.warning'),
          t('notification.missingCompanyOrGroupDescription'),
        );
        return;
      }
      return;
    }

    if (!locationArea) {
      showModal(
        'warning',
        t('notification.warning'),
        t('notification.missingLocationAreaDescription'),
      );
      return;
    }
    setIsModalVisible(true);
  };

  const handleMerchantModalCancel = () => {
    setIsMerchantModalVisible(false);
    setSelectedDevice(null);
    setSelectedMerchantKeys([]);
  };

  const fetchDeviceData = useCallback(async () => {
    setLoading(true);

    try {
      const locationIds = getLocationIdsString();
      let companyId;
      let groupId;
      let companyGroupMappingId;

      if (lastSelectedItem?.itemType == CompanyItemType.Company) {
        companyId = lastSelectedItem.orgId;
        groupId = undefined;
        companyGroupMappingId = lastSelectedItem?.id;
      } else {
        companyId = parentItem?.orgId;
        groupId = lastSelectedItem?.orgId;
      }

      const requestParams: AssignedDeviceParamsApi = {
        locationIdsSelected: locationIds,
        companyId: companyId,
        groupId: groupId,
        pageNumber,
        pageSize,
        searchValue: searchValue,
        searchType: searchType as 'sn' | 'brand' | 'model',
        deviceBrandId: '',
        deviceModelId: '',
        merchantId: '',
        ...(filterValues.brandIds.length > 0 && {
          deviceBrandId: filterValues.brandIds.join(','),
        }),
        ...(filterValues.modelIds.length > 0 && {
          deviceModelId: filterValues.modelIds.join(','),
        }),
        ...(filterValues.merchantIds.length > 0 && {
          merchantId: filterValues.merchantIds.join(','),
        }),
        ...(filterValues.status !== null && {
          isActivate: filterValues.status,
        }),
        companyGroupMappingId: companyGroupMappingId,
      };

      const { data: responseData } =
        await DeviceListApi.getDeviceAssociated(requestParams);

      if (responseData) {
        setDevices(responseData.data);
        setTotalRecord(responseData.totalRecords);
      }
    } catch (error) {
      console.error('Error fetching devices:', error);
    } finally {
      setLoading(false);
    }
  }, [
    pageNumber,
    pageSize,
    storedLocations,
    lastSelectedItem,
    parentItem,
    searchValue,
    searchType,
    filterValues,
  ]);

  // Reset page number when company, group, or locations change
  useEffect(() => {
    setPageNumber(1);
  }, [lastSelectedItem, parentItem, storedLocations]);

  useEffect(() => {
    fetchDeviceData();
  }, [fetchDeviceData]);

  // Function to handle unassigning a device
  const handleUnassignDevice = (record: DeviceAssociatedResponseApiModel) => {
    showModal(
      'delete',
      t('device.confirmUnassignTitle'),
      t('device.confirmUnassignMessage'),
      {
        okText: t('device.confirmUnassignOk'),
        onOk: () => confirmUnassignDevice(record),
        cancelText: t('device.confirmUnassignCancel'),
      },
    );
  };

  const confirmUnassignDevice = async (
    deviceToProcess?: DeviceAssociatedResponseApiModel,
  ) => {
    if (!deviceToProcess) {
      console.error('No device selected for unassignment');
      return;
    }

    setLoading(true);
    try {
      await DeviceListApi.unassignDevicesFromLocation([deviceToProcess.id]);

      showNotification(
        'success',
        t('notification.success'),
        t('device.deviceUnassignedSuccess'),
      );

      fetchDeviceData();
    } catch (error) {
      showNotification(
        'error',
        t('notification.error'),
        t('device.deviceUnassignedError'),
      );
    } finally {
      setLoading(false);
    }
  };

  const handleMenuClick = (
    key: string,
    record: DeviceAssociatedResponseApiModel,
  ) => {
    if (key === MenuActionDevice.Delete) {
      handleUnassignDevice(record);
    } else if (key === MenuActionDevice.Details) {
      navigate(`${ROUTE_PATHS.DeviceInfo}/${record.id}`);
    } else if (key === MenuActionDevice.AssignMerchants) {
      setSelectedDevice(record);
      setIsMerchantModalVisible(true);
    }
  };

  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const onDeviceSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedDeviceKeys(newSelectedRowKeys);
  };

  const onMerchantSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedMerchantKeys(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  const deviceRowSelection = {
    selectedRowKeys: selectedDeviceKeys,
    onChange: onDeviceSelectChange,
  };

  const merchantRowSelection = {
    selectedRowKeys: selectedMerchantKeys,
    onChange: onMerchantSelectChange,
    preserveSelectedRowKeys: true, // Add this to preserve selections
  };

  const handleAssignMerchants = async () => {
    if (!selectedDevice) return;

    try {
      setLoading(true);
      await MerchantListApi.assignMerchants(
        selectedDevice.id,
        selectedMerchantKeys as number[],
      );
      message.success(t('device.merchantsAssignedSuccess'));
      fetchDeviceData();
      setIsMerchantModalVisible(false);
    } catch (error) {
      console.error('Error assigning merchants:', error);
      message.error(t('device.merchantsAssignedError'));
    } finally {
      setLoading(false);
    }
  };

  const tableScrollHeight = useMemo(() => {
    const viewportHeight = window.innerHeight;
    const totalHeight = !layoutCollapse.locationFilterCollapsed
      ? LAYOUT.HEADER_FOOTER_HEIGHT
      : LAYOUT.HEADER_FOOTER_HEIGHT + LAYOUT.LOCATION_FILTER_HEIGHT;

    return Math.max(viewportHeight - totalHeight, 170);
  }, [layoutCollapse.locationFilterCollapsed]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTempSearchValue(e.target.value);
  };

  const handleSearch = () => {
    setSearchValue(tempSearchValue);
    setPageNumber(1);
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const handleSearchTypeChange = (value: string) => {
    setSearchType(value);
  };

  const handleFilterDrawerClose = () => {
    setIsFilterDrawerOpen(false);
  };

  const handleFilterChange = (values: FilterFormValues) => {
    const transformedValues = transformFilterFormValues(values);
    setFilterValues(transformedValues);
    setPageNumber(1);
  };

  return (
    <div className="device-list">
      <div className="device-list-header">
        <span className="body-2-bold">{t('device.deviceList')}</span>
        <div className="device-list-actions">
          <Space size={8}>
            <Button
              type="text"
              icon={<FontIcon size={16} className="icon-filter" />}
              style={{
                width: 16,
                height: 16,
                border: 'none',
              }}
              onClick={() => setIsFilterDrawerOpen(true)}
            />
            <Space.Compact>
              <Select
                value={searchType}
                options={SEARCH_OPTIONS}
                className="body-2-regular search-select"
                onChange={handleSearchTypeChange}
              />
              <Input
                placeholder={`${t('common.search')} by ${SEARCH_OPTIONS.find((opt) => opt.value === searchType)?.label}`}
                className="body-2-regular"
                allowClear
                value={tempSearchValue}
                onChange={handleSearchChange}
                onPressEnter={handleKeyPress}
              />
            </Space.Compact>
            <Button
              onClick={openAssginDevice}
              type="primary"
              icon={<FontIcon size={14} className="icon-plus" />}
            >
              {t('device.assignDevice')}
            </Button>
          </Space>
        </div>
      </div>

      <div className="scroll-overlay-container">
        <div className="scroll-overlay-content">
          <Table
            className="device-assigned"
            rowSelection={rowSelection}
            columns={getDeviceColumns((key, record) =>
              handleMenuClick(key, record as DeviceWithStatus),
            )}
            dataSource={devices}
            rowKey="id"
            loading={loading}
            pagination={false}
            scroll={
              devices && devices.length > 0
                ? { y: tableScrollHeight, x: 'max-content' }
                : undefined
            }
          />
        </div>
      </div>

      <div className="device-list-pagination">
        {devices && (
          <PaginationComponent
            totalRecords={totalRecord}
            pageSize={pageSize}
            setPageSize={setPageSize}
            pageNumber={pageNumber}
            setPageNumber={setPageNumber}
          />
        )}
      </div>

      <DeviceAvailableModal
        visible={isModalVisible}
        onCancel={handleCancel}
        deviceRowSelection={deviceRowSelection}
        groupId={lastSelectedItem?.orgId}
        locationAreaId={locationArea?.id}
        companyId={parentItem?.orgId}
        onSuccess={fetchDeviceData}
      />

      <MerchantAssignModal
        visible={isMerchantModalVisible}
        onCancel={handleMerchantModalCancel}
        onSave={handleAssignMerchants}
        selectedDevice={selectedDevice}
        merchantRowSelection={merchantRowSelection}
      />

      <FilterDrawer
        open={isFilterDrawerOpen}
        onClose={handleFilterDrawerClose}
        onFilterChange={handleFilterChange}
      />
    </div>
  );
};

export default DeviceList;
