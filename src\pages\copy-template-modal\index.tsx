import { Button } from 'antd';
import { useEffect, useState } from 'react';
import './index.scss';

import FontIcon from '../../components/shared/icons/font-icon';
import { useTranslation } from 'react-i18next';
import CommonModal from '../../components/shared/modal';
import CopyTemplate from './copy-template';
import showNotification from '../../components/shared/notification';
import { AppBaseTemplateApi } from '../../apis/app-base-template.api';
import showModal from '../../components/shared/notification-modal';
import { DataItem } from '../../components/shared/column';

interface CopyTemplateModalProps {
  buttonText: string;
  companyId: number;
  templateId: number;
  isNewTemplate: boolean;
  templateName: string;
  onCopySuccess?: () => void;
}

const CopyTemplateModal = ({
  buttonText,
  companyId,
  templateId,
  isNewTemplate,
  templateName,
  onCopySuccess,
}: CopyTemplateModalProps) => {
  const { t } = useTranslation();
  const [_, setFormKey] = useState(0);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<DataItem | null>(
    null,
  );

  const showModalCopy = () => {
    setFormKey((prevKey) => prevKey + 1);
    setIsModalOpen(true);
  };

  const handleCopy = async () => {
    if (!selectedTemplate) {
      showNotification(
        'warning',
        t('appBaseTemplate.actions.pleaseSelectTemplate'),
      );
      return;
    }

    setIsModalOpen(false);

    var content = '';
    if (isNewTemplate) {
      content = t('appBaseTemplate.actions.copyContent');
    } else {
      content = t('appBaseTemplate.actions.copyExistingTemplateContent', {
        targetTemplate: templateName,
        sourceTemplate: selectedTemplate?.label,
      });
    }

    showModal('confirm', t('appBaseTemplate.actions.copyTitle'), content, {
      okText: t('common.copy'),
      onOk: () => confirmCopyTemplate(selectedTemplate),
      cancelText: t('common.cancel'),
    });
  };

  const confirmCopyTemplate = async (selectedTemplate: DataItem) => {
    if (!selectedTemplate) return;

    const result = await AppBaseTemplateApi.copyTemplate(
      Number(selectedTemplate.key),
      templateId,
    );

    if (result.status === 200) {
      showNotification('success', t('appBaseTemplate.actions.copySuccess'));
      if (onCopySuccess) {
        onCopySuccess();
      }
    } else {
      showNotification('error', t('appBaseTemplate.actions.copyError'));
    }
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  useEffect(() => {
    if (!isModalOpen) {
      setSelectedTemplate(null);
    }
  }, [isModalOpen]);

  return (
    <div className="copy-template-modal">
      <Button
        type="text"
        onClick={showModalCopy}
        icon={<FontIcon size={16} className="icon-copy" />}
      >
        {buttonText}
      </Button>

      <CommonModal
        visible={isModalOpen}
        onSave={handleCopy}
        onCancel={handleCancel}
        title={t('appBaseTemplate.setup.copyTemplate')}
        width={920}
        children={
          <CopyTemplate
            companyId={companyId}
            onTemplateSelect={setSelectedTemplate}
          />
        }
        saveText={t('common.copy')}
      ></CommonModal>
    </div>
  );
};

export default CopyTemplateModal;
