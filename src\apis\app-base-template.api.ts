import { AxiosResponse } from 'axios';
import {
  getAsync,
  postAsync,
  putAsync,
  deleteAsync,
  patchAsync,
} from './http-client';
import { CompanyGroupMappingType } from '../constants/app.enums';
import { DataItemResponse } from '../models/common.model';
import {
  AppBaseTemplateInfoResponse,
  ValidateTempleteResponse,
} from '../models/app-template.model';

const baseUrl = import.meta.env.VITE_MMS_API_URL;

const getCompanyGroupMapping = (
  parentId: number,
  type: CompanyGroupMappingType,
): Promise<AxiosResponse<DataItemResponse[]>> => {
  const url = `${baseUrl}/company-details/load-company-application-mapping?parentId=${parentId}&type=${type}`;
  return getAsync(url);
};

const getAppBaseTemplate = (
  companyGroupMappingId: number,
): Promise<AxiosResponse<DataItemResponse[]>> => {
  const url = `${baseUrl}/company-details/load-base-application-template?companyGroupMappingId=${companyGroupMappingId}`;
  return getAsync(url);
};

const getTemplateInfoById = (
  templateId: number,
  companyId: number,
): Promise<AxiosResponse<AppBaseTemplateInfoResponse>> => {
  const url = `${baseUrl}/app-template/get-base-template-info-by-id?templateId=${templateId}&companyId=${companyId}`;
  return getAsync(url);
};

const updateBaseTemplateName = (
  templateId: number,
  templateName: string,
  isNewTemplate: boolean,
): Promise<AxiosResponse<void>> => {
  const url = `${baseUrl}/app-template/update-template-name`;
  return putAsync(url, { templateId, templateName, isNewTemplate });
};

const addAppBaseTemplate = (
  companyGroupMappingId: number,
): Promise<AxiosResponse<number>> => {
  const url = `${baseUrl}/app-template/add-base-template`;
  return postAsync(url, { companyGroupMappingId });
};

const deleteAppBaseTemplate = (
  templateId: number,
): Promise<AxiosResponse<void>> => {
  const url = `${baseUrl}/app-template/delete-base-template/${templateId}`;
  return deleteAsync(url);
};

const activateDeactivateBaseTemplate = (
  templateId: number,
  isActivate: boolean,
): Promise<AxiosResponse<void>> => {
  const url = `${baseUrl}/app-template/activate-deactivate-base-template`;
  return patchAsync(url, { isActivate, templateId });
};

const validateTemplate = (
  templateId: number,
): Promise<AxiosResponse<ValidateTempleteResponse>> => {
  const url = `${baseUrl}/app-template/validate-template/${templateId}`;
  return getAsync(url);
};

const copyTemplate = (
  sourceTemplateId: number,
  targetTemplateId: number,
): Promise<AxiosResponse<void>> => {
  const url = `${baseUrl}/app-template/copy-base-template`;
  return postAsync(url, { sourceTemplateId, targetTemplateId });
};

export const AppBaseTemplateApi = {
  getCompanyGroupMapping,
  getAppBaseTemplate,
  getTemplateInfoById,
  updateBaseTemplateName,
  addAppBaseTemplate,
  deleteAppBaseTemplate,
  activateDeactivateBaseTemplate,
  validateTemplate,
  copyTemplate,
};
