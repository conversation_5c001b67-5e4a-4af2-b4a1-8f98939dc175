import React, { useState, useCallback, useEffect } from 'react';
import { useResizeDetector } from 'react-resize-detector';
import {
  ComposableMap,
  Geographies,
  Geography,
  ZoomableGroup,
} from 'react-simple-maps';
import geography from '../../../../assets/json/geography.json';
import { MAP_CONFIG } from '../../../../constants/app-constants';
import { countryData, regionData } from './data/map.data';
import { Position } from '../../../../models/map.model';
import MapTooltip from './components/MapTooltip';
import MapLegend from './components/MapLegend';
import './index.scss';
import { Spin } from 'antd';

const TerminalMap: React.FC = () => {
  const { width, height, ref } = useResizeDetector();
  const [isLoading, setIsLoading] = useState(true);
  const [position, setPosition] = useState<Position>({
    coordinates: MAP_CONFIG.defaultCoordinates,
    zoom: MAP_CONFIG.defaultZoom,
  });
  const [tooltipContent, setTooltipContent] = useState<string>('');

  const handleZoomEnd = useCallback((position: Position) => {
    setPosition(position);
  }, []);

  const handleGeographyHover = useCallback((countryName: string) => {
    const country = countryData[countryName];
    if (country) {
      setTooltipContent(
        `${country.name}: ${country.terminals.toLocaleString()} terminals`,
      );
    } else {
      setTooltipContent(countryName);
    }
  }, []);

  const handleGeographyLeave = useCallback(() => {
    setTooltipContent('');
  }, []);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <>
      <div ref={ref} style={{ width: '100%', height: '100%', padding: 12 }}>
        <div className="map-title">Terminal Map</div>
        {isLoading && <Spin className="loading-container" />}
        {!isLoading && (
          <div className="map-content">
            <div className="map-container">
              <ComposableMap
                projection={MAP_CONFIG.projection}
                projectionConfig={MAP_CONFIG.projectionConfig}
                width={width}
                height={height}
                style={{
                  width: width ? width - 197 : '100%',
                  height: height ? height - 48 : '100%',
                }}
              >
                <ZoomableGroup
                  zoom={position.zoom}
                  center={position.coordinates}
                  onMoveEnd={handleZoomEnd}
                >
                  <Geographies geography={geography}>
                    {({ geographies }) =>
                      geographies.map((geo) => {
                        const countryName = geo.properties.name;
                        const country = countryData[countryName];
                        return (
                          <Geography
                            key={geo.rsmKey}
                            geography={geo}
                            fill={country?.active ? '#0A8FDC' : '#EAEAEC'}
                            stroke="#D6D6DA"
                            style={{
                              default: { outline: 'none' },
                              hover: {
                                fill: country?.active ? '#66baea' : '#66baea',
                                outline: 'none',
                                cursor: 'pointer',
                              },
                              pressed: {
                                fill: country?.active ? '#0A8FDC' : '#66baea',
                                outline: 'none',
                              },
                            }}
                            onMouseEnter={() =>
                              handleGeographyHover(countryName)
                            }
                            onMouseLeave={handleGeographyLeave}
                          />
                        );
                      })
                    }
                  </Geographies>
                </ZoomableGroup>
              </ComposableMap>
              <MapTooltip content={tooltipContent} />
            </div>
            <MapLegend regions={regionData} />
          </div>
        )}
      </div>
    </>
  );
};

export default TerminalMap;
