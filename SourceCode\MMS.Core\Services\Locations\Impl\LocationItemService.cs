﻿using MMS.Core.Entities;
using MMS.Core.Entities.Locations;
using MMS.Core.Repository;
using MMS.Core.Repository.Impl;
using MMS.Core.Services;
using MMS.Core.Services.Impl.Base;
using MMS.Core.CoreUTI;
using MMS.Core.CoreUTI.Enum;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using MMS.Core.Services.Base;
using System.Threading.Tasks;
using MMS.Model.ApiModelResponse;
using MMS.Model.ApiModelRequest;

namespace MMS.Core.Services.Locations.Impl
{
    public class LocationItemService : BaseService<LocationItem>, ILocationItemService
    {
        private readonly ILocationRepo _locationRepo;
        private readonly ILocationService _locationService;
        private readonly IBaseRepository<LocationItemParentChildrenMap> _locationItemParentChildrenMapRepository;
        private readonly IBaseRepository<LocationAgeRangeConfig> _locationAgeRangeConfigRepository;
        private readonly IBaseRepository<LocationNationalityConfig> _locationNationalityConfigRepository;
        private readonly IBaseRepository<LocationEthnicityConfig> _locationEthnicityConfigRepository;
        private readonly IBaseRepository<LocationIncomeRangeConfig> _locationIncomeRangeConfigRepository;
        private readonly IBaseRepository<LocationLanguageSpokenConfig> _locationLanguageSpokenConfigRepository;
        private readonly IBaseRepository<LocationGenderConfig> _locationGenderConfigRepository;
        private readonly IBaseRepository<LocationSexualPreferenceConfig> _locationSexualPreferenceConfigRepository;
        private readonly ILocationItemSelectService _locationItemSelectService;
        private readonly ILocationItemRepository _locationItemRepository;

        public LocationItemService( //NOSONAR
            IBaseRepository<LocationItem> locationRepository, ILanguageExecuteService languageExecuteService,
            ILocationItemSelectService locationItemSelectService, ILocationItemRepository locationItemRepository,
            ILocationService locationService, IBaseRepository<LocationItemParentChildrenMap> locationItemParentChildrenMapRepository,
            IBaseRepository<LocationAgeRangeConfig> locationAgeRangeConfigRepository,
            IBaseRepository<LocationNationalityConfig> locationNationalityConfigRepository = null,
            IBaseRepository<LocationEthnicityConfig> locationEthnicityConfigRepository = null, IBaseRepository<LocationIncomeRangeConfig> locationIncomeRangeConfigRepository = null,
            IBaseRepository<LocationLanguageSpokenConfig> locationLanguageSpokenConfigRepository = null, IBaseRepository<LocationGenderConfig> locationGenderConfigRepository = null,
            IBaseRepository<LocationSexualPreferenceConfig> locationSexualPreferenceConfigRepository = null, ILocationRepo locationRepo = null) :
            base(locationRepository, languageExecuteService)
        {
            _locationService = locationService;
            _locationItemParentChildrenMapRepository = locationItemParentChildrenMapRepository;
            _locationAgeRangeConfigRepository = locationAgeRangeConfigRepository;
            _locationNationalityConfigRepository = locationNationalityConfigRepository;
            _locationEthnicityConfigRepository = locationEthnicityConfigRepository;
            _locationIncomeRangeConfigRepository = locationIncomeRangeConfigRepository;
            _locationLanguageSpokenConfigRepository = locationLanguageSpokenConfigRepository;
            _locationGenderConfigRepository = locationGenderConfigRepository;
            _locationSexualPreferenceConfigRepository = locationSexualPreferenceConfigRepository;
            _locationRepo = locationRepo;
            _locationItemSelectService = locationItemSelectService;
            _locationItemRepository = locationItemRepository;
        }

        public IQueryable<LocationItem> GetQueryable(Expression<Func<LocationItem, bool>> expression = null, bool includeDeleted = false, bool includeHardCoded = false,
            bool includeDeactivated = false)
        {
            var query = base.GetAll(expression, includeDeleted, includeDeactivated, null, false, 
                p=>p.LocationLevelPOSDeviceUsed, 
                p=>p.Parent.Location, 
                p=>p.AddressLocalItem,
                p =>p.Parents,
                p=>p.Parent.Parent, 
                p=>p.BusinessType, 
                p=>p.Location,
                p=>p.Parent.BusinessType
                ).AsQueryable();
            if (!includeDeactivated)
            {
                query = query.Where(p => !p.DeActivate);
            }

            return query;
        }
        
        public IList<LocationItem> GetAll(Expression<Func<LocationItem, bool>> expression = null, bool includeDeleted = false, bool includeHardCoded = false,
            bool includeDeactivated = false)
        {
            return GetQueryable(expression, includeDeleted, includeHardCoded, includeDeactivated).ToList();
        }

        public void Insert(LocationItem entity)
        {
            ArgumentNullException.ThrowIfNull(entity);

            base.Insert(entity);
            entity.Hierarchy = GenerateHierarchy(entity);
            base.Update(entity);
        }

        public void Insert(IEnumerable<LocationItem> entities, bool noTrack = false)
        {
            ArgumentNullException.ThrowIfNull(entities);

            foreach (var entity in entities)
            {
                entity.Hierarchy = GenerateHierarchy(entity);
            }

            base.InsertAll(entities.ToList());
        }

        public virtual IList<LocationItem> GetAllIndustry()
        {
            var query = base.GetAll(p=>p.Location.LocationLevel == LocationLevel.Industry, false, false, null, false, 
                p=>p.LocationLevelPOSDeviceUsed, 
                p=>p.Parent.Location, 
                p=>p.AddressLocalItem,
                p =>p.Parents,
                p=>p.Parent.Parent, 
                p=>p.BusinessType, 
                p=>p.Location).AsQueryable().OrderBy(p=>p.Name);

            return _languageExecuteService.GetLanguage(query.ToList());
        }

        public IList<LocationItem> GetAllLocations()
        {
            var query = base.GetAll(p=>p.Location.LocationLevel == LocationLevel.Locations, false, false, null, false, 
                p=>p.LocationLevelPOSDeviceUsed, 
                p=>p.Parent.Location, 
                p=>p.AddressLocalItem,
                p =>p.Parents,
                p=>p.Parent.Parent, 
                p=>p.BusinessType, 
                p=>p.Location).AsQueryable().OrderBy(p=>p.Name);

            return _languageExecuteService.GetLanguage(query.ToList());
        }
        
        public IList<LocationItem> GetAllLocationAreas()
        {
            var query = base.GetAll(p=>p.Location.LocationLevel == LocationLevel.LocationAreas, false, false, null, false, 
                p=>p.LocationLevelPOSDeviceUsed, 
                p=>p.Parent.Location, 
                p=>p.AddressLocalItem,
                p =>p.Parents,
                p=>p.Parent.Parent, 
                p=>p.BusinessType, 
                p=>p.Location).AsQueryable().OrderBy(p=>p.Name);

            return _languageExecuteService.GetLanguage(query.ToList());
        }
        
        
        public IList<LocationItem> GetAllLocationItemsByLevel(LocationLevel locationLevel)
        {
            var query = base.GetAll(p=>p.Location.LocationLevel == locationLevel, false, false, null, false, 
                p=>p.LocationLevelPOSDeviceUsed, 
                p=>p.Parent.Location, 
                p=>p.AddressLocalItem,
                p =>p.Parents,
                p=>p.Parent.Parent, 
                p=>p.BusinessType, 
                p=>p.Location).AsQueryable().OrderBy(p=>p.Name);

            return _languageExecuteService.GetLanguage(query.ToList());
        }

        public IDictionary<int, string> GetIndustryList()
        {
            return GetAllIndustry().ToDictionary(p => p.Id, p => p.Name);
        }

        public IList<LocationItem> GetAllChain()
        {
            var query = GetQueryable(p => p.Parent != null && p.Parent.Parent == null
                                          && p.Location.LocationLevel == LocationLevel.Chain);

            return _languageExecuteService.GetLanguage(query.ToList());
        }

        public IList<LocationItem> GetAllByLocationLevel(LocationLevel locationLevel)
        {
            var query = GetQueryable(p => p.Location.LocationLevel == locationLevel);

            return _languageExecuteService.GetLanguage(query.ToList());
        }

        public IList<LocationItem> GetAppsAlongEcommerce(bool isEcommerce)
        {
            var query = GetQueryable(p => p.Location.LocationLevel == LocationLevel.Applications)
                .Where(p => p.ApplicationUsed.IsEcommerce == isEcommerce);

            return _languageExecuteService.GetLanguage(query.ToList());
        }

        public IList<LocationItem> GetChainByIndustry(LocationItem industry)
        {
            var industryId = 0;

            if (industry != null && industry.Location.LocationLevel == LocationLevel.Industry)
            {
                industryId = industry.Id;
            }

            return GetChainByIndustry(industryId);
        }

        public IList<LocationItem> GetChainByIndustry(int industryId)
        {
            var query = GetQueryable(p => p.ParentId == industryId && p.Location.LocationLevel == LocationLevel.Chain);

            return _languageExecuteService.GetLanguage(query.ToList());
        }

        public IList<LocationItem> GetByParent(LocationItem parent)
        {
            var query = GetQueryable(p => p.Parent.Id == parent.Id);

            return _languageExecuteService.GetLanguage(query.ToList());
        }

        public async Task<IList<LocationItem>> GetLocationSource(int parentId, LocationLevel locationLevel)
        {
            var query = await _locationItemRepository.GetLocationItemSource(parentId, locationLevel);

            return _languageExecuteService.GetLanguage(query.ToList());
        }

        public IList<LocationItem> GetByIds(IEnumerable<int> ids)
        {
            var query = GetQueryable(p => ids.Contains(p.Id));

            return query.ToList();
        }

        public IList<LocationItem> GetByIds(IEnumerable<int> ids, bool isActivate)
        {
            var query = GetQueryable(p => ids.Contains(p.Id) && p.DeActivate == !isActivate);

            return query.ToList();
        }

        public LocationItem GetActiveApplicationByDevice(string serialNumber, int applicationId)
        {
            var query = GetQueryable(b => b.LocationLevelPOSDeviceUsed != null
                                          && b.Location.LocationLevel == LocationLevel.DevicesUsed
                                          && !b.DeActivate);

            query = query.Where(b => b.ApplicationForDeviceConfigDevice.Any(p => p.AppId == applicationId && p.IsActive && b.IsStatus != Constants.DELETE_RECORD)); // Show
            query = query.Where(b => b.Children.Any(p => p.ChildLocationId == applicationId && p.IsActive && b.IsStatus != Constants.DELETE_RECORD)); // Assigned

            return query.SelectMany(p => p.Children.Select(p1 => p1.ChildLocation)).FirstOrDefault(p => p.Id == applicationId);
        }

        public IList<LocationItem> GetMenuByParents(int applicationId, int locationId)
        {
            var query = GetQueryable(p => p.ParentId == applicationId && p.LocationParentId == locationId);

            return _languageExecuteService.GetLanguage(query.ToList());
        }

        public bool ApplicationByDeviceExists(string serialNumber, int applicationId)
        {
            var query = GetQueryable(b => b.LocationLevelPOSDeviceUsed != null
                                          && b.LocationLevelPOSDeviceUsed.SerialNumber.Trim() == serialNumber.Trim()
                                          && b.Location.LocationLevel == LocationLevel.DevicesUsed
                                          && !b.DeActivate);

            query = query.Where(b => b.ApplicationForDeviceConfigDevice.Any(p => p.AppId == applicationId && p.IsActive && b.IsStatus != Constants.DELETE_RECORD)); // Show
            query = query.Where(b => b.Children.Any(p => p.ChildLocationId == applicationId && p.IsActive && b.IsStatus != Constants.DELETE_RECORD)); // Assigned

            return query.Any();
        }

        public IList<LocationItem> GetLocationItemById(int locationItemId, int locationId)
        {
            var result = new List<LocationItem>();
            var locationItem = GetById(locationItemId);

            // Get locationItem for locationItems variable.
            if (locationItem == null)
            {
                var locationitems = GetQueryable(p => p.Location.Id.Equals(locationId)).ToList();
                if (locationitems.Any())
                {
                    result.AddRange(locationitems);
                }

                return result;
            }

            result.Add(locationItem);
            return result;
        }     

        public IList<LocationItem> GetByExceptionIds(int[] ids)
        {
            return GetQueryable(p =>
                !p.DeActivate
                && ids.Contains(p.Id)
                ).ToList();
        }

        public LocationItem FindRoot(LocationItem item)
        {
            var temp = item;

            while (temp.Parent != null)
            {
                temp = temp.Parent;
            }
            return temp;
        }

        public LocationItem FindChain(LocationItem item)
        {
            if (item.Location.LocationLevel.Equals(LocationLevel.Industry))
            {
                return null; // Need to check this isn't Industry.
            }

            var temp = item;

            while (temp.Location.LocationLevel != LocationLevel.Chain && temp.Parent != null)
            {
                temp = temp.Parent;
            }
            return temp;
        }

        public LocationItem FindLevel(LocationItem item, LocationLevel level)
        {
            if (item.Location.LocationLevel < level)
            {
                return null; // Unavailable
            }

            if (item.Location.LocationLevel == level)
            {
                return item; // this is result.
            }

            var temp = item;

            while (temp.Location.LocationLevel != level && temp.Parent != null)
            {
                temp = temp.Parent;
            }
            return temp;
        }

        public LocationItem GetIndustryByLocation(LocationItem locationItem)
        {
            if (locationItem != null && locationItem.Parent == null && locationItem.LocationParent == null)
            {
                return locationItem;
            }

            if (locationItem == null)
            {
                return null;
            }

            if (locationItem.Location.LocationLevel == LocationLevel.SpecialMenus)
            {
                return GetIndustryByLocation(locationItem.LocationParent);
            }

            if (String.IsNullOrWhiteSpace(locationItem.Hierarchy))
            {
                var industry = FindRoot(locationItem);

                if (industry == null || industry.Location.LocationLevel != LocationLevel.Industry)
                {
                    return null;
                }

                return industry;
            }

            var parentIds = locationItem.Hierarchy.Split(',').Select(int.Parse);

            var query = GetQueryable(b => parentIds.Contains(b.Id)
                                          && b.Location.LocationLevel == LocationLevel.Industry);

            return query.SingleOrDefault();
        }

        public LocationItem GetIndustryByLocation(int locationItemId)
        {
            var locationItem = GetById(locationItemId);

            if (locationItem == null)
            {
                return null;
            }

            return GetIndustryByLocation(locationItem);
        }

        public IList<LocationItem> ClearRestrictLocationItemBelow(IList<LocationItem> locationItems)
        {
            var locaiontItemChilds = new List<LocationItem>();

            foreach (var each in locationItems)
            {
                var temp = each;

                temp = temp.Parent;

                while (temp != null)
                {
                    var flag = locationItems.Any(p => p.Id == temp.Id);

                    if (flag)
                    {
                        locaiontItemChilds.Add(each);
                        break;
                    }

                    temp = temp.Parent;
                }
            }

            foreach (var each in locaiontItemChilds)
            {
                locationItems.Remove(each);
            }
            return locationItems;
        }

        public bool IsChild(LocationItem locationItem, LocationItem parent)
        {
            while (locationItem.Parent != parent && locationItem.Parent != null)
            {
                locationItem = locationItem.Parent;
            }

            return locationItem.Parent != null;
        }

        public int CoutByLocation(int locationId)
        {
            var query = GetQueryable(b => b.LocationId == locationId);

            return query.Count();
        }

        public void GenerateHierarchyForAllItem()
        {
            var entities = GetAll(includeDeactivated: true);

            foreach (var each in entities)
            {
                each.Hierarchy = GenerateHierarchy(each);
            }

            UpdateAll(entities);
        }

        public string GenerateHierarchy(LocationItem locationItem)
        {
            if (locationItem.LocationId == 0 || locationItem.ParentId == null)
            {
                return string.Empty;
            }

            var parent = GetById(locationItem.ParentId.Value, p=>p.Location, p=>p.Parent);
            if (parent == null)
            {
                return string.Empty;
            }

            if (parent.Location.LocationLevel >= LocationLevel.DevicesUsed)
            {
                return string.Empty;// Object is App, menu, special menu.
            }

            var hierarchyArr = new List<int>();
            do
            {
                hierarchyArr.Add(parent.Id);
                parent = GetById(parent.ParentId.GetValueOrDefault());
            } while (parent != null);


            var hierarchy = UtilConvert.MergeIntToString(hierarchyArr);

            return hierarchy;
        }

        public LocationItem GetParentAtLevel(LocationItem locationItem, LocationLevel level)
        {
            if (locationItem.Location.LocationLevel == level)
            {
                return locationItem; // this is level need to find.
            }

            var ids = locationItem.Hierarchy.Split(',').Select(int.Parse);

            var query = GetQueryable(c => c.Location.LocationLevel == level
                                          && ids.Contains(c.Id));

            return query.SingleOrDefault();
        }

        public void UpdateNationality(LocationItem locationItem, int[] ids)
        {
            if (locationItem == null)
            {
                throw new ArgumentNullException("locationItem");
            }

            var allConfigs = locationItem.LocationNationalityConfigs;
            var configIdsToAdd = ids.Any() ? ids.Where(p => allConfigs.All(p1 => p1.NationalityTypeId != p)) : Enumerable.Empty<int>(); //New gender config id
            var configsToAdd = configIdsToAdd.Select(p => new LocationNationalityConfig //New config (map)
            {
                LocationItem = locationItem,
                NationalityTypeId = p,
            });

            allConfigs = allConfigs.ToList();

            _locationNationalityConfigRepository.Insert(configsToAdd);
            _locationNationalityConfigRepository.Update(allConfigs);
        }

        public void UpdateEthnicity(LocationItem locationItem, int[] ids)
        {
            if (locationItem == null)
            {
                throw new ArgumentNullException("locationItem");
            }

            var allConfigs = locationItem.LocationEthnicityConfigs;
            var configIdsToAdd = ids.Any() ? ids.Where(p => allConfigs.All(p1 => p1.EthnicityTypeId != p)) : Enumerable.Empty<int>(); //New gender config id
            var configsToAdd = configIdsToAdd.Select(p => new LocationEthnicityConfig //New config (map)
            {
                LocationItem = locationItem,
                EthnicityTypeId = p,
            });

            _locationEthnicityConfigRepository.Insert(configsToAdd);
            _locationEthnicityConfigRepository.Update(allConfigs);
        }

        public void UpdateLanguage(LocationItem locationItem, int[] ids)
        {
            ArgumentNullException.ThrowIfNull(locationItem);

            if (ids.Length == 0)
            {
                foreach(var llsc in locationItem.LocationLanguageSpokenConfigs)
                {
                    _locationLanguageSpokenConfigRepository.Delete(llsc);
                }
                return;
            }

            var idsList = ids.ToList();

            var listToDelete = locationItem.LocationLanguageSpokenConfigs
                .Where(p => idsList.TrueForAll(p1 => p.LanguageSpokenTypeId != p1))
                .ToList();

            var configsList = locationItem.LocationLanguageSpokenConfigs.ToList(); 

            var listToAdd = idsList
                .Where(p => configsList.TrueForAll(p1 => p1.LanguageSpokenTypeId != p))
                .ToList();

            foreach (var llsc in listToDelete)
            {
                _locationLanguageSpokenConfigRepository.Delete(llsc);
            }

            foreach (var each in listToAdd)
            {
                locationItem.LocationLanguageSpokenConfigs.Add(new LocationLanguageSpokenConfig
                {
                    LanguageSpokenTypeId = each
                });
            }

            Update(locationItem);
        }

        public void UpdateIncomeRange(LocationItem locationItem, int[] ids)
        {
            if (locationItem == null)
            {
                throw new ArgumentNullException("locationItem");
            }

            var allConfigs = locationItem.LocationIncomeRangeConfigs;
            var configIdsToAdd = ids.Any() ? ids.Where(p => allConfigs.All(p1 => p1.IncomeRangeTypeId != p)) : Enumerable.Empty<int>(); //New gender config id
            var configsToAdd = configIdsToAdd.Select(p => new LocationIncomeRangeConfig //New config (map)
            {
                LocationItem = locationItem,
                IncomeRangeTypeId = p,
            });

            _locationIncomeRangeConfigRepository.Insert(configsToAdd);
            _locationIncomeRangeConfigRepository.Update(allConfigs);
        }

        public void UpdateGender(LocationItem locationItem, int[] ids)
        {
            if (locationItem == null)
            {
                throw new ArgumentNullException("locationItem");
            }

            var allConfigs = locationItem.LocationGenderConfigs;
            var configIdsToAdd = ids.Any() ? ids.Where(p => allConfigs.All(p1 => p1.GenderTypeId != p)) : Enumerable.Empty<int>(); //New gender config id
            var configsToAdd = configIdsToAdd.Select(p => new LocationGenderConfig //New config (map)
            {
                LocationItem = locationItem,
                GenderTypeId = p,
            });

            _locationGenderConfigRepository.Insert(configsToAdd);
            _locationGenderConfigRepository.Update(allConfigs);
        }

        public void UpdateSexualPreference(LocationItem locationItem, int[] ids)
        {
            if (locationItem == null)
            {
                throw new ArgumentNullException("locationItem");
            }

            var allConfigs = locationItem.LocationSexualPreferenceConfigs;
            var configIdsToAdd = ids.Any() ? ids.Where(p => allConfigs.All(p1 => p1.SexualPreferenceId != p)) : Enumerable.Empty<int>(); //New gender config id
            var configsToAdd = configIdsToAdd.Select(p => new LocationSexualPreferenceConfig //New config (map)
            {
                LocationItem = locationItem,
                SexualPreferenceId = p,
            });

            _locationSexualPreferenceConfigRepository.Insert(configsToAdd);
            _locationSexualPreferenceConfigRepository.Update(allConfigs);
        }

        public IList<LocationItem> GetLocationItems(int locationItemId, int locationId, bool includeDeactivated = false)
        {
            var query = GetQueryable(p => p.LocationId == locationId, includeDeactivated: includeDeactivated).ToList();

            if (locationItemId == 0)
            {
                return query;
            }

            query = query.Where(p => p.Hierarchy.Split(',').Contains(locationItemId.ToString())).ToList();

            return query;
        }

        public void UpdateAgeRange(LocationItem locationItem, int[] ids)
        {
            ArgumentNullException.ThrowIfNull(locationItem);

            var allConfigs = locationItem.LocationAgeRangeConfigs;
            var configIdsToAdd = ids.Any() ? ids.Where(p => allConfigs.All(p1 => p1.AgeRangeId != p)) : Enumerable.Empty<int>(); //New ageRange config id
            var configsToAdd = configIdsToAdd.Select(p => new LocationAgeRangeConfig //New config (map)
            {
                LocationItem = locationItem,
                AgeRangeId = p,
            });

            allConfigs = allConfigs.Select(p =>
            {
                p.IsDeactivate = !ids.Contains(p.AgeRangeId);
                return p;
            }).ToList();            

            _locationAgeRangeConfigRepository.Insert(configsToAdd);
            _locationAgeRangeConfigRepository.Update(allConfigs);
        }

        public string GetNameById(int locationItemId)
        {
            var query = GetQueryable(b => b.Id == locationItemId).Select(p => p.Name);

            return query.SingleOrDefault();
        }

        public IEnumerable<int> GetHierarchy(LocationItem locationItem, bool isUseItself)
        {
            var result = new List<int>();

            if (locationItem == null)
            {
                return result;
            }

            var isParentNull = locationItem.Location.LocationLevel == LocationLevel.Industry ||
                               locationItem.Location.LocationLevel == LocationLevel.SpecialMenus ||
                               locationItem.Location.LocationLevel == LocationLevel.Menus ||
                               locationItem.Location.LocationLevel == LocationLevel.Applications ||
                               locationItem.Location.LocationLevel == LocationLevel.DevicesUsed;
            if (!isParentNull)
            {
                if (string.IsNullOrWhiteSpace(locationItem.Hierarchy))
                {
                    locationItem.Hierarchy = GenerateHierarchy(locationItem); // Generate hierarchy in case missed hierarchy.
                    Update(locationItem);
                }
                result = UtilConvert.SeparateStringToInt(locationItem.Hierarchy);
            }

            if (isUseItself)
            {
                result.Add(locationItem.Id);
            }

            return result;
        }

        public IList<LocationItem> GetRestrict(IEnumerable<int> locationItemsRestrictIds)
        {
            var locationIdsString = locationItemsRestrictIds.Select(p => p.ToString()).ToList();

            var query = GetQueryable(b => locationItemsRestrictIds.Contains(b.Id)
                                          ||
                                          (from c in locationIdsString
                                           where b.Hierarchy.StartsWith(c + ",")
                                                 || b.Hierarchy.EndsWith("," + c)
                                                 || b.Hierarchy.Contains("," + c + ",")
                                                 || b.Hierarchy.Equals(c)
                                           select c).Any());

            return query.ToList();
        }

        public IList<LocationItem> GetWithoutRestrict(IEnumerable<int> locationItemsRestrictIds, int locationiItemIdRootOfRestrict)
        {
            var locationIdsString = locationItemsRestrictIds.Select(p => p.ToString()).ToList();
            var locationiItemIdRootOfRestrictstr = locationiItemIdRootOfRestrict.ToString();

            var query = GetQueryable(b =>
                (b.Hierarchy.StartsWith(locationiItemIdRootOfRestrictstr + ",")
                 || b.Hierarchy.EndsWith("," + locationiItemIdRootOfRestrictstr)
                 || b.Hierarchy.Contains("," + locationiItemIdRootOfRestrictstr + ",")
                 || b.Hierarchy.Equals(locationiItemIdRootOfRestrictstr))
                && !locationItemsRestrictIds.Contains(b.Id)
                &&
                !(from c in locationIdsString
                  where b.Hierarchy.StartsWith(c + ",")
                        || b.Hierarchy.EndsWith("," + c)
                        || b.Hierarchy.Contains("," + c + ",")
                        || b.Hierarchy.Equals(c)
                  select c).Any());

            return query.ToList();
        }

        public IList<LocationItem> GetMenusByStoreLocation(int storeLocationItemId)
        {
            var storeLocation = GetById(storeLocationItemId);
            var devices = storeLocation.Childs;
            var menus = devices.SelectMany(p => p.Children.Where(p2 => p2.IsStatus != Constants.DELETE_RECORD && !p2.IsDeactivate).Select(p1 => p1.ChildLocation));

            return _languageExecuteService.GetLanguage(menus.Distinct().OrderBy(p => p.Name).ToList());
        }

        public IList<LocationItem> GetDevicesByStoreLocation(int storeLocationItemId)
        {
            var locationItem = GetById(storeLocationItemId);
            if (locationItem == null || locationItem.Location.LocationLevel != LocationLevel.Locations)
            {
                return new List<LocationItem>();
            }

            var result = locationItem.Childs.Where(p => p.IsActive && p.Location.LocationLevel == LocationLevel.DevicesUsed);
            return _languageExecuteService.GetLanguage(result.ToList())
                .OrderBy(p => p.Name).ToList();
        }

        public IList<LocationItem> GetDeviceByBrandModel(int brandId, int modelId, DeviceType type)
        {
            var query = GetQueryable(p => p.LocationLevelPOSDeviceUsed != null
                                        && p.LocationLevelPOSDeviceUsed.Type == type
                                        //&& p.LocationLevelPOSDeviceUsed.Asset.SelectModelType.DeviceModelTypeId == modelId
                                        //&& p.LocationLevelPOSDeviceUsed.Asset.SelectModelType.SelectBrandType.DeviceBrandTypeId == brandId
                                        );

            return _languageExecuteService.GetLanguage(query.ToList());
        }

        public IList<LocationItem> GetMenusByDevice(int deviceLocationItemId)
        {
            var query = from b in _locationItemParentChildrenMapRepository.GetContext().Set<LocationItemParentChildrenMap>()
                        where !b.IsDeactivate
                        where b.IsStatus != Constants.DELETE_RECORD
                        where b.ParentLocationId == deviceLocationItemId
                        orderby b.ChildLocation.Name
                        select b.ChildLocation;

            return _languageExecuteService.GetLanguage(query.ToList());
        }

        public IList<LocationItem> GetMenuByApplication(int deviceId, int applicationId)
        {
            var query = GetQueryable(p
                => p.Parents.Any(p1 => p1.ParentLocationId == deviceId && p1.ParentLocation.Location.LocationLevel == LocationLevel.DevicesUsed && !p1.IsDeactivate)
                && p.Location.LocationLevel == LocationLevel.Menus
                && p.ParentId == applicationId,
                includeDeactivated: true,
                includeHardCoded: true);

            return _languageExecuteService.GetLanguage(query.ToList());
        }

        public IList<LocationItem> GetMenu(int applicationId)
        {
            var query = GetQueryable(p => p.ParentId == applicationId,
                includeDeactivated: true,
                includeHardCoded: true);

            return _languageExecuteService.GetLanguage(query.ToList()).SortByLevel();
        }

        public void CalculateDeviceId(int parentId)
        {
            if (!IsExisted(parentId))
            {
                return;
            }

            var location = GetById(parentId);
            if (location.Location.LocationLevel != LocationLevel.Locations)
            {
                return; // It must be location level.
            }

            var devicesByLocation = GetQueryable(p => p.ParentId == parentId && p.Location.LocationLevel == LocationLevel.DevicesUsed, includeDeactivated: true).OrderBy(p => p.Id).ToList();
            var step = 1;

            foreach (var deviceUsed in devicesByLocation
                .Where(each => each.LocationLevelPOSDeviceUsed != null)
                .Select(each => each.LocationLevelPOSDeviceUsed))
            {
                deviceUsed.DeviceId = step;
                step++;
            }

            UpdateAll(devicesByLocation);
        }

        public int GenerateDeviceId(int parentId)
        {
            if (!IsExisted(parentId))
            {
                return 0;
            }

            var location = GetById(parentId, p=>p.Location);
            if (location.Location.LocationLevel != LocationLevel.Locations)
            {
                return 0; // It must be location level.
            }

            var devicesByLocation = GetQueryable(p => p.ParentId == parentId && p.Location.LocationLevel == LocationLevel.DevicesUsed, includeDeactivated: true);

            return devicesByLocation.Count() + 1;
        }

        public bool CheckDeviceIdIsExistsed(int parentId, int deviceId, int id)
        {
            if (!IsExisted(parentId))
            {
                return false;
            }

            var location = GetById(parentId, p=>p.Location);
            if (location.Location.LocationLevel != LocationLevel.Locations)
            {
                return false; // It must be location level.
            }

            var device = GetQueryable(p
                => p.ParentId == parentId
                && p.Location.LocationLevel == LocationLevel.DevicesUsed
                && p.LocationLevelPOSDeviceUsed != null
                && p.LocationLevelPOSDeviceUsed.DeviceId == deviceId
                && p.Id != id,
                includeDeactivated: true);

            return device.Any();// Any is existsed device id with the same parentId.
        }

        public LocationItem GetLastInTheTree(IEnumerable<int> locationItemIds)
        {
            var query = GetQueryable(p => locationItemIds.Contains(p.Id)).OrderByDescending(p => p.Location.LocationLevel);

            return query.FirstOrDefault();
        }

        public LocationItem GetStoreLocationByDevice(int deviceId)
        {
            if (!IsExisted(deviceId))
            {
                return null;
            }

            var device = GetById(deviceId);
            if (device == null)
            {
                return null;
            }

            return _languageExecuteService.GetLanguage(device.Parent);
        }


        public IList<int> GetForGetNearest(string locationItemSelectedIds, bool includeItself)
        {
            var result = new List<int>();
            if (string.IsNullOrWhiteSpace(locationItemSelectedIds))
            {
                return result;
            }

            var ids = UtilConvert.SeparateStringToInt(locationItemSelectedIds);

            var lastItemCanUseHierarchy =
                GetQueryable(
                    p => ids.Contains(p.Id) && p.Location != null && p.Location.LocationLevel <= LocationLevel.LocationAreas)
                    .OrderByDescending(p => p.Location.LocationLevel)
                    .FirstOrDefault();

            // Get list item can be use hierarchy via hierarchy (@_@)
            if (lastItemCanUseHierarchy != null)
            {
                result.AddRange(GetHierarchy(lastItemCanUseHierarchy, includeItself));
            }

            // Items can not use hierarchy.
            var itemsCanNotUseHierarchy = GetQueryable(p
                => ids.Contains(p.Id) && p.Location != null && p.Location.LocationLevel >= LocationLevel.LocationAreas)
                .OrderBy(p => p.Location.LocationLevel);
            if (itemsCanNotUseHierarchy.Any())
            {
                var idsCanNotUseHierarchy = itemsCanNotUseHierarchy.Select(p => p.Id).ToList();
                if (!includeItself && idsCanNotUseHierarchy.Count > 0)
                {
                    idsCanNotUseHierarchy.RemoveAt(idsCanNotUseHierarchy.Count - 1);
                }

                result.AddRange(idsCanNotUseHierarchy);
            }

            return result.Distinct().ToList();
        }

        public IList<int> GetAllParentByLocation(int locationItemId)
        {
            var result = new List<int>();
            var location = base.GetById(locationItemId);
            if (location == null)
            {
                return result;
            }

            var hierachy = location.Hierarchy;
            if (string.IsNullOrWhiteSpace(hierachy))
            {
                return result;
            }

            var items = hierachy.Split(',');
            result.AddRange(items.Select(x => Int32.Parse(x)).ToList());
            return result;
        }

        #region Get All locaiton below

        public IList<LocationItem> GetAllLocationItemBelow(int locationItemId, string parentsStr)
        {
            var locationItem = GetById(locationItemId);
            if (locationItem == null)
            {
                return new List<LocationItem>();
            }

            return GetAllLocationItemBelow(locationItem, parentsStr);
        }

        public IList<LocationItem> GetAllLocationItemBelow(int locationItemId, IList<int> parentIds)
        {
            var locationItem = GetById(locationItemId, p=>p.Location);
            if (locationItem == null)
            {
                return new List<LocationItem>();
            }

            return GetAllLocationItemBelow(locationItem, parentIds);
        }

        public IList<LocationItem> GetAllLocationItemBelow(LocationItem locationItem, IList<int> parentIds) //NOSONAR
        {
            var result = new List<LocationItem>();
            if (locationItem.Location == null)
            {
                return result;
            }

            if (locationItem.Location.LocationLevel == LocationLevel.SpecialMenus)
            {
                return result; // Last level, there are no level below this sh*t.
            }

            var parents = GetQueryable(p => parentIds.Contains(p.Id)).ToList();

            if (locationItem.Location.LocationLevel < LocationLevel.LocationAreas)
            {
                var locationItemIdString = locationItem.Id.ToString();
                var query = GetQueryable(locItem
                    => locItem.Location.LocationLevel <= LocationLevel.LocationAreas
                    && (locItem.Hierarchy.StartsWith(locationItemIdString + ",") ||
                    locItem.Hierarchy.Contains("," + locationItemIdString + ",") ||
                    locItem.Hierarchy.EndsWith("," + locationItemIdString) ||
                    locItem.Hierarchy == locationItemIdString),
                    includeDeactivated: false
                    ).ToList();

                result.AddRange(query); // Get all below until reach Location areas
            }

            var storeLocations = result.Where(p => p.Location.LocationLevel == LocationLevel.Locations).ToList();
            var locationAreas = result.Where(p => p.Location.LocationLevel == LocationLevel.LocationAreas).ToList();
            var currentStoreLocation = parents.Find(p => p.Location.LocationLevel == LocationLevel.Locations);
            var currentDevice = parents.Find(p => p.Location.LocationLevel == LocationLevel.DevicesUsed);
            var currentApp = parents.Find(p => p.Location.LocationLevel == LocationLevel.Applications);

            // Get Devices.
            if (locationItem.Location.LocationLevel < LocationLevel.DevicesUsed)
            {
                var locationAreasToQuery = new List<LocationItem>();

                if (locationAreas.Any())
                {
                    locationAreasToQuery = locationAreas;
                }

                if (locationItem.Location.LocationLevel == LocationLevel.LocationAreas && locationItem.ParentId.HasValue)
                {
                    locationAreasToQuery.Add(locationItem);
                }

                result.AddRange(GetAllDevicesUsedByLocationArea(locationAreasToQuery));
            }

            // Get applications.
            var devices = result.Where(p => p.Location.LocationLevel == LocationLevel.DevicesUsed).ToList();
            if (locationItem.Location.LocationLevel < LocationLevel.Applications)
            {
                var devicesToQuery = new List<LocationItem>();
                if (devices.Any())
                {
                    devicesToQuery = devices;
                }
                else if (locationItem.Location.LocationLevel == LocationLevel.DevicesUsed)
                {
                    devicesToQuery.Add(locationItem); // By single device.
                }

                result.AddRange(GetApplicationByDevice(devicesToQuery)); // By multi devices
            }


            // Get menus-------------------------------------------
            var apps = result.Where(p => p.Location.LocationLevel == LocationLevel.Applications).ToList();
            if (locationItem.Location.LocationLevel < LocationLevel.Menus)
            {
                var storeLocationIds = new List<int>();
                var deviceIds = new List<int>();
                var appIds = new List<int>();

                // Get Device Ids 
                if (locationItem.Location.LocationLevel == LocationLevel.DevicesUsed)
                {
                    deviceIds = new List<int> { locationItem.Id }; // Single by it itself.
                }
                else if (currentDevice != default)
                {
                    deviceIds = new List<int> { currentDevice.Id }; // Single by parent Ids.
                }
                else if (devices.Any())
                {
                    deviceIds = devices.Select(p => p.Id).ToList();// Multi.
                }

                // Get store location ids.
                if (locationItem.Location.LocationLevel == LocationLevel.Locations)
                {
                    storeLocationIds = new List<int> { locationItem.Id }; // Single by it itself.
                }
                else if (currentStoreLocation != default)
                {
                    storeLocationIds = new List<int> { currentStoreLocation.Id }; // Single by parent Ids.
                }
                else if (storeLocations.Any())
                {
                    storeLocationIds = storeLocations.Select(p => p.Id).ToList();// Multi.
                }

                // Get application ids.
                if (locationItem.Location.LocationLevel == LocationLevel.Applications)
                {
                    appIds = new List<int> { locationItem.Id };// Single by it itself.
                }
                else if (currentApp != default)
                {
                    appIds = new List<int> { currentApp.Id }; // Single by parent Ids.
                }
                else if (apps.Any())
                {
                    appIds = apps.Select(p => p.Id).ToList();// Multi.
                }

                result.AddRange(GetAllMenuUsedByApp(storeLocationIds, deviceIds, appIds));
            }
            // End get menus-----------------------------------------

            // Get special menus.
            if (locationItem.Location.LocationLevel < LocationLevel.SpecialMenus)
            {
                var menus = result.Where(p => p.Location.LocationLevel == LocationLevel.Menus).ToList();
                var menusToQuery = new List<LocationItem>();
                if (locationItem.Location.LocationLevel == LocationLevel.Menus)
                {
                    menusToQuery = new List<LocationItem> { locationItem };// Single by it itself.
                }
                else
                {
                    menusToQuery = menus;
                }

                result.AddRange(GetAllSpecialMenuByMenu(menusToQuery));
            }

            return result;
        }

        public IList<LocationItem> GetAllLocationItemBelow(LocationItem locationItem, string locationItemIdsStr)
        {
            var locationItemIds = UtilConvert.SeparateStringToInt(locationItemIdsStr);

            return GetAllLocationItemBelow(locationItem, locationItemIds);
        }

        private IList<LocationItem> GetAllDevicesUsedByLocationArea(IList<LocationItem> locationAreas)
        {
            if (!locationAreas.Any())
            {
                return new List<LocationItem>();
            }

            var storeLocationId = locationAreas[0].ParentId;
            var locationAreaIds = locationAreas.Select(p => p.Id).ToList();

            var query = GetQueryable(p
                => p.ParentId == storeLocationId //  Location Store Parent
                && p.Location.LocationLevel == LocationLevel.DevicesUsed // Level
                && locationAreaIds.Contains(p.UsedByItemId.Value), // assiged - isUsed.
                includeDeactivated: false);

            return query.ToList();
        }

        private IList<LocationItem> GetAllSpecialMenuByMenu(IList<LocationItem> menus)
        {
            if (!menus.Any())
            {
                return new List<LocationItem>();
            }

            menus = menus.Where(p => p.LocationParentId.HasValue).ToList(); // Clear incorrect data.
            var storeLocationIds = menus.Select(p => p.LocationParentId.Value).Distinct();
            var menuIds = menus.Select(p => p.Id).ToList();

            var query = GetQueryable(p
                => storeLocationIds.Contains(p.LocationParentId.Value)
                && p.Location.LocationLevel == LocationLevel.SpecialMenus
                && p.Parents.Any(p1 => menuIds.Contains(p1.ParentLocationId) && !p1.IsDeactivate) // IsUsed
                ,
                includeDeactivated: false);

            return query.ToList();
        }

        private IList<LocationItem> GetAllMenuUsedByApp(IList<int> storeLocationIds, IList<int> deviceIds, IList<int> appIds)
        {
            var query = GetQueryable(p
                => storeLocationIds.Contains(p.LocationParentId.Value)//  Store Location
                && p.Parents.Any(p1 => deviceIds.Contains(p1.ParentLocationId) && !p1.IsDeactivate) // IsUsed by Device.
                && appIds.Contains(p.ParentId.Value) // Application is parent.
                && p.Location.LocationLevel == LocationLevel.Menus, // Level
                includeDeactivated: false);

            return query.ToList();
        }

        private IList<LocationItem> GetApplicationByDevice(IList<LocationItem> devices)
        {
            if (!devices.Any())
            {
                return new List<LocationItem>();
            }

            var devicesId = devices.Select(p => p.Id).ToList();
            var query = GetQueryable(p
                => p.Location.LocationLevel == LocationLevel.Applications
                && p.Parents.Any(p1 => devicesId.Contains(p1.ParentLocationId) && !p1.IsDeactivate)
                );

            return query.ToList();
        }
        #endregion

        public IDictionary<int, string> ValidateRestrictedIds(IDictionary<int, string> dictRestricted)
        {
            if (dictRestricted == null)
            {
                return new Dictionary<int, string>();
            }

            var restrictedIds = dictRestricted.Select(p => p.Key).ToList();

            var removeIds = new List<int>();

            foreach (var each in dictRestricted)
            {
                var tempIds = UtilConvert.SeparateStringToInt(each.Value); // Location items below.
                var tempRemoveIds = tempIds.Where(p => restrictedIds.Contains(p)).ToList();
                if (tempRemoveIds.Any())
                {
                    removeIds.AddRange(tempRemoveIds);
                }
            }
            removeIds = removeIds.Distinct().ToList();
            foreach (var item in removeIds)
            {
                dictRestricted.Remove(item);
            }


            return dictRestricted;
        }

        public IList<int> GetAllRestrictId(IDictionary<int, string> dictRestricted)
        {
            var allBelowIds = new List<int>();
            if (dictRestricted == null || !dictRestricted.Any())
            {
                return allBelowIds;
            }

            foreach (var each in dictRestricted)
            {
                var belowIds = UtilConvert.SeparateStringToInt(each.Value);
                allBelowIds.AddRange(belowIds);
            }

            allBelowIds.AddRange(dictRestricted.Select(p => p.Key).ToList());
            allBelowIds = allBelowIds.Distinct().ToList();

            return allBelowIds;
        }

        public LocationItem GetByLevel(string parentIdsStr, LocationLevel locationLevel)
        {
            var parentIds = GetForGetNearest(parentIdsStr, true);

            return GetByLevel(parentIds, locationLevel);
        }

        public LocationItem GetByLevel(IList<int> parentIds, LocationLevel locationLevel)
        {
            var query = GetQueryable(p => parentIds.Contains(p.Id) && p.Location.LocationLevel == locationLevel);

            return query.FirstOrDefault();
        }

        public IList<int> GetParentIdsForAPI(LocationItem app, LocationItem device)
        {
            var listIds = new List<int>
            {
                app.Id,
                device.Id,
                device.ParentId ?? device.UsedByItemId.GetValueOrDefault(),
            };

            return GetForGetNearest(UtilConvert.MergeIntToString(listIds), true);
        }

        public LocationItem GetHighestLevel(IList<int> locationItemIds)
        {
            var query = GetAll(includes:p=>p.Location).Where(p => locationItemIds.Contains(p.Id));
            if (!locationItemIds.Any() || !query.Any())
            {
                return null;
            }

            var result = query
                .GroupBy(p => p.Location.LocationLevel)
                .OrderBy(p => p.Key)
                .FirstOrDefault()?
                .FirstOrDefault();

            return result;
        }

        public IList<LocationItem> GetAllApps()
        {
            var query = GetQueryable(p => p.Location.LocationLevel == LocationLevel.Applications, includeDeactivated: true).SortByLevel();

            return query.ToList();
        }

        public IList<LocationItem> GetAllLocationAreaByParent(int parentId)
        {
            var query = GetQueryable(p => p.ParentId == parentId && p.LocationId == (int)LocationLevel.Locations, includeDeactivated: true).SortByLevel();

            return query.ToList();
        }

        public async Task<LocationItem> GetLocationItemIncludeLevelAsync(int locationItemId)
        {
            var locationItem = await _locationItemRepository.GetLocationItemIncludeLevelAsync(locationItemId);
            return locationItem;
        }

        public async Task<LocationLevel> GetLocationLevelByLocationId(int locationItemId)
        {
            var locationItem = await _locationItemRepository.GetLocationItemIncludeLevelAsync(locationItemId);
            if (locationItem is null) return LocationLevel.Master;

            return locationItem.Location.LocationLevel;
        }

        public IList<LocationItem> GetDevice(int parentId, bool includeDeactivated)
        {
            var devices = GetQueryable(p => p.Location.LocationLevel == LocationLevel.DevicesUsed
            //&& p.LocationLevelPOSDeviceUsed.Asset != null
            //&& (p.LocationLevelPOSDeviceUsed.Asset.IsECRDevice
            //|| p.LocationLevelPOSDeviceUsed.Asset.IsPaymentDevice)
            && p.ParentId == parentId, includeDeactivated: includeDeactivated).ToList();

            return _languageExecuteService.GetLanguage(devices.ToList());
        }

        public IList<LocationItem> GetAppsByDevice(int deviceId)
        {
            var device = GetById(deviceId);
            if (device == null)
            {
                return new List<LocationItem>();//check null
            }

            return GetAppsByDevice(device);
        }

        public IList<LocationItem> GetAppsByDevice(LocationItem device)
        {
            return _languageExecuteService.GetLanguage(device.ApplicationForDeviceConfigDevice.Where(p => p.IsActive).Select(p => p.Application).ToList());
        }

        public IList<int> GetAllParents(string parentIdsStr)
        {
            var parentIds = UtilConvert.SeparateStringToInt(parentIdsStr);
            var parentItems = GetQueryable(p => parentIds.Contains(p.Id));
            if (!parentItems.Any())
            {
                return parentIds;
            }

            var lastLocationItemCanUseParentId = parentItems.Where(p => p.Location.LocationLevel <= LocationLevel.Locations).OrderByDescending(p => p.Location.LocationLevel).FirstOrDefault();
            if (lastLocationItemCanUseParentId == null)
            {
                return parentIds;
            }

            var result = GetAllParentCanUseParentId(lastLocationItemCanUseParentId).ToList();
            result.AddRange(parentItems.Where(p => p.Location.LocationLevel > LocationLevel.Locations).Select(p => p.Id).ToList());

            return result;
        }
        public IList<LocationItem> GetAllParentAtLevel(int id, LocationLevel level)
        {
            var result = new List<LocationItem>();
            var locationItem = GetById(id);
            if (locationItem.Location.LocationLevel > LocationLevel.Locations)
            {
                return result;
            }

            while (locationItem != null && locationItem.Location.LocationLevel >= level)
            {
                result.Add(locationItem);
                locationItem = locationItem.Parent;
            }
            return result.OrderBy(p => p.Location.LocationLevel).ToList();

        }
        public int GetImmediateParentId(int id)
        {
            return GetQueryable(p => p.Id == id).Select(p => p.ParentId.Value).FirstOrDefault();
        }
        private static List<int> GetAllParentCanUseParentId(LocationItem locationItem)
        {
            if (locationItem.Location.LocationLevel > LocationLevel.Locations)
            {
                return new List<int>(); // LocationLevel > Store Location can't set by ParentId.
            }

            var result = new List<int>();

            while (locationItem != null)
            {
                result.Add(locationItem.Id);
                locationItem = locationItem.Parent;
            }

            return result;
        }

        public LocationItem GetIndustry(string parentIds)
        {
            var ids = UtilConvert.SeparateStringToInt(parentIds);

            return
                GetQueryable(p => ids.Contains(p.Id) && p.Location.LocationLevel == LocationLevel.Industry, includeDeactivated: true, includeHardCoded: true).FirstOrDefault();
        }

        public Location GetHighestLocation(IList<int> ids, bool isAllowed)
        {
            if (ids == null || !ids.Any())
            {
                return null;
            }

            var query = GetQueryable(null, includeDeactivated: true, includeHardCoded: true);

            query = isAllowed ? query.Where(p => ids.Contains(p.Id)) : query.Where(p => !ids.Contains(p.Id));

            var group = query
                .GroupBy(p => p.Location.LocationLevel)
                .OrderBy(p => p.Key)
                .FirstOrDefault();

            var gr = group?.FirstOrDefault();

            return gr?.Location;
        }

        public LocationItem GetAppByAppUsedId(int appUsedId)
        {
            var query = GetQueryable(p => p.AppUsedId == appUsedId);

            return query.FirstOrDefault();
        }

        public IList<LocationItem> GetDeviceByUsedItemId(int usedItemId)
        {
            var query = GetQueryable(p => p.UsedByItemId == usedItemId);

            return _languageExecuteService.GetLanguage(query.ToList());
        }

        public IList<LocationItem> SearchDeviceApplication(string name)
        {
            return GetQueryable(p => p.Location.LocationLevel == LocationLevel.Applications && p.Name.Contains(name), includeDeactivated: true).SortByLevel().ToList();
        }

        public int GetCurrencyTypeIdByItemId(int itemId)
        {
            var query = GetQueryable(p => p.Id == itemId).Select(p => p.CurrencyTypeId);

            return query.FirstOrDefault() ?? 0;

        }

        public LocationItem GetDefaultData(string locationItemIdsStr, LocationLevel level, bool includingCurrentLevel)
        {
            var result = new LocationItem();
            var locationItemIds = GetForGetNearest(locationItemIdsStr, true);
            var q = GetQueryable().Where(p => locationItemIds.Contains(p.Id));
            q = includingCurrentLevel ? q.Where(p => p.Location.LocationLevel <= level) : q.Where(p => p.Location.LocationLevel < level);
            q = q.OrderByDescending(p => p.Location.LocationLevel);

            var list = q.ToList();

            foreach (var item in list)
            {
                if (!result.CurrencyTypeId.HasValue && item.CurrencyTypeId.HasValue) result.CurrencyTypeId = item.CurrencyTypeId;
                if (!result.DecimalUsed.HasValue && item.DecimalUsed.HasValue) result.DecimalUsed = item.DecimalUsed;
                if (!result.ThousandSeperatorUsed.HasValue && item.ThousandSeperatorUsed.HasValue) result.ThousandSeperatorUsed = item.ThousandSeperatorUsed;

                if (result.CurrencyTypeId.HasValue && result.ThousandSeperatorUsed.HasValue && result.DecimalUsed.HasValue) break;
            }

            return result;
        }

        public void GenerateIFashionRoamingDefault(LocationItem item)
        {
            if (item == null || item.Location == null) return;
            if (item.Location.LocationLevel != LocationLevel.Locations) return;

            var roaming = GetRoamingDefault(item.Id);

            if (item.IsIFashionRoamingDefault) //Enable roaming item
            {
                if (roaming == null)
                {
                    roaming = new LocationItem();
                    roaming.Name = "Roaming";
                    roaming.ParentId = item.Id;
                    roaming.IsRoamingDefault = true;
                    roaming.LocationId = item.Location.Childs.FirstOrDefault()?.Id ?? 9; // hardcoded of LocationArea is 9. 

                    Insert(roaming);
                }
                else
                {
                    roaming.IsStatus = Constants.DELETE_RECORD; //Revert existed object.
                    Update(roaming);
                }

            }
            else // disable roaming item.
            {
                if (roaming == null) return;

                Delete(roaming, false);
            }
        }

        private LocationItem GetRoamingDefault(int locationItemId)
        {
            var q = from b in GetQueryable(null, true, false, true)
                    where b.IsRoamingDefault
                    where b.ParentId == locationItemId
                    select b;

            var roaming = q.FirstOrDefault();

            return roaming;
        }

        public IList<LocationItem> GetSelectedIndustry(bool useSelectSort, bool includeDeactivated = true)
        {
            var query = GetAll(p =>
                p.IsIndustrySelected &&
                p.BusinessTypeId != null &&
                p.BusinessType.IsActive &&
                p.BusinessType.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD,
                includeDeactivated: includeDeactivated
                ).AsQueryable();

            if (useSelectSort)
            {
                query = query.OrderBy(p => p.OrderIndex);
            }
            else
            {
                query = query.OrderBy(p => p.Name);
            }

            return query.ToList();
        }

        public void UpdateSelectedIndustry(List<int> selectedIds, List<IndustryType> businessTypes, int locationId, bool useSelectSort = false)
        {
            var addableList = new List<LocationItem>();
            var updateList = new List<LocationItem>();

            var selected = GetQueryable(p => p.BusinessTypeId != null, includeDeactivated: true).ToList();
            var index = 1;

            foreach (var each in selectedIds)
            {
                var obj = selected.Find(p => p.BusinessTypeId == each);

                if (obj == default)
                {
                    obj = new LocationItem();

                    obj.BusinessTypeId = each;
                    obj.IsActive = true;
                    obj.LocationId = locationId;

                    addableList.Add(obj);
                }
                else
                {
                    updateList.Add(obj);

                    selected.Remove(obj);
                }

                obj.OrderIndex = index++;
                obj.IsIndustrySelected = true;
            }

            foreach (var item in selected)
            {
                item.IsIndustrySelected = false;
            }

            if (addableList.Count != 0)
            {
                Insert(addableList);
            }

            if(updateList.Count != 0)
            {
                UpdateAll(updateList);
            }

            if (selected.Count != 0)
            {
                UpdateAll(selected);
            }

        }

        public void UpdateRemovedIndustry(List<int> selectedIds)
        {
            var removed = GetQueryable(p => !selectedIds.Contains(p.BusinessTypeId.Value) && !(p.IsIndustrySelected && p.IsSetup)).ToList();

            foreach (var each in removed)
            {
                each.OrderIndex = 0;
                each.IsIndustrySelected = false;
            }
            UpdateAll(removed);
        }

        public IList<LocationItem> GetStoreLocations(int locationItemId)
        {
            if (locationItemId == 0)
            {
                return GetAllByLocationLevel(LocationLevel.Locations);
            }

            var locationItem = GetById(locationItemId);
            if (locationItem == null)
            {
                throw new KeyNotFoundException("Item not found");
            }

            var locationId = locationItem.LocationId;
            var locationLoadId = _locationService.GetByLevel(LocationLevel.Locations).Id;
            var childs = GetByParent(locationItem);

            if (!childs.Any())
            {
                return new List<LocationItem>();
            }

            for (int i = locationId; i < locationLoadId; i++)
            {
                var childsOfChild = new List<LocationItem>();
                foreach (var item in childs)
                {
                    var c = GetByParent(item).ToList();
                    childsOfChild.AddRange(c);
                }

                if (!childsOfChild.Any())
                {
                    return new List<LocationItem>();
                }
                childs = childsOfChild;
            }

            return childs;
        }

        public LocationItem GetFirstDefaultData(string locationItemIdsStr)
        {
            var locationItemIds = UtilConvert.SeparateStringToInt(locationItemIdsStr);

            var query = GetById(locationItemIds);

            return query.FirstOrDefault(p=>p.DefaultDataUsed.Value);
        }    

        public Dictionary<int, int> GetLocationItemsSelectedPre(int locationitemId)
        {
            var result = new Dictionary<int, int>();
            var locationItem = GetById(locationitemId, p=>p.Parent, prop=>prop.Location);
            if (locationItem == null)
                return new Dictionary<int, int>();

            GetParentByLocationItem(result, locationItem);

            return result;
        }

        private void GetParentByLocationItem(Dictionary<int, int> result, LocationItem item)
        {
            if (item == null)
                return;

            if (item.Location.LocationLevel != LocationLevel.Industry)
                GetParentByLocationItem(result, item.Parent);

            result.Add(item.Location.Id, item.Id);
        }

        public IList<Location> GetAllSelectedLocations(IEnumerable<int> ids)
        {
            return _locationRepo.GetById(ids);
        }

        public async Task<IList<LocationItemSelect>> GetSelectedLocations(int? parentId, ScreenType screenType, LocationLevel? locationLevel = null, int? groupMappingId = null)
        {
            return await _locationItemSelectService.GetSelectedLocations(parentId, screenType, locationLevel, groupMappingId);
        }
        
        public async Task UpdateSelectedLocations(List<int> selectedIds, int? parentId, ScreenType screenType, int? groupMapping)
        {
            await _locationItemSelectService.UpdateSelectedLocations(selectedIds, parentId, screenType, groupMapping);
        }

        public IList<LocationItem> GetSelectedChains(int industryId, bool useSelectSort = false)
        {
            var query = GetQueryable(p => p.Location.LocationLevel == LocationLevel.Chain && p.Parent.Id == industryId && p.IsChainSelected, includeDeactivated: true);

            if (useSelectSort)
            {
                query = query.OrderBy(p => p.OrderIndex);
            }
            else
            {
                query = query.OrderBy(p => p.GetLocationItemName);
            }

            return query.ToList();

        }

        public void UpdateSelectedChains(List<int> selectedIds, int industryId, bool useSelectSort = false)
        {
            var configs = GetByParent(GetById(industryId)).Select(p=>p.Id).Distinct().ToList();
            var index = 1;

            foreach (var itemId in configs)
            {
                var isSelected = selectedIds.Contains(itemId);
                var item = GetById(itemId);
                item.IsChainSelected = isSelected;
                item.OrderIndex = isSelected ? index++ : 0;
                Update(item);
            }
        }

        public IList<LocationItem> GetChains(int industryId)
        {
            var query = GetQueryable(p => p.Location.LocationLevel == LocationLevel.Chain && p.Parent.BusinessType.Id == industryId && p.IsChainSelected, includeDeactivated: true);
            return query.ToList();

        }

        public LocationItem GetLocationAreaFirstPaxStore(string locationName)
        {
            var location = GetQueryable(p => p.Location.LocationLevel == LocationLevel.Locations && p.Name == locationName).FirstOrDefault();

            if (location == null)
            {
                location = GetQueryable(p => p.Location.LocationLevel == LocationLevel.Locations && p.Name == "Harris Farm Store 1").FirstOrDefault();
            }

            //get first location area
            var locationAreaFirst = GetQueryable(p => p.Location.LocationLevel == LocationLevel.LocationAreas && p.ParentId == location.Id).FirstOrDefault();

            return locationAreaFirst;

        }

        public async Task<PagingResponse<DataItemResponse>> GetSourceForSelectLocationItemAsync(int parentLocationId, LocationLevel locationLevel, List<int> selectedIds, SelectRemoveFormParameter selectRemoveFormParameter)
        {
            var result = await _locationItemRepository.GetSourceForSelectLocationItemAsync(parentLocationId, locationLevel, selectedIds, selectRemoveFormParameter);

            return result;
        }
    }
}
