import React, { useEffect, useState } from 'react';
import { Form, Select } from 'antd';
import { DrawerCustom } from '../../shared/drawer-custom';
import { DeviceListApi } from '../../../apis/device-list.api';
import './index.scss';
import { DataItemResponse } from '../../../models/common.model';

interface SelectOption {
  value: string;
  label: string;
}

interface FilterFormValues {
  merchant: string[];
  brand: string[];
  model: string[];
  status: string[];
}

interface FilterDrawerProps {
  open: boolean;
  onClose: () => void;
  onFilterChange: (values: FilterFormValues) => void;
}

const FilterDrawer: React.FC<FilterDrawerProps> = ({
  open,
  onClose,
  onFilterChange,
}) => {
  const [form] = Form.useForm();
  const [brands, setBrands] = useState<SelectOption[]>([]);
  const [models, setModels] = useState<SelectOption[]>([]);
  const [merchants, setMerchants] = useState<SelectOption[]>([]);
  const [loading, setLoading] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  const defaultOptions = {
    status: [
      { value: 'active', label: 'Active' },
      { value: 'inactive', label: 'Inactive' },
    ],
  };

  const fetchMerchants = async () => {
    try {
      setLoading(true);
      const { data: responseData } = await DeviceListApi.getAllMerchants();
      if (responseData) {
        const merchantOptions = responseData.map(
          (merchant: DataItemResponse) => ({
            value: merchant.id.toString(),
            label: merchant.name,
          }),
        );
        setMerchants(merchantOptions);
      }
    } catch (error) {
      console.error('Error fetching merchants:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchBrands = async () => {
    try {
      setLoading(true);
      const { data: responseData } = await DeviceListApi.getDeviceBrands();
      if (responseData) {
        const brandOptions = responseData.map(
          (brand: { id: number; name: string }) => ({
            value: brand.id.toString(),
            label: brand.name,
          }),
        );
        setBrands(brandOptions);
      }
    } catch (error) {
      console.error('Error fetching brands:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchAllModels = async () => {
    try {
      setLoading(true);
      const { data: responseData } = await DeviceListApi.getDeviceModels([]);
      if (responseData) {
        const modelOptions = responseData.map(
          (model: { id: number; name: string }) => ({
            value: model.id.toString(),
            label: model.name,
          }),
        );
        setModels(modelOptions);
      }
    } catch (error) {
      console.error('Error fetching all models:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchModels = async (brandIds: string[]) => {
    if (!brandIds.length) {
      setModels([]);
      return;
    }

    try {
      setLoading(true);
      const numericBrandIds = brandIds.map((id) => parseInt(id, 10));
      const { data: responseData } =
        await DeviceListApi.getDeviceModels(numericBrandIds);
      if (responseData) {
        const modelOptions = responseData.map(
          (model: { id: number; name: string }) => ({
            value: model.id.toString(),
            label: model.name,
          }),
        );
        setModels(modelOptions);
      }
    } catch (error) {
      console.error('Error fetching models:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleModelDropdownVisibleChange = (open: boolean) => {
    if (open) {
      const selectedBrands = form.getFieldValue('brand');
      if (selectedBrands?.length) {
        fetchModels(selectedBrands);
      }
    }
  };

  useEffect(() => {
    const initializeData = async () => {
      if (open && !isInitialized) {
        await Promise.all([fetchMerchants(), fetchBrands(), fetchAllModels()]);
        setIsInitialized(true);
      }
    };
    initializeData();
  }, [open, isInitialized]);

  useEffect(() => {
    const selectedBrands = form.getFieldValue('brand');
    if (selectedBrands?.length) {
      fetchModels(selectedBrands);
    } else {
      fetchAllModels();
    }
  }, [form.getFieldValue('brand')]);

  const handleBrandChange = (value: string[]) => {
    form.setFieldValue('model', []);
    if (value?.length) {
      fetchModels(value);
    } else {
      fetchAllModels();
    }
  };

  const handleSave = () => {
    form.validateFields().then((values) => {
      onFilterChange(values as FilterFormValues);
      onClose();
    });
  };

  const handleReset = () => {
    form.resetFields();
    fetchAllModels();
  };

  return (
    <DrawerCustom
      title="Filter Devices"
      open={open}
      onClose={onClose}
      onReset={handleReset}
      onSave={handleSave}
    >
      <Form
        form={form}
        layout="vertical"
        className="filter-form"
        preserve={true}
      >
        <Form.Item name="merchant" label="Merchant" initialValue={[]}>
          <Select
            mode="multiple"
            placeholder="All"
            loading={loading}
            allowClear
            options={merchants}
          />
        </Form.Item>

        <Form.Item name="brand" label="Brand" initialValue={[]}>
          <Select
            mode="multiple"
            placeholder="All"
            loading={loading}
            allowClear
            options={brands}
            onChange={handleBrandChange}
          />
        </Form.Item>

        <Form.Item name="model" label="Model" initialValue={[]}>
          <Select
            mode="multiple"
            placeholder="All"
            loading={loading}
            allowClear
            options={models}
            onOpenChange={handleModelDropdownVisibleChange}
          />
        </Form.Item>

        <Form.Item name="status" label="Status" initialValue={[]}>
          <Select
            mode="multiple"
            placeholder="All"
            allowClear
            options={defaultOptions.status}
          />
        </Form.Item>
      </Form>
    </DrawerCustom>
  );
};

export default FilterDrawer;
