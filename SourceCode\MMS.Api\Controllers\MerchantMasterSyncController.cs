using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MMS.Api.Filters;
using MMS.Api.Security;
using MMS.Core.CoreUTI.Enum;
using MMS.Core.Services;
using MMS.Infrastructure.Services.LinklyFake;

namespace MMS.Api.Controllers
{
    /// <summary>
    /// Controller for merchant master synchronization operations
    /// </summary>
    [ApiController]
    [Authorize]
    [Route("merchant-sync")]
    public class MerchantMasterSyncController : ControllerBase
    {
        private readonly ILinklyFakeService _linklyFakeService;
        private readonly IMerchantMasterSyncService _merchantMasterSyncService;

        /// <summary>
        /// Constructor for MerchantMasterSyncController
        /// </summary>
        /// <param name="linklyFakeService">Service for interacting with the LinklyFake API</param>
        /// <param name="merchantMasterSyncService">Service for synchronizing merchant master data</param>
        public MerchantMasterSyncController(
            ILinklyFakeService linklyFakeService,
            IMerchantMasterSyncService merchantMasterSyncService)
        {
            _linklyFakeService = linklyFakeService ?? throw new ArgumentNullException(nameof(linklyFakeService));
            _merchantMasterSyncService = merchantMasterSyncService ?? throw new ArgumentNullException(nameof(merchantMasterSyncService));
        }

        /// <summary>
        /// Synchronizes merchant masters from the LinklyFake API to the MMS database
        /// </summary>
        /// <returns>Summary of sync operation</returns>
#if DEBUG
        [AllowAnonymous] // Allow anonymous access in debug mode for testing
#endif
        [HttpGet("sync-linkly-merchant-masters")]
        [ApiAuthority(
            Function = ApiSecure.ApiMerchantManagement,
            Action = ApiSecure.EditAction)]
        public async Task<IActionResult> SyncLinklyMerchantMasters()
        {
            // Fetch merchant masters from Linkly API
            var merchantMasters = await _linklyFakeService.GetAllMerchantMastersAsync();

            // Sync merchant masters with our database
            var (inserted, updated, deleted) = await _merchantMasterSyncService.SyncMerchantMastersFromLinklyAsync(merchantMasters);

            // Return summary
            return Ok(new
            {
                Success = true,
                Message = $"Synchronized merchant masters from Linkly: {inserted} inserted, {updated} updated, {deleted} deleted",
                Inserted = inserted,
                Updated = updated,
                Deleted = deleted
            });
        }
    }
}
