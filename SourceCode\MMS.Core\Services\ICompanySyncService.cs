using System.Collections.Generic;
using System.Threading.Tasks;
using MMS.Core.Entities;
using MMS.Core.Services.Base;
using MMS.Model.LinklyFake;

namespace MMS.Core.Services
{
    /// <summary>
    /// Service for synchronizing company data from external sources
    /// </summary>
    public interface ICompanySyncService : IBaseService<CompanyDetails>
    {
        /// <summary>
        /// Synchronizes company data from Linkly to the MMS database
        /// </summary>
        /// <param name="linklyCompanies">List of companies from Linkly API</param>
        /// <returns>Summary of sync operation (inserted, updated, deleted counts)</returns>
        Task<(int inserted, int updated, int deleted)> SyncCompaniesFromLinklyAsync(List<LinklyCompanyDto> linklyCompanies);
    }
}
