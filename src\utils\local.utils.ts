import moment from 'moment';
import { Cookies } from 'react-cookie';
import { CookieSetOptions } from 'universal-cookie';
import {
  LOCAL_COOKIE_KEY,
  LOCAL_STORAGE_KEY,
} from '../constants/app-constants';
import { UserProfileModel } from '../models/user.model';
import { SignInResponseModel } from '../models/sign-in.model';
import { LocationStorageItem } from '../models/location-tree.model';
import { CompanyItem } from '../models/company-tree.model';

// Store selection path info for more complete restoration
interface SelectionPathInfo {
  nodeId: number;
  parentIds: number[];
}

const PATH_MAPPINGS = {
  APP_TEMPLATE: '/app-base-template',
  MERCHANT_LOCATIONS: '/merchant-locations',
} as const;

const transformPathToKey = (path: string): string => {
  return path.replace(/[^a-zA-Z0-9]/g, '_').toUpperCase();
};

export const getStorageKeyFromPath = (path: string): string => {
  const mappedKey = Object.entries(PATH_MAPPINGS).find(
    ([_, value]) => value === path
  )?.[0];

  return mappedKey || transformPathToKey(path);
};

export const LocalUtils = {
  get(key: string) {
    return localStorage.getItem(key);
  },

  set(key: string, value: string) {
    localStorage.setItem(key, value);
  },

  remove(key: string) {
    return localStorage.removeItem(key);
  },

  setCookie(key: string, value: string, opt: CookieSetOptions) {
    const cookies = new Cookies();
    cookies.set(key, value, opt);
  },

  getCookie(key: string) {
    const cookies = new Cookies();
    return cookies.get(key);
  },

  removeCookie(key: string, opt: CookieSetOptions | undefined) {
    const cookies = new Cookies();
    cookies.remove(key, opt);
  },

  // implement local session in here

  clear() {
    const cookies = new Cookies();
    Object.keys(cookies.getAll()).forEach((item) => {
      cookies.remove(item);
    });

    localStorage.clear();
    sessionStorage.clear();
  },

  setAuthenticatedData(data: SignInResponseModel, isRemember: boolean = false) {
    const tokenExpire = moment()
      .add(data.expiresIn - 60, 'seconds')
      .toDate();

    LocalUtils.setCookie(LOCAL_COOKIE_KEY.ID_TOKEN, data.idToken, {
      expires: tokenExpire,
      path: '/',
      secure: window.location.protocol === 'https:',
      sameSite: 'lax'
    });

    LocalUtils.set(LOCAL_STORAGE_KEY.ACCESS_TOKEN, data.accessToken);
    LocalUtils.set(LOCAL_STORAGE_KEY.REFRESH_TOKEN, data.refreshToken);

    const loginExpire = moment()
      .add(isRemember ? 7 : 1, 'days')
      .toDate();

    LocalUtils.setCookie(LOCAL_COOKIE_KEY.IS_LOGGED_IN, 'true', {
      expires: loginExpire,
      path: '/',
      secure: window.location.protocol === 'https:',
      sameSite: 'lax'
    });

    LocalUtils.set(
      LOCAL_STORAGE_KEY.IS_REMEMBER_ME,
      isRemember ? 'true' : 'false',
    );
  },

  setUserInfo(data: UserProfileModel) {
    LocalUtils.set(LOCAL_STORAGE_KEY.USER_INFO, JSON.stringify(data));
  },

  getUserInfo(): UserProfileModel {
    const data = LocalUtils.get(LOCAL_STORAGE_KEY.USER_INFO);

    return data
      ? JSON.parse(data)
      : {
          userId: 0,
          firstName: '',
          lastName: '',
          fullName: '',
          email: '',
          profilePictureUrl: '',
          username: '',
          isAdmin: false,
        };
  },

  setLocationSelections(data: LocationStorageItem[]) {
    LocalUtils.set(LOCAL_STORAGE_KEY.LOCATION_SELECTIONS, JSON.stringify(data));
  },

  getLocationSelections(): LocationStorageItem[] {
    const data = LocalUtils.get(LOCAL_STORAGE_KEY.LOCATION_SELECTIONS);
    return data ? JSON.parse(data) : [];
  },

  getHierarchyKeyLocation(): string {
    const locations = this.getLocationSelections();
    if (locations.length === 0) return '';

    // Keep original order and join with comma
    return locations.map((item) => item.id).join(',');
  },

  setCompanySelection(item: CompanyItem, parentIds: number[] = [], pathname: string) {
    const selectionInfo: SelectionPathInfo = {
      nodeId: item.id,
      parentIds: parentIds || []
    };
    const storageKey = `${LOCAL_STORAGE_KEY.COMPANY_TREE_SELECTION}_${getStorageKeyFromPath(pathname)}`;
    LocalUtils.set(storageKey, JSON.stringify(selectionInfo));
  },

  getCompanySelection(pathname: string): SelectionPathInfo | null {
    const storageKey = `${LOCAL_STORAGE_KEY.COMPANY_TREE_SELECTION}_${getStorageKeyFromPath(pathname)}`;
    const data = LocalUtils.get(storageKey);
    return data ? JSON.parse(data) : null;
  },
};

export default LocalUtils;
