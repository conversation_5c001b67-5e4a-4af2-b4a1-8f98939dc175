import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Image, Input } from 'antd';
import { useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import LanguageDropdown from '../../../components/shared/language';
import { useLocation, useNavigate } from 'react-router-dom';
import showNotification from '../../../components/shared/notification';
import { OTPProps } from 'antd/es/input/OTP';
import '../index.scss';
import { AuthApi } from '../../../apis/auth.api';
import { VerifyOtpModel } from '../../../models/sign-in.model';
import LocalUtils from '../../../utils/local.utils';
import {
  LOCAL_COOKIE_KEY,
  LOCAL_STORAGE_KEY,
} from '../../../constants/app-constants';
import { UserProfileModel } from '../../../models/user.model';
import { getThemeAsset } from '../../../utils/assets';
import { ROUTE_PATHS } from '../../../constants/router.constants';
import moment from 'moment';
import { useSetRecoilState } from 'recoil';
import { authState } from '../../../states/auth';

function ConfirmCodePage() {
  const [loading, setLoading] = useState(false);
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [otp, setOtp] = useState('');
  const otpInputRef = useRef<any>(null);
  const location = useLocation();
  const setAuth = useSetRecoilState(authState);
  const [errorMessage, setErrorMessage] = useState('');

  const baseUrl = import.meta.env.VITE_MMS_API_URL;

  const { isAdmin } = location.state;
  const { selectedCompany, accessStatus, isPartnerAccess } = !isAdmin
    ? location.state
    : { selectedCompany: null, accessStatus: null, isPartnerAccess: false };

  const setUserInfo = async () => {
    const userInfo = await AuthApi.getUserInfo();

    try {
      const userInfor: UserProfileModel = {
        userId: userInfo.data.id,
        firstName: userInfo.data.firstName,
        lastName: userInfo.data.lastName,
        fullName: userInfo.data.fullName,
        email: userInfo.data.email,
        profilePictureUrl: `${baseUrl}/${userInfo.data.profilePictureUrl}`,
        username: userInfo.data.username,
        isAdmin: userInfo.data.isAdmin,
      };

      LocalUtils.setUserInfo(userInfor);

      const loginExpire = moment().add(1, 'days').toDate();

      LocalUtils.setCookie(LOCAL_COOKIE_KEY.IS_LOGGED_IN, 'true', {
        expires: loginExpire,
        path: '/',
        secure: window.location.protocol === 'https:',
        sameSite: 'lax',
      });

      setAuth({
        isLogined: true,
        user: userInfor,
        error: '',
      });
    } catch (error) {
      console.error('Get user info fail:', error);
      showNotification('error', t('action.getUserInfo'));
    }
  };
  

  const handleClick = async (otpValue?: string): Promise<void> => {
    const codeToVerify = otpValue ?? otp;

    if (!codeToVerify) {
      showNotification('warning', t('login.inputConfirmCode'));
      return;
    }
    setErrorMessage('');
    setLoading(true);

    try {
      const verifyOtpModel: VerifyOtpModel = {
        otpCode: codeToVerify,
        companyId: selectedCompany,
        accessStatusId: accessStatus,
        isPartnerAccess: isPartnerAccess,
      };

      const resp = await AuthApi.verifyOtp(verifyOtpModel);
      if (resp.status === 200) {
        LocalUtils.set(LOCAL_STORAGE_KEY.ACCESS_TOKEN, resp.data.token);
        await setUserInfo();

        navigate(ROUTE_PATHS.Home);
      }
    } catch (error: any) {
      setErrorMessage(error.response.data.message);
      setAuth((prevState) => ({
        ...prevState,
        error: 'Failed to verify OTP code',
      }));

      // Clear OTP prevent auto submit multiple time when user enter new OTP
      setOtp('');
      otpInputRef.current?.focus({ cursor: 'start' }); //Focus first cell after clear.
    } finally {
      setLoading(false);
    }
  };

  const onChange: OTPProps['onChange'] = (text) => {
    setOtp(text);

    // Since onChange is only triggered when all fields are filled,
    // we can directly call handleClick with the text value
    handleClick(text);
  };

  const sharedProps: OTPProps = {
    onChange,
    autoFocus: true,
  };

  return (
    <div className="sign-in-page">
      <Card className="card-sign-in">
        <div className="header-sign-in">
          <div className="logo-image">
            <Image
              width={160}
              preview={false}
              src={getThemeAsset('images', 'logo-auth.svg')}
            />
          </div>
          <h2 className="h2-bold mr-bottom-8">{t('login.confirmCode')}</h2>
          <p className="body-2-regular opacity-80">
            {t('login.confirmCodeDescription')}
          </p>

          {errorMessage && (
            <div className="alert-animation-wrapper">
              <Alert
                message={errorMessage}
                type="error"
                showIcon
                closable
                className="alert-animation"
              />
            </div>
          )}
        </div>

        <div className="tab-container">
          <div className="input-otp-content text-center mr-bottom-24">
            <Input.OTP
              variant="filled"
              length={6}
              value={otp}
              ref={otpInputRef}
              {...sharedProps}
            />
          </div>

          <div className="mr body-2-regular text-center">
            <span style={{ color: '#024042', opacity: 0.8 }}>
              {t('login.notReceiveCode')}
            </span>
            <a
              href="/create-account"
              style={{ color: '#024042', textDecoration: 'underline' }}
            >
              {t('login.resendCode')}
            </a>
          </div>
        </div>

        <div className="submit-button">
          <Button
            type="primary"
            htmlType="submit"
            block
            loading={loading}
            onClick={() => handleClick()}
          >
            {t('login.continue')}
          </Button>
        </div>

        <LanguageDropdown className="mr-top-36" />
      </Card>
    </div>
  );
}

export default ConfirmCodePage;
