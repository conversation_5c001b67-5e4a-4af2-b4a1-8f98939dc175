﻿using MMS.Core.Services.Base;
using MMS.Core.Entities;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using MMS.Model.ApiModelRequest;
using MMS.Model.ApiModelResponse;
using MMS.Model.ApiModelResponse.Company;
using MMS.Infrastructure.Commons;

namespace MMS.Core.Services.Companies
{
    public interface ICompanyDetailsService : IBaseService<CompanyDetails>
    {
        IList<ViewItemModel> GetAllCompany(int skip, int take, string searchKey, int userId);
        IList<ViewItemModel> GetAllCompanyAssociated(int skip, int take, string searchKey, int userId);

        IList<string> CheckCompanyNameExist(string value);
        void UpdateBusinessType(int companyDetailId, int[] typeIds);
        void UpdatePersonalDetail(int companyDetailId, int[] personalIds, bool IsDirector = false);
        void UpdateMerchantAssociations(int companyDetailId, int[] merchantIdsInts);
        void UpdateCompanyAssociations(int companyDetailId, int[] companyIds);

        IList<CompanyDetails> GetAllCompanyDetailsesButItSelf(int id);
        IDictionary<int, string> GetAllBySearchKey(string keySearch);
        IList<string> CheckBusinessNameExist(string value, int id);

        IList<CompanyDetails> GetAllCompanyDetailses();
        int LastCompanyDetail();
        IList<ViewItemModel> GetViewListCompanyDetailsByLang(int skip, int take, string searchKey);
        int GetCompanyDetailsNumberByLang(string searchKey);
        IList<ViewItemModel> GetSelectionList(string[] property, string table, string mapTable, int editingId,
            string masterId, string mapId, int language, int take, int skip, string typeOfEntity, string seachKey,
            bool selfReference = false);

        IList<ViewItemModel> GetSelectedList(string[] property, string table, string mapTable, int editingId,
            string masterId, string mapId, int language, int take, int skip, string typeOfEntity, string seachKey);

        bool IsExisted(int systemId, int companyID);

        /// <summary>
        /// Get Sub Companies by Parent id
        /// </summary>
        /// <param name="parentId"></param>
        /// <returns></returns>
        Task<IList<CompanyDetails>> GetSubCompaniesByParentIdAsync(int parentId);

        /// <summary>
        /// Get list view Companies master
        /// </summary>
        /// <param name="skip"></param>
        /// <param name="take"></param>
        /// <param name="searchKey"></param>
        /// <returns></returns>
        Task<IList<CompanyDetails>> GetCompanyDetailsMasterAsync(int skip, int take, string searchKey);

        /// <summary>
        /// Get Companies master
        /// </summary>
        /// <param name="searchKey">Search key for filtering companies</param>
        /// <param name="pagingParameter">Paging parameters</param>
        /// <returns>Paged list of companies</returns>
        Task<PagingResponse<CompanyListResponse>> GetCompaniesMasterAsync(string searchKey, PagingParameter pagingParameter);

        /// <summary>
        /// Activate or Deactivate by list ids
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="isActivate"></param>
        void ActivateDeactivateByListIds(IList<int> ids, bool isActivate);

        /// <summary>
        /// Remove by list ids
        /// </summary>
        /// <param name="ids"></param>
        void RemoveByListIds(IList<int> ids);

        /// <summary>
        /// Asynchronously retrieves a list of all master companies (companies without a parent).
        /// </summary>
        /// <returns>
        /// A task containing a list of <see cref="CompanyDetails"/> objects, ordered by <c>OrderIndex</c>.
        /// </returns>
        Task<List<CompanyDetails>> GetAllMasterCompanyAsync();



        /// <summary>
        /// Asynchronously retrieves a list of all master companies (companies without a parent) excluding ExcludingCompanyId
        /// </summary>
        /// <returns>
        /// A task containing a list of <see cref="excludingCompanyIds"/> objects, ordered by <c>OrderIndex</c>.
        /// </returns>
        Task<List<CompanyDetails>> GetAllCompanyExcludingCurrentAsync(List<int> excludingCompanyIds);

        /// <summary>
        /// Get company info by id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<CompanyInfoResponse> GetCompanyInfo(int id);

        /// <summary>
        /// Get users for select
        /// </summary>
        /// <param name="companyId"></param>
        /// <param name="searchKey"></param>
        /// <param name="skip"></param>
        /// <param name="take"></param>
        /// <param name="filterType"></param>
        /// <returns></returns>
        Task<PagingResponse<DataItemResponse>> GetSourceForSelectUserAsync(int companyId, List<int> selectedIds, string searchKey, SelectedFilterType filterType, PagingParameter pagingParameter);

        /// <summary>
        /// Retrieves a paged list of companies that can be selected, filtering by the provided parameters.
        /// </summary>
        /// <param name="parentId">company group mapping for select</param>
        /// <param name="selectedIds">List of company IDs that are already selected</param>
        /// <param name="selectRemoveFormParameter">Parameters for pagination, filtering and sorting</param>
        /// <returns>A paged response containing company data items</returns>
        Task<PagingResponse<DataItemResponse>> GetSourceForSelectCompanyAsync(int? parentId, List<int> selectedIds, SelectRemoveFormParameter selectRemoveFormParameter);
    }
}
