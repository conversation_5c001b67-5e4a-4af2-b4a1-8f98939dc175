using MMS.Core.CoreUTI;
using MMS.Core.Entities;
using MMS.Core.Repository;
using MMS.Core.Services.Impl.Base;
using MMS.Infrastructure.Commons;
using MMS.Model.ApiModelRequest;
using MMS.Model.ApiModelResponse;
using MMS.Model.Base;
using MMS.Model.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace MMS.Core.Services.Impl
{
    public class MerchantMasterService : BaseService<MerchantMaster>, IMerchantMasterService
    {
        private readonly IMerchantMasterRepository _merchantMasterRepository;
        private readonly IMerchantLocationRepository _merchantLocationRepo;
        public MerchantMasterService(
            ILanguageExecuteService languageExecuteService,
            IMerchantLocationRepository merchantLocationRepo,
            IMerchantMasterRepository merchantMasterRepository) : base(merchantMasterRepository, languageExecuteService)
        {
            _merchantMasterRepository = merchantMasterRepository;
            _merchantLocationRepo = merchantLocationRepo;
        }

        //======================================================
        // Merchant Info
        //======================================================
        public int LastMaster()
        {
            var firstOrDefault = base.GetAll(null, false, true, m => m.Id.ToString(), true).FirstOrDefault();
            return firstOrDefault != null ? firstOrDefault.Id : 0;
        }


        public IList<ViewItemModel> GetViewItemsByLang(string searchKey, int skip, int take)
        {
            return base.GetListItemByLang(p=>p.lszMerchantName, searchKey ?? "", skip,
                take);
        }

        public IList<ViewItemModel> GetViewItemsByLang(string searchKey, int skip, int take, List<int> ids)
        {
            return base.GetListItemByLangWithAdminAccess(p => p.lszMerchantName, searchKey, skip,
                take, ids);
        }

        public IList<ViewItemModel> GetSelectedMerchantByParentsAndSelectedLists(string searchKey, int skip, int take,
            string companies, bool inCompanyList, string ids, bool inIdList)
        {

            var result = new List<ViewItemModel>();
            var entities = inIdList
                ? GetAll(p => ids.Contains(p.MerchantCompanyId.ToString())).ToList()
                : GetAll(p => !ids.Contains(p.MerchantCompanyId.ToString())).ToList();
            entities = inCompanyList
                ? entities.Where(p => companies.Contains(p.MerchantCompanyId.ToString())).ToList()
                : entities.Where(p => !companies.Contains(p.MerchantCompanyId.ToString())).ToList();

            foreach (var item in entities)
            {
                result.Add(new ViewItemModel() { Id = item.Id, IsActive = item.IsActive, Label = item.lszMerchantName });
            }

            if (!string.IsNullOrEmpty(searchKey))
            {
                result = result.Where(p => p.Label.Contains(searchKey)).ToList();
            }

            result = result.OrderBy(p => p.Label).ToList();

            if (take != Constants.TakeAll)
            {
                return result.Skip(skip).Take(take).ToList();
            }

            return result.ToList();
        }


        public IList<ViewItemModel> GetViewMerchantByCompanies(string searchKey, int skip, int take,
            string companies, bool inList, string[] property = null)
        {
            var result = new List<ViewItemModel>();
            var entities = inList
                ? GetAll(p => companies.Contains(p.MerchantCompanyId.ToString())).ToList()
                : GetAll(p => !companies.Contains(p.MerchantCompanyId.ToString())).ToList();

            foreach (var item in entities)
            {
                result.Add(new ViewItemModel() { Id = item.Id, IsActive = item.IsActive, Label = item.lszMerchantName });
            }

            if (!string.IsNullOrEmpty(searchKey))
            {
                result = result.Where(p => p.Label.Contains(searchKey)).ToList();
            }

            result = result.OrderBy(p => p.Label).ToList();

            if (take != Constants.TakeAll)
            {
                return result.Skip(skip).Take(take).ToList();
            }

            return result.ToList();
        }

        public IList<string> CheckMerchantNameExist(string input, int id)
        {
            return CheckExistsRecord("lszMerchantName", input, id);
        }

        public MerchantMaster GetByName(string name)
        {
            var result = FirstOrDefault(m => m.lszMerchantName.Equals(name));
            return result;
        }

        public MerchantMaster GetByEmail(string email)
        {
            var result = FirstOrDefault(m => m.lszEmailAddress.Equals(email));
            return result;
        }

        public IList<MerchantMaster> GetAllMerchantByCompanyAssociations(int companyId)
        {
            return GetAll(m => m.CompanyDetailses.Any(c => c.Id.Equals(companyId)))
                .ToList();
        }

        public IList<int> GetMerchantIdsByMerchantCompanyIds(string merchantCompanyIds)
        {
            return Select(x => x.Id, x => merchantCompanyIds.Contains("|" + x.MerchantCompanyId + "|")) as IList<int>;
        }

        public MerchantMaster GetById(int id,bool isIncludeList = false, params Expression<Func<MerchantMaster, object>>[] includes)
        {
            return _merchantMasterRepository.GetByIdWithIncludes(id, isIncludeList , includes);
        }

        public async Task<(IList<MerchantMaster> MerchantMasters, bool MultiMerchantLocation)> GetSourceForDeviceSelectMerchantAsync(int terminalMasterId)
        {
            return await _merchantMasterRepository.GetSourceForDeviceSelectMerchantAsync(terminalMasterId);
        }

        public async Task<(PagingResponse<SelectListItemModel> Merchants, bool MultiMerchantLocation)> GetSourceForSelectMerchantAsync(int terminalMasterId, IList<int> selectedIds, string searchKey, SelectedFilterType filter, PagingParameter pagingParameter)
        {
            return await _merchantMasterRepository.GetSourceForSelectMerchantAsync(terminalMasterId, selectedIds, searchKey, filter, pagingParameter);
        }

        public async Task<IList<MerchantMaster>> GetSelectMerchantByLocationItemAsync(int locationAreaId)
        {
            return await _merchantMasterRepository.GetSelectMerchantByLocationItemAsync(locationAreaId);
        }

        public async Task<IList<MerchantMaster>> GetSourceMerchantMasterListToSelectForLocationAsync(int locationId)
        {
            var merchantMasterList = await _merchantMasterRepository.GetMerchantMasterListWithNotNullMerchantCompanyIdAsync();
            var merchantListSelectedByOtherLocation = await _merchantLocationRepo.GetMerchantLocationListSelectedByOtherLocationAsync(locationId);

            if (merchantListSelectedByOtherLocation is not null && merchantListSelectedByOtherLocation.Count == 0)
                return merchantMasterList;

            var commonItems = merchantMasterList
                .Where(p => !merchantListSelectedByOtherLocation.Exists(m => m.Id == p.Id))
                .ToList();

            return commonItems; 
        }

        public async Task<PagingResponse<DataItemResponse>> GetSourceMerchantMasterListToSelectForLocationApiAsync(int locationId, IEnumerable<int> selectedIds, SelectRemoveFormParameter selectRemoveFormParameter)
        {
            var merchantMasterList = (await _merchantMasterRepository.GetMerchantMasterListWithNotNullMerchantCompanyIdAsync()).ToList();
            var merchantListSelectedByOtherLocation = await _merchantLocationRepo.GetMerchantLocationListSelectedByOtherLocationAsync(locationId);
            List<MerchantMaster> sourceList;

            if (merchantListSelectedByOtherLocation is not null && merchantListSelectedByOtherLocation.Count == 0)
                sourceList = merchantMasterList;

            sourceList = [.. merchantMasterList.Where(p => !merchantListSelectedByOtherLocation.Exists(m => m.Id == p.Id))];

            var pagingSource = _merchantLocationRepo.GetPagingSourceMerchantMaster(sourceList, selectedIds, selectRemoveFormParameter);

            return pagingSource;
        }

        public IList<ListItemResponse> GetAllActivateMerchants()
        {
            var allMerchants = GetAllV2(p => p.IsActive && p.IsStatus != Constants.DELETE_RECORD);

            return [.. allMerchants.Select(p => new ListItemResponse
            {
                Id = p.Id,
                Name = p.Name,
                IsActive = p.IsActive,
            })];
        }
    }

}
