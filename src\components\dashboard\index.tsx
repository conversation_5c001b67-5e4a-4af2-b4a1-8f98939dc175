import React, { useEffect, useState, useRef } from 'react';
import { Responsive, WidthProvider } from 'react-grid-layout';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import TerminalMap from './charts/terminal-map';
import TerminalStatisticsChart from './charts/terminal-statistics';
import ModelDistribution from './charts/model-distribution';
import OfflineTerminalsStatistics from './charts/offline-terminals-statistics';
import StackedBarChart from './charts/store-client-distribution';
import { Switch, Select, Space, FloatButton, Drawer, Radio, Card } from 'antd';
import { SettingOutlined } from '@ant-design/icons';

const ResponsiveGridLayout = WidthProvider(Responsive);

const widgets = [
  { key: '1', content: <TerminalMap /> },
  {
    key: '2',
    content: (
      <div style={{ width: '100%', height: '100%', padding: 12 }}>
        <div className="title">Terminal Statistics</div>
        <TerminalStatisticsChart />
      </div>
    ),
  },
  {
    key: '3',
    content: (
      <div style={{ width: '100%', height: '100%', padding: 12 }}>
        <div className="title">Store Client Distribution - Bar Chart</div>
        <StackedBarChart />
      </div>
    ),
  },
  {
    key: '4',
    content: (
      <div style={{ width: '100%', height: '100%', padding: 12 }}>
        <div className="title">Model Distribution</div>
        <ModelDistribution />
      </div>
    ),
  },
  {
    key: '5',
    content: (
      <div style={{ width: '100%', height: '100%', padding: 12 }}>
        <div className="title">Offline Terminals Statistics</div>
        <OfflineTerminalsStatistics />
      </div>
    ),
  },
];

const layouts = {
  lg: [
    { i: '1', x: 0, y: 0, w: 4, h: 1 },
    { i: '2', x: 0, y: 0, w: 2, h: 1 },
    { i: '3', x: 2, y: 1, w: 2, h: 1 },
    { i: '4', x: 0, y: 1, w: 2, h: 1 },
    { i: '5', x: 2, y: 2, w: 2, h: 1 },
  ],
  compact: [
    { i: '1', x: 0, y: 0, w: 2, h: 1 },
    { i: '2', x: 2, y: 0, w: 2, h: 1 },
    { i: '3', x: 0, y: 1, w: 2, h: 2 },
    { i: '4', x: 2, y: 1, w: 2, h: 1 },
    { i: '5', x: 2, y: 0, w: 2, h: 1 },
  ],
  wide: [
    { i: '1', x: 0, y: 0, w: 4, h: 1 },
    { i: '2', x: 0, y: 0, w: 2, h: 1 },
    { i: '3', x: 0, y: 1, w: 2, h: 1 },
    { i: '4', x: 2, y: 1, w: 2, h: 1 },
    { i: '5', x: 2, y: 2, w: 2, h: 1 },
  ],
  tall: [
    { i: '1', x: 0, y: 0, w: 2, h: 2 },
    { i: '2', x: 2, y: 0, w: 2, h: 1 },
    { i: '3', x: 2, y: 1, w: 2, h: 1 },
    { i: '4', x: 0, y: 2, w: 2, h: 1 },
    { i: '5', x: 2, y: 2, w: 2, h: 1 },
  ],
};

const LayoutPreview: React.FC<{ type: string }> = ({ type }) => {
  const getLayoutPreview = () => {
    const layout = layouts[type as keyof typeof layouts] || layouts.lg;
    return (
      <div
        style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(4, 1fr)',
          gap: '4px',
          width: '200px',
          height: '120px',
          padding: '8px',
          background: '#f5f5f5',
          borderRadius: '4px',
        }}
      >
        {layout.map((item, index) => (
          <div
            key={index}
            style={{
              gridColumn: `span ${item.w}`,
              gridRow: `span ${item.h}`,
              background: '#00a26d',
              borderRadius: '2px',
              opacity: 0.8,
            }}
          />
        ))}
      </div>
    );
  };

  return getLayoutPreview();
};

const Dashboard: React.FC = () => {
  const [rowHeight, setRowHeight] = useState(0);
  const [isDraggable, setIsDraggable] = useState(() => {
    const saved = localStorage.getItem('dashboard_isDraggable');
    return saved ? JSON.parse(saved) : false;
  });
  const [isResizable, setIsResizable] = useState(() => {
    const saved = localStorage.getItem('dashboard_isResizable');
    return saved ? JSON.parse(saved) : false;
  });
  const [showControls, setShowControls] = useState(false);
  const [currentLayoutType, setCurrentLayoutType] = useState(() => {
    return localStorage.getItem('dashboard_layoutType') || 'default';
  });

  useEffect(() => {
    const calculateRowHeight = () => {
      const height = 300;
      setRowHeight(height);
    };

    calculateRowHeight();
    window.addEventListener('resize', calculateRowHeight);

    return () => {
      window.removeEventListener('resize', calculateRowHeight);
    };
  }, []);

  // Save settings to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('dashboard_isDraggable', JSON.stringify(isDraggable));
  }, [isDraggable]);

  useEffect(() => {
    localStorage.setItem('dashboard_isResizable', JSON.stringify(isResizable));
  }, [isResizable]);

  useEffect(() => {
    localStorage.setItem('dashboard_layoutType', currentLayoutType);
  }, [currentLayoutType]);

  const layoutTypeOptions = [
    { label: 'Default', value: 'default' },
    { label: 'Compact', value: 'compact' },
    { label: 'Wide', value: 'wide' },
    { label: 'Tall', value: 'tall' },
  ];

  const getCurrentLayout = () => {
    if (currentLayoutType === 'default') {
      return layouts.lg;
    }
    return layouts[currentLayoutType as keyof typeof layouts];
  };

  return (
    <div style={{ position: 'relative' }}>
      <FloatButton
        icon={<SettingOutlined />}
        onClick={() => setShowControls(!showControls)}
        type="primary"
      />

      <Drawer
        title="Dashboard Settings"
        placement="right"
        onClose={() => setShowControls(false)}
        open={showControls}
        width={300}
      >
        <Space direction="vertical" size="middle" style={{ width: '100%' }}>
          <div>
            <span style={{ marginRight: 8 }}>Draggable:</span>
            <Switch checked={isDraggable} onChange={setIsDraggable} />
          </div>
          <div>
            <span style={{ marginRight: 8 }}>Resizable:</span>
            <Switch checked={isResizable} onChange={setIsResizable} />
          </div>
          <div>
            <div style={{ marginBottom: 8 }}>Layout Type:</div>
            <Radio.Group
              value={currentLayoutType}
              onChange={(e) => setCurrentLayoutType(e.target.value)}
              style={{ width: '100%' }}
            >
              <Space direction="vertical" style={{ width: '100%' }}>
                {layoutTypeOptions.map((option) => (
                  <Card
                    key={option.value}
                    size="small"
                    style={{
                      cursor: 'pointer',
                      border:
                        currentLayoutType === option.value
                          ? '2px solid #00a26d'
                          : '1px solid #d9d9d9',
                    }}
                    onClick={() => setCurrentLayoutType(option.value)}
                  >
                    <div
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '12px',
                      }}
                    >
                      <LayoutPreview type={option.value} />
                      <span>{option.label}</span>
                    </div>
                  </Card>
                ))}
              </Space>
            </Radio.Group>
          </div>
        </Space>
      </Drawer>

      <ResponsiveGridLayout
        className="layout"
        layouts={{ lg: getCurrentLayout() }}
        breakpoints={{ lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 }}
        cols={{ lg: 4, md: 4, sm: 2, xs: 1, xxs: 1 }}
        rowHeight={rowHeight}
        isDraggable={isDraggable}
        isResizable={isResizable}
        useCSSTransforms
        compactType="vertical"
        autoSize
      >
        {widgets.map((widget) => (
          <div
            key={widget.key}
            className="dashboard-widget"
            style={{
              background: '#fff',
              borderRadius: 8,
              boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              height: '100%',
              width: '100%',
              overflow: 'hidden',
            }}
          >
            {typeof widget.content === 'string' ? (
              <h3>{widget.content}</h3>
            ) : (
              widget.content
            )}
          </div>
        ))}
      </ResponsiveGridLayout>
    </div>
  );
};

export default Dashboard;
