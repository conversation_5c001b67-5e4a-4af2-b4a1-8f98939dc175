import { CountryData } from "../../../../../models/map.model";

export interface Position {
	coordinates: [number, number];
	zoom: number;
  } 

export const countryData: Record<string, CountryData> = {
  // Asia
  Vietnam: { name: 'Vietnam', terminals: 150, active: true },
  Japan: { name: 'Japan', terminals: 200, active: true },
  'South Korea': { name: 'South Korea', terminals: 180, active: true },
  Singapore: { name: 'Singapore', terminals: 120, active: true },
  Malaysia: { name: 'Malaysia', terminals: 90, active: true },
  Indonesia: { name: 'Indonesia', terminals: 80, active: true },
  Thailand: { name: 'Thailand', terminals: 70, active: true },
  Philippines: { name: 'Philippines', terminals: 60, active: true },
  Myanmar: { name: 'Myanmar', terminals: 40, active: true },
  Cambodia: { name: 'Cambodia', terminals: 30, active: true },
  Laos: { name: 'Laos', terminals: 25, active: true },

  // Europe
  Germany: { name: 'Germany', terminals: 250, active: true },
  France: { name: 'France', terminals: 220, active: true },
  'United Kingdom': { name: 'United Kingdom', terminals: 200, active: true },
  Italy: { name: 'Italy', terminals: 180, active: true },
  Spain: { name: 'Spain', terminals: 150, active: true },

  // Americas
  'United States': { name: 'United States', terminals: 300, active: true },
  Canada: { name: 'Canada', terminals: 180, active: true },
  Mexico: { name: 'Mexico', terminals: 120, active: true },
  Brazil: { name: 'Brazil', terminals: 100, active: true },
};

export const regionData = [
  { name: 'North America', terminals: 100000 },
  { name: 'South America', terminals: 350000 },
  { name: 'Asia', terminals: 200000 },
  { name: 'Australia', terminals: 250000 },
]; 