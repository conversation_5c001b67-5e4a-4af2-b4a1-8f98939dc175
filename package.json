{"name": "mms", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "build-skip-types": "vite build --emptyOutDir", "lint": "eslint .", "preview": "vite preview", "format": "prettier --write ."}, "dependencies": {"antd": "^5.25.2", "antd-img-crop": "^4.25.0", "apexcharts": "^4.7.0", "axios": "^1.8.4", "dayjs": "^1.11.13", "i18next": "^24.2.3", "i18next-browser-languagedetector": "^8.0.4", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "moment": "^2.30.1", "qs": "^6.14.0", "react": "^18.2.0", "react-apexcharts": "^1.7.0", "react-cookie": "^8.0.1", "react-dom": "^18.2.0", "react-grid-layout": "^1.5.1", "react-i18next": "^15.4.1", "react-phone-input-2": "^2.15.1", "react-resize-detector": "^12.0.2", "react-router-dom": "^7.4.1", "react-simple-maps": "^3.0.0", "recoil": "^0.7.7", "recoil-nexus": "^0.5.1", "sass": "^1.86.1", "universal-cookie": "^8.0.1"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.16", "@types/qs": "^6.9.18", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/react-router-dom": "^5.3.3", "@types/react-simple-maps": "^3.0.6", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.23.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.5", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "prettier": "^3.5.3", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}