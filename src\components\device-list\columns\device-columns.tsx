import { Button, Dropdown } from 'antd';
import { EllipsisOutlined } from '@ant-design/icons';
import { TableColumnsType } from 'antd';
import { useTranslation } from 'react-i18next';
import { DeviceAssociatedResponseApiModel } from '../../../models/device-list.model';
import { STATUS_COLORS } from '../../../constants/device-list.constants';

export const getDeviceColumns = (
  handleMenuClick: (
    key: string,
    record: DeviceAssociatedResponseApiModel,
  ) => void,
): TableColumnsType<DeviceAssociatedResponseApiModel> => {
  const { t } = useTranslation();

  return [
    {
      title: (
        <span className="body-2-bold">{t('device.columns.deviceName')}</span>
      ),
      dataIndex: 'name',
      key: 'name',
      width: '20%',
    },
    {
      title: (
        <span className="body-2-bold">{t('device.columns.serialNumber')}</span>
      ),
      dataIndex: 'serialNumber',
      key: 'serialNumber',
    },
    {
      title: (
        <span className="body-2-bold">{t('device.columns.merchant')}</span>
      ),
      dataIndex: 'merchantDisplay',
      key: 'merchantDisplay',
    },
    {
      title: (
        <span className="body-2-bold">{t('device.columns.brandModel')}</span>
      ),
      key: 'brandModel',
      render: (record) => `${record.brandName} ${record.modelName}`,
    },
    {
      title: <span className="body-2-bold">{t('device.columns.created')}</span>,
      dataIndex: 'created',
      key: 'created',
    },
    {
      title: <span className="body-2-bold">{t('device.columns.status')}</span>,
      key: 'status',
      render: (record) => {
        const color = record.isActive
          ? STATUS_COLORS.online
          : STATUS_COLORS.offline;
        return (
          <span style={{ color }}>
            {record.isActive
              ? t('device.columns.online')
              : t('device.columns.offline')}
          </span>
        );
      },
    },
    {
      title: '',
      key: 'action',
      width: '8%',
      render: (_, record) => (
        <Dropdown
          menu={{
            items: [
              {
                key: 'details',
                label: t('device.actions.deviceSetup'),
              },
              {
                key: 'assign_merchants',
                label: t('device.actions.assignMerchants'),
              },
              {
                key: 'delete',
                label: t('device.actions.unassignDevice'),
                danger: true,
              },
            ],
            onClick: ({ key }) => handleMenuClick(key, record),
          }}
          trigger={['click']}
          placement="bottomRight"
        >
          <Button
            type="text"
            icon={<EllipsisOutlined />}
            style={{ padding: 0 }}
          />
        </Dropdown>
      ),
    },
  ];
};

export const getDeviceAvailablColumns =
  (): TableColumnsType<DeviceAssociatedResponseApiModel> => {
    const { t } = useTranslation();

    return [
      {
        title: (
          <span className="body-2-bold">
            {t('device.columns.serialNumber')}
          </span>
        ),
        dataIndex: 'serialNumber',
        key: 'serialNumber',
        width: 120,
      },
      {
        title: (
          <span className="body-2-bold">{t('device.columns.deviceName')}</span>
        ),
        dataIndex: 'name',
        key: 'name',
        width: 160,
        render: (text) => (
          <div className="text-overflow" title={text}>
            {text}
          </div>
        ),
      },
      {
        title: <span className="body-2-bold">{t('device.columns.brand')}</span>,
        dataIndex: 'brandName',
        key: 'brandModel',
        width: 80,
        render: (text) => (
          <div className="text-overflow" title={text}>
            {text}
          </div>
        ),
      },
      {
        title: <span className="body-2-bold">{t('device.columns.model')}</span>,
        key: 'modelName',
        width: 75,
        render: (record) => (
          <div className="text-overflow" title={record.modelName}>
            {record.modelName}
          </div>
        ),
      },
    ];
  };
