using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using MMS.Core.ConfigOptions;

namespace MMS.Core.Extensions
{
    /// <summary>
    /// Extension methods for IServiceCollection
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// Add terminal source configuration to the service collection
        /// This is used for both terminal and company data sources
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <param name="configuration">The configuration</param>
        /// <returns>The service collection</returns>
        public static IServiceCollection AddTerminalSourceConfig(this IServiceCollection services, IConfiguration configuration)
        {
            // Register options
            services.Configure<TerminalSourceOptions>(configuration.GetSection("TerminalSourceConfig"));

            return services;
        }
    }
}
