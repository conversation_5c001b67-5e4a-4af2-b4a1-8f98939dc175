using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using MMS.Api.Filters;
using MMS.Api.Security;
using MMS.Core.ConfigOptions;
using MMS.Core.CoreUTI.Enum;
using MMS.Core.Entities;
using MMS.Core.Extensions;
using MMS.Core.Models.Converters;
using MMS.Core.Services;
using MMS.Core.Services.Companies;
using MMS.Core.Services.Users;
using MMS.Core.Utils;
using MMS.Infrastructure.Commons;
using MMS.Infrastructure.Files;
using MMS.Infrastructure.Services.LinklyFake;
using MMS.Model.ApiModelRequest;
using MMS.Model.ApiModelRequest.CompanyManagement;
using MMS.Model.ApiModelResponse;
using MMS.Model.Common;
using Serilog;
using System;

namespace MMS.Api.Controllers;


[AllowAnonymous]
[Authorize]
[ApiController]
[Route("company-details")]
public class CompanyDetailsController : ControllerBase
{
    private readonly ICompanyGroupMappingService _companyGroupMappingService;
    private readonly ICompanyDetailsService _companyDetailsService;
    private readonly ICompanyGroupService _companyGroupService;
    private readonly ICompanySubsidiaryMappingService _companySubsidiaryMappingService;
    private readonly ITokenClaimReader _tokenClaimReader;
    private readonly IUserMasterService _userMasterService;
    private readonly IDeviceTemplatesService _deviceTemplatesService;
    private readonly IFileService _fileService;
    private readonly string _uploadFolder;
    private readonly IUsersCompanyService _usersCompanyService;
    private readonly ITerminalService _terminalService;
    private readonly ILinklyFakeService _linklyFakeService;
    private readonly ICompanySyncService _companySyncService;
    private readonly IOptions<TerminalSourceOptions> _terminalSourceOptions;

    public CompanyDetailsController(
        ICompanyGroupMappingService companyGroupMappingService,
        ICompanyDetailsService companyDetailsService,
        ICompanyGroupService companyGroupService,
        ICompanySubsidiaryMappingService companySubsidiaryMappingService,
        ITokenClaimReader tokenClaimReader,
        IUserMasterService userMasterService,
        IDeviceTemplatesService deviceTemplatesService,
        IFileService fileService,
        ITerminalService terminalService,
        IUsersCompanyService usersCompanyService,
        ILinklyFakeService linklyFakeService,
        ICompanySyncService companySyncService,
        IOptions<TerminalSourceOptions> terminalSourceOptions)
    {
        _companyGroupMappingService = companyGroupMappingService;
        _companyDetailsService = companyDetailsService;
        _companyGroupService = companyGroupService;
        _companySubsidiaryMappingService = companySubsidiaryMappingService;
        _tokenClaimReader = tokenClaimReader;
        _userMasterService = userMasterService;
        _deviceTemplatesService = deviceTemplatesService;
        _fileService = fileService;
        _uploadFolder = $"{FileConstant.ROOT_FOLDER}/{FileConstant.COMPANY_PATH}";
        _usersCompanyService = usersCompanyService;
        _terminalService = terminalService;
        _linklyFakeService = linklyFakeService;
        _companySyncService = companySyncService;
        _terminalSourceOptions = terminalSourceOptions;
    }

    #region Load Company Tree
    /// <summary>
    /// Load company group mapping tree
    /// </summary>
    /// <param name="parentId"></param>
    /// <param name="isIncludeGroup"></param>
    /// <returns></returns>
    [HttpGet("company-group-mapping-load")]
    [ApiAuthority(
        Function = ApiSecure.ApiCompanyGroupMapping,
        Action = ApiSecure.ViewAction,
        IsRoot = true,
        ActionType = ApiSecure.FullAction)]
    [ApiSecurityCustomName("Company Group Mapping")]
    [ProducesResponseType<CompanyTreeResponse>(StatusCodes.Status200OK)]
    public async Task<IActionResult> CompanyGroupMappingLoad(int? parentId, bool isIncludeGroup = true)
    {
        var selectedList = await _companyGroupMappingService.GetCompanyTreeChildrenMappingsAsync(parentId, isIncludeGroup);
        selectedList = selectedList
            .DistinctBy(x => (x.OrgId, x.Type))
            .ToList();

        var currentUser = _userMasterService.GetById(User.GetUserId());
        if (currentUser != null && !currentUser.EnableAdminAccess && !currentUser.EnableAdminAccess)
        {
            //var accessStatusId = _tokenClaimReader.GetAccessStatusIdClaim();
            //var companyId = _tokenClaimReader.GetCompanyIdClaim();
            var companyGroupMappingId = _tokenClaimReader.GetCompanyGroupMappingIdClaim();
            //var securityTemplateMapping = await _cloudSecurityAccessMappingService.GetCloudSecurityAccessMapping(companyId, null, accessStatusId, null);
            //var userAccessMapping = await _cloudSecurityAccessMappingService.GetCloudSecurityAccessMapping(companyId, null, accessStatusId, currentUser.Id);

            //if (securityTemplateMapping != null || userAccessMapping != null)
            //{
            //    var restrictType = type switch
            //    {
            //        CompanyGroupMappingType.Subsidiary => MerchantRestrictDeviceType.Subsidiary,
            //        CompanyGroupMappingType.PartnerAccess => MerchantRestrictDeviceType.PartnerAccess,
            //        CompanyGroupMappingType.SubPartner => MerchantRestrictDeviceType.SubPartner,
            //        CompanyGroupMappingType.Group => MerchantRestrictDeviceType.Group,
            //        _ => MerchantRestrictDeviceType.None
            //    };

            //    var merchantRestrictDeviceList = await _merchantRestrictDeviceService.GetListRestrictForFilterDataAsync(securityTemplateMapping?.Id, userAccessMapping?.Id, restrictType);

            //    List<int> restrictIds = merchantRestrictDeviceList.Select(o => o.RestrictId).Distinct().ToList();
            //    var itemsToRemove = new List<ViewItemModel>();
            //    foreach (var item in vm)
            //    {
            //        if (restrictIds.Contains(item.OrgId))
            //        {
            //            itemsToRemove.Add(item);
            //        }
            //    }

            //    foreach (var itemToRemove in itemsToRemove)
            //    {
            //        vm.Remove(itemToRemove);
            //    }
            //}

            var companyGroupMapping = await _companyGroupMappingService.GetByIdAsync(companyGroupMappingId);
            if (companyGroupMapping != null)
            {
                var availableIds = await _companyGroupMappingService.GetAllParentIdsByHierarchy(companyGroupMapping.HierarchyKey);
                selectedList = selectedList.Where(o => availableIds.Contains(o.Id) || o.Type == CompanyGroupMappingType.Group).ToList();
            }
        }

        var response = new CompanyTreeResponse
        {
            ParentId = parentId.GetValueOrDefault(),
            Items = [.. selectedList.Select(p => new CompanyTreeItemModel(p.OrgId.GetValueOrDefault(), p.Name, (int)p.Type, parentId).MapBaseProperties(p))]
        };

        foreach (var item in response.Items)
        {
            item.HasChild = (await _companyGroupMappingService.GetCompanyTreeChildrenMappingsAsync(item.Id, isIncludeGroup)).Count > 0;
        }

        return Ok(response);
    }

    [AllowAnonymous]
    /// <summary>
    /// Load access partner tree
    /// </summary>
    /// <param name="parentId"></param>
    /// <returns></returns>
    [HttpGet("access-partner-load")]
    [ApiAuthority(
        Function = ApiSecure.ApiCompanyGroupMapping,
        Action = ApiSecure.ViewAction)]
    [ProducesResponseType<CompanyTreeResponse>(StatusCodes.Status200OK)]
    public async Task<IActionResult> AccessPartnerLoad(int? parentId)
    {
        var selectedList = await _companyGroupMappingService.GetPartnerAccessTreeChildrenMappingsAync(parentId);
        selectedList = selectedList.DistinctBy(x => x.OrgId).ToList();

        var response = new CompanyTreeResponse
        {
            ParentId = parentId.GetValueOrDefault(),
            Items = selectedList
                .Select(p => new CompanyTreeItemModel(p.OrgId.GetValueOrDefault(), p.Name, (int)p.Type, parentId).MapBaseProperties(p))
                .ToList()
        };

        foreach (var item in response.Items)
        {
            item.HasChild = (await _companyGroupMappingService.GetPartnerAccessTreeChildrenMappingsAync(item.Id)).Count > 0;
        }

        return Ok(response);
    }

    /// <summary>
    /// Search company tree
    /// </summary>
    /// <param name="searchText"></param>
    /// <returns></returns>
    [HttpGet("company-tree-search")]
    [ApiAuthority(
    Function = ApiSecure.ApiCompanyGroupMapping,
    Action = ApiSecure.ViewAction)]
    [ProducesResponseType<CompanyTreeResponse>(StatusCodes.Status200OK)]
    public async Task<IActionResult> CompanyTreeSearch(string searchText)
    {

        var allCompanyGroupMapping = await _companyGroupMappingService.GetAllCompanyForSearchTreeMappingsAsync();

        if (allCompanyGroupMapping == null || !allCompanyGroupMapping.Any())
        {
            return Ok(new CompanyTreeResponse { Items = new List<CompanyTreeItemModel>() });
        }

        var response = new CompanyTreeResponse
        {
            Items = allCompanyGroupMapping
                .Where(p => p.ParentId == null)
                .Select(p => BuildTreeItemWithChildrenSearch(p, allCompanyGroupMapping, searchText))
                .Where(p => p != null)
                .ToList()
        };

        return Ok(response);
    }

    private CompanyTreeItemModel BuildTreeItemWithChildrenSearch(
        CompanyGroupMapping mapping,
        List<CompanyGroupMapping> allMappings,
        string searchText = "")
    {
        if (mapping == null) return null;

        var item = new CompanyTreeItemModel(
            mapping.OrgId.GetValueOrDefault(),
            mapping.Name ?? string.Empty,
            (int)mapping.Type,
            mapping.ParentId
        ).MapBaseProperties(mapping);

        bool isMatch = !string.IsNullOrEmpty(searchText) &&
                       !string.IsNullOrEmpty(item.Label) &&
                       item.Label.Contains(searchText, StringComparison.OrdinalIgnoreCase);

        var children = allMappings
            .Where(c => c.ParentId == mapping.Id)
            .Select(c => BuildTreeItemWithChildrenSearch(c, allMappings, searchText))
            .Where(c => c != null)
            .ToList();

        item.HasChild = children.Any();
        item.Child = children;

        return isMatch || item.HasChild ? item : null;
    }

    /// <summary>
    /// Load all access partner tree
    /// </summary>
    /// <returns></returns>
    [HttpGet("access-partner-load-all")]
    [ApiAuthority(
        Function = ApiSecure.ApiCompanyGroupMapping,
        Action = ApiSecure.ViewAction)]
    [ProducesResponseType<CompanyTreeResponse>(StatusCodes.Status200OK)]
    public async Task<IActionResult> AccessPartnerLoadAll()
    {
        var allPartnerMappings = await _companyGroupMappingService.GetAllPartnerAccessTreeMappingsAsync();

        var response = new CompanyTreeResponse
        {
            Items = allPartnerMappings
                .Where(p => p.ParentId == null)
                .Select(p => BuildTreeItemWithChildren(p, allPartnerMappings))
                .ToList()
        };

        return Ok(response);
    }

    private CompanyTreeItemModel BuildTreeItemWithChildren(CompanyGroupMapping mapping, List<CompanyGroupMapping> allMappings, string searchText = "")
    {
        var item = new CompanyTreeItemModel(
            mapping.OrgId.GetValueOrDefault(),
            mapping.Name,
            (int)mapping.Type,
            mapping.ParentId
        ).MapBaseProperties(mapping);

        var children = allMappings
            .Where(c => c.ParentId == mapping.Id)
            .ToList();

        item.HasChild = children.Any();

        if (item.HasChild)
        {
            item.Child = children
                .Select(c => BuildTreeItemWithChildren(c, allMappings))
                .ToList();
        }

        return item;
    }

    /// <summary>
    /// Activate or deactivate company group mapping
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    [HttpPost("activate-deactivate-company-group-mapping")]
    [ApiAuthority(
        Function = ApiSecure.ApiCompanyGroupMapping,
        Action = ApiSecure.ActiveAction)]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public IActionResult ActivateDeactivateCompanyGroupMapping(ActivateDeactivateModel request)
    {
        _companyGroupMappingService.ActivateDeactivateByListIds(request.Ids, request.Activate);
        return Ok();
    }
    #endregion Load Company Tree

    #region Select/Remove Companies/Subs/Groups
    /// <summary>
    /// Get source and selected company tree items
    /// </summary>
    /// <param name="parentId"></param>
    /// <param name="type"></param>
    /// <param name="selectRemoveFormParameter"></param>
    /// <returns></returns>
    [HttpGet("source-and-selected-company")]
    [ApiAuthority(
        Function = ApiSecure.ApiCompanyGroupMapping,
        Action = ApiSecure.ViewAction)]
    [ProducesResponseType<CompanyTreeSelectionResponse>(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    public async Task<IActionResult> GetSourceAndSelectedCompanyTreeItems(int? parentId, CompanyGroupMappingType type, [FromQuery] SelectRemoveFormParameter selectRemoveFormParameter)
    {
        var selectedCompanyTreeItems = await _companyGroupMappingService.GetSelectedAsync(type, parentId, ScreenType.MerchantLocation, true);

        var response = new CompanyTreeSelectionResponse(parentId);

        var selectedIds = selectedCompanyTreeItems.Select(p => p.OrgId.GetValueOrDefault()).ToList();

        var source = await _companyDetailsService.GetSourceForSelectCompanyAsync(parentId, selectedIds, selectRemoveFormParameter);

        return Ok(new { source, selectedIds });
    }

    /// <summary>
    /// Select or remove companies
    /// </summary>
    /// <param name="parentId"></param>
    /// <param name="selectRemoveRequest"></param>
    /// <returns></returns>
    [HttpPost("select-remove-companies/{parentId?}")]
    [ApiAuthority(
        Function = ApiSecure.ApiCompanyGroupMapping,
        Action = ApiSecure.SelectRemoveAction)]
    [ProducesResponseType<string>(StatusCodes.Status200OK)]
    public async Task<IActionResult> SelectRemoveCompaniesAsync(int? parentId, [FromBody] SelectRemoveRequest selectRemoveRequest)
    {
        await _companyGroupMappingService.UpdateSelectedAsync(
                selectRemoveRequest.SelectedIds, parentId, CompanyGroupMappingType.Company, ScreenType.MerchantLocation);

        return Ok("Update successfully");
    }

    /// <summary>
    /// Remove selected company
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpPut("remove-company-selected/{id}")]
    [ApiAuthority(
        Function = ApiSecure.ApiCompanyGroupMapping,
        Action = ApiSecure.SelectRemoveAction)]
    [ProducesResponseType<string>(StatusCodes.Status200OK)]
    public async Task<IActionResult> RemoveCompanySelected(int id)
    {
        var companyGroupMapping = _companyGroupMappingService.GetById(id);

        if (companyGroupMapping == null)
            throw new ArgumentNullException(nameof(companyGroupMapping));

        var childs = await _companyGroupMappingService.GetCompanyTreeChildrenMappingsAsync(id);

        if (childs.Count > 0)
        {
            throw new InvalidOperationException("Unable to Remove Company or Sub Company because it has child items.");
        }
        else if (companyGroupMapping.Type == CompanyGroupMappingType.Group)
        {
            var hasDeviceAssigned = await _terminalService.HasAnyDeviceAssignedToGroup(companyGroupMapping.GroupId.GetValueOrDefault());

            if (hasDeviceAssigned)
            {
                throw new InvalidOperationException("Unable to Remove a Group with Assigned Devices.");
            }
        }

        _companyGroupMappingService.Delete(companyGroupMapping);

        return Ok();
    }

    /// <summary>
    /// Check if item can be deleted
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet("can-delete/{id}")]
    [ProducesResponseType<bool>(StatusCodes.Status200OK)]
    public async Task<IActionResult> CanDeleteItem(int id)
    {
        var companyGroupMapping = _companyGroupMappingService.GetById(id);
        if (companyGroupMapping == null)
            throw new ArgumentNullException(nameof(companyGroupMapping));

        // Check if item has children
        var hasChildren = (await _companyGroupMappingService.GetCompanyTreeChildrenMappingsAsync(id)).Count > 0;

        // If has children, cannot delete
        if (hasChildren)
        {
            return Ok(new { canDeleteItem = false });
        }

        // For group type, check if devices are assigned
        if (companyGroupMapping.Type == CompanyGroupMappingType.Group)
        {
            var hasDeviceAssigned = await _terminalService.HasAnyDeviceAssignedToGroup(
                companyGroupMapping.GroupId.GetValueOrDefault());

            if (hasDeviceAssigned)
            {
                return Ok(new { canDeleteItem = false });
            }
        }

        return Ok(new { canDeleteItem = true });
    }

    /// <summary>
    /// Get group by ID
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet("group/{id}")]
    [ProducesResponseType<string>(StatusCodes.Status200OK)]
    public ActionResult GetGroup(int id)
    {
        var group = _companyGroupService.GetById(id);
        if (group == null)
            throw new ArgumentNullException(nameof(group));

        var vm = new CompanyGroupResponse()
        {
            Id = group.Id,
            Name = group.Name,
        };

        return Ok(vm);
    }

    /// <summary>
    /// Add new group
    /// </summary>
    /// <param name="vm"></param>
    /// <returns></returns>
    [HttpPost("group")]
    [ProducesResponseType<string>(StatusCodes.Status200OK)]
    public ActionResult AddGroup(CompanyGroupRequest vm)
    {
        var entity = new CompanyGroup();
        entity.Name = vm.Name;

        _companyGroupService.AddOrUpdate(entity);
        _companyGroupMappingService.InsertGroupAsync(entity.Id, vm.ParentId);

        return Ok();
    }

    /// <summary>
    /// Edit group
    /// </summary>
    /// <param name="id"></param>
    /// <param name="vm"></param>
    /// <returns></returns>
    [HttpPut("group/{id}")]
    [ProducesResponseType<string>(StatusCodes.Status200OK)]
    public ActionResult EditGroup(int id, CompanyGroupRequest vm)
    {
        var entity = _companyGroupService.GetById(id);
        if (entity == null)
            throw new ArgumentNullException(nameof(entity));

        entity.Name = vm.Name;

        _companyGroupService.AddOrUpdate(entity);

        return Ok();
    }

    #endregion Select/Remove Companies/Subs/Groups

    #region Base Application Template

    /// <summary>
    /// Load company application mapping
    /// </summary>
    /// <param name="parentId"></param>
    /// <param name="type"></param>
    /// <returns></returns>
    [HttpGet("load-company-application-mapping")]
    [ApiAuthority(
        Function = ApiSecure.ApiBaseTemplateManagement,
        Action = ApiSecure.ViewAction,
        IsRoot = true,
        ActionType = ApiSecure.FullAction)]
    [ApiSecurityCustomName("Base Template Management")]
    public async Task<IActionResult> LoadCompanyApplicationMapping(int? parentId, CompanyGroupMappingType type)
    {
        List<CompanyGroupMapping> list = await _companyGroupMappingService.GetSelectedAsync(type, parentId, ScreenType.MerchantLocation);

        list = [.. list.DistinctBy(x => x.OrgId)];

        var response = list.Select(x => new ListItemModel
        {
            Id = x.Id,
            Name = x.Name,
            IsActive = x.IsActive,
            IsStatus = x.IsStatus,
        }).ToList();

        return Ok(response);
    }

    /// <summary>
    /// Load base application template
    /// </summary>
    /// <param name="companyGroupMappingId"></param>
    /// <param name="currentTemplateId"></param>
    /// <returns></returns>
    [HttpGet("load-base-application-template")]
    [ApiAuthority(
        Function = ApiSecure.ApiBaseTemplateManagement,
        Action = ApiSecure.ViewAction)]
    public async Task<ActionResult> LoadBaseTemplates(int companyGroupMappingId, int currentTemplateId = 0)
    {
        var baseTemplates = await _deviceTemplatesService.GetByAppVersionAsync(companyGroupMappingId);

        if (currentTemplateId != 0)
        {
            baseTemplates = [.. baseTemplates.Where(p => p.Id != currentTemplateId)];
        }

        var response = baseTemplates.Select(x => new ListItemModel
        {
            Id = x.Id,
            Name = x.Name,
            IsActive = x.IsActive
        }).ToList();

        return Ok(response);
    }

    /// <summary>
    /// Get apps for copy base template
    /// </summary>
    /// <param name="companyId"></param>
    /// <returns></returns>
    [HttpGet("get-app-for-copy-base-template/{companyId}")]
    public async Task<IActionResult> GetAppForCopyBaseTemplate(int companyId)
    {
        var selectedList = await _companyGroupMappingService.GetCompanyTreeChildrenMappingsAsync(companyId);
        selectedList = [.. selectedList.DistinctBy(x => x.OrgId)];
        var result = new List<CompanyGroupMapping>();

        foreach (var item in selectedList)
        {
            var list = await _companyGroupMappingService.GetSelectedAsync(CompanyGroupMappingType.App, item.Id, ScreenType.MerchantLocation);

            list = [.. list.DistinctBy(x => x.OrgId)];
            result.AddRange(list);
        }

        var response = result.Select(x => new ListItemModel
        {
            Id = x.Id,
            Name = x.Name,
            IsActive = x.IsActive
        }).ToList();

        return Ok(response);
    }

    #endregion

    #region Company Master
    /// <summary>
    /// Get all companies
    /// </summary>
    /// <param name="searchKey"></param>
    /// <param name="pagingParameter"></param>
    /// <returns></returns>
    [HttpGet("get-companies")]
    [ApiAuthority(
        Function = ApiSecure.ApiCompanyDetails,
        Action = ApiSecure.ViewAction,
        IsRoot = true,
        ActionType = ApiSecure.FullAction)]
    [ApiSecurityCustomName("Company Management")]
    public async Task<IActionResult> GetCompanies([FromQuery] string? searchKey, [FromQuery] PagingParameter pagingParameter)
    {
        // Auto-sync companies from LinklyFake API before returning the data
        if (_terminalSourceOptions.Value.GetTerminalSource() == TerminalSources.Linkly)
        {
            // Fetch companies from LinklyFake API
            var linklyCompanies = await _linklyFakeService.GetAllCompaniesAsync();

            // Sync companies with our database
            await _companySyncService.SyncCompaniesFromLinklyAsync(linklyCompanies);
        }

        // The repository will use the configured TerminalSource automatically
        return Ok(await _companyDetailsService.GetCompaniesMasterAsync(searchKey, pagingParameter));
    }

    /// <summary>
    /// Get company details by ID
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet("get-company-details/{id}")]
    [ApiAuthority(
        Function = ApiSecure.ApiCompanyDetails,
        Action = ApiSecure.ViewAction)]
    public async Task<IActionResult> GetCompanyInfo(int id)
    {
        var company = await _companyDetailsService.GetCompanyInfo(id);
        if (company == null)
            throw new ArgumentNullException(nameof(company));

        return Ok(company);
    }

    /// <summary>
    /// Add new company
    /// </summary>
    /// <param name="request"></param>
    /// <param name="avatar"></param>
    /// <returns></returns>
    [HttpPost("add-new-company")]
    [ApiAuthority(
        Function = ApiSecure.ApiCompanyDetails,
        Action = ApiSecure.AddAction)]
    public async Task<IActionResult> AddCompanyAsync([FromForm] CompanyInfoRequest request, IFormFile? avatar)
    {
        var entity = new CompanyDetails().ToMasterEntity(request);

        if (avatar is not null)
        {
            var fileName = await _fileService.UploadFileAsync(_uploadFolder, avatar);
            entity.CompanyPhoto = fileName;
        }

        _companyDetailsService.Insert(entity);
        return Ok();
    }

    /// <summary>
    /// Update company
    /// </summary>
    /// <param name="id"></param>
    /// <param name="request"></param>
    /// <param name="avatar"></param>
    /// <returns></returns>
    [HttpPut("update-company/{id}")]
    [ApiAuthority(
        Function = ApiSecure.ApiCompanyDetails,
        Action = ApiSecure.EditAction)]
    public async Task<IActionResult> EditCompanyAsync(int id, [FromForm] CompanyInfoRequest request, IFormFile? avatar)
    {
        var company = _companyDetailsService.GetById(id);
        if (company == null)
            throw new ArgumentNullException(nameof(company));

        company = company.ToMasterEntity(request);

        if (avatar is not null)
        {
            var newFileName = await _fileService.UploadFileAsync(_uploadFolder, avatar, company.CompanyPhoto);
            company.CompanyPhoto = newFileName;
        }

        _companyDetailsService.Update(company);
        return Ok();
    }

    /// <summary>
    /// Activate/Deactivate company
    /// </summary>
    /// <param name="id"></param>
    /// <param name="isActive"></param>
    /// <returns></returns>
    [HttpPatch("activate-deactivate-company/{id}")]
    [ApiAuthority(
        Function = ApiSecure.ApiCompanyDetails,
        Action = ApiSecure.ActiveAction)]
    public IActionResult ActivateDeactivateCompany(int id, bool isActive)
    {
        var company = _companyDetailsService.GetById(id);
        if (company == null)
            throw new ArgumentNullException(nameof(company));

        company.IsActive = isActive;
        _companyDetailsService.Update(company);

        return Ok();
    }

    /// <summary>
    /// Delete company
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpDelete("delete-company/{id}")]
    [ApiAuthority(
        Function = ApiSecure.ApiCompanyDetails,
        Action = ApiSecure.RemoveAction)]
    public IActionResult DeleteCompany(int id)
    {
        var company = _companyDetailsService.GetById(id);
        if (company == null)
            throw new ArgumentNullException(nameof(company));

        _companyDetailsService.Delete(company);
        return Ok();
    }
    #endregion

    /// <summary>
    /// Get all users (unselected and selected)
    /// </summary>
    /// <param name="id"></param>
    /// <param name="searchKey"></param>
    /// <param name="pagingParameter"></param>
    /// <param name="filter"></param>
    /// <returns></returns>
    #region Company Users
    [HttpGet("get-company-users/{id}")]
    [ApiAuthority(
        Function = ApiSecure.ApiCompanyDetails,
        Action = ApiSecure.ViewAction)]
    public async Task<IActionResult> GetCompanyUsers(int id, [FromQuery] string? searchKey, [FromQuery] PagingParameter pagingParameter, [FromQuery] string filter = "all")
    {
        if (!Enum.TryParse<SelectedFilterType>(filter, true, out var filterType))
        {
            filterType = SelectedFilterType.All; // Default to All if invalid filter value
        }

        var company = _companyDetailsService.GetById(id);
        if (company == null)
            throw new ArgumentNullException(nameof(company));

        var listItemSelected = _usersCompanyService.GetSelectedUser(id, false);
        var selectedIds = listItemSelected.Select(o => o.UserMasterId).Distinct().ToList();
        var users = await _companyDetailsService.GetSourceForSelectUserAsync(id, selectedIds, searchKey, filterType, pagingParameter);

        return Ok(new
        {
            users,
            selectedIds
        });
    }

    /// <summary>
    /// Get selected company users
    /// </summary>
    /// <param name="id"></param>
    /// <param name="searchKey"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentNullException"></exception>
    [HttpGet("get-selected-company-users/{id}")]
    [ApiAuthority(
        Function = ApiSecure.ApiCompanyDetails,
        Action = ApiSecure.ViewAction)]
    public async Task<IActionResult> GetSelectedCompanyUsers(int id, [FromQuery] string? searchKey)
    {
        var company = _companyDetailsService.GetById(id);
        if (company == null)
            throw new ArgumentNullException(nameof(company));

        var selectedUsers = await _usersCompanyService.GetSelectedUser(id, searchKey);

        return Ok(new
        {
            selectedUsers
        });
    }

    /// <summary>
    /// Select company users
    /// </summary>
    /// <param name="id"></param>
    /// <param name="selectedIds"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentNullException"></exception>
    [HttpPost("select-company-users/{id}")]
    [ApiAuthority(
        Function = ApiSecure.ApiCompanyDetails,
        Action = ApiSecure.AddAction)]
    public IActionResult SelectCompanyUsers(int id, [FromBody] List<int> selectedIds)
    {
        var company = _companyDetailsService.GetById(id);
        if (company == null)
            throw new ArgumentNullException(nameof(company));

        _usersCompanyService.UpdateSelectedUser(id, selectedIds, false);
        return Ok();
    }

    /// <summary>
    /// activate/deactivate company user
    /// </summary>
    /// <param name="id"></param>
    /// <param name="isActive"></param>
    /// <returns></returns>
    [HttpPatch("activate-deactivate-company-user/{id}")]
    [ApiAuthority(
        Function = ApiSecure.ApiCompanyDetails,
        Action = ApiSecure.ActiveAction)]
    public IActionResult ActivateDeactivateCompanyUser(int id, bool isActive)
    {
        var companyUser = _usersCompanyService.GetById(id);
        if (companyUser == null)
            throw new ArgumentNullException(nameof(companyUser));

        companyUser.IsActive = isActive;
        _usersCompanyService.Update(companyUser);
        return Ok();
    }

    #endregion
}
