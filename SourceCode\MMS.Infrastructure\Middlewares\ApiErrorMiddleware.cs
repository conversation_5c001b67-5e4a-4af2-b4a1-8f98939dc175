using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System.Threading.Tasks;

namespace MMS.Infrastructure.Middlewares
{
    /// <summary>
    /// Middleware for handling exceptions in API applications
    /// </summary>
    public class ApiErrorMiddleware : BaseErrorMiddleware
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="ApiErrorMiddleware"/> class.
        /// </summary>
        /// <param name="next">The next middleware in the pipeline</param>
        public ApiErrorMiddleware(RequestDelegate next) : base(next)
        {
        }

        /// <summary>
        /// Handles the exception based on the request type
        /// </summary>
        /// <param name="context">The HTTP context</param>
        /// <param name="statusCode">The HTTP status code</param>
        /// <param name="errorMessage">The error message</param>
        /// <param name="stackTrace">The stack trace</param>
        /// <returns>A task representing the asynchronous operation</returns>
        protected override async Task HandleExceptionAsync(HttpContext context, int statusCode, string errorMessage, string stackTrace)
        {
            // For API requests, always return J<PERSON>N with the appropriate status code
            context.Response.StatusCode = statusCode;
            context.Response.ContentType = "application/json";

            var errorResponse = new
            {
                Message = errorMessage,
                StatusCode = statusCode,
                // Only include stack trace in development environment
                StackTrace = context.RequestServices.GetService(typeof(Microsoft.AspNetCore.Hosting.IWebHostEnvironment)) is Microsoft.AspNetCore.Hosting.IWebHostEnvironment env && env.EnvironmentName == "Development" ? stackTrace : null
            };

            var settings = new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver()
            };

            await context.Response.WriteAsync(JsonConvert.SerializeObject(errorResponse, settings));
        }
    }
}
