.data-items {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  align-self: stretch;
  border-top: 1px solid var(--neutral-neutrals-3-day, #e6e6e6);
  border-right: 1px solid var(--neutral-neutrals-3-day, #e6e6e6);
  border-bottom: 1px solid var(--neutral-neutrals-3-day, #e6e6e6);
  border-radius: 0;
  min-width: 160px;

  border-radius: 6px;

  .ant-list-header {
    padding-inline: 8px;
    font-weight: 600;
  }

  .search-input-no-border {
    &.ant-input-affix-wrapper {
      width: 100%;
      border: none;
      box-shadow: none;
      padding: 0;

      .ant-input {
        border: none;
        box-shadow: none;
        padding: 0;
        font-size: 12px;

        &::placeholder {
          font-size: 12px;
        }
      }
    }
  }

  .search-input-item {
    border-bottom: 0.5px solid var(--nautral-3);
  }

  .ant-list-items {
    overflow-y: auto;
    .ant-list-item {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      align-self: stretch;
      cursor: pointer;
      border: none;
      border-block-end: none;
      gap: 8px;

      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      &.selected {
        background-color: #f3faf8;
        color: #00a26d;
      }

      .item:not(.selected) {
        background-color: transparent;
        color: inherit;
      }

      .icon-close::before {
        margin: 0;
      }
    }
  }
}

.location-items:first-child {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;

  .ant-list-header {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border-top-right-radius: 0;
  }
}

.location-items:last-child {
  border-bottom-right-radius: 4px;
  border-top-right-radius: 4px;

  .ant-list-header {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 0;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
}

.ant-list-header {
  display: flex;
  padding: 12px 8px;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
  align-self: stretch;
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ant-list-item.search-input-item {
  border-bottom: 0.5px solid #e6e6e6 !important;
}

.ant-spin-nested-loading {
  width: 100%;
}

.column-name {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 4px;
}

.menu-icon {
  width: 12px;
  height: 12px;
  aspect-ratio: 1 / 1;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-left: auto !important;
}

.three-columns-width {
  width: 37.5%;
}

.nine-columns-width {
  width: 12.5%;
}

.columm-big-size {
  .ant-list-header {
    height: 44px;
  }

  .ant-list-items {
    height: 214px;

    .ant-list-item {
      height: 44px;
      padding: 16px;
    }
  }
}

.columm-small-size {
  .ant-list-header {
    height: 36px;
  }

  .ant-list-items {
    height: 240px;

    .ant-list-item {
      height: 32px;
      padding: 8px;
    }
  }
}
