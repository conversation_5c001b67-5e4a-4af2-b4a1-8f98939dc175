﻿@model MMS.Web.Models.TerminalSetupGUI.HostInterfacesModel
@using MMS.Web
@using MMS.Web.Models
@using MMS.Web.Utils

<head>
    <style>
        @Html.Raw(ViewBag.Style)
    </style>
    <script src="~/Scripts/Main/TerminalSetup/SetupMenu.js"></script>
</head>
<form asp-action="SelectGlobalHostInterfaces" asp-controller="TerminalSetupGUI" method="post" enctype="multipart/form-data" id="form">
    @Html.AntiForgeryToken()

    <input type="hidden" asp-for="RedirectUrl" id="redirectUrl" />

    <div class="@CssConstants.Form1ColTerminal @CssConstants.Middle @CssConstants.Color detail-form-plus-in">
        @await Html.PartialAsync(PartialViewConstants.TerminalHeaderPartialView, new TerminalHeaderModel()
   {
       Label = @_helper.GetLanguageText("Select Terminal Host Interfaces")
   })
        <div class="@CssConstants.FormDetails @CssConstants.BorderForm @CssConstants.Color @CssConstants.SearchScope @CssConstants.TerminalContent">
            <div class="@CssConstants.FatherDiv @CssConstants.MenuDescriptionContent">
                <p class="@Constants.terminal_setup_user_user_name @CssConstants.EditLangAble" id="@Constants.terminal_setup_user_user_name">
                    @_helper.GetStringByKey("Host Interfaces")
                </p>
            </div>
            <div class="@CssConstants.Content @CssConstants.IndexList  @CssConstants.List  @CssConstants.TerminalList">
                @await Html.PartialAsync(PartialViewConstants.GetSelectItemsPartial, Model.HostInterfaces)
            </div>
        </div>
        @await Html.PartialAsync(PartialViewConstants.TerminalFooterPartialView, new TerminalFooterModel())

    </div>
</form>

