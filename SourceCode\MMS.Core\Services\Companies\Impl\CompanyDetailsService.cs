﻿using MMS.Core.Entities;
using MMS.Core.Repository;
using MMS.Core.Services.Impl.Base;
using MMS.Core.CoreUTI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MMSConstants = MMS.Core.CoreUTI.Constants;
using MMS.Model.ApiModelResponse;
using MMS.Model.ApiModelRequest;
using MMS.Model.ApiModelResponse.Company;
using MMS.Infrastructure.Commons;

namespace MMS.Core.Services.Companies.Impl
{
    public class CompanyDetailsService : BaseService<CompanyDetails>, ICompanyDetailsService
    {
        private readonly IUsersCompanyRepository _usersCompanyRepository;
        private readonly IBaseRepository<BusinessTypeMaster> _businessTypeRepository;
        private readonly IMerchantMasterRepository _merchantRepository;
        private readonly ICompanyDetailRepository _companyDetailRepository;
        private readonly ICompanyGroupMappingRepository _companyGroupMappingRepository;

        public CompanyDetailsService(IBaseRepository<CompanyDetails> companyDetailsRepository,
            ILanguageExecuteService languageExecuteService,
            IBaseRepository<BusinessTypeMaster> businessTypeRepository,
            IUsersCompanyRepository usersCompanyRepository,
            IMerchantMasterRepository merchantRepository,
            ICompanyDetailRepository companyDetailRepository,
            ICompanyGroupMappingRepository companyGroupMappingRepository
            )
            : base(companyDetailsRepository, languageExecuteService)
        {
            _usersCompanyRepository = usersCompanyRepository;
            _businessTypeRepository = businessTypeRepository;
            _merchantRepository = merchantRepository;
            _companyDetailRepository = companyDetailRepository;
            _companyGroupMappingRepository = companyGroupMappingRepository;
        }
        public IList<ViewItemModel> GetAllCompany(int skip, int take, string searchKey, int userId)
        {

            var details = _usersCompanyRepository.GetListCompanyDetailSelect();

            var result = new List<ViewItemModel>();

            _languageExecuteService.GetLanguage(details);

            foreach (var item in details)
            {
                result.Add(new ViewItemModel()
                {
                    Id = item.Id,
                    Label = item.Name,
                    IsActive = item.UsersCompany.Any(p => p.IsDirector == false && p.UserMasterId == userId)
                });
            }
            if (!string.IsNullOrEmpty(searchKey))
            {
                result = result.Where(p => p.Label.Contains(searchKey)).ToList();
            }

            result = result.OrderBy(p => p.Label).Skip(skip).Take(take).ToList();

            return result;
            //ToDo Test
        }

        public IList<ViewItemModel> GetAllCompanyAssociated(int skip, int take, string searchKey, int userId)
        {
            var userCompanies = _usersCompanyRepository.GetListCompanyDetailSelected(userId, false);
            var result = new List<ViewItemModel>();

            _languageExecuteService.GetLanguage(userCompanies.Select(p => p.CompanyDetails).ToList());

            foreach (var item in userCompanies)
            {
                result.Add(new ViewItemModel()
                {
                    Id = item.CompanyDetails.Id,
                    IsActive = item.CompanyDetails.IsActive,
                    Label = item.CompanyDetails.Name,
                });
            }
            if (!string.IsNullOrEmpty(searchKey))
            {
                result = result.Where(p => p.Label.Contains(searchKey)).ToList();
            }

            result = result.OrderBy(p => p.Label).Skip(skip).Take(take).ToList();

            return result;
        }

        #region Company Details Base Function

        public int LastCompanyDetail()
        {
            var lastOrDefault = GetAll(includeDeactivated: true, includeDeleted: true).LastOrDefault();
            return lastOrDefault != null ? lastOrDefault.Id : 0;
        }

        #endregion

        #region Company Details Function By Language



        public IList<string> CheckCompanyNameExist(string value)
        {
            return CheckExistsRecord("Name", value);
        }

        public IList<CompanyDetails> GetAllCompanyDetailsesButItSelf(int id)
        {
            return GetAll(orderBy: m => m.Name);
        }

        public IDictionary<int, string> GetAllBySearchKey(string searchKey)
        {
            var resultList = _languageExecuteService.GetBySearchKey(typeof(CompanyDetails), "BusinessName", searchKey);
            var result = new Dictionary<int, string>();
            foreach (var each in resultList)
            {
                var id = Int32.Parse(each.Value.Split('.').ElementAt(2));
                var value = each.Value;
                result.Add(id, value);
            }
            return result;
        }

        public IList<string> CheckBusinessNameExist(string value, int id)
        {
            return CheckExistsRecord("CompanyRegisteredName", value, id);
        }

        public IList<CompanyDetails> GetAllCompanyDetailses()
        {
            return GetAll(orderBy: m => m.Name);
        }

        public IList<ViewItemModel> GetViewListCompanyDetailsByLang(int skip,
            int take, string searchKey)
        {
            return GetListSelectedItem(p => p.Name, p => p.ParentId == null, searchKey ?? "", skip, take);
        }

        public int GetCompanyDetailsNumberByLang(string searchKey)
        {
            return GetItemNumberByLang(p => p.Name, searchKey ?? "");
        }

        #endregion

        //get all item for selection
        public IList<ViewItemModel> GetSelectionList(string[] property, string table, string mapTable, int editingId,
            string masterId, string mapId, int language, int take, int skip, string typeOfEntity, string seachKey,
            bool selfReference = false)
        {
            //return _companyDetailRepository.GetListAssociatonItemByLang(property, language, typeOfEntity, seachKey, skip,
            //    take, table, mapTable, editingId, masterId, mapId, selfReference);
            return new List<ViewItemModel>(); //TODO TRUC
        }

        public IList<ViewItemModel> GetSelectedList(string[] property, string table, string mapTable, int editingId,
            string masterId, string mapId, int language, int take, int skip, string typeOfEntity, string seachKey)
        {
            //return _companyDetailRepository.GetListSelectedItemByLang(property, language, typeOfEntity, seachKey, skip,
            //    take, table, mapTable, editingId, masterId, mapId);
            return new List<ViewItemModel>(); //TODO TRUC
        }

        #region Update Association For Company Details

        public void UpdateBusinessType(int companyDetailId, int[] typeIds)
        {

            var company = GetById(companyDetailId);
            var types = _businessTypeRepository.GetAll(false, true, m => m.CompanyDetailses.Any(c => c.Id.Equals(companyDetailId)))
                .ToList();
            if (company.BusinessTypeMasters != null)
            {
                for (int i = 0; i < types.Count; i++)
                {
                    company.BusinessTypeMasters.Remove(types.ElementAt(i));
                }
            }
            Update(company);

            if (typeIds != null)
            {
                var businessTypes = _businessTypeRepository.GetAll(false, true, m => typeIds.Contains(m.Id)).ToList();
                foreach (var each in businessTypes)
                {
                    company.BusinessTypeMasters.Add(each);
                }
            }
            Update(company);
        }

        public void UpdatePersonalDetail(int companyDetailId, int[] personalIds, bool IsDirector = false)
        {
            var usersCompanies = _usersCompanyRepository.GetAll(false,
                whereClause: x => x.CompanyDetailId.Equals(companyDetailId) && x.IsDirector.Equals(IsDirector),
                includes: p => p.UserMaster).ToList();

            //delete record
            if (usersCompanies != null && usersCompanies.Count > 0)
            {
                foreach (var item in usersCompanies)
                {
                    if (personalIds != null)
                    {
                        if (!personalIds.Any(x => x.Equals(item.UserMaster)))
                        {
                            item.IsStatus = Constants.DELETE_RECORD;
                            _usersCompanyRepository.Update(item);
                        }
                    }
                    else
                    {
                        item.IsStatus = Constants.DELETE_RECORD;
                        _usersCompanyRepository.Update(item);
                    }
                }
            }

            //insert record
            if (personalIds != null && personalIds.Length > 0)
            {
                foreach (var item in personalIds)
                {
                    if (usersCompanies != null)
                    {
                        if (!usersCompanies.Any(x => x.UserMaster.Equals(item)))
                        {
                            _usersCompanyRepository.Insert(new UsersCompany
                            {
                                UserMasterId = item,
                                CompanyDetailId = companyDetailId,
                                IsDirector = IsDirector,
                                IsActive = true
                            });
                        }
                    }
                    else
                    {
                        _usersCompanyRepository.Insert(new UsersCompany
                        {
                            UserMasterId = item,
                            CompanyDetailId = companyDetailId,
                            IsDirector = IsDirector,
                            IsActive = true
                        });
                    }
                }
            }

        }

        public void UpdateMerchantAssociations(int companyDetailId, int[] merchantIdsInts)
        {
            var company = GetById(companyDetailId, p => p.MerchantMasters);
            var items = _merchantRepository.GetAll(false, true, m => m.CompanyDetailses.Any(c => c.Id.Equals(companyDetailId)))
                .ToList();
            if (company.MerchantMasters != null)
            {
                for (int i = 0; i < items.Count; i++)
                {
                    company.MerchantMasters.Remove(items.ElementAt(i));
                }
            }
            Update(company);

            if (merchantIdsInts != null)
            {
                var merchant = _merchantRepository.GetAll(false, true, m => merchantIdsInts.Contains(m.Id)).ToList();
                foreach (var each in merchant)
                {
                    company.MerchantMasters.Add(each);
                }
            }
            else
            {
                company.MerchantMasters.Clear();
            }
            Update(company);

        }

        public void UpdateCompanyAssociations(int companyDetailId, int[] companyIds)
        {
            var company = base.GetById(companyDetailId, p => p.CompanyDetailses);
            var items = base.GetAll(whereClauses: m => m.CompanyDetailses.Any(c => c.Id.Equals(companyDetailId)))
                .ToList();
            if (company.CompanyDetailses != null)
            {
                for (int i = 0; i < items.Count; i++)
                {
                    company.CompanyDetailses.Remove(items.ElementAt(i));
                }
            }
            base.Update(company);

            if (companyIds != null)
            {
                var companies = base.GetAll(whereClauses: m => companyIds.Contains(m.Id)).ToList();
                foreach (var each in companies)
                {
                    company.CompanyDetailses.Add(each);
                }
            }
            else
            {
                company.CompanyDetailses.Clear();
            }
            base.Update(company);
        }


        public bool IsExisted(int systemId, int companyID)
        {
            var query = GetAll(p => p.CompanyIdNumber == companyID && p.Id != systemId);

            return query.Any();
        }

        public async Task<IList<CompanyDetails>> GetSubCompaniesByParentIdAsync(int parentId)
        {
            var subsidiaries = await _companyDetailRepository.GetSubCompaniesByParentIdAsync(parentId);

            return subsidiaries;
        }

        public async Task<IList<CompanyDetails>> GetCompanyDetailsMasterAsync(int skip, int take, string searchKey)
        {
            var entities = await _companyDetailRepository.GetCompaniesMasterAsync(skip, take, searchKey);

            return entities;
        }

        public async Task<PagingResponse<CompanyListResponse>> GetCompaniesMasterAsync(string searchKey, PagingParameter pagingParameter)
        {
            var entities = await _companyDetailRepository.GetCompaniesMasterAsync(searchKey, pagingParameter);

            return entities;
        }

        public void ActivateDeactivateByListIds(IList<int> ids, bool isActivate)
        {
            var items = GetById(ids, includeDeactivated: true);

            foreach (var item in items)
            {
                item.IsActive = isActivate;
            }

            UpdateAll(items);
        }

        public void RemoveByListIds(IList<int> ids)
        {
            var items = GetById(ids, includeDeactivated: true);

            foreach (var item in items)
            {
                item.IsStatus = MMSConstants.DELETE_RECORD;
            }

            UpdateAll(items);
        }

        public async Task<List<CompanyDetails>> GetAllMasterCompanyAsync()
        {
            return await _companyDetailRepository.GetAllMasterCompanyAsync();
        }

        public async Task<List<CompanyDetails>> GetAllCompanyExcludingCurrentAsync(List<int> excludingCompanyIds)
        {
            return await _companyDetailRepository.GetAllCompanyExcludingCurrentAsync(excludingCompanyIds);
        }

        #endregion

        public async Task<CompanyInfoResponse> GetCompanyInfo(int id)
        {
            return await _companyDetailRepository.GetCompanyInfoByIdAsync(id);
        }

        public async Task<PagingResponse<DataItemResponse>> GetSourceForSelectUserAsync(int companyId, List<int> selectedIds, string searchKey, SelectedFilterType filterType, PagingParameter pagingParameter)
        {
            var result = await _companyDetailRepository.GetSourceForSelectUserAsync(companyId, selectedIds, searchKey, filterType, pagingParameter);
            return result;
        }

        public async Task<PagingResponse<DataItemResponse>> GetSourceForSelectCompanyAsync(int? parentId, List<int> selectedIds, SelectRemoveFormParameter selectRemoveFormParameter)
        {
            var ignoreIds = await _companyGroupMappingRepository.GetAllCompanyIdSelected(parentId);

            var result = await _companyDetailRepository.GetSourceForSelectCompanyAsync(ignoreIds, selectedIds, selectRemoveFormParameter);
            return result;
        }
    }
}
