import { useEffect } from 'react';
import { useBreadcrumb } from '../../hooks/useBreadcrumb';
import { ROUTE_PATHS } from '../../constants/router.constants';
import { useTranslation } from 'react-i18next';
import Dashboard from '../../components/dashboard';

function HomePage() {
  const { updateBreadcrumb } = useBreadcrumb();
  const { t } = useTranslation();

  useEffect(() => {
    updateBreadcrumb(
      {
        title: t('breadcrumbs.dashboard'),
        path: ROUTE_PATHS.Home,
      },
      true,
    );
  }, [t]);

  return <Dashboard />;
}

export default HomePage;
