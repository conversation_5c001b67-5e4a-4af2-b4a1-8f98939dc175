using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MMS.Api.ApiModels.LocationDeviceSetup;
using MMS.Api.Filters;
using MMS.Api.Security;
using MMS.Core.Entities;
using MMS.Core.Extensions;
using MMS.Core.Services;
using MMS.Infrastructure.Commons;
using MMS.Infrastructure.Services.LinklyFake;
using MMS.Model.ApiModelRequest;
using MMS.Model.LinklyFake;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MMS.Api.Controllers
{

    [Authorize]
    [ApiController]
    [Route("location-device-setup")]
    public class LocationDeviceSetupController : ControllerBase
    {
        private readonly ITerminalService _terminalMasterService;
        private readonly IMerchantTerminalService _merchantTerminalService;
        private readonly ITerminalTypeMasterService _brandTypesService;
        private readonly ITerminalService _terminalService;
        private readonly ITerminalModelMasterService _modelTypesService;
        private readonly ICloudSecurityAccessMappingService _cloudSecurityAccessMappingService;
        private readonly IMerchantRestrictDeviceService _merchantRestrictDeviceService;
        private readonly IUserMasterService _userMasterService;
        private readonly ILinklyFakeService _linklyFakeService;
        private readonly IApplicationDeviceMappingService _applicationDeviceMappingService;
        private readonly IMerchantMasterService _merchantMasterService;

        public LocationDeviceSetupController(
            IApplicationDeviceMappingService applicationDeviceMappingService,
            ITerminalService terminalMasterService,
            IMerchantTerminalService merchantTerminalService,
            ITerminalService terminalService,
            ITerminalTypeMasterService brandTypesService,
            ITerminalModelMasterService modelTypesService,
            ICloudSecurityAccessMappingService cloudSecurityAccessMappingService,
            IMerchantRestrictDeviceService merchantRestrictDeviceService,
            IUserMasterService userMasterService,
            ILinklyFakeService linklyFakeService,
            IMerchantMasterService merchantMasterService)
        {
            _applicationDeviceMappingService = applicationDeviceMappingService;
            _terminalMasterService = terminalMasterService;
            _merchantTerminalService = merchantTerminalService;
            _terminalService = terminalService;
            _brandTypesService = brandTypesService;
            _modelTypesService = modelTypesService;
            _cloudSecurityAccessMappingService = cloudSecurityAccessMappingService;
            _merchantRestrictDeviceService = merchantRestrictDeviceService;
            _userMasterService = userMasterService;
            _linklyFakeService = linklyFakeService;
            _merchantMasterService = merchantMasterService;
        }

        [HttpPost("assign-device")]
        [ApiAuthority(
            Function = ApiSecure.ApiDeviceManagement,
            Action = ApiSecure.AddAction)]
        public async Task<IActionResult> AssignDeviceToLocation([FromBody] AssignDeviceModel model)
        {
            if (model.LocationAreaId == 0)
            {
                throw new BadHttpRequestException("The locationAreaId is invalid");
            }

            // TODO: Check valid TerminalMasterId, LocationAreaId, GroupId
            await _terminalMasterService.AssignDeviceToLocationForApiAsync(model.TerminalMasterId, model.LocationAreaId, model.GroupId);
            await _merchantTerminalService.AutoAssignSingleMerchantToDeviceAsync(model.LocationAreaId, model.TerminalMasterId);

            return Ok();
        }

        [HttpPost("assign-devices")]
        [ApiAuthority(
            Function = ApiSecure.ApiDeviceManagement,
            Action = ApiSecure.AddAction)]
        public async Task<IActionResult> AssignDevicesToLocation([FromBody] AssignDevicesModel model)
        {
            if (model.LocationAreaId == 0)
            {
                throw new BadHttpRequestException("The locationAreaId is invalid");
            }

            // TODO: Check valid TerminalMasterIds, LocationAreaId, GroupId
            await _terminalMasterService.AssignDevicesToLocationForApiAsync(model.TerminalMasterIds, model.LocationAreaId, model.GroupId);
            await _merchantTerminalService.AutoAssignSingleMerchantToDevicesAsync(model.LocationAreaId, model.TerminalMasterIds);
            _applicationDeviceMappingService.AutoAddDefaultAppForLinklyDevice(model.TerminalMasterIds);

            return Ok();
        }


        [HttpPut("unassign-device")]
        [ApiAuthority(
            Function = ApiSecure.ApiDeviceManagement,
            Action = ApiSecure.EditAction)]
        public async Task<IActionResult> UnassignDevice([FromBody] IList<int> terminalMasterIds)
        {
            if (terminalMasterIds.Count == 0)
            {
                throw new BadHttpRequestException("The TerminalMasterIds is invalid");
            }

            await _terminalMasterService.UnassignDevicesToLocationForApiAsync(terminalMasterIds);

            return Ok();
        }

#if DEBUG
        [AllowAnonymous] // Allow anonymous access in debug mode for testing
#endif
        [HttpGet("available-devices")]
        [ApiAuthority(
            Function = ApiSecure.ApiDeviceManagement,
            Action = ApiSecure.ViewAction,
            IsRoot = true, // This is a root-level permission
            ActionType = ApiSecure.FullAction)]
        [ApiSecurityCustomName("Device Management")]
        public async Task<IActionResult> GetAvailableDevices([FromQuery] GetAvailableDevicesModel model, [FromQuery] PagingParameter pagingParameter)
        {
            if (model == null)
            {
                return BadRequest("Invalid request parameters.");
            }

            // Get data from the regular service
            var devices = await _terminalService.GetAllTerminalUnAssignedDeviceForApiAsync(model.CompanyDetailId, model.DeviceBrandIds, model.DeviceModelIds, model.KeySearch, pagingParameter);

            return Ok(devices);
        }

        [HttpGet("device-brands")]
        [ApiAuthority(
            Function = ApiSecure.ApiDeviceManagement,
            Action = ApiSecure.ViewAction)]
        public async Task<IActionResult> GetDeviceBrands()
        {
            var brands = await _brandTypesService.GetAllBrandsForApiAsync();

            return Ok(brands);
        }

        [HttpGet("device-models")]
        [ApiAuthority(
            Function = ApiSecure.ApiDeviceManagement,
            Action = ApiSecure.ViewAction)]
        public async Task<IActionResult> GetDeviceModels([FromQuery] List<int> brandIds)
        {
            var models = await _modelTypesService.GetModelsByBrandForApiAsync(brandIds);

            return Ok(models);
        }

        [HttpGet("devices-associated")]
        [ApiAuthority(
            Function = ApiSecure.ApiDeviceManagement,
            Action = ApiSecure.ViewAction)]
        public async Task<IActionResult> GetDeviceAssociated([FromQuery] AssignedDeviceParamsApiModel paramsModel, [FromQuery] string? searchValue, [FromQuery] PagingParameter pagingParameter, [FromQuery] int cloudSecurityAccessMappingId = 0, [FromQuery] string searchType = "sn")
        {
            if (!Enum.TryParse<SearchAssignDeviceType>(searchType, true, out var searchTypeEnum))
            {
                searchTypeEnum = SearchAssignDeviceType.SN;
            }

            var assignedDevices = await _terminalService.GetAssignedDeviceAsync(paramsModel, pagingParameter, searchValue, searchTypeEnum);

            if (assignedDevices == null || !assignedDevices.Data.Any())
            {
                return Ok(new List<AssignedDeviceModel>());
            }

            // var items = AssignedDeviceModel.ToModels([.. assignedDevices.Data]);

            // if (cloudSecurityAccessMappingId > 0)
            // {
            //     var cloudSecurityAccessMapping = _cloudSecurityAccessMappingService.GetById(cloudSecurityAccessMappingId);
            //     if (cloudSecurityAccessMapping.UserId != null)
            //     {
            //         var securityTemplateMapping = await _cloudSecurityAccessMappingService.GetCloudSecurityAccessMapping(cloudSecurityAccessMapping.CompanyId, cloudSecurityAccessMapping.GroupId,
            //         cloudSecurityAccessMapping.SecurityTemplateId ?? 0, null, cloudSecurityAccessMapping.IsPartner);

            //         var restrictListForSecurityTemplate = await _merchantRestrictDeviceService.GetListRestrictByTypeAsync(securityTemplateMapping.Id, MerchantRestrictDeviceType.Device);
            //         List<int> restrictIdsForTemplate = restrictListForSecurityTemplate.Select(o => o.RestrictId).ToList();
            //         assignedDevices = assignedDevices.Where(o => !restrictIdsForTemplate.Contains(o.Id)).ToList();
            //     }

            //     var merchantRestrictDeviceList = await _merchantRestrictDeviceService.GetListRestrictByTypeAsync(cloudSecurityAccessMappingId, MerchantRestrictDeviceType.Device);
            //     List<int> restrictMerchantIds = merchantRestrictDeviceList.Select(o => o.RestrictId).ToList();
            //     items = AssignedDeviceModel.ToModels([.. assignedDevices], restrictMerchantIds);
            // }
            // else
            // {
            //     var currentUser = _userMasterService.GetById(User.GetUserId());
            //     if (currentUser != null && !currentUser.EnableAdminAccess)
            //     {
            //         var accessStatusId = User.GetAccessStatusId();
            //         var companySesstionId = User.GetCompanyId();
            //         var securityTemplateMapping = await _cloudSecurityAccessMappingService.GetCloudSecurityAccessMapping(companySesstionId, null, accessStatusId, null);
            //         var userAccessMapping = await _cloudSecurityAccessMappingService.GetCloudSecurityAccessMapping(companySesstionId, null, accessStatusId, currentUser.Id);

            //         if (securityTemplateMapping != null || userAccessMapping != null)
            //         {
            //             var merchantRestrictDeviceList = await _merchantRestrictDeviceService.GetListRestrictForFilterDataAsync(securityTemplateMapping?.Id, userAccessMapping?.Id, MerchantRestrictDeviceType.Device);

            //             List<int> restrictIds = merchantRestrictDeviceList.Select(o => o.RestrictId).Distinct().ToList();

            //             items = AssignedDeviceModel.ToModels([.. assignedDevices.Where(o => !restrictIds.Contains(o.Id))]);
            //         }
            //     }
            // }

            return Ok(assignedDevices);
        }

#if DEBUG
        [AllowAnonymous] // Allow anonymous access in debug mode for testing
#endif
        [HttpGet("sync-linkly-devices/{companyId}")]
        [ApiAuthority(
            Function = ApiSecure.ApiDeviceManagement,
            Action = ApiSecure.EditAction)]
        public async Task<IActionResult> SyncLinklyDevices(int companyId)
        {
            if (companyId <= 0)
            {
                throw new ArgumentException("Must be a positive integer.", nameof(companyId));
            }

            // Fetch terminals from Linkly API
            var terminals = await _linklyFakeService.GetAllTerminalsAsync();

            // Sync terminals with our database
            var (inserted, updated, deleted) = await _terminalService.SyncTerminalsFromLinklyAsync(terminals, companyId);

            // Return summary of sync operation
            return Ok(new
            {
                message = "Terminals synchronized successfully",
                company_id = companyId,
                terminals_count = terminals.Count,
                inserted,
                updated,
                deleted
            });
        }

        /// <summary>
        /// Retrieves all active merchants for filtering devices.
        /// </summary>
        /// <returns></returns>
        [HttpGet("all-activate-merchants")]
        public IActionResult GetAllMerchantToFilterDevices()
        {
            var allMerchants = _merchantMasterService.GetAllActivateMerchants();

            return Ok(allMerchants);
        }
    }
}
