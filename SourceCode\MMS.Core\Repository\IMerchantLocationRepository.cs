﻿using MMS.Core.Entities;
using MMS.Core.CoreUTI.Enum;
using MMS.Core.Entities.Locations;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;
using MMS.Model.ApiModelRequest;
using MMS.Model.ApiModelResponse;

namespace MMS.Core.Repository
{
    public interface IMerchantLocationRepository : IBaseRepository<MerchantLocations>
    {
        /// <summary>
        /// Asynchronously retrieves all merchant locations associated with the specified location item ID.
        /// </summary>
        /// <param name="locationItemId">
        /// The ID of the location item to retrieve the associated merchant locations for.
        /// </param>
        /// <returns>
        /// A task that represents the asynchronous operation. 
        /// The task result contains a list of <see cref="MerchantLocations"/> objects associated with the specified location item ID.
        /// </returns>
        Task<IList<MerchantLocations>> GetAllMerchantLocationAsync(int locationItemId);

        /// <summary>
        /// Retrieves a <see cref="MerchantLocations"/> entity based on the provided <paramref name="locationItemId"/>.
        /// Optionally includes related navigation properties specified in the <paramref name="includes"/> parameter.
        /// </summary>
        /// <param name="locationItemId">
        /// The unique identifier of the location item to retrieve.
        /// </param>
        /// <param name="includes">
        /// An array of expressions specifying the navigation properties to include in the query. 
        /// These are optional and can be omitted if no related data is needed.
        /// </param>
        /// <returns>
        /// A task representing the asynchronous operation. 
        /// The task result contains the <see cref="MerchantLocations"/> entity if found; otherwise, <c>null</c>.
        /// </returns>
        /// <exception cref="ArgumentNullException">
        /// Thrown if <paramref name="includes"/> contains invalid expressions or <paramref name="locationItemId"/> is invalid.
        /// </exception>
        Task<MerchantLocations> GetMerchantLocationWithOptionalIncludeAsync(int locationItemId, params Expression<Func<MerchantLocations, object>>[] includes);

        /// <summary>
        /// Retrieves a list of merchant locations associated with a specific Merchant Master ID.
        /// </summary>
        /// <param name="merchantMasterId">The ID of the Merchant Master to filter the locations.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains a list of merchant locations.</returns>
        Task<List<MerchantLocations>> GetMerchantLocationListByMerchantMasterIdAsync(int merchantMasterId);

        /// <summary>
        /// Retrieves detailed information about a specific location item by its ID.
        /// </summary>
        /// <param name="locationItemId">The ID of the location item to retrieve information for.</param>
        /// <returns>
        /// A task that represents the asynchronous operation. The task result is a tuple containing:
        /// <list type="bullet">
        /// <item><description>The <see cref="LocationLevel"/> of the location.</description></item>
        /// <item><description>The name of the location item as a <see cref="string"/>.</description></item>
        /// <item><description>The parent ID of the location item as an <see cref="int"/> (0 if no parent ID exists).</description></item>
        /// </list>
        /// If the location item is not found, the method returns a default tuple with 
        /// <see cref="LocationLevel.Master"/>, an empty string, and 0.
        /// </returns>
        Task<(LocationLevel, string, int)> GetLocationItemInforAsync(int locationItemId);

        /// <summary>
        /// Retrieves a merchant location by the specified location item ID, including the associated merchant master data.
        /// </summary>
        /// <param name="locationItemId">
        /// The ID of the location item to retrieve the merchant location for.
        /// </param>
        /// <returns>
        /// A task that represents the asynchronous operation. 
        /// The task result contains a <see cref="MerchantLocations"/> object with the associated merchant master data.
        /// </returns>
        Task<MerchantLocations> GetMerchantLocationIncludeMerchantMasterAsync(int locationItemId);

        /// <summary>
        /// Retrieves a list of merchant locations by the specified location area ID, including the associated merchant master data.
        /// </summary>
        /// <param name="locationAreaId">
        /// The ID of the location area to retrieve the merchant locations for.
        /// </param>
        /// <returns>
        /// A task that represents the asynchronous operation. 
        /// The task result contains a list of <see cref="MerchantLocations"/> objects with the associated merchant master data.
        /// </returns>
        Task<IList<MerchantLocations>> GetMerchantLocationListByLocationAreaIdIncludeMerchantMasterAsync(int locationAreaId);

        /// <summary>
        /// Retrieves a list of merchants that are associated with locations other than the specified location or its sublocations.
        /// </summary>
        /// <param name="locationId">The ID of the location to evaluate.</param>
        /// <returns>
        /// A task representing the asynchronous operation. The task result contains a list of <see cref="MerchantMaster"/> objects associated with locations other than the specified location or its sublocations. 
        /// Returns an empty list if the specified location or its details cannot be found.
        /// </returns>
        Task<List<MerchantMaster>> GetMerchantLocationListSelectedByOtherLocationAsync(int locationId);

        /// <summary>
        /// Get paging source Merchant master
        /// </summary>
        /// <param name="sourceList"></param>
        /// <param name="selectedIds"></param>
        /// <param name="selectRemoveFormParameter"></param>
        /// <returns></returns>
        PagingResponse<DataItemResponse> GetPagingSourceMerchantMaster(List<MerchantMaster> sourceList, IEnumerable<int> selectedIds, SelectRemoveFormParameter pagingParameter);
    }
}
