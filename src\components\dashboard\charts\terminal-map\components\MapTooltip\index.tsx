import React from 'react';
import { MapTooltipProps } from '../../../../../../models/map.model';

const MapTooltip: React.FC<MapTooltipProps> = ({ content }) => {
  if (!content) return null;

  return (
    <div
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        background: '#4ecb8f',
        color: 'white',
        padding: '8px 12px',
        borderRadius: '4px',
        fontSize: '14px',
        pointerEvents: 'none',
        zIndex: 1000,
      }}
    >
      {content}
    </div>
  );
};

export default MapTooltip;
