import { useEffect, useState } from 'react';
import { Tabs, Form } from 'antd';
import { useBreadcrumb } from '../../hooks/useBreadcrumb';
import { ROUTE_PATHS } from '../../constants/router.constants';
import './index.scss';
import {
  CompanyDetailsRequest,
  CompanyDetailsResponse,
} from '../../models/company.model';
import { CompanyApi } from '../../apis/company.api';
import { useNavigate, useParams } from 'react-router-dom';
import CompanyDetailsContent from './company-info';
import CompanyUserList from './company-user';

function CompanyDetailsPage() {
  const { updateBreadcrumb } = useBreadcrumb();
  const { companyId } = useParams<{ companyId: string }>();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [isEdit, setIsEdit] = useState(false);
  const [companyInfo, setCompanyInfo] = useState<CompanyDetailsResponse>();
  const [activeTab, setActiveTab] = useState('company-info');
  const TERMINAL_SOURCE = import.meta.env.VITE_TERMINAL_SOURCE;

  const convertToFormData = (
    companyInfo: CompanyDetailsResponse,
  ): CompanyDetailsRequest => {
    return {
      companyIdNumber: companyInfo?.companyIdNumber ?? 0,
      companyRegisteredName: companyInfo?.companyRegisteredName ?? '',
      companyTradingName: companyInfo?.companyTradingName ?? '',
      email: companyInfo?.email ?? '',
      phoneNumber: companyInfo?.phoneNumber ?? '',
      address: '',
      avatar: null,
    };
  };

  const getCompanyInfo = async () => {
    if (companyId === '0') {
      setCompanyInfo({} as CompanyDetailsResponse);
      setIsEdit(true);
      return;
    }

    try {
      const { data } = await CompanyApi.getCompanyDetails(Number(companyId));
      if (data) {
        setCompanyInfo(data);
        form.setFieldsValue(convertToFormData(data));
      }
    } catch (error) {
      console.log(error);
    }
  };

  const handleEdit = () => {
    setIsEdit(!isEdit);
    if (!isEdit && companyInfo) {
      const formData = convertToFormData(companyInfo);
      form.setFieldsValue(formData);
    }
  };

  const handleSave = async () => {
    try {
      const formValues = form.getFieldsValue();

      if (companyId === '0') {
        await CompanyApi.addNewCompany(formValues);
        navigate(ROUTE_PATHS.Companies);
      } else {
        await CompanyApi.updateCompany(Number(companyId), formValues);
        navigate(`${ROUTE_PATHS.Companies}/${companyId}`);
      }

      setIsEdit(false);
    } catch (error) {
      console.log(error);
    }
  };

  const handleCancel = () => {
    setIsEdit(false);
    if (companyId === '0') {
      navigate(ROUTE_PATHS.Companies);
    } else if (companyInfo) {
      const formData = convertToFormData(companyInfo);
      form.setFieldsValue(formData);
    }
  };

  useEffect(() => {
    updateBreadcrumb({
      title: 'Company Details',
      path: `${ROUTE_PATHS.Companies}/${companyId}`,
    });
  }, []);

  useEffect(() => {
    getCompanyInfo();
  }, [companyId, isEdit]);

  const tabItems = [
    {
      key: 'company-info',
      label: 'Company Info',
      children: (
        <CompanyDetailsContent
          company={companyInfo}
          isEdit={isEdit}
          form={form}
        />
      ),
    },
    {
      key: 'company-user',
      label: 'Company Users',
      children: <CompanyUserList />,
    },
  ];

  const handleTabChange = (key: string) => {
    setActiveTab(key);
  };

  return (
    <div className="company-details-container">
      <Tabs
        type="card"
        activeKey={activeTab}
        onChange={handleTabChange}
        items={tabItems}
        tabBarExtraContent={null}
      />
    </div>
  );
}

export default CompanyDetailsPage;
