﻿using MMS.Core.Entities.TerminalSetupGUI;
using MMS.Core.Repository;
using MMS.Core.Entities;
using MMS.Core.Services;
using MMS.Core.Services.Impl.Base;
using MMS.Core.CoreUTI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace MMS.Core.Services.Impl
{
    public class HostInterfacesService : BaseService<HostInterfaces>, IHostInterfacesService
    {
        private readonly IHostInterfacesRepository _hostInterfacesRepository;
        public HostInterfacesService(IBaseRepository<HostInterfaces> repository, ILanguageExecuteService languageExecuteService,
            IHostInterfacesRepository hostInterfacesRepository)
        :base(repository, languageExecuteService)
        {
            _hostInterfacesRepository = hostInterfacesRepository;
        }
        public new IHostInterfacesService UsingSessionContext()
        {
            base.UsingSessionContext();

            _hostInterfacesRepository.UsingSessionContext();

            return this;
        }

        public HostInterfaces GetHostInterfacesById(int id)
        {
            return Repo.GetById(id);
        }

        public IList<HostInterfaces> GetHostInterfacesByIds(IList<int> ids)
        {
            return _hostInterfacesRepository.GetHostInterfacesByIds(ids);
        }

        public IList<HostInterfaces> GetHostInterfacesByTerminalMasterId(int teminalMasterId)
         {
            var result = Repo.GetAll(whereClause: p => p.TerminalMasterId == teminalMasterId);
            return result;
        }
        public IList<HostInterfaces> GetHostInterfaces(int teminalMasterId, IList<int> hostInterfacesIds)
        {
            var q = Repo.GetAll(false, true, p => p.TerminalMasterId == teminalMasterId && hostInterfacesIds.AsEnumerable().Contains(p.Id));

            return q.ToList();
        }

        public IList<ViewItemModel> GetHostInterfacesByMerchantTeminalId(int merchantTemninalId)
        {
            var hostInterfaces = _hostInterfacesRepository.GetHostInterfaceByMerchantTerminalId(merchantTemninalId);

            var result = new List<ViewItemModel>();

            foreach (var item in hostInterfaces)
            {
                var source = item;
                if (!item.IsUpdated && item.ParentId.HasValue)
                {
                    source = item.Parent;
                    if (!item.Parent.IsUpdated && item.Parent.ParentId.HasValue)
                    {
                        source = item.Parent.Parent;
                    }
                }

                result.Add(new ViewItemModel()
                {
                    Id = item.Id,
                    Label = source.Name,
                    Image = source.IconId,
                    ImagePath = Constants.PATH_MERCHANT
                });
            }

            return result;
        }

        public IList<ViewItemModel> GetHostInterfacesByMerchantTerminalAndSurchargeRule(int merchantTerminalId, int surchargeRuleId)
        {
            var hostInterfaces = _hostInterfacesRepository.GetHostInterfacesByMerchantTerminalAndSurchargeRule(merchantTerminalId, surchargeRuleId);

            var result = new List<ViewItemModel>();

            foreach (var item in hostInterfaces)
            {

                result.Add(new ViewItemModel()
                {
                    Id = item.Id,
                    Label = item.Name,
                    Image = item.IconId,
                    ImagePath = Constants.PATH_MERCHANT
                });
            }

            return result;
        }

        public IList<ViewItemModel> GetHostInterfacesNotSelectedByMerchantId(int merchantTemninalId, int terminalMasterId)
        {
            var result = new List<ViewItemModel>();

            if (merchantTemninalId == 0 || terminalMasterId == 0)
            {
                return result;
            }

            var hostInterfaces = _hostInterfacesRepository.GetHostInterfacesNotSelectedByMerchantId(merchantTemninalId, terminalMasterId);

            foreach (var item in hostInterfaces)
            {
                var source = item;
                if (!item.IsUpdated && item.ParentId.HasValue)
                {
                    source = item.Parent;
                    if (!item.Parent.IsUpdated && item.Parent.ParentId.HasValue)
                    {
                        source = item.Parent.Parent;
                    }
                }

                result.Add(new ViewItemModel
                {
                    Id = item.Id,
                    IsActive = false,
                    Label = source.Name,
                    Image = source.IconId,
                    ImagePath = Constants.PATH_MERCHANT
                });
            }

            return result;
        }
        
        public IList<ViewItemModel> GetHostInterfacesNotSelectedByMerchantAndSurchargeRule(int merchantTerminalId, int terminalMasterId, int surchargeRuleId)
        {
            var result = new List<ViewItemModel>();

            if (merchantTerminalId == 0 || terminalMasterId == 0 || surchargeRuleId == 0)
            {
                return result;
            }

            var hostInterfaces = _hostInterfacesRepository.GetHostInterfacesNotSelectedByMerchantAndSurchargeRule(merchantTerminalId, terminalMasterId, surchargeRuleId);

            foreach (var item in hostInterfaces)
            {
                result.Add(new ViewItemModel { Id = item.Id, IsActive = false, Label = item.Name, Image = item.IconId, ImagePath = Constants.PATH_MERCHANT });
            }

            return result;
        }

        public IList<HostInterfaces> GetHostInterfacesNotSelectedByTerminal(int terminalMasterId)
        {
            return _hostInterfacesRepository.GetHostInterfacesNotSelectedByTerminal(terminalMasterId);
        }

        public IList<HostInterfaces> GetGlobalHostInterfaces()
        {
            return _hostInterfacesRepository.GetGlobalHostInterfaces();
        }

        public IList<HostInterfaces> GetTerminalHostInterfaces(int terminalMasterId)
        {
            return _hostInterfacesRepository.GetTerminalHostInterfaces(terminalMasterId);
        }
    }
}
