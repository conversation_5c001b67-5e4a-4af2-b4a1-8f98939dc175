import { ApiBaseModel, PaginationParams } from './common.model';

export interface AvailableDevicesApiModel extends ApiBaseModel {
  name: string;
  serialNumber: string;
  brandName: string;
  modelName: string;
  merchantDisplay: string;
}

export interface DeviceAssociatedResponseApiModel extends ApiBaseModel {
  name: string;
  serialNumber: string;
  brandName: string;
  modelName: string;
  merchantDisplay: string;
}

export interface DeviceBrand extends ApiBaseModel {
  id: number;
  name: string;
}

export interface DeviceModel extends ApiBaseModel {
  id: number;
  name: string;
  brandId: number;
}

export interface DeviceWithStatus extends AvailableDevicesApiModel {
  randomStatus: {
    isOnline: boolean;
    status: string;
  };
  isStatus: number;
  isActive: boolean;
  created: string;
  modified: string;
}

export interface AssignedDeviceParamsApi extends PaginationParams {
  locationIdsSelected?: string;
  locationCategoryId?: number;
  deviceTypeId?: number;
  deviceIndustryId?: number;
  deviceBrandId: string;
  deviceModelId: string;
  merchantId: string;
  isActivate?: boolean;
  companyId?: number;
  groupId?: number;
  groupMappingId?: number;
  cloudSecurityAccessMappingId?: number;
  searchValue?: string;
  searchType?: 'sn' | 'brand' | 'model';
  companyGroupMappingId?: number;
}

export interface AssignDevicesModel {
  locationAreaId: number;
  terminalMasterIds: number[];
  groupId?: number;
}

export interface AvailableDeviceParams extends PaginationParams {
  companyDetailId?: number;
  keySearch?: string;
  deviceBrandIds?: number[];
  deviceModelIds?: number[];
}

export enum MenuActionDevice {
  Delete = 'delete',
  Details = 'details',
  AssignMerchants = 'assign_merchants',
}

export interface SyncLinklyDeviceResponse {
  message: string;
  company_id: number;
  terminals_count: number;
  inserted: number;
  updated: number;
  deleted: number;
}
