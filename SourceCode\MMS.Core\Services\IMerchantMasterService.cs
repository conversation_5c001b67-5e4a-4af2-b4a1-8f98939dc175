using MMS.Core.Services.Base;
using MMS.Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using MMS.Model.ApiModelResponse;
using MMS.Model.Common;
using MMS.Model.ApiModelRequest;
using MMS.Infrastructure.Commons;
using MMS.Model.Base;

namespace MMS.Core.Services
{
    public interface IMerchantMasterService: IBaseService<MerchantMaster>
    {
        MerchantMaster GetById(int id, bool isIncludeList = false, params Expression<Func<MerchantMaster, object>>[] includes);
        int LastMaster();
        MerchantMaster GetByName(string name);
        MerchantMaster GetByEmail(string email);
        IList<MerchantMaster> GetAllMerchantByCompanyAssociations(int companyId);
        IList<ViewItemModel> GetViewItemsByLang(string searchKey, int skip, int take);
        IList<ViewItemModel> GetViewItemsByLang(string searchKey, int skip, int take, List<int> ids);
        IList<ViewItemModel> GetSelectedMerchantByParentsAndSelectedLists(string searchKey, int skip, int take,
            string companies, bool inCompanyList, string ids, bool inIdList);
        IList<ViewItemModel> GetViewMerchantByCompanies(string searchKey, int skip, int take, 
            string companies, bool inList, string[] property = null);
        IList<string> CheckMerchantNameExist(string input, int id);
        IList<int> GetMerchantIdsByMerchantCompanyIds(string merchantCompanyIds);

        /// <summary>
        /// Getting source for select remove form with flag is multi merchant or not.
        /// </summary>
        /// <param name="terminalMasterId"></param>
        /// <returns></returns>
        Task<(IList<MerchantMaster> MerchantMasters, bool MultiMerchantLocation)> GetSourceForDeviceSelectMerchantAsync(int terminalMasterId);

        /// <summary>
        /// Getting source for select remove form with flag is multi merchant or not.
        /// </summary>
        /// <param name="terminalMasterId"></param>
        /// <param name="selectedIds"></param>
        /// <returns></returns>
        Task<(PagingResponse<SelectListItemModel> Merchants, bool MultiMerchantLocation)> GetSourceForSelectMerchantAsync(int terminalMasterId, IList<int> selectedIds, string searchKey, SelectedFilterType filter, PagingParameter pagingParameter);

        /// <summary>
        /// Get selected merchant by location area id
        /// </summary>
        /// <param name="locationAreaId"></param>
        /// <returns></returns>
        Task<IList<MerchantMaster>> GetSelectMerchantByLocationItemAsync(int locationAreaId);

        /// <summary>
        /// Retrieves a filtered list of merchant masters that can be selected for a specified location.
        /// </summary>
        /// <param name="locationId">The ID of the location for which the merchant masters are being selected.</param>
        /// <returns>
        /// A task representing the asynchronous operation. The task result contains a list of <see cref="MerchantMaster"/> objects that are not already associated with other locations.
        /// </returns>
        Task<IList<MerchantMaster>> GetSourceMerchantMasterListToSelectForLocationAsync(int locationId);

        /// <summary>
        /// Get all activate merchants
        /// </summary>
        /// <returns></returns>
        IList<ListItemResponse> GetAllActivateMerchants();

        /// <summary>
        /// Get source for select Merchant api
        /// </summary>
        /// <param name="id"></param>
        /// <param name="selectedIds"></param>
        /// <param name="selectRemoveFormParameter"></param>
        /// <returns></returns>
        Task<PagingResponse<DataItemResponse>> GetSourceMerchantMasterListToSelectForLocationApiAsync(int id, IEnumerable<int> selectedIds, SelectRemoveFormParameter selectRemoveFormParameter);
    }
}
