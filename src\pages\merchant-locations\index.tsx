import { Collapse } from 'antd';
import LocationTree from '../../components/location-tree';
import './index.scss';
import DeviceList from './../../components/device-list/index';
import { useEffect, useMemo } from 'react';
import { useBreadcrumb } from '../../hooks/useBreadcrumb';
import { ROUTE_PATHS } from '../../constants/router.constants';
import { useRecoilState, useRecoilValue } from 'recoil';
import { selectedLocationItemState } from '../../states/location-tree.state';
import { layoutCollapseState } from '../../states/layout';
import { useTranslation } from 'react-i18next';
import { LAYOUT } from '../../constants/app-constants';

function MerchantLocationPage() {
  const selectedLocation = useRecoilValue(selectedLocationItemState);
  const [layoutCollapse, setLayoutCollapse] =
    useRecoilState(layoutCollapseState);
  const { updateBreadcrumb } = useBreadcrumb();
  const { t } = useTranslation();

  useEffect(() => {
    updateBreadcrumb(
      {
        title: t('breadcrumbs.deviceManager'),
        path: ROUTE_PATHS.MerchantLocations,
      },
      true,
    );
  }, [t]);

  const onChange = (key: string | string[]) => {
    const isEmptyArray = Array.isArray(key) && key.length === 0;

    setLayoutCollapse({
      ...layoutCollapse,
      locationFilterCollapsed: !isEmptyArray,
    });
  };

  const calculateTreeWidth = useMemo(() => {
    const sidebarWidth = layoutCollapse.sidebarCollapsed
      ? LAYOUT.WIDTH_MENU_COLLAPSED + LAYOUT.MARGIN
      : LAYOUT.WIDTH_MENU + LAYOUT.MARGIN;

    const companyTreeWidth = layoutCollapse.companyTreeCollapsed
      ? LAYOUT.WIDTH_COMPANY_TREE_COLLAPSED
      : LAYOUT.WIDTH_COMPANY_TREE + LAYOUT.MARGIN;

    return `calc(100vw - ${sidebarWidth + companyTreeWidth + LAYOUT.MARGIN}px)`;
  }, [layoutCollapse.sidebarCollapsed, layoutCollapse.companyTreeCollapsed]);

  return (
    <div className="container">
      <div className="merchant-location " style={{ width: calculateTreeWidth }}>
        <Collapse
          onChange={onChange}
          className="merchant-collapse"
          defaultActiveKey={layoutCollapse.locationFilterCollapsed ? ['1'] : []}
          ghost
          items={[
            {
              key: '1',
              label: (
                <span style={{ fontWeight: 600 }}>
                  Merchant Location Filter: {selectedLocation?.label || ''}
                </span>
              ),
              children: <LocationTree />,
            },
          ]}
        />
        <DeviceList />
      </div>
    </div>
  );
}

export default MerchantLocationPage;
