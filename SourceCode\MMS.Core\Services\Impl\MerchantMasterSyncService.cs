using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MMS.Core.CoreUTI.Enum;
using MMS.Core.Entities;
using MMS.Core.Repository;
using MMS.Core.Services.Impl.Base;
using MMS.Model.LinklyFake;

namespace MMS.Core.Services.Impl
{
    /// <summary>
    /// Service for synchronizing merchant master data from external sources
    /// </summary>
    public class MerchantMasterSyncService : BaseService<MerchantMaster>, IMerchantMasterSyncService
    {
        private readonly IMerchantMasterRepository _merchantMasterRepository;

        /// <summary>
        /// Constructor for MerchantMasterSyncService
        /// </summary>
        /// <param name="merchantMasterRepository">Repository for merchant master data</param>
        public MerchantMasterSyncService(
            IMerchantMasterRepository merchantMasterRepository,
            ILanguageExecuteService languageExecuteService
            ) : base(merchantMasterRepository, languageExecuteService)
        {
            _merchantMasterRepository = merchantMasterRepository ?? throw new ArgumentNullException(nameof(merchantMasterRepository));
        }

        /// <inheritdoc />
        public async Task<(int inserted, int updated, int deleted)> SyncMerchantMastersFromLinklyAsync(List<LinklyMerchantMasterDto> linklyMerchantMasters)
        {
            int inserted = 0, updated = 0, deleted = 0;

            // Always use Linkly source for this method since it's specifically for syncing with Linkly
            var existingLinklyMerchantMasters = await _merchantMasterRepository.GetLinklyMerchantMastersAsync();

            // Get all existing LinklyIds for quick lookup
            var existingLinklyIds = existingLinklyMerchantMasters
                .Where(m => !string.IsNullOrEmpty(m.LinklyId))
                .Select(m => m.LinklyId)
                .ToHashSet();

            // Process each merchant master from Linkly - only insert new ones
            foreach (var linklyMerchantMaster in linklyMerchantMasters)
            {
                // Skip if merchant master already exists
                if (!string.IsNullOrEmpty(linklyMerchantMaster.LinklyId) && existingLinklyIds.Contains(linklyMerchantMaster.LinklyId))
                {
                    continue;
                }

                // Create new merchant master
                var newMerchantMaster = MapLinklyMerchantMasterToMerchantMaster(linklyMerchantMaster);
                _merchantMasterRepository.Insert(newMerchantMaster, commit: false);
                inserted++;
            }

            // Save all changes asynchronously
            await _merchantMasterRepository.SaveChangesAsync();

            return (inserted, updated, deleted);
        }

        /// <summary>
        /// Maps a LinklyMerchantMasterDto to a MerchantMaster entity
        /// </summary>
        /// <param name="linklyMerchantMaster">The LinklyMerchantMasterDto to map</param>
        /// <returns>A new MerchantMaster entity</returns>
        private static MerchantMaster MapLinklyMerchantMasterToMerchantMaster(LinklyMerchantMasterDto linklyMerchantMaster)
        {
            return new MerchantMaster
            {
                LinklyId = linklyMerchantMaster.LinklyId,
                lszMerchantName = linklyMerchantMaster.MerchantName,
                lszEmailAddress = linklyMerchantMaster.EmailAddress,
                lszLogo = linklyMerchantMaster.Logo,
                MerchantID = linklyMerchantMaster.MerchantID,
                lszPhone = linklyMerchantMaster.Phone,
                lszMobilePhone = linklyMerchantMaster.MobilePhone,
                // CompanyDetailId = linklyMerchantMaster.CompanyDetailId, // We might need the FK id later if client requested.
                // PersonalDetailId = linklyMerchantMaster.PersonalDetailId,
                // BusinessHourId = linklyMerchantMaster.BusinessHourId,
                // MerchantCompanyId = linklyMerchantMaster.MerchantCompanyId,
                Latitude = linklyMerchantMaster.Latitude,
                Longitude = linklyMerchantMaster.Longitude,
                IsRoaming = linklyMerchantMaster.IsRoaming,
                lszPicture = linklyMerchantMaster.Picture,
                lszStreetAddressGeo = linklyMerchantMaster.StreetAddressGeo,
                lszIPGeo = linklyMerchantMaster.IPGeo,
                fMobileLocation = linklyMerchantMaster.FMobileLocation,
                UseSortBusinessCate = linklyMerchantMaster.UseSortBusinessCate,
                UseSortContactPersons = linklyMerchantMaster.UseSortContactPersons,
                UseSortMerchantUsers = linklyMerchantMaster.UseSortMerchantUsers,
                IsUserChangesAllowed = linklyMerchantMaster.IsUserChangesAllowed,
                IsMerchantTemplate = linklyMerchantMaster.IsMerchantTemplate,
                IsActive = linklyMerchantMaster.Status.Equals("Active", StringComparison.OrdinalIgnoreCase),
                IsStatus = CoreUTI.Constants.NOTCHANGE_RECORD,
                Created = linklyMerchantMaster.Created,
                Modified = linklyMerchantMaster.Modified,
            };
        }
    }
}
