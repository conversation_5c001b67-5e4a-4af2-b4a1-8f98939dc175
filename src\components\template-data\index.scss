.merchant-settings-component {
  .tabs-section {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;

    .tab-button {
      display: flex;
      align-items: center;
      gap: 8px;

      &.ant-btn-primary {
        background-color: var(--main-1);
        border-color: var(--main-1);
        color: white;

        &:hover,
        &:focus {
          background-color: var(--main-1-hover);
          border-color: var(--main-1-hover);
        }
      }
    }
  }
}

.ant-dropdown {
  .ant-dropdown-menu {
    max-height: 300px;
    overflow-y: auto;

    .dropdown-menu-item {
      display: flex;
      align-items: center;
      gap: 8px;

      &.active {
        color: white;
        font-weight: 500;
        background-color: var(--main-1);
        border-radius: 4px;
        padding: 6px 8px;
        margin: -6px -8px;
      }
    }

    .ant-dropdown-menu-item {
      &:hover {
        background-color: var(--main-1);
        color: white;
      }
    }

    .ant-dropdown-menu-item-active {
      background-color: var(--main-1);
      color: white;
    }
  }
}

.merchant-settings-component {
  .merchant-settings {
    flex: 1;

    .settings-card {
      margin-bottom: 16px;
      height: calc(100% - 16px);
      border-radius: 8px;

      .table-scroll-container {
        height: calc(100vh - 440px);
        overflow-y: auto;

        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb:hover {
          background: #a8a8a8;
        }
      }

      .ant-card-body {
        padding: 0;
        height: 100%;
        display: flex;
        flex-direction: column;
      }
    }

    .settings-table {
      width: 100%;
      border-collapse: collapse;
      flex: 1;

      tr {
        position: relative;
      }

      tr::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: #e6e6e6;
      }

      .setting-row {
        &:last-child {
          border-bottom: none;
        }

        &.nested-row {
          background-color: #f9f9f9;

          &.indented {
            background-color: #ffffff;
          }
        }

        &.collapsible {
          cursor: pointer;

          &:hover {
            background-color: #f5f5f5;
          }

          &.expanded {
            background-color: #f5f5f5;
          }
        }

        &.array-item {
          background-color: #fafbfd;
        }

        .setting-name {
          padding: 12px 16px;

          &.has-children {
            cursor: pointer;
            user-select: none;

            &:hover {
              background-color: #f5f5f5;
            }
          }

          &.group-header {
            font-weight: 500;
          }

          .expand-icon {
            display: inline-block;
            margin-right: 8px;
            font-size: 10px;
            width: 10px;
            text-align: center;
            color: var(--main-1);
          }
        }

        .setting-value {
          padding: 12px 16px;
          text-align: right;
        }
      }
    }
  }
}
