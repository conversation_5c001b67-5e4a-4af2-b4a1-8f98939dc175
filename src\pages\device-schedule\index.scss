.device-schedule-container {
  width: 100%;
  padding: 16px;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  height: 100%;
  min-width: 800px;

  // App Info Section
  .app-info-section {
    margin-bottom: 32px;

    .ant-input {
      border: none;
    }
  }

  // Section Title
  .section-title {
    font-size: 14px;
    font-weight: 600;
    color: #262626;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e8e8e8;
  }

  // Template Form Section - 3 columns
  .template-form-section {
    .column-with-border {
      .form-header {
        margin-bottom: 10px;
      }

      .template-input {
        padding: 13px 16px;
      }

      .template-select {
        width: 100%;

        .ant-select-selector {
          padding: 0 16px;

          .ant-select-selection-item {
            font-size: 14px;
          }
        }

        .ant-select-arrow {
          right: 8px;
        }
      }

      // Merchant section
      .merchant-section {
        margin-top: 16px;
        border-radius: 6px;
        height: auto !important;
        max-height: 360px;
        overflow-y: auto;

        .ant-list-header {
          justify-content: start;
          padding-inline: 8px;
        }

        .ant-list-items {
          padding: 0 12px;

          .ant-list-item {
            height: 44px;
          }
        }

        .merchant-list-header {
          font-weight: bold;
        }
      }

      // Template type section
      .template-type-section {
        margin: 16px 0;
        border-radius: 4px;

        .template-type-block {
          padding: 16px;
          display: flex;
          justify-content: space-between;
          border: 1px solid #e6e6e6;
          margin-bottom: 16px;
          border-radius: 6px;

          .template-type-content {
            width: 80%;

            .template-type-label {
              font-size: 12px;
              font-style: normal;
              font-weight: 400;
              line-height: 122%;
            }

            .template-type-value {
              font-size: 16px;
              font-style: normal;
              font-weight: 600;
              line-height: 140%;
              width: 100%;
              background-color: transparent;
              border: none;
              outline: none;
              cursor: pointer;
              text-align: left;
              padding: 0;
            }

            .ant-spin {
              .ant-spin-dot-holder {
                color: var(--main-2);
              }

              .ant-spin-text {
                font-size: 16px;
              }
            }
          }

          .close-btn {
            display: flex;
            width: 32px;
            height: 32px;
            padding: 20px;
            justify-content: center;
            align-items: center;
            flex-shrink: 0;
            aspect-ratio: 1/1;
            outline: none;
            border: none;
            background-color: #f5f6fa;
            border-radius: 4px;
          }
        }

        .schedule-upgrade {
          padding: 14px 16px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          background-color: #f5f6fa;
          border-radius: 6px;
          gap: 8px;

          span {
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 122%;
            width: 100%;
          }
        }
      }
    }

    // Action buttons column
    .action-column {
      display: flex;
      flex-direction: column;
      align-items: flex-end;

      .button-row {
        width: 100%;
        margin-top: 25px;
        display: flex;
        flex-wrap: wrap;
        gap: 16px;

        .action-btn {
          flex: 1 1 0;
          min-width: 130px;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 122%;
          height: 44px;
          display: flex;
          align-items: center;
          justify-content: center;
          white-space: nowrap;

          .ant-btn-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;

            svg {
              width: 16px;
              height: 16px;
              flex-shrink: 0;
            }
          }

          .btn-text {
            flex-shrink: 0;
          }
        }

        .template-setup-btn {
          background-color: #f3faf8;
          color: #00a26d;
        }

        .save-template-btn {
          background-color: #00a26d;
          border-color: #00a26d;
        }
      }
    }
  }
}
.selectable-list.ant-list-bordered {
  .ant-list-header {
    display: none !important;
  }
}

@media screen and (max-width: 991px) {
  .action-column {
    .button-row {
      .action-btn {
        font-size: 11px;
      }
    }
  }
}

@media screen and (max-width: 767px) {
  .action-column {
    .button-row {
      .action-btn {
        font-size: 12px;
      }
    }
  }
}
