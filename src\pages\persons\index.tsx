import {
  Table,
  Avatar,
  Button,
  Space,
  Input,
  Select,
  TableColumnsType,
  Tag,
} from 'antd';
import { PersonItemResponse } from '../../models/person.model';
import './index.scss';
import { useEffect, useState } from 'react';
import { useBreadcrumb } from '../../hooks/useBreadcrumb';
import FontIcon from '../../components/shared/icons/font-icon';
import PaginationComponent from '../../components/shared/pagination';
import { ROUTE_PATHS } from '../../constants/router.constants';
import { useNavigate, useLocation, useParams } from 'react-router-dom';
import { PersonApi } from '../../apis/person.api';
import { useTranslation } from 'react-i18next';
import { SelectOptionNumberValue } from '../../models/common.model';
import { SearchPersonOption } from '../../constants/app.enums';
import showNotification from '../../components/shared/notification';
import showModal from '../../components/shared/notification-modal';
import DefaultUserImage from '../../assets/images/default-user.jpg';

function PersonsPage() {
  const baseUrl = import.meta.env.VITE_MMS_API_URL;
  const location = useLocation();
  const navigate = useNavigate();
  const { t } = useTranslation();

  // Get page from URL query parameters
  const getPageFromUrl = (): number => {
    const { page } = useParams<{ page: string }>();
    return page ? parseInt(page, 10) : 1;
  };

  const searchPersonOptions: SelectOptionNumberValue[] = [
    {
      label: t('personManagement.searchOptiopns.name'),
      value: SearchPersonOption.Name,
    },
    {
      label: t('personManagement.searchOptiopns.email'),
      value: SearchPersonOption.Email,
    },
  ];

  const columns: TableColumnsType<PersonItemResponse> = [
    {
      title: t('personManagement.personList.columns.personName'),
      dataIndex: 'name',
      key: 'name',
      width: '30%',
      render: (text: string, record: PersonItemResponse) => (
        <Space>
          <Avatar
            size={44}
            src={
              record.profileImage
                ? `${baseUrl}/${record.profileImage}`
                : DefaultUserImage
            }
          />
          <span className="body-1-bold">{text}</span>
        </Space>
      ),
      onCell: (record: PersonItemResponse) => ({
        onClick: () => record.id && handleViewPersonDetails(record.id),
      }),
    },
    {
      title: t('personManagement.personList.columns.email'),
      dataIndex: 'email',
      key: 'email',
      width: '30%',
      onCell: (record: PersonItemResponse) => ({
        onClick: () => record.id && handleViewPersonDetails(record.id),
      }),
    },
    {
      title: t('personManagement.personList.columns.lastLogin'),
      dataIndex: 'lastLogin',
      key: 'lastLogin',
      width: '30%',
      render: (lastLogin: string) => (
        <div className="status-tag">
          <span>{lastLogin}</span>
        </div>
      ),
      onCell: (record: PersonItemResponse) => ({
        onClick: () => record.id && handleViewPersonDetails(record.id),
      }),
    },
    {
      key: 'action',
      align: 'right' as const,
      width: '10%',
      render: (_: any, record: PersonItemResponse) => (
        <Space size="middle">
          {record.suspendLogonAccess ? (
            <Tag
              icon={<FontIcon size={16} className="icon-clock" />}
              className="action-btn btn-lock"
              title={t('personManagement.personList.actions.lockTitle')}
            />
          ) : (
            ''
          )}
          <Button
            type="text"
            icon={<FontIcon size={16} className="icon-_close" />}
            className="action-btn btn-delete"
            onClick={() => {
              showModal(
                'delete',
                t('personManagement.personList.actions.deleteConfirmTitle'),
                t(
                  'personManagement.personList.actions.deleteConfirmDescription',
                ),
                {
                  okText: t(
                    'personManagement.personList.actions.deleteConfirmOk',
                  ),
                  cancelText: t(
                    'personManagement.personList.actions.deleteConfirmCancel',
                  ),
                  onOk: () => handleDeletePerson(record.id),
                },
              );
            }}
          />
        </Space>
      ),
    },
  ];

  const { updateBreadcrumb } = useBreadcrumb();

  const [loading, setLoading] = useState(true);
  const defaultPageSize = 10;
  const [pageNumber, setPageNumber] = useState(getPageFromUrl());
  const [pageSize, setPageSize] = useState(defaultPageSize);
  const [totalRecord, setTotalRecord] = useState(50);
  const [persons, setPersons] = useState<PersonItemResponse[]>([]);

  const SEARCH_STRING = t('personManagement.searchString');
  const [searchSelectedOption, setSearchSelectedOption] = useState(
    searchPersonOptions[0].value,
  );

  useEffect(() => {
    updateBreadcrumb(
      {
        title: t('personManagement.title'),
        path: ROUTE_PATHS.Persons,
      },
      true,
    );
  }, [t]);

  // Update URL when page number changes
  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);

    if (pageNumber === 1) {
      queryParams.delete('page');
    } else {
      queryParams.set('page', pageNumber.toString());
    }

    const newSearch = queryParams.toString();
    const newPath = `${location.pathname}${newSearch ? `?${newSearch}` : ''}`;

    // Only navigate if the URL would actually change
    if (location.search !== (newSearch ? `?${newSearch}` : '')) {
      navigate(newPath, { replace: true });
    }
  }, [pageNumber, location.pathname, location.search, navigate]);

  const handleViewPersonDetails = (id: number) => {
    navigate(`${ROUTE_PATHS.Persons}/${id}`);
  };

  const handleDeletePerson = async (id: number) => {
    try {
      await PersonApi.deletePerson(id);
      showNotification(
        'success',
        t('personManagement.personList.actions.deleteSuccessMessage'),
        t('personManagement.personList.actions.deleteSuccessDescription'),
      );
      const { data } = await PersonApi.getPersonList(
        '',
        SearchPersonOption.Name,
        pageNumber,
        pageSize,
        true,
      );
      if (data && data.data.length === 0 && pageNumber > 1) {
        setPageNumber(pageNumber - 1);
      } else {
        setPersons(data.data);
        setTotalRecord(data.totalRecords);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const getPersons = async (searchString = '', searchType = 1) => {
    try {
      const { data } = await PersonApi.getPersonList(
        searchString,
        searchType,
        pageNumber,
        pageSize,
        true,
      );
      if (data) {
        setPersons(data.data);
        setTotalRecord(data.totalRecords);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  const handleSelectSearchOptions = (value: number) => {
    setSearchSelectedOption(value);
  };

  const handleSearch = (value: string) => {
    getPersons(value, searchSelectedOption);
    setPageNumber(1); // Reset to first page on search
  };

  const handleChangePageSize = (newPageSize: number) => {
    setPageSize(newPageSize);
    const maxPage = Math.ceil(totalRecord / newPageSize);
    if (pageNumber > maxPage) {
      setPageNumber(1);
    }
  };

  useEffect(() => {
    getPersons();
  }, [pageNumber, pageSize]);

  return (
    <div className="person-container">
      <div className="person-header">
        <span className="body-2-bold">
          {t('personManagement.personList.title')}
        </span>
        <Space
          className="person-header-action-container"
          direction="horizontal"
        >
          <Space.Compact>
            <Select
              className="person-header-select"
              defaultValue={searchPersonOptions[0].value}
              options={searchPersonOptions}
              onChange={(value) => handleSelectSearchOptions(value)}
            />
            <Input
              className="person-header-input body-2-regular"
              placeholder={`${SEARCH_STRING} ${searchSelectedOption === searchPersonOptions[0].value ? searchPersonOptions[0].label : searchPersonOptions[1].label}`}
              allowClear={{
                clearIcon: <FontIcon size={12} className="icon-_close" />,
              }}
              onPressEnter={(e) =>
                handleSearch((e.target as HTMLInputElement).value)
              }
            />
          </Space.Compact>
          <Button
            type="primary"
            icon={<FontIcon size={16} className="icon-plus" />}
            onClick={() => {
              navigate(ROUTE_PATHS.Persons + '/0');
            }}
            className="add-btn"
          >
            {t('personManagement.addPerson')}
          </Button>
        </Space>
      </div>
      <Table<PersonItemResponse>
        loading={loading}
        dataSource={persons}
        columns={columns}
        rowKey="id"
        pagination={false}
        rowClassName={(_, index) =>
          index % 2 === 0 ? 'table-row-light' : 'table-row-dark'
        }
        tableLayout="fixed"
        className="person-table"
      />

      <div className="persons-pagination">
        {persons && (
          <PaginationComponent
            totalRecords={totalRecord}
            pageSize={pageSize}
            setPageSize={handleChangePageSize}
            pageNumber={pageNumber}
            setPageNumber={setPageNumber}
          />
        )}
      </div>
    </div>
  );
}

export default PersonsPage;
