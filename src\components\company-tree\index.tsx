
import { CloseOutlined, ReloadOutlined } from '@ant-design/icons';
import { Input, Spin, Tree, Space, Dropdown, App, Button } from 'antd';
import type { MenuProps } from 'antd';
import React, { useEffect, useState, useCallback } from 'react';
import { useRecoilState, useRecoilValue } from 'recoil';
import { CompanyTreeApi } from '../../apis/company-tree.api';
import { CompanyItemType } from '../../constants/app.enums';
import { LOCAL_STORAGE_KEY } from '../../constants/app-constants';
import { CompanyItem, TreeNode } from '../../models/company-tree.model';
import { companyTreeDataState } from '../../states/companyTree';
import { layoutCollapseState } from '../../states/layout';
import { AppIcons } from '../shared/icons';
import './index.scss';
import { useTranslation } from 'react-i18next';
import FontIcon from '../shared/icons/font-icon';
import LocalUtils from '../../utils/local.utils';
import { useLocation } from 'react-router-dom';
import SelectCompaniesModal from './select-companies';
import showModal from '../shared/notification-modal';
import AddGroupModal from './add-group';
import { getStorageKeyFromPath } from '../../utils/local.utils';
import debounce from 'lodash/debounce';

const CompanyTree: React.FC = () => {
  const location = useLocation();
  const { isIncludeGroup } = useRecoilValue(companyTreeDataState);
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const [searchResults, setSearchResults] = useState<CompanyItem[]>([]);
  const [loadedKeys, setLoadedKeys] = useState<string[]>([]);
  const [treeDataState, setTreeDataState] = useState<TreeNode[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [loadingKeys, setLoadingKeys] = useState<React.Key[]>([]);
  const [companyTreeData, setCompanyTreeData] =
    useRecoilState(companyTreeDataState);
  const [layoutCollapse, setLayoutCollapse] =
    useRecoilState(layoutCollapseState);
  const { t } = useTranslation();
  const [restored, setRestored] = useState(false);
  const [selectedParentKeys, setSelectedParentKeys] = useState<string[]>([]);
  const [selectCompaniesModalVisible, setSelectCompaniesModalVisible] =
    useState(false);
  const [addGroupModalVisible, setAddGroupModalVisible] = useState(false);
  const { message } = App.useApp();
  const [showSearch, setShowSearch] = useState(false);

  useEffect(() => {
    // Reset companyTreeData when pathname changes
    setCompanyTreeData((prev) => ({
      ...prev,
      selectedItems: [],
      lastSelectedItem: null,
      parentItem: null,
    }));
    // Reset tree state
    setTreeDataState([]);
    setExpandedKeys([]);
    setSelectedKeys([]);
    setLoadedKeys([]);
    setSelectedParentKeys([]);
    setRestored(false);
  }, [location.pathname]);

  // Load the original company data when the component is mounted or isIncludeGroup changes
  useEffect(() => {
    fetchRootCompanies();
  }, [isIncludeGroup]);

  // Effect to restore selection only once after F5
  useEffect(() => {
    if (!restored && treeDataState.length > 0 && !loading) {
      const savedSelection = LocalUtils.getCompanySelection(location.pathname);
      if (savedSelection) {
        // Add a check to prevent double loading
        const isAlreadyExpanded = expandedKeys.includes(
          savedSelection.nodeId.toString(),
        );
        if (!isAlreadyExpanded) {
          restoreSavedSelection(savedSelection);
          setRestored(true);
        }
      }
    }
  }, [treeDataState, loading, restored, location.pathname]);

  // Function to save selected company and its path to localStorage
  const saveSelectedCompany = (
    selectedItem: CompanyItem,
    parentIds: number[] = [],
  ) => {
    if (selectedItem) {
      LocalUtils.setCompanySelection(
        selectedItem,
        parentIds,
        location.pathname,
      );
    }
  };

  const fetchRootCompanies = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await CompanyTreeApi.getCompanyGroupMapping(
        null,
        isIncludeGroup,
      );
      if (response.data && response.data.items) {
        const rootCompanies = transformApiDataToTreeNodes(response.data.items);
        setTreeDataState(rootCompanies);
      } else {
        setError('Invalid data from API');
      }
    } catch (error) {
      setError('Unable to load data from API');
    } finally {
      setLoading(false);
    }
  };

  // Function to restore the saved selection
  const restoreSavedSelection = async (savedSelection: {
    nodeId: number;
    parentIds: number[];
  }) => {
    const { nodeId, parentIds } = savedSelection;

    try {
      // First, set the expanded keys and selected keys
      const keysToExpand = [
        ...(parentIds || []).map((id) => id.toString()),
        nodeId.toString(),
      ];
      setExpandedKeys(keysToExpand);
      setSelectedKeys([nodeId.toString()]);
      setSelectedParentKeys((parentIds || []).map(String));

      // Load parent nodes in sequence from root to leaf
      if (parentIds && parentIds.length > 0) {
        // Sort parentIds to ensure we load from root to leaf
        const sortedParentIds = [...parentIds].sort((a, b) => a - b);

        for (const parentId of sortedParentIds) {
          const parentKey = parentId.toString();

          // Skip if already loaded
          if (
            loadedKeys.includes(parentKey) ||
            loadingKeys.includes(parentKey)
          ) {
            continue;
          }

          setLoadingKeys((prev) => [...prev, parentKey]);
          try {
            const { data } = await CompanyTreeApi.getCompanyGroupMapping(
              parentId,
              isIncludeGroup,
            );
            if (data && data.items && data.items.length > 0) {
              const childrenData = transformApiDataToTreeNodes(data.items);
              setTreeDataState((origin) =>
                updateTreeData(origin, parentKey, childrenData),
              );
              setLoadedKeys((prev) => [...prev, parentKey]);
            }
          } catch (error) {
            console.error(
              `Error fetching children for parentId ${parentId}:`,
              error,
            );
          } finally {
            setLoadingKeys((prev) => prev.filter((k) => k !== parentKey));
          }
        }
      }

      // Wait for a short delay to ensure tree data is updated
      await new Promise((resolve) => setTimeout(resolve, 100));

      // Find and set the selected item
      const findItemById = (
        nodes: TreeNode[],
        id: number,
      ): CompanyItem | null => {
        for (const node of nodes) {
          if (node.item && node.item.id === id) {
            return node.item;
          }
          if (node.children) {
            const found = findItemById(node.children, id);
            if (found) return found;
          }
        }
        return null;
      };

      const selectedItem = findItemById(treeDataState, nodeId);
      if (selectedItem) {
        const parentItem = selectedItem.parentId
          ? findItemById(treeDataState, selectedItem.parentId)
          : null;
        setCompanyTreeData((prev) => ({
          ...prev,
          selectedItems: [selectedItem],
          lastSelectedItem: selectedItem,
          parentItem: parentItem,
        }));
      }
    } catch (err) {
      console.error('Error restoring selection:', err);
    }
  };

  const getIconByItemType = (
    itemType: number,
    parentId?: number,
  ): React.ReactNode => {
    switch (itemType) {
      case CompanyItemType.Company: // Company
        if (parentId) {
          return <AppIcons.SubsidiaryIcon />;
        } else {
          return <AppIcons.CompanyIcon />;
        }
      case CompanyItemType.Subsidiary: // Subsidiary
        return <AppIcons.SubsidiaryIcon />;
      case CompanyItemType.Group: // Group
        return <AppIcons.LayerIcon />;
      default:
        return <AppIcons.CompanyIcon />;
    }
  };

  // Function that converts API data into TreeNode format
  const transformApiDataToTreeNodes = (items: CompanyItem[]): TreeNode[] => {
    items = items.sort((a, b) => a.itemType - b.itemType);

    return items.map((item) => {
      let title: React.ReactNode = item.label;
      if (item.itemType === CompanyItemType.Group) {
        title = <span className="group">{item.label}</span>;
      }

      const isLeaf = item.parentId !== null;

      return {
        title,
        key: item.id.toString(),
        icon: getIconByItemType(item.itemType, item.parentId || undefined),
        isLeaf,
        hasChild: item.hasChild,
        item: item,
      };
    });
  };

  // Function to load data to node when expanding
  const loadData = async (node: any) => {
    const { key, hasChild, children } = node;

    // If we're in search mode, filter children from searchResults
    if (searchValue.trim()) {
      // Recursively find the current node in searchResults
      const findNode = (
        items: CompanyItem[],
        targetId: number,
      ): CompanyItem | undefined => {
        for (const item of items) {
          if (item.id === targetId) {
            return item;
          }
          if (item.child) {
            const found = findNode(item.child, targetId);
            if (found) return found;
          }
        }
        return undefined;
      };

      const currentNode = findNode(searchResults, Number(key));
      if (!currentNode) return;

      // Get children directly from currentNode
      const childrenData = transformApiDataToTreeNodes(currentNode.child || []);

      if (childrenData.length > 0) {
        setTreeDataState((origin) => updateTreeData(origin, key, childrenData));
      }
      return;
    } else {
      if (
        (loadedKeys.includes(key as string) &&
          children &&
          children.length > 0) ||
        loadingKeys.includes(key as string) ||
        hasChild === false
      ) {
        return;
      }

      setLoadingKeys((prev) => [...prev, key]);
      try {
        const { data } = await CompanyTreeApi.getCompanyGroupMapping(
          key,
          isIncludeGroup,
        );
        if (data && data.items && data.items.length > 0) {
          const childrenData = transformApiDataToTreeNodes(data.items);
          setTreeDataState((origin) =>
            updateTreeData(origin, key, childrenData),
          );
          setLoadedKeys((prev) => [...prev, key]);
        }
      } catch (error) {
        console.error(`Error loading children for key ${key}:`, error);
      } finally {
        setLoadingKeys((prev) => prev.filter((k) => k !== key));
      }
    }
  };

  // Function to update tree data when new data is available
  const updateTreeData = (
    list: TreeNode[],
    key: string,
    children: TreeNode[],
  ): TreeNode[] => {
    return list.map((node) => {
      if (node.key === key) {
        return {
          ...node,
          children,
        };
      }
      if (node.children) {
        return {
          ...node,
          children: updateTreeData(node.children, key, children),
        };
      }
      return node;
    });
  };

  const transformTreeData = (
    nodes: any[],
    parentPath: string[] = [],
  ): {
    title: React.ReactNode;
    key: string;
    icon: React.ReactNode | null;
    children?: any[];
    isLeaf?: boolean;
    className?: string;
    hasChild?: boolean;
    item?: any;
  }[] => {
    return nodes.map((node) => {
      const titleText = typeof node.title === 'string' ? node.title : '';
      const isMatched = titleText
        .toLowerCase()
        .includes(searchValue.toLowerCase());

      const currentPath = [...parentPath, node.key];
      const level = currentPath.length;
      const baseClassName = `node-level-${level}`;
      const selectedClassName = selectedParentKeys.includes(node.key)
        ? 'parent-of-selected'
        : '';
      const className = `${baseClassName} ${selectedClassName}`.trim();

      // Check if node has loaded children
      const hasLoadedChildren = node.children && node.children.length > 0;
      // Only show children if the node is expanded AND has loaded children
      const shouldShowChildren =
        expandedKeys.includes(node.key) && hasLoadedChildren;

      return {
        title: (
          <div className="tree-node-content">
            {isMatched ? (
              <span title={node.title} className="highlight">
                {node.title}
              </span>
            ) : (
              node.title
            )}
            {node.hasChild && (
              <FontIcon
                size={16}
                className={`icon-down ${shouldShowChildren ? 'expanded' : ''}`}
                onClick={(e) => {
                  e.stopPropagation();
                  if (shouldShowChildren) {
                    setExpandedKeys(
                      expandedKeys.filter((key) => key !== node.key),
                    );
                  } else {
                    setExpandedKeys([...expandedKeys, node.key]);
                    loadData({ key: node.key, hasChild: node.hasChild });
                  }
                }}
              />
            )}
          </div>
        ),
        key: node.key,
        icon: node.icon,
        children: shouldShowChildren
          ? transformTreeData(node.children, currentPath)
          : undefined,
        isLeaf: node.isLeaf,
        hasChild: node.hasChild,
        item: node.item,
        className: className,
      };
    });
  };

  const debouncedSearch = useCallback(
    debounce((value: string) => {
      handleSearch(value);
    }, 400),
    [],
  );

  // Update handleSearch to store full search results
  const handleSearch = async (value: string) => {
    if (!value.trim()) {
      // If search is empty, only reload if we were in search mode before
      if (searchResults.length > 0) {
        setSearchResults([]);
        fetchRootCompanies();
      }
      return;
    }

    setLoading(true);
    try {
      const response = await CompanyTreeApi.searchCompanyTree(value);
      if (response.data && response.data.items) {
        // Store full search results
        setSearchResults(response.data.items);

        // Get root items for initial display
        const rootItems = response.data.items
          .filter((item) => item.parentId === null)
          .map((item) => ({
            title:
              item.itemType === CompanyItemType.Group ? (
                <span className="group">{item.label}</span>
              ) : (
                item.label
              ),
            key: item.id.toString(),
            icon: getIconByItemType(item.itemType),
            isLeaf:
              item.itemType === CompanyItemType.Group &&
              response.data.items.every(
                (p) => p.itemType === CompanyItemType.Group,
              ) &&
              !response.data.items.some((child) => child.parentId === item.id),
            hasChild:
              item.hasChild ||
              response.data.items.some((child) => child.parentId === item.id),
            item: item,
          }));

        setTreeDataState(rootItems);
        setSelectedParentKeys([]);
        LocalUtils.remove(
          `${LOCAL_STORAGE_KEY.COMPANY_TREE_SELECTION}_${getStorageKeyFromPath(location.pathname)}`,
        );
        setCompanyTreeData((prev) => ({
          ...prev,
          selectedItems: [],
          lastSelectedItem: null,
          parentItem: null,
        }));
        setSelectedKeys([]);
      }
    } catch (error) {
      console.error('Error searching company tree:', error);
      message.error(t('common.searchError'));
    } finally {
      setLoading(false);
    }
  };

  // Update handleExpand to handle search mode
  const handleExpand = (keys: React.Key[]) => {
    setExpandedKeys(keys as string[]);
  };

  const handleSelect = (keys: React.Key[], info: any) => {
    setSelectedKeys(keys as string[]);
    if (info.selected) {
      const selectedItem = info.node.item;

      // Manually expand to check for children
      if (
        selectedItem.hasChild &&
        !expandedKeys.includes(selectedItem.id.toString())
      ) {
        const newExpandedKeys = [...expandedKeys, selectedItem.id.toString()];
        setExpandedKeys(newExpandedKeys);
        loadData({
          key: selectedItem.id.toString(),
          hasChild: selectedItem.hasChild,
        });
      }

      // Find the parent item by traversing the tree
      const findParentItem = (
        nodes: TreeNode[],
        targetId: number,
      ): CompanyItem | null => {
        for (const node of nodes) {
          if (node.children) {
            // Check if any of this node's children is our target
            const isParent = node.children.some(
              (child) => child.item?.id === targetId,
            );
            if (isParent && node.item) {
              return node.item;
            }

            // If not found, search deeper in the tree
            const foundParent = findParentItem(node.children, targetId);
            if (foundParent) return foundParent;
          }
        }
        return null;
      };

      // Get parent item using the parentId
      const parentItem = selectedItem.parentId
        ? findParentItem(treeDataState, selectedItem.id) || null
        : null;

      setCompanyTreeData((prev) => ({
        ...prev,
        selectedItems: [selectedItem],
        lastSelectedItem: selectedItem,
        parentItem: parentItem,
      }));

      // Gather all parent IDs from the current node up to the root
      const parentIds: number[] = [];
      let currentParentId = selectedItem.parentId;

      // Recursive function to find all parent IDs
      const findParentIds = (nodes: TreeNode[], targetId: number): boolean => {
        for (const node of nodes) {
          if (node.key === targetId.toString()) {
            return true;
          }

          if (node.children) {
            if (findParentIds(node.children, targetId)) {
              if (node.item && node.item.id) {
                parentIds.unshift(node.item.id); // Add parent ID to beginning of array
              }
              return true;
            }
          }
        }
        return false;
      };

      // Add immediate parent ID if it exists
      if (currentParentId) {
        parentIds.push(currentParentId);
        findParentIds(treeDataState, currentParentId);
      }

      setSelectedParentKeys(parentIds.map(String));

      // Save the selected company and its complete path to localStorage
      saveSelectedCompany(selectedItem, parentIds);
    } else {
      setSelectedParentKeys([]);
      setCompanyTreeData((prev) => ({
        ...prev,
        selectedItems: [],
        lastSelectedItem: null,
        parentItem: null,
      }));

      // Remove COMPANY_TREE_SELECTION from local storage when unselecting
      LocalUtils.remove(
        `${LOCAL_STORAGE_KEY.COMPANY_TREE_SELECTION}_${getStorageKeyFromPath(location.pathname)}`,
      );
    }
  };

  // Function to retry on error
  const handleRetry = () => {
    setSearchValue('');
    fetchRootCompanies();
  };

  // Function to toggle company tree collapse
  const toggleCollapseCompanyTree = () => {
    setLayoutCollapse({
      ...layoutCollapse,
      companyTreeCollapsed: !layoutCollapse.companyTreeCollapsed,
    });
  };

  useEffect(() => {
    if (selectedKeys.length > 0 && treeDataState.length > 0) {
      const selectedKey = selectedKeys[0];
      const findItemById = (
        nodes: TreeNode[],
        id: number,
      ): CompanyItem | null => {
        for (const node of nodes) {
          if (node.item && node.item.id === id) {
            return node.item;
          }
          if (node.children) {
            const found = findItemById(node.children, id);
            if (found) return found;
          }
        }
        return null;
      };
      const selectedItem = findItemById(treeDataState, Number(selectedKey));
      if (selectedItem) {
        const parentItem = selectedItem.parentId
          ? findItemById(treeDataState, selectedItem.parentId)
          : null;
        setCompanyTreeData((prev) => ({
          ...prev,
          lastSelectedItem: selectedItem,
          parentItem: parentItem,
        }));
      }
    }
  }, [selectedKeys, treeDataState]);

  const getMenuItems = (): MenuProps['items'] => {
    const selectedItem = companyTreeData.lastSelectedItem;

    if (!selectedItem) {
      return [
        {
          key: '1',
          label: t('companyTree.selectCompanies'),
          onClick: () => setSelectCompaniesModalVisible(true),
        },
      ];
    }

    if (selectedItem.itemType === CompanyItemType.Group) {
      return [
        {
          key: '1',
          label: t('companyTree.editGroup'),
          onClick: () => setAddGroupModalVisible(true),
        },
        {
          type: 'divider',
        },
        {
          key: '2',
          label: t('companyTree.remove'),
          danger: true,
          onClick: () => handleRemoveCompanySelected(),
        },
      ];
    }

    return [
      {
        key: '1',
        label: t('companyTree.selectSubsidiaries'),
        onClick: () => setSelectCompaniesModalVisible(true),
      },
      {
        key: '2',
        label: t('companyTree.addGroup'),
        onClick: () => setAddGroupModalVisible(true),
      },
      {
        type: 'divider',
      },
      {
        key: '3',
        label: t('companyTree.remove'),
        danger: true,
        onClick: () => handleRemoveCompanySelected(),
      },
    ];
  };

  const handleSelectCompaniesModalCancel = () => {
    setSelectCompaniesModalVisible(false);
  };

  const handleSelectCompaniesModalSave = async () => {
    setSelectCompaniesModalVisible(false);
    fetchRootCompanies();
  };

  const handleRemoveCompanySelected = async () => {
    let id = companyTreeData.lastSelectedItem?.id;
    let name = companyTreeData.lastSelectedItem?.label;
    let type = companyTreeData.lastSelectedItem?.itemType;
    if (id) {
      const { data } = await CompanyTreeApi.canDeleteItem(id);
      if (data.canDeleteItem) {
        showModal(
          'delete',
          type == CompanyItemType.Group
            ? t('companyTree.removeGroup')
            : t('companyTree.removeCompany'),
          t('companyTree.deleteCompanyConfirm', { name }),
          {
            okText: t('common.delete'),
            cancelText: t('common.cancel'),
            onOk: async () => {
              try {
                await CompanyTreeApi.removeCompanySelected(id);
                message.success(
                  t('companyTree.deleteCompanySuccess', { name }),
                );

                setSelectedParentKeys([]);
                LocalUtils.remove(
                  `${LOCAL_STORAGE_KEY.COMPANY_TREE_SELECTION}_${getStorageKeyFromPath(location.pathname)}`,
                );
                setCompanyTreeData((prev) => ({
                  ...prev,
                  selectedItems: [],
                  lastSelectedItem: null,
                  parentItem: null,
                }));
                fetchRootCompanies();
              } catch (error) {
                console.error('Error removing company:', error);
              }
            },
          },
        );
      } else {
        showModal(
          'warning',
          t('notification.warning'),
          companyTreeData.lastSelectedItem?.itemType === CompanyItemType.Company
            ? t('notification.cannotDeleteCompanyOrSubCompany', { name })
            : t('notification.cannotDeleteGroup', { name }),
        );
      }
    }
  };

  const handleAddGroupModalCancel = () => {
    setAddGroupModalVisible(false);
  };

  const handleAddGroupModalSave = () => {
    setAddGroupModalVisible(false);
    fetchRootCompanies();
  };

  const handleCloseSearch = () => {
    setShowSearch(false);
    setSearchValue('');
    setSearchResults([]);
    fetchRootCompanies();
  };

  return (
    <div
      className={`company-tree ${layoutCollapse.companyTreeCollapsed ? 'collapsed' : ''}`}
    >
      <div className="company-tree-header">
        {showSearch ? (
          <Input
            suffix={<CloseOutlined size={16} onClick={handleCloseSearch} />}
            placeholder="Enter search value here"
            className="search-tree"
            value={searchValue}
            onChange={(e) => {
              setSearchValue(e.target.value);
              debouncedSearch(e.target.value);
            }}
          />
        ) : (
          <>
            <FontIcon
              size={16}
              className="icon-down"
              onClick={toggleCollapseCompanyTree}
            />
            <span className="company-tree-title body-2-bold">Company</span>
            <div className="company-tree-actions">
              <Space>
                {showSearch ? (
                  <FontIcon
                    size={16}
                    hoverEffect={true}
                    className="icon-_close"
                    onClick={handleCloseSearch}
                  />
                ) : (
                  <FontIcon
                    size={16}
                    hoverEffect={true}
                    className="icon-search"
                    onClick={() => setShowSearch(true)}
                  />
                )}
                <Dropdown
                  placement="bottomRight"
                  menu={{ items: getMenuItems() }}
                  trigger={['click']}
                  overlayStyle={{
                    width: '180px',
                    flexDirection: 'column',
                  }}
                >
                  <FontIcon
                    size={16}
                    hoverEffect={true}
                    className="icon-hamburger-menu"
                    style={{ cursor: 'pointer' }}
                  />
                </Dropdown>
              </Space>
            </div>
          </>
        )}
      </div>
      <div
        style={{
          width: '208px',
          height: '1px',
          background: '#E6E6E6',
          marginBottom: '4px',
        }}
      ></div>

      <div className="tree-content">
        {loading ? (
          <div style={{ textAlign: 'center', padding: '20px 0' }}>
            <Spin />
          </div>
        ) : error ? (
          <div className="message-container">
            <p className="error-text">{error}</p>
            <Button icon={<ReloadOutlined />} onClick={handleRetry}>
              {t('common.refresh')}
            </Button>
          </div>
        ) : treeDataState.length === 0 ? (
          <div className="message-container">
            <p>{t('common.noDataAvailable')}</p>
            <Button icon={<ReloadOutlined />} onClick={handleRetry}>
              {t('common.refresh')}
            </Button>
          </div>
        ) : (
          <Tree
            showLine={true}
            showIcon
            loadData={loadData}
            expandedKeys={expandedKeys}
            selectedKeys={selectedKeys}
            onExpand={handleExpand}
            onSelect={handleSelect}
            switcherIcon={() => null}
            treeData={transformTreeData(treeDataState)}
          />
        )}
      </div>

      <SelectCompaniesModal
        visible={selectCompaniesModalVisible}
        onCancel={handleSelectCompaniesModalCancel}
        onAfterSave={handleSelectCompaniesModalSave}
        selectedCompany={companyTreeData.lastSelectedItem}
        companyTreeType={CompanyItemType.Company}
      />
      <AddGroupModal
        visible={addGroupModalVisible}
        onCancel={handleAddGroupModalCancel}
        onAfterSave={handleAddGroupModalSave}
        companyId={companyTreeData.lastSelectedItem?.id || 0}
        lastSelectedItem={companyTreeData.lastSelectedItem}
      />
    </div>
  );
};

export default CompanyTree;
