.app-base-template-page {
  display: flex;
  flex-direction: column;
  background: var(--neutral-1);
  border-radius: 6px;
  height: 100%;

  .app-base-template {
    display: flex;
    height: 296px;
    align-items: flex-start;
    align-self: stretch;
    flex-shrink: 0;
    white-space: nowrap;
    gap: 16px;
    padding: 16px;

    position: relative;
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 16px;
      right: 16px;
      height: 1px;
      background-color: var(--neutral-3);
    }
  }

  .app-base-template-data {
    padding: 16px 16px 0 16px;
    width: auto;

    .tabs-section {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;

      .tab-button {
        display: flex;
        align-items: center;
        gap: 8px;

        &.ant-btn-primary {
          background-color: var(--main-1);
          border-color: var(--main-1);
          color: white;

          &:hover,
          &:focus {
            background-color: var(--main-1);
            border-color: var(--main-1);
          }
        }
      }
    }

    .table-scroll-container {
      height: calc(100vh - 500px) !important;
    }

    .ant-empty {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      min-height: 400px;

      .ant-empty-image {
        margin-bottom: 16px;
      }

      .ant-empty-description {
        color: rgba(0, 0, 0, 0.45);
      }
    }
  }
}
