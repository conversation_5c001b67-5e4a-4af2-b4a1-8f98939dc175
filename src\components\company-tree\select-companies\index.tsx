import React, { Key, useEffect, useState } from 'react';
import { Button, Dropdown, Input, message, Table } from 'antd';
import { useTranslation } from 'react-i18next';
import CommonModal from '../../shared/modal';
import FontIcon from '../../shared/icons/font-icon';
import PaginationComponent from '../../shared/pagination';
import { CompanyItemType, FilterKey } from '../../../constants/app.enums';
import { DEFAULT_PAGE_SIZE } from '../../../constants/device-list.constants';
import './index.scss';
import { CompanyItem } from '../../../models/company-tree.model';
import { CompanyTreeApi } from '../../../apis/company-tree.api';
import { ApiBaseModel, ApiError } from '../../../models/common.model';
import showModal from '../../shared/notification-modal';

interface SelectCompaniesModalProps {
  visible: boolean;
  companyTreeType: CompanyItemType;
  onCancel: () => void;
  onAfterSave: () => void;
  selectedCompany: CompanyItem | null;
}

const SelectCompaniesModal: React.FC<SelectCompaniesModalProps> = ({
  visible,
  companyTreeType,
  onCancel,
  onAfterSave,
  selectedCompany,
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<ApiBaseModel[]>([]);
  const [totalRecords, setTotalRecords] = useState<number>(0);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
  const [pageNumber, setPageNumber] = useState(1);
  const [filter, setFilter] = useState<FilterKey>(FilterKey.ALL);
  const [searchValue, setSearchValue] = useState<string>('');
  const [searchKey, setSearchKey] = useState<string>('');
  const [isSearchVisible, setIsSearchVisible] = useState(false);
  const [selectedDatas, setSelectedDatas] = useState<number[]>([]);

  const fetchData = async (parentId: number | null) => {
    try {
      setLoading(true);
      const { data } = await CompanyTreeApi.getSelectedCompanies({
        pageNumber: pageNumber,
        pageSize: pageSize,
        parentId: parentId ?? null,
        selectedFilterType: filter.toLowerCase(),
        searchString: searchKey,
        type: companyTreeType.toString(),
      });

      setData(data.source.data);
      setTotalRecords(data.source.totalRecords);
      if (visible && selectedDatas.length === 0) {
        setSelectedDatas(data.selectedIds);
      }
    } catch (error) {
      console.error('Error fetching companies:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (visible) {
      fetchData(selectedCompany?.id ?? null);
    }
  }, [visible, selectedCompany?.id, pageNumber, pageSize, filter, searchKey]);

  const handleModalCancel = () => {
    setFilter(FilterKey.ALL);
    setPageNumber(1);
    setSearchValue('');
    setSearchKey('');
    setSelectedDatas([]);
    onCancel();
  };

  function parseError(message: string) {
    const marker = 'ItemNames:';
    let errorText = message;
    let itemNames: string[] = [];

    if (message.includes(marker)) {
      errorText = message.substring(0, message.indexOf(marker)).trim();
      const itemsPart = message.substring(
        message.indexOf(marker) + marker.length,
      );
      itemNames = itemsPart
        .split(',')
        .map((s) => s.trim())
        .filter(Boolean);
    }

    return { errorText, itemNames };
  }

  const handleSave = async () => {
    try {
      setLoading(true);
      await CompanyTreeApi.updateCompanySelection(selectedCompany?.id ?? null, {
        selectedIds: selectedDatas,
      });
      message.success(t('companyManagement.selectCompanySuccess'));

      onAfterSave();
      handleClearSearch();
    } catch (error: unknown) {
      const apiError = error as ApiError;
      const { errorText, itemNames } = parseError(
        apiError?.response?.data?.message || '',
      );
      showModal(
        'warning',
        t('notification.warning'),
        <span
          dangerouslySetInnerHTML={{
            __html: t('companyTree.unableToRemoveCompanies', {
              companies: `<b>${itemNames.join(', ')}</b>`,
            }),
          }}
        />,
        {
          top: '20%',
        },
      );
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    if (!isSearchVisible) {
      setIsSearchVisible(true);
      return;
    }
    if (selectedCompany?.id) {
      setPageNumber(1);
      setSearchKey(searchValue);
      setIsSearchVisible(false);
    }
  };

  const handleClearSearch = () => {
    setSearchValue('');
    setSearchKey('');
    setSelectedDatas([]);
    setFilter(FilterKey.ALL);
    setPageNumber(1);
  };

  const updatedRowSelection = {
    ...selectedCompany,
    selectedRowKeys: selectedDatas,
    preserveSelectedRowKeys: true,
    onChange: (selectedRowKeys: Key[]) => {
      setSelectedDatas(selectedRowKeys as number[]);
    },
  };

  const tableColumns = [
    {
      title: (
        <span className="body-2-bold">
          {t('companyManagement.companyName')}
        </span>
      ),
      dataIndex: 'name',
      key: 'name',
    },
  ];

  const title = selectedCompany
    ? t('companyManagement.selectSubsidiaries')
    : t('companyManagement.selectCompanies');

  return (
    <CommonModal
      visible={visible}
      onCancel={handleModalCancel}
      onSave={handleSave}
      title={`${title}`}
      width={582}
    >
      <div className="selected-devices-section">
        <div className="selected-devices-header">
          <span className="section-title">
            {t('common.itemSelected', {
              item: selectedDatas.length,
            })}
          </span>
          <div className="section-actions">
            <div className="search-wrapper">
              <Button
                type="text"
                onClick={handleSearch}
                size="small"
                icon={<FontIcon size={16} className="icon-search main-color" />}
              >
                {t('common.search')}
              </Button>
              {isSearchVisible && (
                <div className="search-input">
                  <Input
                    autoFocus
                    placeholder={t('common.searchByName')}
                    value={searchValue}
                    onChange={(e) => setSearchValue(e.target.value)}
                    onPressEnter={() => {
                      if (selectedCompany?.id) {
                        setPageNumber(1);
                        setSearchKey(searchValue);
                        setIsSearchVisible(false);
                      }
                    }}
                    onBlur={() => setIsSearchVisible(false)}
                    style={{ width: 200, height: '36px' }}
                  />
                </div>
              )}
            </div>
            <Button
              type="text"
              icon={<FontIcon size={16} className="icon-close main-color" />}
              size="small"
              onClick={handleClearSearch}
            >
              {t('common.clearAll')}
            </Button>
            <Dropdown
              menu={{
                items: [
                  { key: FilterKey.ALL, label: t('common.all') },
                  { key: FilterKey.SELECTED, label: t('common.selected') },
                  {
                    key: FilterKey.NOT_SELECTED,
                    label: t('common.notSelected'),
                  },
                ],
                onClick: ({ key }) => {
                  setPageNumber(1);
                  setFilter(key as FilterKey);
                },
              }}
              trigger={['click']}
              placement="bottomRight"
            >
              <Button
                type="text"
                icon={<FontIcon size={16} className="icon-filter main-color" />}
                size="small"
              >
                Filter
              </Button>
            </Dropdown>
          </div>
        </div>
        <div className="selected-devices-table">
          <Table
            rowSelection={{
              type: 'checkbox',
              ...updatedRowSelection,
            }}
            className="device-assigned"
            columns={tableColumns}
            dataSource={data}
            rowKey="id"
            loading={loading}
            pagination={false}
            scroll={{ y: 230 }}
          />
          <PaginationComponent
            totalRecords={totalRecords}
            pageSize={pageSize}
            setPageSize={setPageSize}
            pageNumber={pageNumber}
            setPageNumber={setPageNumber}
          />
        </div>
      </div>
    </CommonModal>
  );
};

export default SelectCompaniesModal;
