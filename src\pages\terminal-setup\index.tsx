import { useEffect, useRef, useState, useCallback } from 'react';
import { Card, Button, Space, message } from 'antd';
import { useLocation, useParams } from 'react-router-dom';
import { SaveOutlined, UndoOutlined } from '@ant-design/icons';
import { useBreadcrumb } from '../../hooks/useBreadcrumb';
import { ROUTE_PATHS } from '../../constants/router.constants';
import { TERMINAL_SETUP_CONFIG } from '../../constants/terminal-setup.constants';
import { TerminalSetupApi } from '../../apis/terminal-setup.api';
import TerminalSetupGUI from '../../components/terminal-setup-gui';
import showNotification from '../../components/shared/notification';
import { useTranslation } from 'react-i18next';
import './index.scss';
import { AppBaseTemplateApi } from '../../apis/app-base-template.api';

const TerminalSetupPage = (): JSX.Element => {
  const { updateBreadcrumb, navigateToPreviousBreadcrumb, breadcrumbs } =
    useBreadcrumb();
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const location = useLocation();
  const { terminalMasterId } = useParams<{ terminalMasterId: string }>();
  const { t } = useTranslation();

  const deviceInfo = location.state?.deviceInfo;
  const [loading, setLoading] = useState(false);

  /**
   * IMPORTANT: This message API implementation is different from other components in the codebase.
   * We use explicit message keys and non-blocking calls to prevent interference with postMessage events.
   *
   * 1. We use specific keys ('saveMessage', 'discardMessage') for each message
   * 2. We don't use 'await' with messageApi.open() to avoid blocking the event loop
   * 3. We explicitly destroy messages when receiving responses from the iframe
   *
   * This approach ensures that the message API doesn't interfere with the postMessage
   * communication between our React app and the MVC application in the iframe.
   */
  const [messageApi, contextHolder] = message.useMessage();

  useEffect(() => {
    updateBreadcrumb(
      {
        title: t('breadcrumbs.terminalSetup'),
        path: `${ROUTE_PATHS.TerminalSetup}/${terminalMasterId}`,
        state: { deviceInfo },
      },
      false,
    );
  }, [terminalMasterId, t, deviceInfo, updateBreadcrumb]);

  // Memoized handler function that always has access to the latest breadcrumb state
  const memoizedHandleActionSuccess = useCallback(
    (
      eventType: string,
      messageKey: string,
      notificationType: 'success' | 'info',
      notificationMessage: string,
    ) => {
      setLoading(false);

      messageApi.destroy(messageKey);

      if (notificationType !== 'success') {
        showNotification(notificationType, notificationMessage);
      }

      TerminalSetupApi.iframeSignout(iframeRef);

      navigateToPreviousBreadcrumb();
    },
    [
      breadcrumbs,
      setLoading,
      messageApi,
      TerminalSetupApi.iframeSignout,
      iframeRef,
      navigateToPreviousBreadcrumb,
    ],
  );

  // Add event listener for messages from the MVC application
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      // In development, accept messages from any origin for testing
      // In production, we should check the origin for security
      if (
        import.meta.env.MODE === 'production' &&
        event.origin !== TERMINAL_SETUP_CONFIG.POST_MESSAGE.ORIGIN &&
        event.origin !== window.location.origin
      ) {
        return;
      }

      // Handle different event types
      const eventType =
        typeof event.data === 'object' && event.data !== null
          ? event.data.type
          : event.data;

      if (!eventType) return;

      // Handle save success event
      if (eventType === TERMINAL_SETUP_CONFIG.POST_MESSAGE.SAVE_SUCCESS_EVENT) {
        memoizedHandleActionSuccess(
          'Save success',
          'saveMessage',
          'success',
          'Terminal setup saved successfully',
        );
      }
      // Handle discard success event
      else if (
        eventType === TERMINAL_SETUP_CONFIG.POST_MESSAGE.DISCARD_SUCCESS_EVENT
      ) {
        memoizedHandleActionSuccess(
          'Discard success',
          'discardMessage',
          'info',
          'Changes discarded',
        );
      }
    };

    window.addEventListener('message', handleMessage);

    // Clean up
    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [memoizedHandleActionSuccess]);

  // Common handler for save and discard actions
  const handleAction = async (
    actionType: 'save' | 'discard',
    messageKey: string,
    loadingMessage: string,
    errorMessage: string,
    sendAction: (window?: Window) => boolean | undefined,
  ) => {
    // Show loading spinner on button
    setLoading(true);

    // Show message notification
    messageApi.open({
      key: messageKey,
      type: 'loading',
      content: loadingMessage,
      duration: 0,
    });

    // Get the iframe content window
    const iframeWindow = iframeRef.current?.contentWindow;
    if (!iframeWindow) {
      setLoading(false); // Hide spinner if there's an error
      messageApi.destroy(messageKey); // Destroy the message
      showNotification('error', 'Cannot access iframe content');
      return;
    }

    // Send action via postMessage
    const result = sendAction(iframeWindow);

    // If sending the message failed, hide the spinner and message
    if (!result) {
      setLoading(false);
      messageApi.destroy(messageKey);
      showNotification('error', errorMessage);
    }

    if (messageKey === 'saveMessage') {
      try {
        const validateResponse = await AppBaseTemplateApi.validateTemplate(
          Number(deviceInfo.newTemplateId),
        );
        if (!validateResponse.data.isValidTemplate) {
          showNotification(
            'warning',
            t('appBaseTemplate.setup.saveIncompleteWarning'),
          );
        }
      } catch (error) {
        console.error('Error validating template:', error);
      }
    }
  };

  // Handle save button click
  const handleSave = () => {
    handleAction(
      'save',
      'saveMessage',
      'Saving changes...',
      'Failed to send save request',
      TerminalSetupApi.sendSaveAction,
    );
  };

  // Handle discard button click
  const handleDiscard = () => {
    handleAction(
      'discard',
      'discardMessage',
      'Discarding changes...',
      'Failed to send discard request',
      TerminalSetupApi.sendDiscardAction,
    );
  };

  return (
    <div className="terminal-setup-page">
      {contextHolder}
      <div className="terminal-setup-container">
        <Card className="terminal-setup-card">
          <div className="terminal-setup-actions">
            <Space>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                onClick={handleSave}
                loading={loading}
              >
                Save
              </Button>
              <Button
                danger
                icon={<UndoOutlined />}
                onClick={handleDiscard}
                loading={loading}
              >
                Discard
              </Button>
            </Space>
          </div>
          <div className="terminal-setup-content">
            <TerminalSetupGUI
              terminalMasterId={
                terminalMasterId
                  ? Number(terminalMasterId)
                  : (deviceInfo?.id ?? 0)
              }
              iframeRef={iframeRef}
            />
          </div>
        </Card>
      </div>
    </div>
  );
};

export default TerminalSetupPage;
