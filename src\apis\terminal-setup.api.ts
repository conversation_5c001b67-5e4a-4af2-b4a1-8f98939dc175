import { TERMINAL_SETUP_CONFIG } from '../constants/terminal-setup.constants';
import LocalUtils from '../utils/local.utils';

const baseUrl = import.meta.env.VITE_MMS_WEB_URL;

// Extract domain from URL
const extractDomain = (url: string): string => {
  try {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname;

    // For localhost, return as is
    if (hostname === 'localhost') {
      return hostname;
    }

    // For production domains, extract the parent domain
    // e.g., from mms-mvc.projectjumpstart.site, get .projectjumpstart.site
    const parts = hostname.split('.');
    if (parts.length >= 2) {
      // Return with leading dot for parent domain to work across subdomains
      return '.' + parts.slice(-2).join('.');
    }

    return hostname;
  } catch (error) {
    console.error('[TerminalSetupAPI] Error extracting domain from URL', {
      url,
      error,
    });
    return 'localhost';
  }
};

// IMPORTANT: I have been here 20 hours. If you want to change this.
// Please make sure you have a deep knowledge of CORS, <PERSON><PERSON>, Samesite
// As if not, don't touch it.
// Author: Nam Mai
// Get cookie settings based on environment and domain
// IMPORTANT: These settings are critical for cross-domain cookies to work correctly
// For localhost in development: SameSite=Lax, secure=false
// For production with parent domain (.domain.com): SameSite=Lax, secure=true
const getCookieSettings = (domain: string) => {
  const isProduction = import.meta.env.MODE === 'production';
  const isLocalhost = domain === 'localhost';

  // For production, we need secure=true
  // For localhost in development, we can use secure=false
  const requiresSecure = isProduction || !isLocalhost;

  // Since we're using parent domain (.domain.com) in production,
  // we can use SameSite=Lax which is more secure than None
  return {
    secure: requiresSecure,
    sameSite: 'lax' as const,
  };
};

const setAuthCookie = () => {
  const { NAME, VALUE, PATH } = TERMINAL_SETUP_CONFIG.AUTH_COOKIE;
  const domain = extractDomain(baseUrl);
  const cookieSettings = getCookieSettings(domain);

  LocalUtils.setCookie(NAME, VALUE, {
    domain,
    maxAge: 30 * 60, // 30 minutes in seconds
    path: PATH,
    ...cookieSettings,
  });
};

const clearAuthCookie = () => {
  const { NAME, PATH } = TERMINAL_SETUP_CONFIG.AUTH_COOKIE;

  const domain = extractDomain(baseUrl);
  const cookieSettings = getCookieSettings(domain);

  LocalUtils.removeCookie(NAME, {
    domain,
    path: PATH,
    maxAge: 0,
    ...cookieSettings,
  });
};

const iframeSignout = (iframeRef?: React.RefObject<HTMLIFrameElement>) => {
  // First clear the auth cookie
  clearAuthCookie();

  // Then redirect the iframe to the LogOff endpoint if available
  if (iframeRef?.current) {
    iframeRef.current.src = `${baseUrl}/Account/LogOff`;
  }
};

const getTerminalSetupUrl = (terminalMasterId?: number): string => {
  const params = new URLSearchParams();

  params.append('type', TERMINAL_SETUP_CONFIG.QUERY_PARAMS.type.toString());
  params.append('isSimpleUI', TERMINAL_SETUP_CONFIG.QUERY_PARAMS.isSimpleUI);

  if (terminalMasterId) {
    params.append('id', terminalMasterId.toString());
  }

  return `${baseUrl}/TerminalSetupGUI/Index?${params.toString()}`;
};

const sendPostMessage = (action: string, data?: any, targetWindow?: Window) => {
  if (!targetWindow) {
    return false;
  }

  const message = {
    type: action,
    data,
    source: 'mms-fe',
  };

  try {
    const targetOrigin =
      import.meta.env.MODE === 'production'
        ? TERMINAL_SETUP_CONFIG.POST_MESSAGE.ORIGIN
        : '*';

    targetWindow.postMessage(message, targetOrigin);
    return true;
  } catch (error) {
    console.error('[TerminalSetupAPI] Failed to send postMessage', error);
    return false;
  }
};

const sendSaveAction = (targetWindow?: Window) => {
  return sendPostMessage(
    TERMINAL_SETUP_CONFIG.POST_MESSAGE.SAVE_ACTION,
    { timestamp: new Date().toISOString() },
    targetWindow,
  );
};

const sendDiscardAction = (targetWindow?: Window) => {
  return sendPostMessage(
    TERMINAL_SETUP_CONFIG.POST_MESSAGE.DISCARD_ACTION,
    { timestamp: new Date().toISOString() },
    targetWindow,
  );
};

export const TerminalSetupApi = {
  getTerminalSetupUrl,
  sendPostMessage,
  sendSaveAction,
  sendDiscardAction,
  setAuthCookie,
  clearAuthCookie,
  iframeSignout,
};
