import { Button, Divider, Form, Input, message } from 'antd';
import './index.scss';
import { useTranslation } from 'react-i18next';
import TerminalSetupGUI from '../../components/terminal-setup-gui';
import { useCallback, useEffect, useRef, useState } from 'react';
import { TerminalSetupApi } from '../../apis/terminal-setup.api';
import showNotification from '../../components/shared/notification';
import { TERMINAL_SETUP_CONFIG } from '../../constants/terminal-setup.constants';
import FontIcon from '../../components/shared/icons/font-icon';
import { ROUTE_PATHS } from '../../constants/router.constants';
import { useLocation, useParams } from 'react-router-dom';
import { useBreadcrumb } from '../../hooks/useBreadcrumb';
import { AppBaseTemplateApi } from '../../apis/app-base-template.api';
import CopyTemplateModal from '../copy-template-modal';

const AppBaseTemplateSetupPage = (): JSX.Element => {
  const { updateBreadcrumb, navigateToPreviousBreadcrumb, breadcrumbs } =
    useBreadcrumb();
  const { terminalTemplateId } = useParams();
  const { t } = useTranslation();
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [loading, setLoading] = useState(false);
  const [messageApi, contextHolder] = message.useMessage();
  const location = useLocation();
  const {
    companyName,
    templateName: initialTemplateName,
    templateId,
    isNewTemplate,
    companyId,
  } = location.state;
  const [templateName, setTemplateName] = useState(initialTemplateName);
  const [form] = Form.useForm();
  const [shouldHandleEvents, setShouldHandleEvents] = useState(true);

  useEffect(() => {
    updateBreadcrumb(
      {
        title: t('breadcrumbs.appBaseTemplateSetup', {
          templateName,
        }),
        path: `${ROUTE_PATHS.AppBaseTemplateSetup}/${terminalTemplateId}`,
        state: {
          companyName,
          templateName,
          templateId,
          isNewTemplate,
        },
      },
      false,
    );
  }, [terminalTemplateId, t, updateBreadcrumb]);

  const memoizedHandleActionSuccess = useCallback(
    (
      _: string,
      messageKey: string,
      notificationType: 'success' | 'info',
      notificationMessage: string,
    ) => {
      setLoading(false);
      messageApi.destroy(messageKey);

      if (shouldHandleEvents) {
        showNotification(notificationType, notificationMessage);
        TerminalSetupApi.iframeSignout(iframeRef);
        navigateToPreviousBreadcrumb();
      }
    },
    [
      breadcrumbs,
      setLoading,
      messageApi,
      TerminalSetupApi.iframeSignout,
      iframeRef,
      navigateToPreviousBreadcrumb,
      shouldHandleEvents,
    ],
  );

  const sendAction = (
    _: 'save' | 'discard',
    messageKey: string,
    loadingMessage: string,
    errorMessage: string,
    sendActionFn: (window?: Window) => boolean | undefined,
  ) => {
    setLoading(true);

    messageApi.open({
      key: messageKey,
      type: 'loading',
      content: loadingMessage,
      duration: 0,
    });

    const iframeWindow = iframeRef.current?.contentWindow;
    if (!iframeWindow) {
      setLoading(false);
      messageApi.destroy(messageKey);
      showNotification('error', 'Cannot access iframe content');
      return;
    }

    const result = sendActionFn(iframeWindow);

    if (!result) {
      setLoading(false);
      messageApi.destroy(messageKey);
      showNotification('error', errorMessage);
    }
  };

  const handleAction = (
    actionType: 'save' | 'discard',
    messageKey: string,
    loadingMessage: string,
    errorMessage: string,
    sendActionFn: (window?: Window) => boolean | undefined,
  ) => {
    setShouldHandleEvents(true);
    sendAction(
      actionType,
      messageKey,
      loadingMessage,
      errorMessage,
      sendActionFn,
    );
  };

  const handleSave = async () => {
    try {
      // Validate form
      await form.validateFields();

      if (templateName !== location.state.templateName) {
        await AppBaseTemplateApi.updateBaseTemplateName(
          Number(templateId),
          templateName,
          isNewTemplate,
        );
      }

      handleAction(
        'save',
        'saveMessage',
        t('appBaseTemplate.setup.savingChanges'),
        t('appBaseTemplate.setup.sendFailed'),
        TerminalSetupApi.sendSaveAction,
      );
    } catch (error: any) {
      if (error.errorFields) {
        return;
      }
      showNotification('error', t('appBaseTemplate.setup.saveFailed'));
      console.error('Error saving changes:', error);
    }
  };

  const handleCopySuccess = () => {
    setShouldHandleEvents(false);
    sendAction(
      'discard',
      'discardMessage',
      '',
      '',
      TerminalSetupApi.sendDiscardAction,
    );
  };

  const handleFirstDiscard = useCallback(() => {
    setShouldHandleEvents(false);
  }, []);

  const handleDiscard = () => {
    handleAction(
      'discard',
      'discardMessage',
      t('appBaseTemplate.setup.discardingChanges'),
      t('appBaseTemplate.setup.sendFailed'),
      TerminalSetupApi.sendDiscardAction,
    );
  };

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (
        import.meta.env.MODE === 'production' &&
        event.origin !== TERMINAL_SETUP_CONFIG.POST_MESSAGE.ORIGIN &&
        event.origin !== window.location.origin
      ) {
        return;
      }

      const eventType =
        typeof event.data === 'object' && event.data !== null
          ? event.data.type
          : event.data;

      if (!eventType) return;

      if (eventType === TERMINAL_SETUP_CONFIG.POST_MESSAGE.SAVE_SUCCESS_EVENT) {
        memoizedHandleActionSuccess(
          'Save success',
          'saveMessage',
          'success',
          t('appBaseTemplate.setup.success'),
        );
      } else if (
        eventType === TERMINAL_SETUP_CONFIG.POST_MESSAGE.DISCARD_SUCCESS_EVENT
      ) {
        memoizedHandleActionSuccess(
          'Discard success',
          'discardMessage',
          'info',
          t('appBaseTemplate.setup.discardSuccess'),
        );
      }
    };

    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [memoizedHandleActionSuccess]);

  return (
    <div className="app-template-setup">
      {contextHolder}
      <div className="header-app-base-template-setup">
        <h1>{t('appBaseTemplate.setup.appBaseTemplate')}</h1>

        <div className="button-group">
          <CopyTemplateModal
            buttonText={t('appBaseTemplate.setup.copyTemplate')}
            companyId={Number(companyId)}
            templateId={Number(templateId)}
            isNewTemplate={isNewTemplate}
            templateName={templateName}
            onCopySuccess={handleCopySuccess}
          />

          <Button
            className="discard-button"
            icon={<FontIcon size={16} className="icon-delete" />}
            onClick={handleDiscard}
          >
            {t('appBaseTemplate.setup.discardChanges')}
          </Button>

          <Button
            type="primary"
            icon={<FontIcon size={16} className="icon-save" />}
            className="save-button"
            loading={loading}
            onClick={handleSave}
          >
            {t('appBaseTemplate.setup.save')}
          </Button>
        </div>
      </div>

      <Divider />

      <div className="app-info-section">
        <Form form={form} layout="vertical" initialValues={{ templateName }}>
          <div className="form-row">
            <Form.Item
              label={t('deviceSchedule.templateName')}
              className="form-item-flex"
              name="templateName"
              rules={[
                {
                  required: true,
                  message: t('common.requireValidation', {
                    field: t('deviceSchedule.templateName'),
                  }),
                },
                {
                  whitespace: true,
                  message: t('common.requireValidation', {
                    field: t('deviceSchedule.templateName'),
                  }),
                },
              ]}
            >
              <Input
                className="template-name"
                onChange={(e) => setTemplateName(e.target.value)}
              />
            </Form.Item>
            <Form.Item
              label={t('deviceSchedule.companyName')}
              className="form-item-flex"
            >
              <Input value={companyName} disabled />
            </Form.Item>
          </div>
        </Form>
      </div>

      <div className="terminal-setup-content">
        <TerminalSetupGUI
          terminalMasterId={Number(terminalTemplateId)}
          iframeRef={iframeRef}
          onFirstDiscard={handleFirstDiscard}
          isAutoDiscard={true}
        />
      </div>
    </div>
  );
};

export default AppBaseTemplateSetupPage;
 