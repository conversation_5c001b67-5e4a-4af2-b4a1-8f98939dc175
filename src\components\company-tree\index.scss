@use '../../scss/helpers/variables' as *;

.company-tree {
  width: 240px;
  background: var(--neutral-1);
  padding: 4px 16px 16px 16px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-height: calc(100vh - 32px);
  border-top: 1px solid var(--neutral-neutrals-3-day, #e6e6e6);
  border-right: 1px solid var(--neutral-neutrals-3-day, #e6e6e6);
  border-bottom: 1px solid var(--neutral-neutrals-3-day, #e6e6e6);
  transition:
    width 0.45s ease,
    opacity 0.45s ease,
    visibility 0.45s ease,
    padding 0.45s ease,
    transform 0.45s ease;
  transition: none !important;
  opacity: 1;
  visibility: visible;
  position: relative;

  .tree-content {
    height: calc(100vh - 100px);
    overflow: auto;
  }

  // &:not(.collapsed) {
  //   .company-tree-header {
  //     transition-delay: 0s;
  //   }

  //   .company-tree-title {
  //     transition-delay: 0.2s;
  //   }

  //   .company-tree-actions {
  //     transition-delay: 0.3s;
  //   }
  //   .tree-content {
  //     transition-delay: 0.4s;
  //   }
  // }

  &.collapsed > * {
    opacity: 0;
  }

  &.collapsed {
    width: 0;
    padding: 0;
    margin-inline: 0;
    opacity: 0;
    visibility: hidden;
    overflow: hidden;
    pointer-events: none;
    position: absolute;
  }

  .company-tree-header {
    display: flex;
    height: 44px;
    padding: 12px 0px;
    justify-content: flex-end;
    align-items: center;
    gap: 12px;
    flex-shrink: 0;
    align-self: stretch;

    .menu-icon {
      font-size: 18px;
      cursor: pointer;
    }

    .company-tree-title {
      font-weight: bold;
    }

    .company-tree-actions {
      margin-left: auto;
      cursor: pointer;
      .company-tree-icon {
        font-size: 18px;
      }
    }
  }

  .search-tree {
    border-radius: 6px;
    height: 35px;
    font-size: 12px;
    transition: all 0.3s ease-in-out;
    border: none;

    input::placeholder {
      font-size: 12px;
    }

    input {
      border: none;
      box-shadow: none;
    }
  }

  .ant-tree {
    background: transparent;

    .ant-tree-node-content-wrapper {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      padding: 8px 8px;
      font-size: 12px;
      color: #000;
      width: 100%;

      .ant-tree-iconEle {
        margin-right: 5px;
        height: 40px;
      }
    }
  }

  .ant-tree
    .ant-tree-treenode:not(.ant-tree-treenode-disabled)
    .ant-tree-node-content-wrapper.ant-tree-node-selected:hover {
    background-color: var(--main-1);
    color: #f1efef;
  }

  .ant-tree .ant-tree-switcher:before {
    transition: none;
  }

  .ant-tree .ant-tree-switcher {
    transition: none;
  }

  .ant-tree .ant-tree-switcher:not(.ant-tree-switcher-noop):hover:before {
    background-color: transparent;
  }

  .ant-tree .ant-tree-switcher-leaf-line:before {
    border-inline-end: 1.5px solid #606060;
  }

  .ant-tree .ant-tree-switcher-leaf-line:after {
    border-bottom: 1.5px solid #606060;
  }

  .ant-tree-show-line .ant-tree-indent-unit:before {
    border-inline-end: 1.5px solid #606060;
  }
}

.ant-tree-treenode {
  transition: none !important;
  width: 100%;
  line-height: 44px !important;
  margin: 0;
  padding: 0;

  .ant-tree-node-content-wrapper-open.ant-tree-node-selected {
    .ant-tree-title {
      color: var(--neutral-1) !important;
    }
    .ant-tree-icon__customize {
      color: var(--neutral-1);
    }
  }

  // &.ant-tree-treenode-switcher-open:not(.ant-tree-treenode-selected) {
  //   .ant-tree-node-content-wrapper {
  //     background-color: var(--main-3);
  //   }
  //   .ant-tree-switcher.ant-tree-switcher_open {
  //     background-color: var(--main-3);
  //   }
  //   .ant-tree-title {
  //     color: var(--main-color);
  //   }
  //   .ant-tree-icon__customize {
  //     color: var(--main-color);
  //   }
  // }

  &.ant-tree-treenode-switcher-open,
  &.ant-tree-treenode-switcher-close {
    align-self: stretch;

    .ant-tree-switcher_open,
    .ant-tree-switcher_close {
      display: none;
      height: 44px;
      width: 18px;
      background-color: #ffff;
      border-top-right-radius: 6px;
      border-bottom-right-radius: 6px;
    }
  }

  .ant-tree-node-content-wrapper-normal {
    white-space: nowrap;
    width: 50px;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  &.ant-tree-treenode-selected {
    border: none;

    .ant-tree-switcher_open,
    .ant-tree-switcher_close {
      border-top-right-radius: 6px;
      border-bottom-right-radius: 6px;
      background-color: var(--main-1);
      color: var(--neutral-1);
      transition: none;
    }

    .ant-tree-switcher_open:hover,
    .ant-tree-switcher_close:hover {
      background-color: var(--main-1) !important;
      transition: none;
    }
  }

  .ant-tree-node-content-wrapper:hover {
    color: #f1efef;
  }

  .ant-tree-node-content-wrapper {
    border-radius: 6px !important;
    height: 44px;
    width: 100%;
    background-color: #ffff;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    transition: none;
    order: 1;
    flex: 1;

    &.ant-tree-node-content-wrapper-normal {
      border-radius: 6px;
    }
    &.ant-tree-node-content-wrapper-close.ant-tree-node-selected,
    &.ant-tree-node-content-wrapper-open.ant-tree-node-selected {
      background-color: var(--main-1);
      border-top-left-radius: 6px;
      border-bottom-left-radius: 6px;
      color: var(--neutral-2);
      height: 44px;
    }
    &.ant-tree-node-content-wrapper-normal.ant-tree-node-selected {
      background-color: var(--main-1);
      border-radius: 6px;
      color: var(--neutral-2);
      height: 44px;
    }
  }
}

.ant-tree .ant-tree-treenode-leaf-last .ant-tree-switcher-leaf-line:before {
  height: 21px !important;
}

.ant-tree .ant-tree-switcher-leaf-line:after {
  width: 15px !important;
}

.ant-tree .ant-tree-switcher-leaf-line:after {
  height: 21px;
}

.ant-tree-switcher {
  width: 100%;
  transition: none;
  &.ant-tree-switcher_open,
  &.ant-tree-switcher_close {
    order: 2;
  }
}

.ant-tree .ant-tree-node-content-wrapper,
.ant-tree .ant-tree-checkbox + span,
.ant-tree .ant-tree-switcher {
  border-radius: 0;
}

.ant-tree-treenode-switcher-close:not(:has(.ant-tree-title .group)),
.ant-tree-treenode-switcher-open:not(:has(.ant-tree-title .group)) {
  .ant-tree-switcher-noop,
  .ant-tree-switcher-leaf-line {
    display: none !important;
  }
}

.parent-of-selected .ant-tree-node-content-wrapper,
.parent-of-selected .ant-tree-switcher {
  background: #f3faf8 !important;
  color: #00a26d !important;
}

.message-container {
  text-align: center;
  padding: 10px 0;

  .error-text {
    color: red;
  }
}
// .ant-tree-list-holder {
//   width: 400px;
// }
.ant-tree-title {
  width: 100%;
}

.tree-node-content {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 4px;

  .highlight:has(+ .icon-down) {
    min-width: 0;
    width: calc(100% - 20px);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .icon-down {
    transition: transform 0.5s;
    margin-left: auto;
    position: absolute;
    right: 8px;
    // float: right;
    // width: 20px;

    &.expanded {
      transform: rotate(180deg) translateX(-2.142857142857143px);
    }
  }
}

.parent-of-selected .ant-tree-node-content-wrapper .ant-tree-switcher-leaf-line,
.parent-of-selected .ant-tree-switcher .ant-tree-switcher-leaf-line {
  background: #ffff !important;
}
