using AutoMapper;
using Microsoft.EntityFrameworkCore;
using MMS.Core.CoreUTI;
using MMS.Core.dbContext;
using MMS.Core.Entities;
using MMS.Core.Entities.Merchants;
using MMS.Core.Entities.TerminalSetupGUI;
using MMS.Core.Repository;
using MMS.Core.Services.Impl.Base;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using MMS.Core.CoreUTI.Enum;
using MMS.Core.Attributes;
using MMS.Core.Repository.Impl;
using MMS.Core.Utils;
using Serilog;
using Hangfire.Logging;
using MMS.Model.ApiModelResponse;
using MMS.Model.ApiModelRequest;
using MMS.Infrastructure.Commons;

namespace MMS.Core.Services.Impl
{
    public partial class TerminalService : BaseService<TerminalMaster>, ITerminalService
    {
        private readonly string NotFoundTerminalMessage = "No terminal master found with ID = ";

        private readonly IMerchantTerminalRepository _merchantTerminalRepository;
        private readonly IBaseRepository<POSConnection> _repoPOSConnection;
        private readonly IBaseRepository<RS232Interface> _repoRS232Interface;
        private readonly IBaseRepository<IPInterface> _repoIPInterface;
        private readonly IBaseRepository<USBInterface> _repoUSBInterface;
        private readonly IBaseRepository<POSInterface> _repoPOSInterface;

        private readonly IBaseRepository<ICSSetupByTerminalType> _repoICSSetupByTerminalType;
        private readonly IBaseRepository<ICSSetupByCardDataInput> _repoICSSetupByCardDataInput;
        private readonly IBaseRepository<ICSSetupByCVMCapability> _repoICSSetupByCVMCapability;
        private readonly IBaseRepository<ICSSetupBySecurityCapability> _repoICSSetupBySecurityCapability;
        private readonly IBaseRepository<ICSSetupByTmDataInput> _repoICSSetupByTmDataInput;
        private readonly IBaseRepository<ICSSetupByTmDataOutput> _repoICSSetupByTmDataOutput;
        private readonly IBaseRepository<ICSSetupByCDAMode> _repoICSSetupByCDAMode;

        private readonly ITerminalMasterRepository _terminalMasterRepository;
        private readonly ITerminalChangeRepository _terminalChangeRepository;
        private readonly ILocationItemRepository _locationItemRepository;
        private readonly ILocationDeviceMappingRepository _locationDeviceMappingRepository;
        private readonly IBaseRepository<TerminalSetupTemplate> _terminalSetupTemplateRepository;
        private readonly IBaseRepository<PayAtTableTerminal> _payAtTableTerminalRepository;
        private readonly IBaseRepository<MerchantTerminalHistory> _merchantTerminalHistoryRepository;
        private readonly IBaseRepository<ApplicationVersion> _applicationVersionRepository;
        private readonly IBaseRepository<FirmwareVersion> _firmwareVersionRepository;
        private readonly IBaseRepository<MerchantSecurityLevel> _merchantSecurityLevelRepository;
        private readonly IBaseRepository<TerminalUpdateSchedule> _terminalUpdateScheduleRepository;
        private readonly IBaseRepository<PaymentModeOption> _repoPaymentModeOption;
        private readonly IBaseRepository<PaymentMode> _repoPaymentMode;
        private readonly IBaseRepository<MerchantTerminal> _repoMerchantTerminal;
        private readonly IBaseRepository<TerminalSetup> _repoTerminalSetup;
        private readonly IBaseRepository<SoundTerminal> _repoSoundTerminal;
        private readonly IBaseRepository<TerminalCloud> _repoTerminalCloud;
        private readonly IBaseRepository<CountryTerminal> _repoCountryTerminal;
        private readonly IBaseRepository<UserMaster> _repoUserMaster;
        private readonly IBaseRepository<FallBackTerminal> _repoFallBackTerminal;
        private readonly IBaseRepository<ExtraModudle> _repoExtraModudle;
        private readonly IBaseRepository<MerchantCardFallback> _repoMerchantCardFallback;
        private readonly IBaseRepository<TerminalWifi> _repoTerminalWifi;
        private readonly IBaseRepository<TerminalWAN> _repoTerminalWAN;
        private readonly IBaseRepository<PayplusProcessor> _repoPayplusProcessor;
        private readonly ILocationItemSelectRepository _locationItemSelectRepository;

        private readonly ICompanyDeviceSetupMappingRepository _companyDeviceSetupMappingRepository;
        private readonly ICompanyDeviceSetupMappingService _companyDeviceSetupMappingService;
        private readonly ICompanyGroupMappingService _companyGroupMappingService;
        private readonly IDeviceTemplatesService _deviceTemplatesService;
        private readonly ITerminalTypeMasterService _terminalTypeMasterService;
        private readonly ITerminalModelMasterService _terminalModelMasterService;
        private readonly ICompanyGroupMappingRepository _companyGroupMappingRepository;

        private readonly IMapper _mapper;

        public TerminalService( //NOSONAR
        IMerchantTerminalRepository merchantTerminalRepository,
        IBaseRepository<POSConnection> repoPOSConnection,
        IBaseRepository<RS232Interface> repoRS232Interface,
        IBaseRepository<IPInterface> repoIPInterface,
        IBaseRepository<USBInterface> repoUSBInterface,
        IBaseRepository<POSInterface> repoPOSInterface,
        IBaseRepository<ICSSetupByTerminalType> repoICSSetupByTerminalType,
        IBaseRepository<ICSSetupByCardDataInput> repoICSSetupByCardDataInput,
        IBaseRepository<ICSSetupByCVMCapability> repoICSSetupByCVMCapability,
        IBaseRepository<ICSSetupBySecurityCapability> repoICSSetupBySecurityCapability,
        IBaseRepository<ICSSetupByTmDataInput> repoICSSetupByTmDataInput,
        IBaseRepository<ICSSetupByTmDataOutput> repoICSSetupByTmDataOutput,
        IBaseRepository<ICSSetupByCDAMode> repoICSSetupByCDAMode,
        IBaseRepository<PayplusProcessor> repoPayplusProcessor,
        IBaseRepository<TerminalWifi> repoTerminalWifi,
        IBaseRepository<TerminalWAN> repoTerminalWAN,
        IBaseRepository<MerchantCardFallback> repoMerchantCardFallback,
        IBaseRepository<ExtraModudle> repoExtraModudle,
        IBaseRepository<FallBackTerminal> repoFallBackTerminal,
        IBaseRepository<UserMaster> repoUserMaster,
        IBaseRepository<CountryTerminal> repoCountryTerminal,
        IBaseRepository<TerminalCloud> repoTerminalCloud,
        IBaseRepository<SoundTerminal> repoSoundTerminal,
        IBaseRepository<TerminalSetup> repoTerminalSetup,
        IBaseRepository<MerchantTerminal> repoMerchantTerminal,
        IBaseRepository<PaymentMode> repoPaymentMode,
        IBaseRepository<PaymentModeOption> repoPaymentModeOption,
        ILanguageExecuteService languageExecuteService,
        ITerminalMasterRepository terminalMasterRepository,
        ITerminalChangeRepository terminalChangeRepository,
        IBaseRepository<TerminalSetupTemplate> terminalSetupTemplateRepository,
        IBaseRepository<PayAtTableTerminal> payAtTableTerminalRepository,
        IBaseRepository<MerchantTerminalHistory> merchantTerminalHistoryRepository,
        IBaseRepository<ApplicationVersion> applicationVersionRepository,
        IBaseRepository<FirmwareVersion> firmwareVersionRepository,
        IBaseRepository<MerchantSecurityLevel> merchantSecurityLevelRepository,
        IBaseRepository<TerminalUpdateSchedule> terminalUpdateScheduleRepository,
        ILocationItemRepository locationItemRepository,
        ILocationDeviceMappingRepository locationDeviceMappingRepository,
        ICompanyDeviceSetupMappingRepository companyDeviceSetupMappingRepository,
        ICompanyDeviceSetupMappingService companyDeviceSetupMappingService,
        ILocationItemSelectRepository locationItemSelectRepository,
        ICompanyGroupMappingService companyGroupMappingService,
        IDeviceTemplatesService deviceTemplatesService,
        ITerminalTypeMasterService terminalTypeMasterService,
        ITerminalModelMasterService terminalModelMasterService,
        ICompanyGroupMappingRepository companyGroupMappingRepository,
        IMapper mapper) : base(terminalMasterRepository, languageExecuteService)
        {
            _merchantTerminalRepository = merchantTerminalRepository;
            _companyGroupMappingRepository = companyGroupMappingRepository;

            _repoPOSConnection = repoPOSConnection;
            _repoUSBInterface = repoUSBInterface;
            _repoIPInterface = repoIPInterface;
            _repoRS232Interface = repoRS232Interface;
            _repoPOSInterface = repoPOSInterface;

            _repoICSSetupByCDAMode = repoICSSetupByCDAMode;
            _repoICSSetupByTmDataOutput = repoICSSetupByTmDataOutput;
            _repoICSSetupByTmDataInput = repoICSSetupByTmDataInput;
            _repoICSSetupBySecurityCapability = repoICSSetupBySecurityCapability;
            _repoICSSetupByCVMCapability = repoICSSetupByCVMCapability;
            _repoICSSetupByCardDataInput = repoICSSetupByCardDataInput;
            _repoICSSetupByTerminalType = repoICSSetupByTerminalType;

            _repoPayplusProcessor = repoPayplusProcessor;

            _repoTerminalWAN = repoTerminalWAN;
            _repoTerminalWifi = repoTerminalWifi;

            _repoMerchantCardFallback = repoMerchantCardFallback;
            _repoExtraModudle = repoExtraModudle;
            _repoFallBackTerminal = repoFallBackTerminal;
            _repoUserMaster = repoUserMaster;
            _repoCountryTerminal = repoCountryTerminal;
            _repoTerminalCloud = repoTerminalCloud;
            _repoSoundTerminal = repoSoundTerminal;
            _repoTerminalSetup = repoTerminalSetup;

            _repoMerchantTerminal = repoMerchantTerminal;
            _repoPaymentModeOption = repoPaymentModeOption;
            _repoPaymentMode = repoPaymentMode;

            _terminalMasterRepository = terminalMasterRepository;
            _terminalChangeRepository = terminalChangeRepository;
            _terminalSetupTemplateRepository = terminalSetupTemplateRepository;
            _payAtTableTerminalRepository = payAtTableTerminalRepository;
            _merchantTerminalHistoryRepository = merchantTerminalHistoryRepository;
            _applicationVersionRepository = applicationVersionRepository;
            _firmwareVersionRepository = firmwareVersionRepository;
            _merchantSecurityLevelRepository = merchantSecurityLevelRepository;
            _terminalUpdateScheduleRepository = terminalUpdateScheduleRepository;
            _locationItemRepository = locationItemRepository;
            _locationDeviceMappingRepository = locationDeviceMappingRepository;
            _companyDeviceSetupMappingRepository = companyDeviceSetupMappingRepository;
            _companyDeviceSetupMappingService = companyDeviceSetupMappingService;
            _locationItemSelectRepository = locationItemSelectRepository;
            _companyGroupMappingService = companyGroupMappingService;
            _deviceTemplatesService = deviceTemplatesService;
            _terminalTypeMasterService = terminalTypeMasterService;
            _terminalModelMasterService = terminalModelMasterService;
            _mapper = mapper;
        }

        public new ITerminalService UsingSessionContext()
        {
            base.UsingSessionContext();
            _terminalMasterRepository.UsingSessionContext();
            _terminalChangeRepository.UsingSessionContext();
            _terminalSetupTemplateRepository.UsingSessionContext();
            _payAtTableTerminalRepository.UsingSessionContext();
            _merchantTerminalHistoryRepository.UsingSessionContext();
            _applicationVersionRepository.UsingSessionContext();
            _firmwareVersionRepository.UsingSessionContext();
            _merchantSecurityLevelRepository.UsingSessionContext();
            _terminalUpdateScheduleRepository.UsingSessionContext();
            return this;
        }

        #region Terminal Master Function

        public IList<TerminalMaster> GetAllTerminalMaster(Expression<Func<TerminalMaster, bool>> whereClauses = null, bool includeDeleted = false, bool includeDeactivated = true, Expression<Func<TerminalMaster, object>> orderBy = null, bool isDescendingOrder = false, params Expression<Func<TerminalMaster, object>>[] includes)
        {
            return base.GetAll(whereClauses, includeDeleted, includeDeactivated, orderBy, isDescendingOrder, includes);
        }

        public int LastMaster()
        {
            return _terminalMasterRepository.LastMaster();
        }

        public IList<TerminalMaster> GetAllTerminals()
        {
            return base.GetAll(whereClauses: p => p.DeviceSetupType != CoreUTI.Enum.DeviceSetupType.BaseTemplate, includeDeleted: false, orderBy: m => m.TerminalSerialNumber).ToList();
        }

        public IList<TerminalMaster> GetAllTerminalQueryable()
        {
            return base.GetAll(whereClauses: null,
                includeDeleted: false, true,
                orderBy: m => m.TerminalSerialNumber,
                isDescendingOrder: false,
                p => p.MerchantTerminals, p => p.GroupMaster, p => p.TerminalModelType, p => p.TerminalApplication, p => p.IoTDevice, p => p.IoTDeviceCompanyAssigneds, p => p.IoTDeviceAssociatedStatus, p => p.IoTDeviceAssociatedStatus);
        }


        public IList<TerminalMaster> GetAllTerminalsByApplicationTemplateId(int id)
        {
            return base.GetAll(whereClauses: m => m.ApplicationUpgradeTemplate.Id.Equals(id),
                includeDeleted: false, true,
                orderBy: m => m.TerminalSerialNumber,
                isDescendingOrder: false,
                p => p.ApplicationUpgradeTemplate);
        }

        public IList<TerminalMaster> GetAllTerminalsByApplicationTemplate(int id)
        {
            return _terminalMasterRepository.GetAllTerminalsByApplicationTemplate(id);
        }


        public IList<TerminalMaster> GetAllTerminalsByFirmwareTemplateId(int id)
        {
            return base.GetAll(whereClauses: m => m.FirmwareUpgradeTemplate.Id.Equals(id),
                includeDeleted: false, true,
                orderBy: m => m.TerminalSerialNumber,
                isDescendingOrder: false,
                p => p.FirmwareUpgradeTemplate);
        }

        public IList<TerminalMaster> GetAllTerminalsByFirmwareTemplate(int id)
        {
            var result = base.GetAll(whereClauses: m => m.FirmwareUpgradeTemplate.Id.Equals(id), includeDeleted: false, true,
                                    orderBy: m => m.TerminalSerialNumber,
                                    isDescendingOrder: false,
                                    p => p.FirmwareUpgradeTemplate,
                                    p => p.MerchantTerminals);
            foreach (var item in result)
            {
                item.MerchantTerminals = item.MerchantTerminals.AsQueryable().Include(p => p.MerchantMaster).ToList();
            }

            return result;
        }

        public IList<TerminalMaster> GetAllTerminalsByUpdateTemplateId(int id)
        {
            var result = base.GetAll(whereClauses: m => m.TerminalSetupTemplate != null && m.TerminalSetupTemplate.Id == id, includeDeleted: false, true,
                                    orderBy: m => m.TerminalSerialNumber,
                                    isDescendingOrder: false,
                                    p => p.TerminalSetupTemplate,
                                    p => p.MerchantTerminals);
            foreach (var item in result)
            {
                item.MerchantTerminals = item.MerchantTerminals.AsQueryable().Include(p => p.MerchantMaster).ToList();
            }

            return result;
        }



        public IList<TerminalMaster> GetTerminalsByApplicationId(int id)
        {
            return base.GetAll(whereClauses: x => x.ApplicationId == id, includeDeleted: false);
        }

        public IList<TerminalMaster> GetTerminalsByParameterLocationId(int id)

        {
            return base.GetAll(whereClauses: x => x.TerminalParameterLocationId == id && x.IsStatus != Constants.DELETE_RECORD);
        }



        public List<int> GetAllTerminalsIdByUpdateTemplateId(int id)
        {
            return base.Select(selectClause: m => m.Id, whereClauses: m => m.TerminalSetupTemplate != null && m.TerminalSetupTemplate.Id == id, false) as List<int>;
        }

        public TerminalMaster GetTerminalBySerialNumber(string serialNumber, string macAddress, int modelId)
        {
            return _terminalMasterRepository.GetTerminalBySerialNumber(serialNumber, macAddress, modelId);

        }

        public TerminalMaster GetTerminalBySerialNumber(string serialNumber)
        {
            return _terminalMasterRepository.GetTerminalBySerialNumber(serialNumber);
        }

        public bool SetIsUpdate(string table, List<int> ids, bool isUpdated)
        {
            return _terminalMasterRepository.SetIsUpdated(table, ids, isUpdated);
        }

        #endregion

        #region Support Functions

        //--------------------------------------------------
        // Support Terminal
        //--------------------------------------------------

        public SupportTerminal GetSupportByTerminalId(int id)
        {
            return _terminalMasterRepository.GetNaviProperty<SupportTerminal>(id, p => p.Support);
        }

        public List<int> GetAllSupportIdByTemplateId(int id)
        {
            return base.Select(m => m.Support.Id, m => m.TerminalSetupTemplate.Id.Equals(id), false, p => p.Support, p => p.TerminalSetupTemplate) as List<int>;
        }

        #endregion

        #region Datetime Zone Functions

        //--------------------------------------------------
        // DateTimeZone Terminal
        //--------------------------------------------------
        public DateTimeZoneTerminal GetDateTimeZoneByTerminalId(int id)
        {
            return _terminalMasterRepository.GetNaviProperty<DateTimeZoneTerminal>(id, p => p.Date);
        }

        public List<int> GetAllDateTimeZoneIdByTemplateId(int id)
        {
            return base.Select(m => m.Date.Id, m => m.TerminalSetupTemplate.Id.Equals(id), false, p => p.TerminalSetupTemplate, p => p.Date) as List<int>;
        }

        #endregion


        #region Pos Request Function

        //--------------------------------------------------
        // Pos Request Terminal
        //--------------------------------------------------

        public List<int> GetAllPosRequestIdByTemplateId(int id)
        {
            throw new NotImplementedException();
        }

        #endregion

        #region Pos Request Function

        //--------------------------------------------------
        // Pay At Table Terminal
        //--------------------------------------------------
        public PayAtTableTerminal GetPayAtTableByPosRequestId(int id)
        {
            return _payAtTableTerminalRepository.FirstOrDefault(x => x.PosRequestId == id);
        }

        #endregion

        #region System Sale Type

        //--------------------------------------------------
        // Sale Type
        //--------------------------------------------------


        #endregion

        #region Sound Function

        //--------------------------------------------------
        // Sound Terminal
        //--------------------------------------------------

        public SoundTerminal GetSoundByTerminalId(int id)
        {
            return _terminalMasterRepository.GetNaviProperty<SoundTerminal>(id, p => p.Sound);
        }

        public List<int> GetAllSoundTerminalIdByTemplateId(int id)
        {
            return (Select(m => m.Sound.Id, m => m.TerminalSetupTemplate.Id == id, false, p => p.TerminalSetupTemplate, p => p.Sound) as List<int>) ?? new List<int>();
        }



        #endregion

        #region FallBack Function

        //--------------------------------------------------
        // FallBack Terminal
        //--------------------------------------------------

        public FallBackTerminal GetFallBackByTerminalId(int id)
        {
            return _terminalMasterRepository.GetNaviProperty<FallBackTerminal>(id, p => p.FallBack);
        }

        public List<int> GetAllFallBackIdByTemplateId(int id)
        {
            return
                 base.Select(m => m.FallBack.Id, m => m.TerminalSetupTemplate.Id == id, false, p => p.TerminalSetupTemplate, p => p.FallBack) as List<int>;
        }



        #endregion

        #region PosRequest Function

        //--------------------------------------------------
        // PosRequest Terminal
        //--------------------------------------------------
        public PosRequestTerminal GetPosRequestByTerminalId(int id)
        {
            return _terminalMasterRepository.GetNaviProperty<PosRequestTerminal>(id, p => p.PosRequest, p => p.PosRequest.PushModeImageList);
        }

        #endregion

        #region Country Function

        //--------------------------------------------------
        // Country Terminal
        //--------------------------------------------------
        public CountryTerminal GetCountryByTerminalId(int id)
        {
            return _terminalMasterRepository.GetNaviProperty<CountryTerminal>(id, p => p.CountryTerminal);
        }


        #endregion

        #region Debit Cash Back

        //--------------------------------------------------
        // Debit CashBack Terminal
        //--------------------------------------------------

        public List<int> GetAllDeditCardBackIdByTemplateId(int id)
        {
            return
                 base.Select(m => m.CashBack.Id, m => m.TerminalSetupTemplate.Id.Equals(id), false, p => p.TerminalSetupTemplate, p => p.CashBack) as List<int>;
        }

        #endregion

        public IList<MerchantTerminalHistory> GetMerchantTerminalHistories(int terminalId)
        {
            return _merchantTerminalHistoryRepository.GetAll(false, true, x => x.TerminalID == terminalId).OrderBy(x => x.AssignedDate).ToList();
        }

        #region Processor Merchant Setup

        //--------------------------------------------------=
        // Merchant Message type Info
        //--------------------------------------------------=


        public void Insert(TerminalChange entity, int id, bool commit = true)
        {
            _terminalChangeRepository.Insert(entity, id, commit);
        }

        public void Update(TerminalChange entity)
        {
            _terminalChangeRepository.Update(entity);
        }

        public IList<TerminalChange> GetTerminalChanges(int terminalId, int status)
        {
            return
                _terminalChangeRepository.GetAll(false, true, x => x.TerminalId == terminalId && x.IsClientUpdated == status).ToList();
        }

        #endregion

        #region Terminal Backup

        //--------------------------------------------------
        // Terminal backUp
        //--------------------------------------------------

        public IList<TerminalUserSecurityAccess> GetTerminalSecurityAccessByTerminalId(int id)
        {
            return _terminalMasterRepository.GetNaviPropertyList<TerminalUserSecurityAccess>(id, p => p.TerminalSecurityAccesses);
        }

        public TerminalMaster GetTerminalBySerialNumberAndBrand(string serialNumber, int brandId)
        {
            return base.FirstOrDefault(m => m.TerminalModelType.TerminalBrandId.Equals(brandId) && m.TerminalSerialNumber.Equals(serialNumber), false, p => p.TerminalModelType);
        }

        public bool SetSetupTemplate(int templateId, List<int> terminalids)
        {
            return _terminalMasterRepository.SetTemplateForTerminal(templateId, terminalids, "TerminalSetupTemplateId");
        }

        //Application Upgrade Terminal
        public ApplicationVersion GetApplicationVersionByModelAndVersionNumber(int modelId, string versionNumber)
        {
            return
                _applicationVersionRepository.FirstOrDefault(m => m.TerminalModel.Id.Equals(modelId) && m.VersionNumber.Equals(versionNumber.Trim()));
        }


        //Firmware Upgrade Terminal

        public FirmwareVersion GetFirmwareVersionByModelAndVersionNumber(int modelId, string versionNumber)
        {
            return _firmwareVersionRepository.FirstOrDefault(m => m.TerminalModel.Id.Equals(modelId) && m.VersionNumber.Equals(versionNumber.Trim()), false, null, p => p.TerminalModel);
        }

        //Setup Update for terminal

        public TerminalSetupUpdate GetTerminalSetupUpdateByTerminalId(int id, out int isAssign)
        {
            isAssign = 0;
            var terminal = base.GetById(id, p => p.TerminalSetupTemplate, p => p.TerminalSetupUpdate);
            if (terminal != null)
            {
                if (terminal.TerminalSetupTemplate != null) isAssign = terminal.TerminalSetupTemplate.Id;
                return terminal.TerminalSetupUpdate;
            }
            return null;
        }

        public List<int> GetAllTerminalSetupUpdateByTemplateId(int id)
        {
            return _terminalMasterRepository.GetAllTerminalSetupUpdateByTemplateId(id);
        }

        #endregion

        #region merchant report

        #endregion

        public TrainingTerminal GetTrainingByTerminalId(int id)
        {
            return _terminalMasterRepository.GetNaviProperty<TrainingTerminal>(id, p => p.Training, p => p.Training.User);
        }


        public AutoLoginTerminal GetAutoLoginByTerminalId(int id)
        {
            return _terminalMasterRepository.GetNaviProperty<AutoLoginTerminal>(id, p => p.AutoLogin, p => p.AutoLogin.User);
        }

        public bool SetIsStatus(string table, List<int> ids, int isStatus)
        {
            return _terminalMasterRepository.SetIsStatus(table, ids, isStatus);
        }

        public bool SetIsStatus(string table, string whereStatus, int isStatus)
        {
            return _terminalMasterRepository.SetIsStatus(table, whereStatus, isStatus);
        }

        public async Task<bool> SetIsStatusWithColumnGroupAsync(string table, string whereStatus, int isStatus, string columnName)
        {
            return await _terminalMasterRepository.SetIsStatusWithColumnGroupAsync(table, whereStatus, isStatus, columnName);
        }

        public bool SetIsClientId(string table, List<int> ids, int clientId)
        {
            return _terminalMasterRepository.SetIsClientId(table, ids, clientId);
        }


        public IList<MerchantSecurityLevel> GetMerchantSecurityLevelByMerchantId(int id)
        {
            return _merchantSecurityLevelRepository.GetAll(false, true, x => x.MerchantTerminalId == id).ToList();
        }

        public TerminalCloud GetTerminalCloudByTerminalId(int id)
        {
            return _terminalMasterRepository.GetNaviProperty<TerminalCloud>(id, select: p => p.TerminalCloud);
        }


        public TerminalSetup GetTerminalSetupByTerminalId(int id)
        {
            return _terminalMasterRepository.GetNaviProperty<TerminalSetup>(id, select: p => p.TerminalSetup);
        }

        public LanguageBy GetLanguageByByTerminalId(int id)
        {
            return _terminalMasterRepository.GetNaviProperty<LanguageBy>(id, select: p => p.LanguageBy);
        }

        public IList<TerminalUpdateSchedule> GetTerminalUpdateSchedulesByTerminalId(int id, int status)
        {
            return _terminalUpdateScheduleRepository.GetAll(false, true, x => x.TerminalId == id && x.Status == status).OrderBy(x => x.NextDownloadDate.ToString()).ToList();
        }

        public TerminalMaster GetTerminalDownload(string serialNumber, string macAddress, string ipAddress)
        {
            return FirstOrDefault(m => m.TerminalSerialNumber == (serialNumber) && m.LogonMacAddress == (macAddress) && m.LogonIpAddress == (ipAddress), false, m => m.ApplicationUpgradeTerminal.ApplicationVersion);
        }

        public void UndoAll()
        {
            _terminalMasterRepository.UndoAll();
        }

        public void SaveChanges()
        {
            _terminalMasterRepository.SaveChanges();
        }

        /// <summary>
        /// Ensures all brands and models from Linkly terminals exist in the database.
        /// Uses batch operations (InsertAll) for both brands and models to minimize database connections.
        /// The key for determining if a brand or model exists is the name.
        /// If name exists, get ID and use that ID. If name doesn't exist, insert new and get ID.
        /// </summary>
        /// <param name="linklyTerminals">List of terminals from Linkly API</param>
        /// <returns>Task representing the asynchronous operation</returns>
        private Task EnsureBrandsAndModelsExistAsync(List<MMS.Model.LinklyFake.LinklyTerminalDto> linklyTerminals)
        {
            // Process brands first
            ProcessBrands(linklyTerminals);

            // Then process models using the brand mappings
            ProcessModels(linklyTerminals);

            return Task.CompletedTask;
        }

        /// <summary>
        /// Ensures that all necessary CompanyDeviceSetupMapping entries exist for the given terminals and company
        /// </summary>
        /// <param name="linklyTerminals">List of terminals from Linkly API</param>
        /// <param name="companyId">Company ID to associate with the terminals</param>
        /// <returns>Task representing the asynchronous operation</returns>
        private async Task EnsureCompanyDeviceSetupMappingsExistAsync(List<MMS.Model.LinklyFake.LinklyTerminalDto> linklyTerminals, int companyId)
        {
            // Get all unique internal brand IDs from the terminals
            var brandIds = new List<int>();
            foreach (var terminal in linklyTerminals)
            {
                var internalBrandId = GetBrandIdFromMapping(terminal.BrandId, terminal.BrandName);
                if (internalBrandId.HasValue && !brandIds.Contains(internalBrandId.Value))
                {
                    brandIds.Add(internalBrandId.Value);
                }
            }

            // Get all unique internal model IDs from the terminals
            var modelMappings = new List<(int BrandId, int ModelId)>();
            foreach (var terminal in linklyTerminals)
            {
                var internalBrandId = GetBrandIdFromMapping(terminal.BrandId, terminal.BrandName);
                var internalModelId = GetModelIdFromMapping(terminal.Model, terminal.BrandId);

                if (internalBrandId.HasValue && internalModelId.HasValue)
                {
                    var mapping = (internalBrandId.Value, internalModelId.Value);
                    if (!modelMappings.Contains(mapping))
                    {
                        modelMappings.Add(mapping);
                    }
                }
            }

            // Get existing company device setup mappings for this company
            var existingMappings = await _companyDeviceSetupMappingService.GetDeviceSetupMappingsAsync(
                CoreUTI.Enum.CompanyDeviceSetupTypes.Company,
                companyId: companyId);

            // If no company mapping exists, create it
            if (!existingMappings.Any(m => m.CompanyId == companyId && m.Type == CoreUTI.Enum.CompanyDeviceSetupTypes.Company))
            {
                var companyMapping = new CompanyDeviceSetupMapping
                {
                    CompanyId = companyId,
                    Type = CoreUTI.Enum.CompanyDeviceSetupTypes.Company,
                    IsActive = true,
                    IsStatus = CoreUTI.Constants.NOTCHANGE_RECORD,
                    OrderIndex = 1
                };

                _companyDeviceSetupMappingService.Insert(companyMapping);
            }

            // Get existing brand mappings for this company
            var existingBrandMappings = await _companyDeviceSetupMappingService.GetDeviceSetupMappingsAsync(
                CoreUTI.Enum.CompanyDeviceSetupTypes.DeviceBrand,
                companyId: companyId);

            // Create a list to hold new mappings
            var newMappings = new List<CompanyDeviceSetupMapping>();

            // Check and create brand mappings
            foreach (var internalBrandId in brandIds)
            {
                if (!existingBrandMappings.Any(m => m.DeviceBrandId == internalBrandId))
                {
                    var brandMapping = new CompanyDeviceSetupMapping
                    {
                        CompanyId = companyId,
                        DeviceBrandId = internalBrandId,
                        Type = CoreUTI.Enum.CompanyDeviceSetupTypes.DeviceBrand,
                        IsActive = true,
                        IsStatus = CoreUTI.Constants.NOTCHANGE_RECORD,
                        OrderIndex = existingBrandMappings.Count + newMappings.Count + 1
                    };

                    newMappings.Add(brandMapping);
                }
            }

            // Get existing model mappings for this company
            var existingModelMappings = await _companyDeviceSetupMappingService.GetDeviceSetupMappingsAsync(
                CoreUTI.Enum.CompanyDeviceSetupTypes.DeviceModel,
                companyId: companyId);

            // Check and create model mappings
            foreach (var mapping in modelMappings)
            {
                if (!existingModelMappings.Any(m => m.DeviceModelId == mapping.ModelId && m.DeviceBrandId == mapping.BrandId))
                {
                    var modelMapping = new CompanyDeviceSetupMapping
                    {
                        CompanyId = companyId,
                        DeviceBrandId = mapping.BrandId,
                        DeviceModelId = mapping.ModelId,
                        Type = CoreUTI.Enum.CompanyDeviceSetupTypes.DeviceModel,
                        IsActive = true,
                        IsStatus = CoreUTI.Constants.NOTCHANGE_RECORD,
                        OrderIndex = existingModelMappings.Count + newMappings.Count + 1
                    };

                    newMappings.Add(modelMapping);
                }
            }

            // Insert all new mappings in a single batch operation
            if (newMappings.Any())
            {
                _companyDeviceSetupMappingService.InsertAll(newMappings);
            }
        }

        /// <summary>
        /// Extracts unique brands from terminals and ensures they exist in the database
        /// </summary>
        /// <param name="linklyTerminals">List of terminals from Linkly API</param>
        private void ProcessBrands(List<MMS.Model.LinklyFake.LinklyTerminalDto> linklyTerminals)
        {
            var uniqueBrands = ExtractUniqueBrands(linklyTerminals);
            var existingBrands = GetExistingBrands();

            // Initialize brand mapping dictionary (external ID to internal ID)
            _brandIdMapping = new Dictionary<int, int>();

            var newBrands = IdentifyNewBrands(uniqueBrands, existingBrands);

            InsertNewBrands(newBrands, uniqueBrands);
        }

        /// <summary>
        /// Extracts unique brands from terminals
        /// </summary>
        private List<dynamic> ExtractUniqueBrands(List<MMS.Model.LinklyFake.LinklyTerminalDto> linklyTerminals)
        {
            return linklyTerminals
                .GroupBy(t => t.BrandName, StringComparer.OrdinalIgnoreCase) // Group by name to ensure uniqueness
                .Select(g => new { BrandId = g.First().BrandId, BrandName = g.Key })
                .ToList<dynamic>();
        }

        /// <summary>
        /// Gets all existing brands from the database
        /// </summary>
        private Dictionary<string, TerminalBrandMaster> GetExistingBrands()
        {
            return _terminalTypeMasterService.GetAll(b => b.IsStatus != CoreUTI.Constants.DELETE_RECORD)
                .ToDictionary(b => b.TerminalBrandName, b => b, StringComparer.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Identifies brands that need to be created
        /// </summary>
        private List<TerminalBrandMaster> IdentifyNewBrands(
            List<dynamic> uniqueBrands,
            Dictionary<string, TerminalBrandMaster> existingBrands)
        {
            var newBrands = new List<TerminalBrandMaster>();

            foreach (var brand in uniqueBrands)
            {
                if (existingBrands.TryGetValue(brand.BrandName, out TerminalBrandMaster existingBrand))
                {
                    // Brand exists, map external ID to internal ID
                    _brandIdMapping[brand.BrandId] = existingBrand.Id;
                }
                else
                {
                    // Brand doesn't exist, add to list for batch insertion
                    newBrands.Add(new TerminalBrandMaster
                    {
                        TerminalBrandName = brand.BrandName,
                        IsActive = true
                    });
                }
            }

            return newBrands;
        }

        /// <summary>
        /// Inserts new brands and updates the brand mapping
        /// </summary>
        private void InsertNewBrands(List<TerminalBrandMaster> newBrands, List<dynamic> uniqueBrands)
        {
            if (!newBrands.Any())
            {
                return;
            }

            // Use InsertAll to batch insert all brands at once
            _terminalTypeMasterService.InsertAll(newBrands);

            // Update the brand mapping with the newly inserted brands
            foreach (var newBrand in newBrands)
            {
                // Find the external brand ID for this brand name
                var externalBrandId = uniqueBrands.FirstOrDefault(b =>
                    string.Equals(b.BrandName, newBrand.TerminalBrandName, StringComparison.OrdinalIgnoreCase))?.BrandId ?? 0;

                if (externalBrandId > 0)
                {
                    _brandIdMapping[externalBrandId] = newBrand.Id;
                }
            }
        }

        /// <summary>
        /// Processes models to ensure they exist in the database
        /// </summary>
        /// <param name="linklyTerminals">List of terminals from Linkly API</param>
        private void ProcessModels(List<MMS.Model.LinklyFake.LinklyTerminalDto> linklyTerminals)
        {
            var uniqueModels = ExtractUniqueModels(linklyTerminals);
            var existingModels = GetExistingModels();

            var newModels = IdentifyNewModels(uniqueModels, existingModels);

            InsertNewModels(newModels);
        }

        /// <summary>
        /// Extracts unique models from terminals
        /// </summary>
        private List<dynamic> ExtractUniqueModels(List<MMS.Model.LinklyFake.LinklyTerminalDto> linklyTerminals)
        {
            return linklyTerminals
                .GroupBy(t => new { ModelName = t.Model, BrandId = t.BrandId },
                    (key, group) => new { ModelName = key.ModelName, BrandId = key.BrandId, ModelId = group.First().ModelId })
                .ToList<dynamic>();
        }

        /// <summary>
        /// Gets all existing models for the brands we're working with
        /// </summary>
        private Dictionary<string, TerminalModelMaster> GetExistingModels()
        {
            // Initialize model mapping dictionary
            _modelIdMapping = new Dictionary<string, int>();

            var relevantBrandIds = _brandIdMapping.Values.Where(id => id > 0).ToList();
            var existingModels = new Dictionary<string, TerminalModelMaster>(StringComparer.OrdinalIgnoreCase);

            foreach (var brandId in relevantBrandIds)
            {
                var models = _terminalModelMasterService.GetAll(
                    m => m.TerminalBrandId == brandId && m.IsStatus != CoreUTI.Constants.DELETE_RECORD);

                foreach (var model in models)
                {
                    var key = $"{model.TerminalBrandId}:{model.TerminalModelName}";
                    existingModels[key] = model;
                    _modelIdMapping[key] = model.Id;
                }
            }

            return existingModels;
        }

        /// <summary>
        /// Identifies models that need to be created
        /// </summary>
        private List<TerminalModelMaster> IdentifyNewModels(
            List<dynamic> uniqueModels,
            Dictionary<string, TerminalModelMaster> existingModels)
        {
            var newModels = new List<TerminalModelMaster>();

            foreach (var model in uniqueModels)
            {
                if (_brandIdMapping.TryGetValue(model.BrandId, out int internalBrandId) && internalBrandId > 0)
                {
                    var key = $"{internalBrandId}:{model.ModelName}";
                    if (!existingModels.ContainsKey(key))
                    {
                        newModels.Add(new TerminalModelMaster
                        {
                            TerminalModelName = model.ModelName,
                            TerminalBrandId = internalBrandId,
                            IsActive = true
                        });
                    }
                }
            }

            return newModels;
        }

        /// <summary>
        /// Inserts new models and updates the model mapping
        /// </summary>
        private void InsertNewModels(List<TerminalModelMaster> newModels)
        {
            if (!newModels.Any())
            {
                return;
            }

            // Use InsertAll to batch insert all models at once
            _terminalModelMasterService.InsertAll(newModels);

            // Update the model mapping with the newly inserted models
            foreach (var newModel in newModels)
            {
                var key = $"{newModel.TerminalBrandId}:{newModel.TerminalModelName}";
                _modelIdMapping[key] = newModel.Id;
            }
        }

        // These dictionaries will be populated by EnsureBrandsAndModelsExistAsync
        private Dictionary<int, int> _brandIdMapping;
        private Dictionary<string, int> _modelIdMapping;

        /// <summary>
        /// Maps properties from a LinklyTerminalDto to a TerminalMaster entity
        /// </summary>
        /// <param name="terminal">The terminal entity to update</param>
        /// <param name="linklyTerminal">The source Linkly terminal data</param>
        private void MapLinklyTerminalProperties(TerminalMaster terminal, MMS.Model.LinklyFake.LinklyTerminalDto linklyTerminal)
        {
            terminal.TerminalSerialNumber = linklyTerminal.SerialNumber;
            terminal.TerminalServiceNumber = linklyTerminal.ServiceNumber;
            terminal.lszTerminalName = linklyTerminal.Name;
            terminal.TerminalBrandId = GetBrandIdFromMapping(linklyTerminal.BrandId, linklyTerminal.BrandName);
            terminal.TerminalModelTypeId = GetModelIdFromMapping(linklyTerminal.Model, linklyTerminal.BrandId);
            terminal.IsMobile = linklyTerminal.IsMobile;
            terminal.DeviceStatusSelected = (DeviceStatus)linklyTerminal.DeviceStatusSelected;
            terminal.SmartClaimLink = linklyTerminal.SmartClaimLink;
            terminal.ShowDate = linklyTerminal.ShowDate;
            terminal.ShowTime = linklyTerminal.ShowTime;
            terminal.fContactlessEnabled = linklyTerminal.ContactlessEnabled;
            terminal.ContactlessTimeDelayEnabled = linklyTerminal.ContactlessTimeDelayEnabled;
            terminal.ContactlessTimeDelay = linklyTerminal.ContactlessTimeDelay;
            terminal.cPINCharacters = linklyTerminal.CPINCharacters;
            terminal.IsActive = linklyTerminal.IsActive;
        }

        /// <summary>
        /// Gets the internal brand ID from the mapping
        /// </summary>
        /// <param name="externalBrandId">External brand ID</param>
        /// <param name="brandName">Brand name</param>
        /// <returns>Internal brand ID</returns>
        private int? GetBrandIdFromMapping(int externalBrandId, string brandName)
        {
            if (_brandIdMapping != null && _brandIdMapping.TryGetValue(externalBrandId, out int brandId))
            {
                return brandId;
            }

            // Fallback to looking up by name if not in mapping
            var existingBrand = _terminalTypeMasterService.FirstOrDefault(b =>
                b.TerminalBrandName == brandName &&
                b.IsStatus != CoreUTI.Constants.DELETE_RECORD);

            return existingBrand?.Id;
        }

        /// <summary>
        /// Gets the internal model ID from the mapping
        /// </summary>
        /// <param name="modelName">Model name</param>
        /// <param name="externalBrandId">External brand ID</param>
        /// <returns>Internal model ID</returns>
        private int? GetModelIdFromMapping(string modelName, int externalBrandId)
        {
            // Get the internal brand ID
            var brandId = GetBrandIdFromMapping(externalBrandId, "");
            if (brandId == null)
            {
                return null;
            }

            // Try to find the model in the mapping
            var key = $"{brandId.Value}:{modelName}";
            if (_modelIdMapping != null && _modelIdMapping.TryGetValue(key, out int modelId))
            {
                return modelId;
            }

            // Fallback to looking up by name and brand if not in mapping
            var existingModel = _terminalModelMasterService.FirstOrDefault(m =>
                m.TerminalModelName == modelName &&
                m.TerminalBrandId == brandId.Value &&
                m.IsStatus != CoreUTI.Constants.DELETE_RECORD);

            return existingModel?.Id;
        }

        /// <summary>
        /// Synchronizes terminal data from Linkly to the MMS database
        /// </summary>
        /// <param name="linklyTerminals">List of terminals from Linkly API</param>
        /// <param name="companyId">Company ID to associate with the terminals</param>
        /// <returns>Summary of sync operation (inserted, updated, deleted counts)</returns>
        public async Task<(int inserted, int updated, int deleted)> SyncTerminalsFromLinklyAsync(List<MMS.Model.LinklyFake.LinklyTerminalDto> linklyTerminals, int companyId)
        {
            // First, ensure all brands and models exist in our database
            await EnsureBrandsAndModelsExistAsync(linklyTerminals);

            // Ensure company device setup mappings exist
            await EnsureCompanyDeviceSetupMappingsExistAsync(linklyTerminals, companyId);

            int inserted = 0, updated = 0, deleted = 0;

            // Always use Linkly source for this method since it's specifically for syncing with Linkly
            var existingLinklyTerminals = await _terminalMasterRepository.GetTerminalsBySourceAsync(
                source: CoreUTI.Enum.TerminalSources.Linkly,
                includeDeleted: false,
                includeDeactivated: true
            );

            // Track existing terminal IDs to determine which ones to delete
            var processedTerminalIds = new HashSet<int>();

            // Process each terminal from Linkly

            List<TerminalMaster> insertOrUpdateList = new List<TerminalMaster>();

            foreach (var linklyTerminal in linklyTerminals)
            {
                // Try to find existing terminal by serial number
                var existingTerminal = existingLinklyTerminals.FirstOrDefault(t =>
                    t.TerminalSerialNumber == linklyTerminal.SerialNumber);

                if (existingTerminal == null)
                {
                    // Create new terminal
                    var newTerminal = new TerminalMaster();

                    // Set common properties
                    MapLinklyTerminalProperties(newTerminal, linklyTerminal);

                    // Set creation-specific properties
                    newTerminal.Created = linklyTerminal.Created;
                    newTerminal.Modified = linklyTerminal.Modified;

                    // Always use Linkly source for terminals created through this method
                    newTerminal.TerminalSource = CoreUTI.Enum.TerminalSources.Linkly;

                    // Set the company ID
                    newTerminal.TerminalServiceDetail = companyId;

                    // Add terminal to database using repository
                    insertOrUpdateList.Add(newTerminal);
                    inserted++;
                }
                else
                {
                    // Set common properties
                    MapLinklyTerminalProperties(existingTerminal, linklyTerminal);

                    // Set update-specific properties
                    existingTerminal.Modified = DateTime.Now;

                    // Make sure the terminal is marked as from Linkly
                    existingTerminal.TerminalSource |= CoreUTI.Enum.TerminalSources.Linkly;

                    // Set the company ID
                    // existingTerminal.TerminalServiceDetail = companyId;

                    // Update terminal in database using repository
                    insertOrUpdateList.Add(existingTerminal);
                    updated++;

                    // Mark as processed
                    processedTerminalIds.Add(existingTerminal.Id);
                }
            }

            // Find terminals that exist in our database but not in Linkly API response
            var terminalsToDelete = existingLinklyTerminals
                .Where(t => !processedTerminalIds.Contains(t.Id) && t.TerminalServiceDetail == companyId)
                .ToList();

            // Soft delete terminals that no longer exist in Linkly
            foreach (var terminalToDelete in terminalsToDelete)
            {
                terminalToDelete.IsActive = false;
                terminalToDelete.IsStatus = CoreUTI.Constants.DELETE_RECORD;
                terminalToDelete.Modified = DateTime.Now;

                insertOrUpdateList.Add(terminalToDelete);
                deleted++;
            }

            _terminalMasterRepository.AddOrUpdateAll(insertOrUpdateList);

            return (inserted, updated, deleted);
        }

        public TerminalMaster GetTerminalByTemplateId(int templateId)
        {
            return FirstOrDefault(p => p.DeviceSetupType == CoreUTI.Enum.DeviceSetupType.BaseTemplate && p.CopyDeviceTemplateId == templateId);
        }

        public async Task<TerminalMaster> GetTerminalByDeviceTemplateId(int templateId, DeviceSetupType type)
        {
            return await _terminalMasterRepository.GetTerminalByDeviceTemplateId(templateId, type);
        }

        public async Task<List<TerminalMaster>> GetDeletedTemplateTerminalsAsync()
        {
            return await _terminalMasterRepository.GetDeletedTemplateTerminalsAsync();
        }

        public TerminalMaster GetTerminalByTerminalId(int terminalId)
        {
            return _terminalMasterRepository.GetTerminalByTerminalId(terminalId);
        }

        public TerminalMaster GetMerchantCardFallback(int terminalMasterId)
        {
            return _terminalMasterRepository.GetMerchantCardFallback(terminalMasterId);
        }


        public PaymentMode PaymentModeOptionGet(int terminalMasterId, IList<MerchantTerminal> merchantTerminals)
        {
            var terminalMaster = base.GetById(terminalMasterId, p => p.MerchantTerminals);
            if (terminalMaster == null)
                throw new InvalidOperationException("The terminal master cannot be null.");

            var firstMerchantTerminal = merchantTerminals.FirstOrDefault();

            if (firstMerchantTerminal != null)
            {
                firstMerchantTerminal = _repoMerchantTerminal.GetById(firstMerchantTerminal.Id, p => p.PaymentModes);
                var firstPaymentMode = firstMerchantTerminal.PaymentModes.FirstOrDefault();

                if (firstPaymentMode != null)
                {
                    firstPaymentMode = _repoPaymentMode.GetById(firstPaymentMode.Id, p => p.PaymentModeOption);

                    return firstPaymentMode;
                }

                var paymentModeOption = new PaymentModeOption();
                _repoPaymentModeOption.Insert(paymentModeOption);

                firstPaymentMode = new PaymentMode();

                firstPaymentMode.MerchantId = firstMerchantTerminal.Id;
                firstPaymentMode.ModeOptionId = paymentModeOption.Id;

                _repoPaymentMode.Insert(firstPaymentMode);

                return firstPaymentMode;
            }

            return new PaymentMode();
        }

        public List<PaymentMode> GetPaymentModes(IList<MerchantTerminal> merchantTerminals)
        {
            var paymentModes = new List<PaymentMode>();
            if (merchantTerminals != null && merchantTerminals.Any())
            {
                foreach (var item in merchantTerminals)
                {
                    var merchantTerminal = _repoMerchantTerminal.GetById(item.Id, p => p.PaymentModes);

                    var validPaymentModes = merchantTerminal.PaymentModes
                        .Where(p => p.IdParent == 0 && p.IsStatus != Constants.PRE_DELETE_RECORD && p.IsStatus != Constants.DELETE_RECORD)
                        .ToList();

                    var fetchedPaymentModes = validPaymentModes
                        .Select(pm => _repoPaymentMode.GetById(pm.Id, p => p.PaymentModeOption))
                        .Where(mode => mode != null)
                        .ToList();

                    paymentModes.AddRange(fetchedPaymentModes);
                }
            }

            return paymentModes;
        }

        public bool PaymentModeOptionUpdate(PaymentModeOption entity)
        {
            _repoPaymentModeOption.Update(entity);

            return true;
        }

        public bool TEMUpdateTerminalMaster(TerminalMaster terminalMaster)
        {
            if (terminalMaster.TerminalWifiId == null)
            {
                _repoTerminalWifi.Insert(terminalMaster.TerminalWifi);
            }
            else
            {
                _repoTerminalWifi.Update(terminalMaster.TerminalWifi);
            }

            if (terminalMaster.TerminalEthernetId == null)
            {
                _repoTerminalWAN.Insert(terminalMaster.TerminalWAN);
            }
            else
            {
                _repoTerminalWAN.Update(terminalMaster.TerminalWAN);
            }

            _terminalMasterRepository.Update(terminalMaster);

            return true;
        }


        public bool SoundTerminalUpdate(SoundTerminal entity)
        {
            _repoSoundTerminal.Update(entity);

            return true;
        }

        public bool SoundTerminalInsert(int terminalMasterId, SoundTerminal entity)
        {
            _repoSoundTerminal.Insert(entity);

            var terminalMaster = _terminalMasterRepository.GetById(terminalMasterId);
            if (terminalMaster == null)
                throw new InvalidOperationException(NotFoundTerminalMessage + terminalMasterId);

            terminalMaster.SoundId = entity.Id;

            _terminalMasterRepository.Update(terminalMaster);
            return true;
        }

        public bool TerminalSetupUpdate(TerminalSetup entity)
        {
            _repoTerminalSetup.Update(entity);

            return true;
        }

        public bool TerminalSetupInsert(int terminalMasterId, TerminalSetup entity)
        {
            _repoTerminalSetup.Insert(entity);

            var terminalMaster = _terminalMasterRepository.GetById(terminalMasterId);
            if (terminalMaster == null)
                throw new InvalidOperationException(NotFoundTerminalMessage + terminalMasterId);

            terminalMaster.TerminalSetupId = entity.Id;

            _terminalMasterRepository.Update(terminalMaster);
            return true;
        }

        public bool TerminalCloudUpdate(TerminalCloud entity)
        {
            _repoTerminalCloud.Update(entity);
            return true;
        }
        public bool TerminalCloudInsert(int terminalMasterId, TerminalCloud entity)
        {
            _repoTerminalCloud.Insert(entity);

            var terminalMaster = _terminalMasterRepository.GetById(terminalMasterId);
            if (terminalMaster == null)
                throw new InvalidOperationException(NotFoundTerminalMessage + terminalMasterId);

            terminalMaster.TerminalCloudId = entity.Id;

            _terminalMasterRepository.Update(terminalMaster);
            return true;
        }
        public bool CountryTerminalUpdate(CountryTerminal entity)
        {
            entity.alCoinDenominations.usCountryCode = entity.usCountryCode;
            entity.alNotesDenominations.usCountryCode = entity.usCountryCode;
            _repoCountryTerminal.Update(entity);
            return true;
        }
        public bool CountryTerminalInsert(int terminalMasterId, CountryTerminal entity)
        {
            _repoCountryTerminal.Insert(entity);

            var terminalMaster = _terminalMasterRepository.GetById(terminalMasterId);
            if (terminalMaster == null)
                throw new InvalidOperationException(NotFoundTerminalMessage + terminalMasterId);

            terminalMaster.CountryTerminalId = entity.Id;
            terminalMaster.CountryTerminal.alCoinDenominations.usCountryCode = terminalMaster.CountryTerminal.usCountryCode;
            terminalMaster.CountryTerminal.alNotesDenominations.usCountryCode = terminalMaster.CountryTerminal.usCountryCode;

            _terminalMasterRepository.Update(terminalMaster);
            return true;
        }

        public bool UserMasterUpdate(UserMaster entity)
        {
            _repoUserMaster.Update(entity);
            return true;
        }

        public bool FallBackTerminalUpdate(FallBackTerminal entity)
        {
            _repoFallBackTerminal.Update(entity);
            return true;
        }
        public bool FallBackTerminalInsert(int terminalMasterId, FallBackTerminal entity)
        {
            _repoFallBackTerminal.Insert(entity);

            var terminalMaster = _terminalMasterRepository.GetById(terminalMasterId);
            if (terminalMaster == null)
                throw new InvalidOperationException(NotFoundTerminalMessage + terminalMasterId);

            terminalMaster.FallBackId = entity.Id;

            _terminalMasterRepository.Update(terminalMaster);
            return true;
        }

        public bool ExtraModudleUpdate(ExtraModudle entity)
        {
            _repoExtraModudle.Update(entity);
            return true;
        }
        public bool ExtraModudleInsert(int terminalMasterId, ExtraModudle entity)
        {
            _repoExtraModudle.Insert(entity);

            var terminalMaster = _terminalMasterRepository.GetById(terminalMasterId);
            if (terminalMaster == null)
                throw new InvalidOperationException(NotFoundTerminalMessage + terminalMasterId);

            terminalMaster.ExtraModudleId = entity.Id;

            _terminalMasterRepository.Update(terminalMaster);
            return true;
        }

        public bool PayplusProcessorUpdate(PayplusProcessor entity)
        {
            _repoPayplusProcessor.Update(entity);
            return true;
        }
        public bool PayplusProcessorInsert(int terminalMasterId, PayplusProcessor entity)
        {
            _repoPayplusProcessor.Insert(entity);

            var terminalMaster = _terminalMasterRepository.GetById(terminalMasterId);
            if (terminalMaster == null)
                throw new InvalidOperationException(NotFoundTerminalMessage + terminalMasterId);

            terminalMaster.PayplusProcessorId = entity.Id;

            _terminalMasterRepository.Update(terminalMaster);
            return true;
        }

        public bool MerchantCardFallbacUpdateInsert(int terminalMasterId, int merchantCardFallbackType, IList<TemMerchantCardFallback> apiEntities, IList<MerchantCardFallback> sources)
        {
            var addingList = new List<MerchantCardFallback>();
            var updatingList = new List<MerchantCardFallback>();
            var removingList = new List<MerchantCardFallback>();

            if (sources != null)
            {
                sources = sources.Where(p => p.MerchantCardFallbackType == merchantCardFallbackType).ToList();


                var listCardType = apiEntities.Select(p => p.CardType).ToList();
                removingList = sources.Where(p => !listCardType.Contains(p.CardType)).ToList();
            }

            foreach (var apiEntity in apiEntities)
            {
                MerchantCardFallback entity = sources != null ? sources.FirstOrDefault(p => p.CardType == apiEntity.CardType) : null;

                if (entity == null)
                {
                    entity = new MerchantCardFallback();
                    entity.TerminalMasterId = terminalMasterId;
                    entity.MerchantCardFallbackType = merchantCardFallbackType; // debit credite
                    entity.CardType = apiEntity.CardType;
                }

                entity.Day = apiEntity.Day.ToString();
                entity.Month = apiEntity.Month.ToString();
                entity.Year = apiEntity.Year.ToString();

                entity.IsStatus = Constants.EDIT_RECORD;
                if (entity.Id == 0)
                {
                    addingList.Add(entity);
                }
                else
                {
                    updatingList.Add(entity);
                }
            }

            foreach (var entity in addingList)
            {
                _repoMerchantCardFallback.Insert(entity);

            }

            foreach (var entity in updatingList)
            {
                _repoMerchantCardFallback.Update(entity);
            }

            foreach (var entity in removingList)
            {
                entity.IsStatus = Constants.DELETE_RECORD;
                _repoMerchantCardFallback.Update(entity);
            }

            return true;
        }

        public bool apiICSSetupByTerminalTypeUpdate(ICSSetupByTerminalType entity)
        {
            _repoICSSetupByTerminalType.Update(entity);
            return true;
        }

        public bool apiICSSetupByTerminalTypeInsert(ICSSetupByTerminalType entity, int terminalMasterId)
        {
            _repoICSSetupByTerminalType.Insert(entity);

            var id = entity.Id;

            var terminalMaster = Repo.GetById(terminalMasterId);
            if (terminalMaster == null)
                throw new InvalidOperationException(NotFoundTerminalMessage + terminalMasterId);

            terminalMaster.ByTerminalTypeId = id;
            Repo.Update(terminalMaster);

            return true;
        }


        public bool apiICSSetupByCardDataInputUpdate(ICSSetupByCardDataInput entity)
        {
            _repoICSSetupByCardDataInput.Update(entity);
            return true;
        }

        public bool apiICSSetupByCardDataInputInsert(ICSSetupByCardDataInput entity, int terminalMasterId)
        {
            _repoICSSetupByCardDataInput.Insert(entity);

            var id = entity.Id;

            var terminalMaster = Repo.GetById(terminalMasterId);
            if (terminalMaster == null)
                throw new InvalidOperationException(NotFoundTerminalMessage + terminalMasterId);

            terminalMaster.ByCardDataInputId = id;
            Repo.Update(terminalMaster);

            return true;
        }

        public bool apiICSSetupByCVMCapabilityUpdate(ICSSetupByCVMCapability entity)
        {
            _repoICSSetupByCVMCapability.Update(entity);
            return true;
        }

        public bool apiICSSetupByCVMCapabilityInsert(ICSSetupByCVMCapability entity, int terminalMasterId)
        {
            _repoICSSetupByCVMCapability.Insert(entity);

            var id = entity.Id;

            var terminalMaster = Repo.GetById(terminalMasterId);
            if (terminalMaster == null)
                throw new InvalidOperationException(NotFoundTerminalMessage + terminalMasterId);

            terminalMaster.ByCVMCapabilityId = id;
            Repo.Update(terminalMaster);

            return true;
        }

        public bool apiICSSetupBySecurityCapabilityUpdate(ICSSetupBySecurityCapability entity)
        {
            _repoICSSetupBySecurityCapability.Update(entity);
            return true;
        }

        public bool apiICSSetupBySecurityCapabilityInsert(ICSSetupBySecurityCapability entity, int terminalMasterId)
        {
            _repoICSSetupBySecurityCapability.Insert(entity);

            var id = entity.Id;

            var terminalMaster = Repo.GetById(terminalMasterId);
            if (terminalMaster == null)
                throw new InvalidOperationException(NotFoundTerminalMessage + terminalMasterId);

            terminalMaster.BySecurityCapabilityId = id;
            Repo.Update(terminalMaster);

            return true;
        }

        public bool apiICSSetupByTmDataInputUpdate(ICSSetupByTmDataInput entity)
        {
            _repoICSSetupByTmDataInput.Update(entity);
            return true;
        }

        public bool apiICSSetupByTmDataInputInsert(ICSSetupByTmDataInput entity, int terminalMasterId)
        {
            _repoICSSetupByTmDataInput.Insert(entity);

            var id = entity.Id;

            var terminalMaster = Repo.GetById(terminalMasterId);
            if (terminalMaster == null)
                throw new InvalidOperationException(NotFoundTerminalMessage + terminalMasterId);

            terminalMaster.ByTmDataInputId = id;
            Repo.Update(terminalMaster);

            return true;
        }


        public bool apiICSSetupByTmDataOutputUpdate(ICSSetupByTmDataOutput entity)
        {
            _repoICSSetupByTmDataOutput.Update(entity);
            return true;
        }

        public bool apiICSSetupByTmDataOutputInsert(ICSSetupByTmDataOutput entity, int terminalMasterId)
        {
            _repoICSSetupByTmDataOutput.Insert(entity);

            var id = entity.Id;

            var terminalMaster = Repo.GetById(terminalMasterId);
            if (terminalMaster == null)
                throw new InvalidOperationException(NotFoundTerminalMessage + terminalMasterId);

            terminalMaster.ByTmDataOutputId = id;
            Repo.Update(terminalMaster);

            return true;
        }

        public bool apiICSSetupByCDAModeUpdate(ICSSetupByCDAMode entity)
        {
            _repoICSSetupByCDAMode.Update(entity);
            return true;
        }

        public bool apiICSSetupByCDAModeInsert(ICSSetupByCDAMode entity, int terminalMasterId)
        {
            _repoICSSetupByCDAMode.Insert(entity);

            var id = entity.Id;

            var terminalMaster = Repo.GetById(terminalMasterId);
            if (terminalMaster == null)
                throw new InvalidOperationException(NotFoundTerminalMessage + terminalMasterId);

            terminalMaster.ByCDAModeId = id;
            Repo.Update(terminalMaster);

            return true;
        }

        public bool apiPOSConnectionUpdate(POSConnection entity)
        {
            var usbInterface = _repoUSBInterface.GetById(entity.USBInterfaceId);

            if (usbInterface != null)
            {
                usbInterface.UsbInterface = entity.USBInterface.UsbInterface;
                _repoUSBInterface.Update(usbInterface);
            }

            var ipInterface = _repoIPInterface.GetById(entity.IPInterfaceId);

            if (ipInterface != null)
            {
                ipInterface.IPAddress = entity.IPInterface.IPAddress;
                ipInterface.IPPort = entity.IPInterface.IPPort;

                _repoIPInterface.Update(ipInterface);
            }

            var rs232Interface = _repoRS232Interface.GetById(entity.IPInterfaceId);

            if (rs232Interface != null)
            {
                rs232Interface.RS232Port = entity.RS232Interface.RS232Port;
                rs232Interface.Rs232Interface = entity.RS232Interface.Rs232Interface;

                _repoRS232Interface.Update(rs232Interface);
            }

            _repoPOSConnection.Update(entity);

            return true;
        }

        public bool apiPOSConnectionInsert(POSConnection entity, int terminalMasterId)
        {
            var terminalMaster = Repo.GetById(terminalMasterId);
            if (terminalMaster == null)
                throw new InvalidOperationException(NotFoundTerminalMessage + terminalMasterId);

            //USBInterface
            var usbInterface = new USBInterface();
            usbInterface.UsbInterface = entity.USBInterface.UsbInterface;
            _repoUSBInterface.Insert(usbInterface);

            //IPInterface
            var ipInterface = new IPInterface();
            ipInterface.IPAddress = entity.IPInterface.IPAddress;
            ipInterface.IPPort = entity.IPInterface.IPPort;
            _repoIPInterface.Insert(ipInterface);

            //RS232Interface
            var rs232Interface = new RS232Interface();
            rs232Interface.RS232Port = entity.RS232Interface.RS232Port;
            rs232Interface.Rs232Interface = entity.RS232Interface.Rs232Interface;
            _repoRS232Interface.Insert(rs232Interface);

            //POSConnection
            entity.TerminalMasterId = terminalMasterId;
            entity.RS232InterfaceId = rs232Interface.Id;
            entity.IPInterfaceId = ipInterface.Id;
            entity.USBInterfaceId = usbInterface.Id;

            _repoPOSConnection.Insert(entity);

            return true;
        }

        public bool apiPOSInterfaceUpdate(POSInterface entity)
        {
            _repoPOSInterface.Update(entity);

            return true;
        }

        public bool apiPOSInterfaceInsert(POSInterface entity, int terminalMasterId)
        {
            var terminalMaster = Repo.GetById(terminalMasterId);
            if (terminalMaster == null)
                throw new InvalidOperationException(NotFoundTerminalMessage + terminalMasterId);

            entity.TerminalMasterId = terminalMasterId;

            _repoPOSInterface.Insert(entity);

            return true;
        }

        public IList<TerminalMaster> GetAllTerminalByIoTDevice(int IoTDeviceTypeId, int IoTDeviceCateId, int IoTDeviceId, bool isAssignedDevice = false, string searchKey = "")
        {
            return GetAllTerminalQueryable().
                Where(x => x.IoTDeviceTypeId == IoTDeviceTypeId &&
                    x.IoTDeviceCateId == IoTDeviceCateId &&
                    x.IoTDeviceId == IoTDeviceId &&
                    x.TerminalSerialNumber.Contains(searchKey) &&
                    x.IsAssignedDevice == isAssignedDevice &&
                    x.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD).ToList();
        }

        public IList<TerminalMaster> GetAllTerminalByIoTDeviceView(int? IoTDeviceTypeId, int? IoTDeviceCateId, int? IoTDeviceId, string searchKey = "")
        {
            return GetAllTerminalQueryable().
                Where(x => x.IoTDeviceTypeId == IoTDeviceTypeId &&
                    x.IoTDeviceCateId == IoTDeviceCateId &&
                    x.IoTDeviceId == IoTDeviceId &&
                    x.TerminalSerialNumber.Contains(searchKey) &&
                    x.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD).ToList();
        }

        public string GetAllXmlChanges(int terminalMasterId)
        {
            return _terminalMasterRepository.GetAllXmlChanges(terminalMasterId);
        }

        public IList<TerminalMaster> GetAllTerminalForIoTDeviceManager(IList<int> ioTDeviceTypeIds = null, IList<int> ioTDeviceCateIds = null, IList<int> ioTDeviceIds = null)
        {
            var list = GetAllTerminalQueryable().Where(x => x.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD).ToList();
            if (ioTDeviceTypeIds != null)
            {
                list = list.Where(p => ioTDeviceTypeIds.Contains(p.IoTDeviceTypeId.GetValueOrDefault())).ToList();
            }
            else if (ioTDeviceCateIds != null)
            {
                list = list.Where(p => ioTDeviceCateIds.Contains(p.IoTDeviceCateId.GetValueOrDefault())).ToList();
            }
            else if (ioTDeviceIds != null)
            {
                list = list.Where(p => ioTDeviceIds.Contains(p.IoTDeviceId.GetValueOrDefault())).ToList();
            }

            return list;
        }

        public TerminalMaster GetForDeviceSetupEdit(int terminalMasterId)
        {
            var terminalMaster = _terminalMasterRepository.GetForDeviceSetupEdit(terminalMasterId);
            terminalMaster = _languageExecuteService.GetLanguage(terminalMaster);

            return terminalMaster;
        }

        public async Task<List<ApplicationDeviceMapping>> GetSelectedApplicationsListByTerminalMasterIdAsync(int terminalMaterId)
        {
            var terminal = GetById(terminalMaterId);
            ArgumentNullException.ThrowIfNull(terminal.TerminalServiceDetail);
            ArgumentNullException.ThrowIfNull(terminal.AssignedGroupId);

            var sourceSelected = (await _companyGroupMappingService.GetSelectedAppByCompanyOrSubsidiaryAsync(terminal.TerminalServiceDetail.Value, terminal.AssignedGroupId.Value)).Select(p => p.AppId.Value).ToList();
            return await _terminalMasterRepository.GetSelectedApplicationListByTerminalMasterIdAsync(terminalMaterId, sourceSelected);
        }

        public IList<TerminalMaster> GetAllTerminalByCompanyAndAccessStatus(int companyDetailId,
            int brandId,
            int modelId,
            bool isAssignedDevice = false,
            string searchKey = "",
            int companyId = 0,
            int accessStatusId = 0)
        {
            using (var context = Repo.GetContext())
            {
                var obj = context.TerminalMaster
                    .Include(b => b.IoTDeviceCompanyAssigneds)
                    .Include(b => b.IoTDeviceAssociatedStatus).ThenInclude(p => p.CompanyAccessStatus)
                    .Include(b => b.IoTDevice)
                    .Where(b => b.IsAssignedDevice == isAssignedDevice
                                 && b.TerminalServiceDetail == companyDetailId // companyDetails Id
                                 && b.TerminalBrandId == brandId // terminalBrandMaster Id
                                 && b.TerminalModelTypeId == modelId // terminalModelMaster Id
                                 && b.TerminalSerialNumber.Contains(searchKey)
                                 && b.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD);

                if (isAssignedDevice && companyId != 0 && accessStatusId != 0)
                {
                    obj = obj.Where(b => b.IoTDeviceCompanyAssigneds.Any(x => x.CompanyId == companyId) && b.IoTDeviceAssociatedStatus.Any(x => x.CompanyAccessStatus.AccessStatusId == accessStatusId));
                }

                return obj.ToList();
            }
        }

        public async Task<List<TerminalMaster>> GetAllTerminalUnAssignedDeviceAsync(int companyIdOrg,
            int brandIdOrg,
            int modelIdOrg,
            bool isAssignedDevice = false,
            string searchKey = "",
            int companyId = 0,
            int accessStatusId = 0)
        {
            return await _terminalMasterRepository.GetAllTerminalUnAssignedDeviceAsync(companyIdOrg, brandIdOrg, modelIdOrg, isAssignedDevice, searchKey, companyId, accessStatusId);
        }

        public async Task AssignDeviceToLocationAsync(int terminalId, int locationAreaId, int deviceIndustryId, int groupId)
        {
            await _terminalMasterRepository.AssignDeviceToLocationAsync(terminalId, locationAreaId, deviceIndustryId, groupId);
        }

        public async Task AssignDeviceIndustryAsync(int terminalId, int deviceIndustryId)
        {
            await _terminalMasterRepository.AssignDeviceIndustryAsync(terminalId, deviceIndustryId);
        }

        public async Task<IList<TerminalMaster>> GetAssignedDeviceAsync(AssignedDeviceParamsModel paramsModel)
        {
            var locationItemRegionIds = await _locationItemSelectRepository.GetLocationItemRegionIdsAsync(paramsModel.GroupMappingId);

            var locationAreaIds = new List<int>();
            if (paramsModel.LocationIdsSelected == null)
            {
                locationAreaIds = await _locationItemRepository.GetLocationAreaIdsForAllRegionIdsAsync(locationItemRegionIds);
            }
            else
            {
                locationAreaIds = (await _locationItemRepository.GetLocationAreaIdByStringHierarchy(paramsModel.LocationIdsSelected)).ToList();
            }

            var deviceIndustryIds = await _locationDeviceMappingRepository.GetDeviceIndustryIdAsync(paramsModel.LocationCategoryId, paramsModel.DeviceTypeId, paramsModel.DeviceIndustryId);


            var result = await _terminalMasterRepository.GetDeviceAssignedAsync(locationAreaIds, deviceIndustryIds, paramsModel.DeviceBrandId, paramsModel.DeviceModelId, paramsModel.CompanyId, paramsModel.GroupId);

            return result;
        }

        public async Task<IList<TerminalMaster>> GetListTerminalByListIdAsync(IList<int> ids)
        {
            var result = await _terminalMasterRepository.GetListDeviceByListIdAsync(ids);
            return result;
        }

        public async Task<IList<TerminalBrandMaster>> GetTerminalBrandMasterAsync(string locationStringHierarchyIds, int deviceCategoryId, int deviceTypeId, int deviceIndustryId, int companyId, int groupId)
        {
            var companyBrandMapping = await _companyDeviceSetupMappingRepository.GetDeviceSetupMappingsAsync(CompanyDeviceSetupTypes.DeviceBrand, companyId);
            var brandIds = companyBrandMapping.Select(p => p.DeviceBrandId.GetValueOrDefault()).ToList();

            var deviceIndustryIds = await _locationDeviceMappingRepository.GetDeviceIndustryIdAsync(deviceCategoryId, deviceTypeId, deviceIndustryId);

            if (string.IsNullOrEmpty(locationStringHierarchyIds))
                return await _terminalMasterRepository.GetTerminalMasterBrandWithoutLocationAreaId(deviceIndustryIds);
            else
            {
                var locationAreaIds = await _locationItemRepository.GetLocationAreaIdByStringHierarchy(locationStringHierarchyIds);
                return await _terminalMasterRepository.GetTerminalBrandMasterOfAssignedDeviceAsync(locationAreaIds, deviceIndustryIds, brandIds, companyId, groupId);
            }
        }

        public async Task<IList<TerminalModelMaster>> GetTerminalModelMasterAsync(string locationStringHierarchyIds, int deviceCategoryId, int deviceTypeId, int deviceIndustryId, int deviceBrandId, int companyId, int groupId)
        {
            var deviceIndustryIds = await _locationDeviceMappingRepository.GetDeviceIndustryIdAsync(deviceCategoryId, deviceTypeId, deviceIndustryId);

            if (string.IsNullOrEmpty(locationStringHierarchyIds))
                return await _terminalMasterRepository.GetTerminalMasterModelWithoutLocationAreaId(deviceIndustryIds, deviceBrandId, companyId, groupId);
            else
            {
                var locationAreaIds = await _locationItemRepository.GetLocationAreaIdByStringHierarchy(locationStringHierarchyIds);
                return await _terminalMasterRepository.GetTerminalModelMasterOfAssignedDeviceAsync(locationAreaIds, deviceIndustryIds, deviceBrandId, companyId, groupId);
            }
        }

        public void UnassignDevice(int terminalId)
        {
            var terminal = GetById(terminalId);

            if (terminal.AssignedDeviceIndustryId == null || terminal.AssignedLocationAreaId == null)
                throw new ArgumentException("This Device not assign to any Location Area yet!");

            terminal.IsAssignedDevice = false;
            terminal.AssignedDeviceIndustryId = null;
            terminal.AssignedLocationAreaId = null;

            Update(terminal);
        }

        public List<TerminalMaster> GetTerminalForPaxStore(Expression<Func<TerminalMaster, bool>> whereClause = null)
        {
            var terminalMaster = _terminalMasterRepository.GetTerminalForPaxStore(whereClause);
            terminalMaster = _languageExecuteService.GetLanguage(terminalMaster).ToList();

            return terminalMaster;
        }

        public async Task<List<TerminalMaster>> GetTerminalByCompanyAndAccessStatus(int companyId)
        {
            using (var context = Repo.GetContext())
            {
                var obj = context.TerminalMaster
                    .Include(b => b.IoTDeviceCompanyAssigneds)
                    .Include(b => b.IoTDeviceAssociatedStatus)
                    .Where(b => b.IsAssignedDevice
                         && b.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD
                         && b.TerminalServiceDetail == companyId
                         && b.TerminalSerialNumber != null);

                return await obj.ToListAsync();
            }
        }

        public async Task<List<TerminalMaster>> GetTerminalByCompany(int companyId)
        {
            using (var context = Repo.GetContext())
            {
                var obj = context.TerminalMaster
                    .Include(b => b.IoTDeviceCompanyAssigneds)
                    .Include(b => b.IoTDeviceAssociatedStatus)
                    .Where(b => b.IsAssignedDevice
                                 && b.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD
                                 && b.IoTDeviceCompanyAssigneds.Any(x => x.CompanyId == companyId));

                return await obj.ToListAsync();
            }
        }

        public async Task<List<TerminalMaster>> GetTerminalAssigneds()
        {
            using (var context = Repo.GetContext())
            {
                var obj = context.TerminalMaster
                    .Include(b => b.IoTDeviceCompanyAssigneds)
                    .Include(b => b.IoTDeviceAssociatedStatus)
                    .Where(b => b.IsAssignedDevice && b.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD);

                return await obj.ToListAsync();
            }
        }

        public async Task<int> GetTerminalIdBySerialNumber(string serialNumber)
        {
            using (var context = Repo.GetContext())
            {
                var obj = context.TerminalMaster
                    .Where(o => o.TerminalSerialNumber == serialNumber && o.IsStatus != MMS.Core.CoreUTI.Constants.DELETE_RECORD).Select(o => o.Id);

                return await obj.FirstOrDefaultAsync();
            }
        }

        #region Copy merchant terminal
        private static async Task<MerchantTerminal> LoadMerchantTerminalToRemoveAsync(MMSContext context, int merchantTerminalId)
        {
            return await context.MerchantTerminal
                .Where(o => o.Id == merchantTerminalId)
                .Include(mt => mt.SmsReceipt)
                .Include(mt => mt.EmailReceipt)
                .Include(mt => mt.PreEmvSurcharge)
                .Include(mt => mt.SecurityLevels)
                .Include(mt => mt.Tips)
                .Include(mt => mt.SalesTypes)
                .Include(mt => mt.CardLimits)
                .Include(mt => mt.DebitCardsAccepted)
                .Include(mt => mt.CreditCardsAccepted)
                .Include(mt => mt.CalculatePay)
                .Include(mt => mt.Pending)
                .Include(mt => mt.DefaultTransaction)
                .Include(mt => mt.Batches)
                .Include(mt => mt.MerchantReport)
                .Include(mt => mt.EOVMerchantDebitCard)
                .Include(mt => mt.EOVMerchantCreditCard)
                .Include(mt => mt.MerchantCreditCardsAccepted)
                    .ThenInclude(mca => mca.Limits)
                .Include(mt => mt.MerchantCreditCardsAccepted)
                    .ThenInclude(mca => mca.ManualCardNotPresent)
                .Include(mt => mt.MerchantCreditCardsAccepted)
                    .ThenInclude(mca => mca.ManualCardPresent)
                .Include(mt => mt.MerchantCreditCardsAccepted)
                    .ThenInclude(mca => mca.ManualMailOrder)
                .Include(mt => mt.MerchantCreditCardsAccepted)
                    .ThenInclude(mca => mca.ManualPhoneOrder)
                .Include(mt => mt.MerchantCreditCardsAccepted)
                    .ThenInclude(mca => mca.CashOut)
                .Include(mt => mt.MerchantCreditCardsAccepted)
                    .ThenInclude(mca => mca.BankCashOut)
                .Include(mt => mt.MerchantCreditCardsAccepted)
                    .ThenInclude(mca => mca.PurchaseProof)
                .Include(mt => mt.MerchantCreditCardsAccepted)
                    .ThenInclude(mca => mca.MerchantDccPayment)
                        .ThenInclude(mdp => mdp.ProofId)
                .Include(mt => mt.MerchantDebitCardsAccepted)
                    .ThenInclude(mda => mda.Limits)
                .Include(mt => mt.MerchantDebitCardsAccepted)
                    .ThenInclude(mda => mda.CashOut)
                .Include(mt => mt.MerchantDebitCardsAccepted)
                    .ThenInclude(mda => mda.MerchantDccPayment)
                        .ThenInclude(mdp => mdp.ProofId)
                .Include(mt => mt.MerchantRewardsCardsAccepted)
                    .ThenInclude(mra => mra.Limits)
                .Include(mt => mt.MerchantRewardsCardsAccepted)
                    .ThenInclude(mra => mra.CashOut)
                .Include(mt => mt.MerchantRewardsCardsAccepted)
                    .ThenInclude(mra => mra.CardManual)
                .Include(mt => mt.MerchantGiftCardsAccepted)
                    .ThenInclude(mga => mga.Limits)
                .Include(mt => mt.MerchantGiftCardsAccepted)
                    .ThenInclude(mga => mga.CashOut)
                .Include(mt => mt.MerchantGiftCardsAccepted)
                    .ThenInclude(mga => mga.CardManual)
                .Include(mt => mt.MerchantCheckCardsAccepted)
                    .ThenInclude(mca => mca.Limits)
                .Include(mt => mt.MerchantCheckCardsAccepted)
                    .ThenInclude(mca => mca.CashOut)
                .Include(mt => mt.MerchantCheckCardsAccepted)
                    .ThenInclude(mca => mca.SaleID)
                .Include(mt => mt.Charities)
                .Include(mt => mt.MerchantCharities)
                .Include(mt => mt.MerchantProcessors)
                .Include(mt => mt.MerchantUsers)
                .Include(mt => mt.FunctionTypes)
                .Include(mt => mt.PaymentTenderTypes)
                .Include(mt => mt.DccCards)
                .Include(mt => mt.ServiceItems)
                .Include(mt => mt.SurveyItems)
                .Include(mt => mt.Surcharges)
                .Include(mt => mt.SurchargeRules)
                .Include(mt => mt.MOTOCards)
                .Include(mt => mt.EOVCardLimits)
                .Include(mt => mt.MerchantCash)
                .Include(mt => mt.CashOutFees)
                .Include(mt => mt.StoreForwards)
                .Include(mt => mt.Setups)
                .Include(mt => mt.ManualCards)
                .Include(mt => mt.ProcessorMerchants)
                    .ThenInclude(pm => pm.ProcessorMerchantSetup)
                .Include(mt => mt.ProcessorMerchants)
                    .ThenInclude(pm => pm.ProcessorMerchantSettleBatchSetup)
                .Include(mt => mt.PaymentModes)
                    .ThenInclude(pm => pm.PaymentModeOption)
                        .ThenInclude(pmo => pmo.Adverts)
                .Include(mt => mt.PaymentModes)
                    .ThenInclude(pm => pm.PaymentModeOption)
                        .ThenInclude(pmo => pmo.References)
                .Include(mt => mt.PaymentModes)
                    .ThenInclude(pm => pm.PaymentModeOption)
                        .ThenInclude(pmo => pmo.ProofId)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.HeaderLine1)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.HeaderLine2)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.HeaderLine3)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.HeaderLine4)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.HeaderLine5)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.HeaderLine6)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.FooterLine1)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.FooterLine2)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.FooterLine3)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.FooterLine4)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.FooterLine5)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.FooterLine6)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.ReturnLine1)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.ReturnLine2)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.ReturnLine3)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.ReturnLine4)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.ReturnLine5)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.ReturnLine6)
                .AsSplitQuery()
                .FirstOrDefaultAsync();
        }

        /// <summary>
        /// Discards all pending changes for a MerchantTerminal and its related entities asynchronously.
        /// </summary>
        /// <param name="merchantTerminalId">The ID of the MerchantTerminal to reset.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        public async Task DiscardMerchantAsync(int merchantTerminalId)
        {
            var context = Repo.GetContext(); // Ensure context is disposed properly

            Log.Information("DiscardMerchant: {MerchantTerminalId} - Before change tracker", merchantTerminalId);
            context.PrintChangeTracker(); // Pass logger for consistency

            await context.DiscardChangesForEntityAndRelatedAsync(
                typeof(MerchantTerminal).FullName,
                merchantTerminalId
            ).ConfigureAwait(false);

            Log.Information("DiscardMerchant: {MerchantTerminalId} - After change tracker", merchantTerminalId);
            context.PrintChangeTracker();
        }

        public async Task<MerchantTerminal> GetMerchantTerminalForCopy(int merchantTerminalId)
        {
            using var context = Repo.GetContext();

            var merchantTerminal = await context.MerchantTerminal.AsNoTracking()
                .Where(mt => mt.Id == merchantTerminalId && mt.IsStatus == Constants.NOTCHANGE_RECORD)
                .Include(mt => mt.SmsReceipt)
                .Include(mt => mt.EmailReceipt)
                .Include(mt => mt.PreEmvSurcharge)
                .Include(mt => mt.SecurityLevels)
                .Include(mt => mt.Tips)
                .Include(mt => mt.SalesTypes)
                .Include(mt => mt.CardLimits)
                .Include(mt => mt.DebitCardsAccepted)
                .Include(mt => mt.CreditCardsAccepted)
                .Include(mt => mt.CalculatePay)
                .Include(mt => mt.Pending)
                .Include(mt => mt.DefaultTransaction)
                .Include(mt => mt.MerchantCreditCardsAccepted)
                    .ThenInclude(mca => mca.Limits)
                .Include(mt => mt.MerchantCreditCardsAccepted)
                    .ThenInclude(mca => mca.ManualCardNotPresent)
                .Include(mt => mt.MerchantCreditCardsAccepted)
                    .ThenInclude(mca => mca.ManualCardPresent)
                .Include(mt => mt.MerchantCreditCardsAccepted)
                    .ThenInclude(mca => mca.ManualMailOrder)
                .Include(mt => mt.MerchantCreditCardsAccepted)
                    .ThenInclude(mca => mca.ManualPhoneOrder)
                .Include(mt => mt.MerchantCreditCardsAccepted)
                    .ThenInclude(mca => mca.CashOut)
                .Include(mt => mt.MerchantCreditCardsAccepted)
                    .ThenInclude(mca => mca.BankCashOut)
                .Include(mt => mt.MerchantCreditCardsAccepted)
                    .ThenInclude(mca => mca.PurchaseProof)
                .Include(mt => mt.MerchantCreditCardsAccepted)
                    .ThenInclude(mca => mca.MerchantDccPayment)
                        .ThenInclude(mca => mca.ProofId)
                .Include(mt => mt.MerchantDebitCardsAccepted)
                    .ThenInclude(mda => mda.Limits)
                .Include(mt => mt.MerchantDebitCardsAccepted)
                    .ThenInclude(mda => mda.CashOut)
                .Include(mt => mt.MerchantDebitCardsAccepted)
                    .ThenInclude(mca => mca.MerchantDccPayment)
                        .ThenInclude(mdp => mdp.ProofId)
                .Include(mt => mt.MerchantRewardsCardsAccepted)
                    .ThenInclude(mra => mra.Limits)
                .Include(mt => mt.MerchantRewardsCardsAccepted)
                    .ThenInclude(mra => mra.CashOut)
                .Include(mt => mt.MerchantRewardsCardsAccepted)
                    .ThenInclude(mra => mra.CardManual)
                .Include(mt => mt.MerchantGiftCardsAccepted)
                    .ThenInclude(mra => mra.Limits)
                .Include(mt => mt.MerchantGiftCardsAccepted)
                    .ThenInclude(mra => mra.CashOut)
                .Include(mt => mt.MerchantGiftCardsAccepted)
                    .ThenInclude(mra => mra.CardManual)
                .Include(mt => mt.MerchantCheckCardsAccepted)
                    .ThenInclude(mca => mca.Limits)
                .Include(mt => mt.MerchantCheckCardsAccepted)
                    .ThenInclude(mca => mca.CashOut)
                .Include(mt => mt.MerchantCheckCardsAccepted)
                    .ThenInclude(mca => mca.SaleID)
                .Include(mt => mt.Charities.Where(si => si.IsStatus == Constants.NOTCHANGE_RECORD))
                    .ThenInclude(c => c.Amounts.Where(si => si.IsStatus == Constants.NOTCHANGE_RECORD))
                .Include(mt => mt.MerchantCharities)
                .Include(mt => mt.MerchantProcessors.Where(si => si.IsStatus == Constants.NOTCHANGE_RECORD))
                .Include(mt => mt.FunctionTypes)
                .Include(mt => mt.PaymentTenderTypes)
                .Include(mt => mt.DccCards)
                .Include(mt => mt.ServiceItems.Where(si => si.IsStatus == Constants.NOTCHANGE_RECORD))
                .Include(mt => mt.SurveyItems.Where(si => si.IsStatus == Constants.NOTCHANGE_RECORD))
                .Include(mt => mt.Surcharges)
                .Include(mt => mt.SurchargeRules.Where(sr => sr.IsStatus == Constants.NOTCHANGE_RECORD))
                    .ThenInclude(sr => sr.AmountRanges.Where(ar => ar.IsStatus == Constants.NOTCHANGE_RECORD))
                .Include(mt => mt.MOTOCards)
                .Include(mt => mt.EOVCardLimits)
                .Include(mt => mt.MerchantCash)
                .Include(mt => mt.CashOutFees)
                .Include(mt => mt.StoreForwards)
                    .ThenInclude(sf => sf.MerchantSFFloorLimit)
                .Include(mt => mt.Setups)
                .Include(mt => mt.ManualCards)
                .Include(mt => mt.MerchantReport)
                .Include(mt => mt.EOVMerchantDebitCard)
                .Include(mt => mt.EOVMerchantCreditCard)
                .Include(mt => mt.ProcessorMerchants)
                    .ThenInclude(pm => pm.ProcessorMerchantSetup)
                .Include(mt => mt.ProcessorMerchants)
                    .ThenInclude(pm => pm.ProcessorMerchantSettleBatchSetup)
                .Include(mt => mt.PaymentModes.Where(pm => pm.IsStatus == Constants.NOTCHANGE_RECORD))
                    .ThenInclude(pm => pm.PaymentModeOption)
                        .ThenInclude(pmo => pmo.Adverts)
                .Include(mt => mt.PaymentModes.Where(pm => pm.IsStatus == Constants.NOTCHANGE_RECORD))
                    .ThenInclude(pm => pm.PaymentModeOption)
                        .ThenInclude(pmo => pmo.References)
                .Include(mt => mt.PaymentModes.Where(pm => pm.IsStatus == Constants.NOTCHANGE_RECORD))
                    .ThenInclude(pm => pm.PaymentModeOption)
                        .ThenInclude(pmo => pmo.ProofId)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.HeaderLine1)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.HeaderLine2)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.HeaderLine3)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.HeaderLine4)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.HeaderLine5)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.HeaderLine6)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.FooterLine1)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.FooterLine2)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.FooterLine3)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.FooterLine4)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.FooterLine5)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.FooterLine6)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.ReturnLine1)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.ReturnLine2)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.ReturnLine3)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.ReturnLine4)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.ReturnLine5)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.ReturnLine6)
                .AsSplitQuery()
                .FirstOrDefaultAsync();

            return merchantTerminal;
        }

        public async Task<MerchantTerminal> CopyMerchantTerminal(int merchantTerminalId, MerchantTerminal merchantTerminalTemplate)
        {
            var strategy = Repo.GetContext().Database.CreateExecutionStrategy();
            MerchantTerminal merchantTerminal = null;

            await strategy.ExecuteAsync(async () =>
            {
                using var context = Repo.GetContext();
                using var transaction = await context.Database.BeginTransactionAsync();

                try
                {
                    merchantTerminal = await LoadMerchantTerminalAsync(context, merchantTerminalId);

                    if (merchantTerminal == null) return;

                    // do not copy merchant details so need some temp variables to keep
                    var tempMerchantId = merchantTerminal.MerchantId;
                    var tempLszTerminalIDNumber = merchantTerminal.lszTerminalIDNumber;
                    var tempLszEmailAddress = merchantTerminal.lszEmailAddress;
                    var tempLszContact = merchantTerminal.lszContact;
                    var tempLszFirstPhone = merchantTerminal.lszFirstPhone;
                    var tempLszLastPhone = merchantTerminal.lszLastPhone;
                    var tempLszFirstCell = merchantTerminal.lszFirstCell;
                    var tempLszLastCell = merchantTerminal.lszLastCell;
                    var tempImage = merchantTerminal.Image;
                    var tempLszMerchantName = merchantTerminal.lszMerchantName;
                    var tempTerminalId = merchantTerminal.TerminalId;

                    var merchantTerminalsToRemove = await LoadMerchantTerminalToRemoveAsync(context, merchantTerminalId);
                    await DeleteAllMerchantTerminalDependencies(context, merchantTerminalsToRemove);

                    _mapper.Map(merchantTerminalTemplate, merchantTerminal);

                    merchantTerminal.MerchantId = tempMerchantId;
                    merchantTerminal.lszTerminalIDNumber = tempLszTerminalIDNumber;
                    merchantTerminal.lszEmailAddress = tempLszEmailAddress;
                    merchantTerminal.lszContact = tempLszContact;
                    merchantTerminal.lszFirstPhone = tempLszFirstPhone;
                    merchantTerminal.lszLastPhone = tempLszLastPhone;
                    merchantTerminal.lszFirstCell = tempLszFirstCell;
                    merchantTerminal.lszLastCell = tempLszLastCell;
                    merchantTerminal.Image = tempImage;
                    merchantTerminal.lszMerchantName = tempLszMerchantName;
                    merchantTerminal.TerminalId = tempTerminalId;
                    merchantTerminal.Modified = DateTime.Now;

                    context.Update(merchantTerminal);
                    await context.SaveChangesAsync();

                    // use mapping ids to update id correctly after copy merchant terminal in table Charity, MerchantCharity, MerchantProcessor
                    CoreUtils.CharityIdMap.TryGetValue($"{merchantTerminalTemplate.TerminalId}-{tempTerminalId}", out List<KeyValuePair<int, int>> charityIds);
                    if (charityIds != null && charityIds.Count != 0)
                    {
                        foreach (var child in merchantTerminal.Charities)
                        {
                            var parentMapping = charityIds.Find(m => m.Key == child.ParentCharityId);
                            child.ParentCharityId = parentMapping.Value;
                            child.TerminalId = tempTerminalId;
                        }

                        foreach (var child in merchantTerminal.MerchantCharities)
                        {
                            var parentMapping = charityIds.Find(m => m.Key == child.CharityId);
                            child.CharityId = parentMapping.Value;
                        }
                    }

                    CoreUtils.HostInterfaceIdMap.TryGetValue($"{merchantTerminalTemplate.TerminalId}-{tempTerminalId}", out List<KeyValuePair<int, int>> hostInterfaceIds);
                    if (hostInterfaceIds != null && hostInterfaceIds.Count != 0)
                    {
                        foreach (var child in merchantTerminal.MerchantProcessors)
                        {
                            var parentMapping = hostInterfaceIds.Find(m => m.Key == child.IdProcessor);
                            child.IdProcessor = parentMapping.Value;
                        }
                    }

                    context.Update(merchantTerminal);
                    await context.SaveChangesAsync();

                    await transaction.CommitAsync();
                }
                catch
                {
                    await transaction.RollbackAsync();
                    throw;
                }
            });

            return merchantTerminal;
        }

        private static async Task<MerchantTerminal> LoadMerchantTerminalAsync(MMSContext context, int merchantTerminalId)
        {
            return await context.MerchantTerminal
                .Where(o => o.Id == merchantTerminalId && o.IsStatus != Constants.DELETE_RECORD)
                .FirstOrDefaultAsync();
        }
        #endregion

        public async Task<TerminalMaster> GetTerminalMasterForCopy(int terminalMasterId, bool includeMerchantTerminal = true, bool isBaseToBase = false)
        {
            var context = Repo.GetContext();

            // Load TerminalMaster with essential includes using AsSplitQuery for optimization
            var query = context.TerminalMaster.AsNoTracking()
                .Where(o => o.Id == terminalMasterId && o.IsStatus == Constants.NOTCHANGE_RECORD)
                .Include(o => o.Support)
                .Include(o => o.Sound)
                .Include(o => o.FallBack)
                .Include(o => o.CountryTerminal)
                    .ThenInclude(o => o.alCoinDenominations)
                .Include(o => o.CountryTerminal)
                    .ThenInclude(o => o.alNotesDenominations)
                .Include(o => o.TimeZoneMaster)
                .Include(o => o.TerminalSetup)
                .Include(o => o.TerminalCloud)
                .Include(o => o.PosIntegration)
                .Include(o => o.CloudPosIntegration)
                .Include(o => o.TerminalStyle)
                .Include(o => o.TerminalRebootSchedule)
                .Include(o => o.MerchantCardFallbacks)
                .Include(o => o.TerminalSecurityAccesses.Where(tsa => tsa.IsStatus == Constants.NOTCHANGE_RECORD))
                .Include(o => o.UsersAccessTerminal.Where(uat => uat.IsStatus == Constants.NOTCHANGE_RECORD))
                .Include(o => o.UserTerminals.Where(ut => ut.IsStatus == Constants.NOTCHANGE_RECORD))
                    .ThenInclude(o => o.MerchantUsers.Where(mu => mu.IsStatus == Constants.NOTCHANGE_RECORD))
                .Include(o => o.HostInterfaces.Where(ct => ct.IsStatus == Constants.NOTCHANGE_RECORD))
                    .ThenInclude(o => o.MerchantProcessors.Where(mp => mp.IsStatus == Constants.NOTCHANGE_RECORD))
                .Include(o => o.HostInterfaces.Where(h => h.IsStatus == Constants.NOTCHANGE_RECORD))
                    .ThenInclude(h => h.Batches.Where(b => b.IsStatus == Constants.NOTCHANGE_RECORD))
                .Include(o => o.CountryCardSetupTerminals.Where(ct => ct.IsStatus == Constants.NOTCHANGE_RECORD))
                .AsQueryable();


            if (includeMerchantTerminal)
            {
                query = query.Include(o => o.CharityTerminals.Where(ct => ct.IsStatus == Constants.NOTCHANGE_RECORD))
                                .ThenInclude(o => o.MerchantCharities.Where(mc => mc.IsStatus == Constants.NOTCHANGE_RECORD))
                            .Include(o => o.CharityTerminals.Where(ct => ct.IsStatus == Constants.NOTCHANGE_RECORD))
                                .ThenInclude(o => o.Amounts.Where(mc => mc.IsStatus == Constants.NOTCHANGE_RECORD));
            }
            else
            {
                query = query.Include(o => o.CharityTerminals.Where(ct => ct.IsStatus == Constants.NOTCHANGE_RECORD && ct.MerchantTerminalId == null))
                                .ThenInclude(o => o.Amounts.Where(mc => mc.IsStatus == Constants.NOTCHANGE_RECORD));
            }

            var terminalMasterEntity = await query.AsSplitQuery().FirstOrDefaultAsync();

            if (terminalMasterEntity == null)
            {
                return null;
            }

            if (includeMerchantTerminal)
            {
                // Load MerchantTerminals separately with their includes
                var merchantTerminalsQuery = context.MerchantTerminal.AsNoTracking()
                    .Where(mt => mt.TerminalId == terminalMasterId && mt.IsStatus == Constants.NOTCHANGE_RECORD);

                if (isBaseToBase)
                {
                    merchantTerminalsQuery = merchantTerminalsQuery.Include(mt => mt.MerchantMaster);
                }

                merchantTerminalsQuery = merchantTerminalsQuery
                    .Include(mt => mt.SmsReceipt)
                    .Include(mt => mt.EmailReceipt)
                    .Include(mt => mt.PreEmvSurcharge)
                    .Include(mt => mt.SecurityLevels)
                    .Include(mt => mt.Tips)
                    .Include(mt => mt.SalesTypes)
                    .Include(mt => mt.CardLimits)
                    .Include(mt => mt.DebitCardsAccepted)
                    .Include(mt => mt.CreditCardsAccepted)
                    .Include(mt => mt.CalculatePay)
                    .Include(mt => mt.Pending)
                    .Include(mt => mt.DefaultTransaction)
                    .Include(mt => mt.MerchantCreditCardsAccepted)
                        .ThenInclude(mca => mca.Limits)
                    .Include(mt => mt.MerchantCreditCardsAccepted)
                        .ThenInclude(mca => mca.ManualCardNotPresent)
                    .Include(mt => mt.MerchantCreditCardsAccepted)
                        .ThenInclude(mca => mca.ManualCardPresent)
                    .Include(mt => mt.MerchantCreditCardsAccepted)
                        .ThenInclude(mca => mca.ManualMailOrder)
                    .Include(mt => mt.MerchantCreditCardsAccepted)
                        .ThenInclude(mca => mca.ManualPhoneOrder)
                    .Include(mt => mt.MerchantCreditCardsAccepted)
                        .ThenInclude(mca => mca.CashOut)
                    .Include(mt => mt.MerchantCreditCardsAccepted)
                        .ThenInclude(mca => mca.BankCashOut)
                    .Include(mt => mt.MerchantCreditCardsAccepted)
                        .ThenInclude(mca => mca.PurchaseProof)
                    .Include(mt => mt.MerchantCreditCardsAccepted)
                        .ThenInclude(mca => mca.MerchantDccPayment)
                            .ThenInclude(mca => mca.ProofId)
                    .Include(mt => mt.MerchantDebitCardsAccepted)
                        .ThenInclude(mda => mda.Limits)
                    .Include(mt => mt.MerchantDebitCardsAccepted)
                        .ThenInclude(mda => mda.CashOut)
                    .Include(mt => mt.MerchantDebitCardsAccepted)
                        .ThenInclude(mca => mca.MerchantDccPayment)
                            .ThenInclude(mdp => mdp.ProofId)
                    .Include(mt => mt.MerchantRewardsCardsAccepted)
                        .ThenInclude(mra => mra.Limits)
                    .Include(mt => mt.MerchantRewardsCardsAccepted)
                        .ThenInclude(mra => mra.CashOut)
                    .Include(mt => mt.MerchantRewardsCardsAccepted)
                        .ThenInclude(mra => mra.CardManual)
                    .Include(mt => mt.MerchantGiftCardsAccepted)
                        .ThenInclude(mra => mra.Limits)
                    .Include(mt => mt.MerchantGiftCardsAccepted)
                        .ThenInclude(mra => mra.CashOut)
                    .Include(mt => mt.MerchantGiftCardsAccepted)
                        .ThenInclude(mra => mra.CardManual)
                    .Include(mt => mt.MerchantCheckCardsAccepted)
                        .ThenInclude(mca => mca.Limits)
                    .Include(mt => mt.MerchantCheckCardsAccepted)
                        .ThenInclude(mca => mca.CashOut)
                    .Include(mt => mt.MerchantCheckCardsAccepted)
                        .ThenInclude(mca => mca.SaleID)
                    .Include(mt => mt.FunctionTypes)
                    .Include(mt => mt.PaymentTenderTypes)
                    .Include(mt => mt.DccCards)
                    .Include(mt => mt.ServiceItems.Where(si => si.IsStatus == Constants.NOTCHANGE_RECORD))
                    .Include(mt => mt.SurveyItems.Where(si => si.IsStatus == Constants.NOTCHANGE_RECORD))
                    .Include(mt => mt.Surcharges)
                    .Include(mt => mt.SurchargeRules.Where(sr => sr.IsStatus == Constants.NOTCHANGE_RECORD))
                        .ThenInclude(sr => sr.AmountRanges.Where(ar => ar.IsStatus == Constants.NOTCHANGE_RECORD))
                    .Include(mt => mt.MOTOCards)
                    .Include(mt => mt.EOVCardLimits)
                    .Include(mt => mt.MerchantCash)
                    .Include(mt => mt.CashOutFees)
                    .Include(mt => mt.StoreForwards)
                        .ThenInclude(sf => sf.MerchantSFFloorLimit)
                    .Include(mt => mt.Setups)
                    .Include(mt => mt.ManualCards)
                    .Include(mt => mt.MerchantReport)
                    .Include(mt => mt.EOVMerchantDebitCard)
                    .Include(mt => mt.EOVMerchantCreditCard)
                    .Include(mt => mt.ProcessorMerchants)
                        .ThenInclude(pm => pm.ProcessorMerchantSetup)
                    .Include(mt => mt.ProcessorMerchants)
                        .ThenInclude(pm => pm.ProcessorMerchantSettleBatchSetup)
                    .Include(mt => mt.PaymentModes.Where(pm => pm.IsStatus == Constants.NOTCHANGE_RECORD))
                        .ThenInclude(pm => pm.PaymentModeOption)
                            .ThenInclude(pmo => pmo.Adverts)
                    .Include(mt => mt.PaymentModes.Where(pm => pm.IsStatus == Constants.NOTCHANGE_RECORD))
                        .ThenInclude(pm => pm.PaymentModeOption)
                            .ThenInclude(pmo => pmo.References)
                    .Include(mt => mt.PaymentModes.Where(pm => pm.IsStatus == Constants.NOTCHANGE_RECORD))
                        .ThenInclude(pm => pm.PaymentModeOption)
                            .ThenInclude(pmo => pmo.ProofId)
                    .Include(mt => mt.PrintMessage)
                        .ThenInclude(pm => pm.HeaderLine1)
                    .Include(mt => mt.PrintMessage)
                        .ThenInclude(pm => pm.HeaderLine2)
                    .Include(mt => mt.PrintMessage)
                        .ThenInclude(pm => pm.HeaderLine3)
                    .Include(mt => mt.PrintMessage)
                        .ThenInclude(pm => pm.HeaderLine4)
                    .Include(mt => mt.PrintMessage)
                        .ThenInclude(pm => pm.HeaderLine5)
                    .Include(mt => mt.PrintMessage)
                        .ThenInclude(pm => pm.HeaderLine6)
                    .Include(mt => mt.PrintMessage)
                        .ThenInclude(pm => pm.FooterLine1)
                    .Include(mt => mt.PrintMessage)
                        .ThenInclude(pm => pm.FooterLine2)
                    .Include(mt => mt.PrintMessage)
                        .ThenInclude(pm => pm.FooterLine3)
                    .Include(mt => mt.PrintMessage)
                        .ThenInclude(pm => pm.FooterLine4)
                    .Include(mt => mt.PrintMessage)
                        .ThenInclude(pm => pm.FooterLine5)
                    .Include(mt => mt.PrintMessage)
                        .ThenInclude(pm => pm.FooterLine6)
                    .Include(mt => mt.PrintMessage)
                        .ThenInclude(pm => pm.ReturnLine1)
                    .Include(mt => mt.PrintMessage)
                        .ThenInclude(pm => pm.ReturnLine2)
                    .Include(mt => mt.PrintMessage)
                        .ThenInclude(pm => pm.ReturnLine3)
                    .Include(mt => mt.PrintMessage)
                        .ThenInclude(pm => pm.ReturnLine4)
                    .Include(mt => mt.PrintMessage)
                        .ThenInclude(pm => pm.ReturnLine5)
                    .Include(mt => mt.PrintMessage)
                        .ThenInclude(pm => pm.ReturnLine6);

                var merchantTerminals = await merchantTerminalsQuery
                    .AsSplitQuery()
                    .ToListAsync();

                terminalMasterEntity.MerchantTerminals = merchantTerminals;
            }

            return terminalMasterEntity;
        }

        public async Task<TerminalMaster> CopyTemplate(int terminalMasterId, TerminalMaster template, bool includeMerchantTerminal = true)
        {
            var strategy = Repo.GetContext().Database.CreateExecutionStrategy();
            TerminalMaster terminalMaster = null;

            await strategy.ExecuteAsync(async () =>
            {
                var context = Repo.GetContext();
                using var transaction = await context.Database.BeginTransactionAsync();

                try
                {
                    terminalMaster = await LoadTerminalMasterAsync(context, terminalMasterId);
                    if (terminalMaster == null) return;

                    var merchantTerminalsToRemove = await LoadMerchantTerminalsToRemoveAsync(context, terminalMasterId);
                    terminalMaster.Modified = DateTime.Now;

                    await DeleteExistingMerchantTerminalsAsync(context, merchantTerminalsToRemove);

                    terminalMaster = MappingCopyFields(terminalMaster, template); // copy fields which has CopyFieldAttribute (sometime: string, int, bool...)

                    UpdateOneToOneRelationships(context, terminalMaster, template);
                    ReplaceCollections(context, terminalMaster, template, includeMerchantTerminal);

                    context.Update(terminalMaster);
                    await context.SaveChangesAsync();

                    if (includeMerchantTerminal)
                    {
                        await HandleMerchantSaleTypePaymentModeMappingAsync(context, terminalMaster, template);
                        await UpdateRelatedEntitiesWithNewIdsAsync(terminalMaster, template);

                        context.Update(terminalMaster);
                        await context.SaveChangesAsync();
                    }

                    await transaction.CommitAsync();
                }
                catch
                {
                    await transaction.RollbackAsync();
                    throw;
                }
            });

            return terminalMaster;
        }

        private static async Task<TerminalMaster> LoadTerminalMasterAsync(MMSContext context, int terminalMasterId)
        {
            return await context.TerminalMaster
                .Where(o => o.Id == terminalMasterId && o.IsStatus == Constants.NOTCHANGE_RECORD)
                .Include(o => o.Support)
                .Include(o => o.Sound)
                .Include(o => o.FallBack)
                .Include(o => o.CountryTerminal)
                .Include(o => o.TimeZoneMaster)
                .Include(o => o.TerminalSetup)
                .Include(o => o.TerminalCloud)
                .Include(o => o.PosIntegration)
                .Include(o => o.CloudPosIntegration)
                .Include(o => o.TerminalStyle)
                .Include(o => o.TerminalRebootSchedule)
                .Include(o => o.TerminalSecurityAccesses)
                .Include(o => o.UserTerminals)
                .Include(o => o.UsersAccessTerminal)
                .Include(o => o.CharityTerminals)
                .Include(o => o.HostInterfaces)
                    .ThenInclude(h => h.Batches)
                .Include(o => o.CountryCardSetupTerminals)
                .FirstOrDefaultAsync();
        }

        private static async Task<List<MerchantTerminal>> LoadMerchantTerminalsToRemoveAsync(MMSContext context, int terminalMasterId)
        {
            return await context.MerchantTerminal
                .Where(o => o.TerminalId == terminalMasterId)
                .Include(mt => mt.MerchantReport)
                .Include(mt => mt.EOVMerchantDebitCard)
                .Include(mt => mt.EOVMerchantCreditCard)
                .Include(mt => mt.MerchantCreditCardsAccepted)
                    .ThenInclude(mca => mca.Limits)
                .Include(mt => mt.MerchantCreditCardsAccepted)
                    .ThenInclude(mca => mca.ManualCardNotPresent)
                .Include(mt => mt.MerchantCreditCardsAccepted)
                    .ThenInclude(mca => mca.ManualCardPresent)
                .Include(mt => mt.MerchantCreditCardsAccepted)
                    .ThenInclude(mca => mca.ManualMailOrder)
                .Include(mt => mt.MerchantCreditCardsAccepted)
                    .ThenInclude(mca => mca.ManualPhoneOrder)
                .Include(mt => mt.MerchantCreditCardsAccepted)
                    .ThenInclude(mca => mca.CashOut)
                .Include(mt => mt.MerchantCreditCardsAccepted)
                    .ThenInclude(mca => mca.BankCashOut)
                .Include(mt => mt.MerchantCreditCardsAccepted)
                    .ThenInclude(mca => mca.PurchaseProof)
                .Include(mt => mt.MerchantCreditCardsAccepted)
                    .ThenInclude(mca => mca.MerchantDccPayment)
                        .ThenInclude(mdp => mdp.ProofId)
                .Include(mt => mt.MerchantDebitCardsAccepted)
                    .ThenInclude(mda => mda.Limits)
                .Include(mt => mt.MerchantDebitCardsAccepted)
                    .ThenInclude(mda => mda.CashOut)
                .Include(mt => mt.MerchantDebitCardsAccepted)
                    .ThenInclude(mda => mda.MerchantDccPayment)
                        .ThenInclude(mdp => mdp.ProofId)
                .Include(mt => mt.MerchantRewardsCardsAccepted)
                    .ThenInclude(mra => mra.Limits)
                .Include(mt => mt.MerchantRewardsCardsAccepted)
                    .ThenInclude(mra => mra.CashOut)
                .Include(mt => mt.MerchantRewardsCardsAccepted)
                    .ThenInclude(mra => mra.CardManual)
                .Include(mt => mt.MerchantGiftCardsAccepted)
                    .ThenInclude(mga => mga.Limits)
                .Include(mt => mt.MerchantGiftCardsAccepted)
                    .ThenInclude(mga => mga.CashOut)
                .Include(mt => mt.MerchantGiftCardsAccepted)
                    .ThenInclude(mga => mga.CardManual)
                .Include(mt => mt.MerchantCheckCardsAccepted)
                    .ThenInclude(mca => mca.Limits)
                .Include(mt => mt.MerchantCheckCardsAccepted)
                    .ThenInclude(mca => mca.CashOut)
                .Include(mt => mt.MerchantCheckCardsAccepted)
                    .ThenInclude(mca => mca.SaleID)
                .Include(mt => mt.ProcessorMerchants)
                    .ThenInclude(pm => pm.ProcessorMerchantSetup)
                .Include(mt => mt.ProcessorMerchants)
                    .ThenInclude(pm => pm.ProcessorMerchantSettleBatchSetup)
                .Include(mt => mt.PaymentModes)
                    .ThenInclude(pm => pm.PaymentModeOption)
                        .ThenInclude(pmo => pmo.Adverts)
                .Include(mt => mt.PaymentModes)
                    .ThenInclude(pm => pm.PaymentModeOption)
                        .ThenInclude(pmo => pmo.References)
                .Include(mt => mt.PaymentModes)
                    .ThenInclude(pm => pm.PaymentModeOption)
                        .ThenInclude(pmo => pmo.ProofId)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.HeaderLine1)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.HeaderLine2)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.HeaderLine3)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.HeaderLine4)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.HeaderLine5)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.HeaderLine6)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.FooterLine1)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.FooterLine2)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.FooterLine3)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.FooterLine4)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.FooterLine5)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.FooterLine6)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.ReturnLine1)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.ReturnLine2)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.ReturnLine3)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.ReturnLine4)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.ReturnLine5)
                .Include(mt => mt.PrintMessage)
                    .ThenInclude(pm => pm.ReturnLine6)
                .AsSplitQuery()
                .ToListAsync();
        }

        private static async Task DeleteAllMerchantTerminalDependencies(MMSContext context, MerchantTerminal merchantTerminal)
        {
            RemoveMerchantTerminalDependencies(context, merchantTerminal);

            RemoveEntitiesIfNotNullAndEmpty(context, merchantTerminal.MerchantProcessors);
            RemoveEntitiesIfNotNullAndEmpty(context, merchantTerminal.MerchantUsers);
            RemoveEntitiesIfNotNullAndEmpty(context, merchantTerminal.Batches);
            RemoveEntitiesIfNotNullAndEmpty(context, merchantTerminal.SecurityLevels);
            RemoveEntitiesIfNotNullAndEmpty(context, merchantTerminal.Tips);
            RemoveEntitiesIfNotNullAndEmpty(context, merchantTerminal.SalesTypes);
            RemoveEntitiesIfNotNullAndEmpty(context, merchantTerminal.DebitCardsAccepted);
            RemoveEntitiesIfNotNullAndEmpty(context, merchantTerminal.CreditCardsAccepted);
            RemoveEntitiesIfNotNullAndEmpty(context, merchantTerminal.Surcharges);
            RemoveEntitiesIfNotNullAndEmpty(context, merchantTerminal.SurchargeRules);
            RemoveEntitiesIfNotNullAndEmpty(context, merchantTerminal.EOVCardLimits);
            RemoveEntitiesIfNotNullAndEmpty(context, merchantTerminal.MerchantCash);
            RemoveEntitiesIfNotNullAndEmpty(context, merchantTerminal.CashOutFees);
            RemoveEntitiesIfNotNullAndEmpty(context, merchantTerminal.StoreForwards);
            RemoveEntitiesIfNotNullAndEmpty(context, merchantTerminal.Setups);
            RemoveEntitiesIfNotNullAndEmpty(context, merchantTerminal.ManualCards);
            RemoveEntitiesIfNotNullAndEmpty(context, merchantTerminal.Charities);
            RemoveEntitiesIfNotNullAndEmpty(context, merchantTerminal.SurveyItems);
            RemoveEntitiesIfNotNullAndEmpty(context, merchantTerminal.CardLimits);
            RemoveEntitiesIfNotNullAndEmpty(context, merchantTerminal.ServiceItems);
            RemoveEntitiesIfNotNullAndEmpty(context, merchantTerminal.FunctionTypes);
            RemoveEntitiesIfNotNullAndEmpty(context, merchantTerminal.PaymentTenderTypes);
            RemoveEntitiesIfNotNullAndEmpty(context, merchantTerminal.DccCards);
            RemoveEntityIfNotNull(context, merchantTerminal.DefaultTransaction);
            RemoveEntityIfNotNull(context, merchantTerminal.CalculatePay);
            RemoveEntityIfNotNull(context, merchantTerminal.Pending);
            RemoveEntityIfNotNull(context, merchantTerminal.PreEmvSurcharge);

            await Task.CompletedTask;
        }

        private static async Task DeleteExistingMerchantTerminalsAsync(MMSContext context, List<MerchantTerminal> merchantTerminals)
        {
            foreach (var merchantTerminal in merchantTerminals)
            {
                RemoveMerchantTerminalDependencies(context, merchantTerminal);
                context.Remove(merchantTerminal);
            }

            await Task.CompletedTask;
        }

        private static T MappingCopyFields<T>(T destination, T source) where T : BaseEntity
        {
            if (destination is null)
            {
                throw new ArgumentNullException(nameof(destination), "Destination object cannot be null.");
            }

            if (source is null)
            {
                throw new ArgumentNullException(nameof(source), "Source object cannot be null.");
            }

            var properties = typeof(T)
                .GetProperties(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance)
                .Where(p => Attribute.IsDefined(p, typeof(CopyFieldAttribute)) && p.CanWrite && p.CanRead);

            foreach (var prop in properties)
            {
                var value = prop.GetValue(source);
                prop.SetValue(destination, value);
            }

            return destination;
        }

        private static void RemoveMerchantTerminalDependencies(MMSContext context, MerchantTerminal merchantTerminal)
        {
            RemoveEntityIfNotNull(context, merchantTerminal.MerchantReport);
            RemoveEntityIfNotNull(context, merchantTerminal.EOVMerchantDebitCard);
            RemoveEntityIfNotNull(context, merchantTerminal.EOVMerchantCreditCard);

            RemoveCollectionDependencies(context, merchantTerminal.MerchantCreditCardsAccepted);
            RemoveCollectionDependencies(context, merchantTerminal.MerchantDebitCardsAccepted);
            RemoveCollectionDependencies(context, merchantTerminal.MerchantRewardsCardsAccepted);
            RemoveCollectionDependencies(context, merchantTerminal.MerchantGiftCardsAccepted);
            RemoveCollectionDependencies(context, merchantTerminal.MerchantCheckCardsAccepted);
            RemoveProcessorMerchants(context, merchantTerminal.ProcessorMerchants);
            RemovePaymentModes(context, merchantTerminal.PaymentModes);
            RemovePrintMessage(context, merchantTerminal.PrintMessage);
        }

        private static void RemoveEntityIfNotNull(MMSContext context, object entity)
        {
            if (entity != null)
            {
                context.Remove(entity);
            }
        }

        private static void RemoveEntitiesIfNotNullAndEmpty<T>(MMSContext context, IList<T> entities)
        {
            if (entities != null && entities.Count > 0)
            {
                foreach (var entity in entities)
                {
                    context.Remove(entity);
                }
            }
        }

        private static void RemoveCollectionDependencies<T>(MMSContext context, ICollection<T> collection) where T : class
        {
            if (collection == null) return;

            foreach (var item in collection.ToList())
            {
                RemoveChildEntities(context, item);
                context.Remove(item);
            }
        }

        private static void RemoveChildEntities(MMSContext context, dynamic item)
        {
            // Use the DynamicPropertyHelper to safely check and remove properties
            if (DynamicPropertyHelper.HasProperty(item, "Limits"))
            {
                if (item.Limits != null) context.Remove(item.Limits);
            }

            DynamicPropertyHelper.SafeRemoveIfExists(context, item, "ManualCardNotPresent");
            DynamicPropertyHelper.SafeRemoveIfExists(context, item, "ManualCardPresent");
            DynamicPropertyHelper.SafeRemoveIfExists(context, item, "ManualMailOrder");
            DynamicPropertyHelper.SafeRemoveIfExists(context, item, "ManualPhoneOrder");
            DynamicPropertyHelper.SafeRemoveIfExists(context, item, "CashOut");
            DynamicPropertyHelper.SafeRemoveIfExists(context, item, "BankCashOut");
            DynamicPropertyHelper.SafeRemoveIfExists(context, item, "PurchaseProof");

            if (DynamicPropertyHelper.HasProperty(item, "MerchantDccPayment"))
            {
                var merchantDccPayment = DynamicPropertyHelper.GetPropertyValueOrDefault<object>(item, "MerchantDccPayment");
                if (merchantDccPayment != null)
                {
                    if (DynamicPropertyHelper.HasProperty(merchantDccPayment, "ProofId"))
                    {
                        var proofId = DynamicPropertyHelper.GetPropertyValueOrDefault<object>(merchantDccPayment, "ProofId");
                        if (proofId != null)
                            context.Remove(proofId);
                    }
                    context.Remove(merchantDccPayment);
                }
            }
        }

        private static void RemoveProcessorMerchants(MMSContext context, ICollection<ProcessorMerchant> processorMerchants)
        {
            if (processorMerchants == null) return;

            foreach (var pm in processorMerchants.ToList())
            {
                RemoveEntityIfNotNull(context, pm.ProcessorMerchantSetup);
                RemoveEntityIfNotNull(context, pm.ProcessorMerchantSettleBatchSetup);
                context.Remove(pm);
            }
        }

        private static void RemovePaymentModes(MMSContext context, ICollection<PaymentMode> paymentModes)
        {
            if (paymentModes == null) return;

            foreach (var paymentMode in paymentModes.ToList())
            {
                var option = paymentMode.PaymentModeOption;
                if (option != null)
                {
                    if (option.Adverts != null)
                        context.RemoveRange(option.Adverts);
                    if (option.ProofId != null)
                        context.Remove(option.ProofId);
                    context.Remove(option);
                }
                context.Remove(paymentMode);
            }
        }

        private static void RemovePrintMessage(MMSContext context, MessageMerchant printMessage)
        {
            if (printMessage == null) return;

            RemoveLineIfNotNull(context, printMessage.HeaderLine1);
            RemoveLineIfNotNull(context, printMessage.HeaderLine2);
            RemoveLineIfNotNull(context, printMessage.HeaderLine3);
            RemoveLineIfNotNull(context, printMessage.HeaderLine4);
            RemoveLineIfNotNull(context, printMessage.HeaderLine5);
            RemoveLineIfNotNull(context, printMessage.HeaderLine6);

            RemoveLineIfNotNull(context, printMessage.FooterLine1);
            RemoveLineIfNotNull(context, printMessage.FooterLine2);
            RemoveLineIfNotNull(context, printMessage.FooterLine3);
            RemoveLineIfNotNull(context, printMessage.FooterLine4);
            RemoveLineIfNotNull(context, printMessage.FooterLine5);
            RemoveLineIfNotNull(context, printMessage.FooterLine6);

            RemoveLineIfNotNull(context, printMessage.ReturnLine1);
            RemoveLineIfNotNull(context, printMessage.ReturnLine2);
            RemoveLineIfNotNull(context, printMessage.ReturnLine3);
            RemoveLineIfNotNull(context, printMessage.ReturnLine4);
            RemoveLineIfNotNull(context, printMessage.ReturnLine5);
            RemoveLineIfNotNull(context, printMessage.ReturnLine6);

            context.Remove(printMessage);
        }

        private static void RemoveLineIfNotNull(MMSContext context, LineMessageMerchant line)
        {
            if (line != null)
            {
                context.Remove(line);
            }
        }

        private void UpdateOneToOneRelationships(MMSContext context, TerminalMaster terminalMaster, TerminalMaster template)
        {
            terminalMaster.Support = UpdateOneToOneEntity(context, terminalMaster.Support, template.Support);
            terminalMaster.Sound = UpdateOneToOneEntity(context, terminalMaster.Sound, template.Sound);
            terminalMaster.FallBack = UpdateOneToOneEntity(context, terminalMaster.FallBack, template.FallBack);
            terminalMaster.CountryTerminal = UpdateOneToOneEntity(context, terminalMaster.CountryTerminal, template.CountryTerminal);
            terminalMaster.TimeZoneMaster = UpdateOneToOneEntity(context, terminalMaster.TimeZoneMaster, template.TimeZoneMaster);
            terminalMaster.TerminalSetup = UpdateOneToOneEntity(context, terminalMaster.TerminalSetup, template.TerminalSetup);
            terminalMaster.TerminalCloud = UpdateOneToOneEntity(context, terminalMaster.TerminalCloud, template.TerminalCloud);
            terminalMaster.PosIntegration = UpdateOneToOneEntity(context, terminalMaster.PosIntegration, template.PosIntegration);
            terminalMaster.CloudPosIntegration = UpdateOneToOneEntity(context, terminalMaster.CloudPosIntegration, template.CloudPosIntegration);
            terminalMaster.TerminalStyle = UpdateOneToOneEntity(context, terminalMaster.TerminalStyle, template.TerminalStyle);
            terminalMaster.TerminalRebootSchedule = UpdateOneToOneEntity(context, terminalMaster.TerminalRebootSchedule, template.TerminalRebootSchedule);
        }

        private T UpdateOneToOneEntity<T>(MMSContext context, T existingEntity, T templateEntity) where T : class, new()
        {
            if (templateEntity == null)
            {
                if (existingEntity != null)
                {
                    context.Remove(existingEntity);
                }
                return null;
            }
            else
            {
                if (existingEntity == null)
                {
                    existingEntity = new T();
                    context.Add(existingEntity);
                }
                _mapper.Map(templateEntity, existingEntity);

                return existingEntity;
            }
        }

        private void ReplaceCollections(MMSContext context, TerminalMaster terminalMaster, TerminalMaster template, bool includeMerchantTerminal)
        {
            ReplaceCollection(terminalMaster.HostInterfaces, template.HostInterfaces, existing => context.RemoveRange(existing));
            ReplaceCollection(terminalMaster.CountryCardSetupTerminals, template.CountryCardSetupTerminals, existing => context.RemoveRange(existing));
            ReplaceCollection(terminalMaster.UserTerminals, template.UserTerminals, existing => context.RemoveRange(existing));
            ReplaceCollection(terminalMaster.UsersAccessTerminal, template.UsersAccessTerminal, existing => context.RemoveRange(existing));
            ReplaceCollection(terminalMaster.TerminalSecurityAccesses, template.TerminalSecurityAccesses, existing => context.RemoveRange(existing));
            ReplaceCollection(terminalMaster.CharityTerminals, template.CharityTerminals, existing => context.RemoveRange(existing));

            // Replace MerchantTerminals separately if needed
            if (includeMerchantTerminal)
                terminalMaster.MerchantTerminals = _mapper.Map<List<MerchantTerminal>>(template.MerchantTerminals);
        }

        private void ReplaceCollection<T>(ICollection<T> existingCollection, ICollection<T> templateCollection, Action<ICollection<T>> removeExisting)
            where T : class
        {
            // Validate that 'existingCollection' is not null.
            if (existingCollection == null)
            {
                throw new ArgumentNullException(nameof(existingCollection), "existingCollection cannot be null.");
            }

            // If the existing collection has items, invoke the 'removeExisting' action to handle cleanup.
            if (existingCollection.Count > 0)
            {
                removeExisting(existingCollection);
            }

            existingCollection.Clear();

            // Add each mapped item to the existing collection.
            foreach (var item in _mapper.Map<List<T>>(templateCollection))
            {
                existingCollection.Add(item);
            }
        }

        private static async Task HandleMerchantSaleTypePaymentModeMappingAsync(MMSContext context, TerminalMaster terminalMaster, TerminalMaster template)
        {
            var paymentModeIdMapping = new Dictionary<int, int>();
            var merchantSaleTypeIdMapping = new Dictionary<int, int>();
            var merchantSaleTypePaymentModeMappings = new List<MerchantSaleTypePaymentModeMapping>();

            for (int i = 0; i < template.MerchantTerminals.Count; i++)
            {
                var oldTerminal = template.MerchantTerminals[i];
                var newTerminal = terminalMaster.MerchantTerminals[i];

                if (oldTerminal.DefaultTransaction != null && newTerminal.DefaultTransaction != null)
                {
                    var mappings = await context.MerchantSaleTypePaymentModeMapping
                        .Where(m => m.MerchantSaleTypeId == oldTerminal.DefaultTransaction.Id)
                        .ToListAsync();

                    merchantSaleTypeIdMapping[oldTerminal.DefaultTransaction.Id] = newTerminal.DefaultTransaction.Id;

                    foreach (var mapping in mappings)
                    {
                        var oldPaymentModeId = mapping.PaymentModeId;
                        var newPaymentModeId = terminalMaster.MerchantTerminals[i].PaymentModes.FirstOrDefault(pm => pm.Id == oldPaymentModeId)?.Id ?? oldPaymentModeId;

                        paymentModeIdMapping[oldPaymentModeId] = newPaymentModeId;

                        merchantSaleTypePaymentModeMappings.Add(new MerchantSaleTypePaymentModeMapping(
                            merchantSaleTypeIdMapping[mapping.MerchantSaleTypeId],
                            paymentModeIdMapping[oldPaymentModeId],
                            Constants.NOTCHANGE_RECORD)
                        {
                            IsActive = mapping.IsActive,
                            Level = mapping.Level
                        });
                    }
                }
            }

            context.MerchantSaleTypePaymentModeMapping.AddRange(merchantSaleTypePaymentModeMappings);
        }

        private static async Task UpdateRelatedEntitiesWithNewIdsAsync(TerminalMaster terminalMaster, TerminalMaster template)
        {
            var merchantTerminalIdMapping = template.MerchantTerminals
                .Select((mt, index) => (oldId: mt.Id, newId: terminalMaster.MerchantTerminals[index].Id))
                .ToList();

            var charityIdMapping = template.CharityTerminals
                .Select((ct, index) => (oldId: ct.Id, newId: terminalMaster.CharityTerminals[index].Id))
                .ToList();

            await UpdateMerchantUsersAsync(terminalMaster, merchantTerminalIdMapping);
            await UpdateMerchantProcessorsAsync(terminalMaster, merchantTerminalIdMapping);
            await UpdateBatchesAsync(terminalMaster, merchantTerminalIdMapping);
            await UpdateChildCharitiesAsync(terminalMaster, charityIdMapping, merchantTerminalIdMapping);
            await UpdateMerchantCharitiesAsync(terminalMaster, merchantTerminalIdMapping);
        }

        private static async Task UpdateMerchantUsersAsync(TerminalMaster terminalMaster, List<(int oldId, int newId)> idMapping)
        {
            var newMerchantUsers = terminalMaster.UserTerminals.SelectMany(ut => ut.MerchantUsers).ToList();
            foreach (var user in newMerchantUsers)
            {
                var mapping = idMapping.Find(m => m.oldId == user.MerchantTerminalId);
                if (mapping != default)
                {
                    user.MerchantTerminalId = mapping.newId;
                }
            }

            await Task.CompletedTask;
        }

        private static async Task UpdateMerchantProcessorsAsync(TerminalMaster terminalMaster, List<(int oldId, int newId)> idMapping)
        {
            var newMerchantProcessors = terminalMaster.HostInterfaces.SelectMany(hi => hi.MerchantProcessors).ToList();
            foreach (var processor in newMerchantProcessors)
            {
                var mapping = idMapping.Find(m => m.oldId == processor.MerchantTerminalId);
                if (mapping != default)
                {
                    processor.MerchantTerminalId = mapping.newId;
                }
            }

            await Task.CompletedTask;
        }

        private static async Task UpdateBatchesAsync(TerminalMaster terminalMaster, List<(int oldId, int newId)> idMapping)
        {
            var newBatches = terminalMaster.HostInterfaces.SelectMany(hi => hi.Batches).ToList();
            foreach (var batch in newBatches)
            {
                var mapping = idMapping.Find(m => m.oldId == batch.MerchantTerminalId);
                if (mapping != default)
                {
                    batch.MerchantTerminalId = mapping.newId;
                }
            }

            await Task.CompletedTask;
        }

        private static async Task UpdateChildCharitiesAsync(TerminalMaster terminalMaster, List<(int oldId, int newId)> charityIdMapping, List<(int oldId, int newId)> merchantTerminalIdMapping)
        {
            var childCharities = terminalMaster.CharityTerminals.Where(ct => ct.ParentCharityId != null).ToList();
            foreach (var child in childCharities)
            {
                var parentMapping = charityIdMapping.Find(m => m.oldId == child.ParentCharityId);
                if (parentMapping != default)
                {
                    child.ParentCharityId = parentMapping.newId;
                }

                var terminalMapping = merchantTerminalIdMapping.Find(m => m.oldId == child.MerchantTerminalId);
                if (terminalMapping != default)
                {
                    child.MerchantTerminalId = terminalMapping.newId;
                }
            }

            await Task.CompletedTask;
        }

        private static async Task UpdateMerchantCharitiesAsync(TerminalMaster terminalMaster, List<(int oldId, int newId)> merchantTerminalIdMapping)
        {
            var merchantCharities = terminalMaster.CharityTerminals.SelectMany(ct => ct.MerchantCharities).ToList();
            foreach (var mc in merchantCharities)
            {
                var mapping = merchantTerminalIdMapping.Find(m => m.oldId == mc.MerchantTerminalId);
                if (mapping != default)
                {
                    mc.MerchantTerminalId = mapping.newId;
                }
            }

            await Task.CompletedTask;
        }

        public TerminalMaster GetTerminalIncludedById(int terminalMasterId)
        {
            return _terminalMasterRepository.GetTerminalMasterIncludedById(terminalMasterId);
        }

        public void SaveMappingIdAfterCopy(TerminalMaster terminalMasterTemplate, TerminalMaster copiedTerminal)
        {
            string key = $"{terminalMasterTemplate.Id}-{copiedTerminal.Id}";

            if (terminalMasterTemplate.CharityTerminals.Count > 0)
            {
                var charityIdList = new List<KeyValuePair<int, int>>();
                for (int i = 0; i < terminalMasterTemplate.CharityTerminals.Count; i++)
                {
                    charityIdList.Add(new KeyValuePair<int, int>(
                        terminalMasterTemplate.CharityTerminals[i].Id,
                        copiedTerminal.CharityTerminals[i].Id
                    ));
                }

                CoreUtils.CharityIdMap.TryAdd(key, charityIdList);
            }

            if (terminalMasterTemplate.HostInterfaces.Count > 0)
            {
                var hostInterfaceIdList = new List<KeyValuePair<int, int>>();
                for (int i = 0; i < terminalMasterTemplate.HostInterfaces.Count; i++)
                {
                    hostInterfaceIdList.Add(new KeyValuePair<int, int>(
                        terminalMasterTemplate.HostInterfaces[i].Id,
                        copiedTerminal.HostInterfaces[i].Id
                    ));
                }

                CoreUtils.HostInterfaceIdMap.TryAdd(key, hostInterfaceIdList);
            }
        }

        public async Task<TerminalMaster> GetTerminalTemplateForApiDownload(string serialNumber, int applicationId)
        {
            var terminalMaster = await _terminalMasterRepository.GetTerminalBySerialNumberNotInclude(serialNumber)
                ?? throw new InvalidOperationException($"Terminal not found for serial number: {serialNumber}");

            var downloadableDeviceTemplate = await _deviceTemplatesService.GetTemplateForXmlDownloadAsync(terminalMaster.Id, applicationId)
                ?? throw new InvalidOperationException($"DeviceTemplate not found for terminal ID: {terminalMaster.Id}");

            return await GetTerminalByDeviceTemplateId(downloadableDeviceTemplate.Id, DeviceSetupType.VirtualTemplateDevice)
                ?? throw new InvalidOperationException($"Terminal not found for DeviceTemplate ID: {downloadableDeviceTemplate.Id}");
        }

        public async Task<TerminalMaster> GetTerminalByDeviceTemplateId(int templateId)
        {
            return await _terminalMasterRepository.GetTerminalByDeviceTemplateId(templateId);
        }

        public async Task<PagingResponse<AvailableDevicesApiModel>> GetAllTerminalUnAssignedDeviceForApiAsync(int companyId, List<int> brandIds, List<int> modelIds, string keySearch, PagingParameter pagingParameter)
        {
            return await _terminalMasterRepository.GetAllTerminalUnAssignedDeviceForApiAsync(companyId, brandIds, modelIds, keySearch, pagingParameter);
        }

        public async Task AssignDeviceToLocationForApiAsync(int terminalId, int locationAreaId, int groupId)
        {
            await _terminalMasterRepository.AssignDeviceToLocationForApiAsync(terminalId, locationAreaId, groupId);
        }

        public async Task AssignDevicesToLocationForApiAsync(IList<int> terminalIds, int locationAreaId, int groupId)
        {
            await _terminalMasterRepository.AssignDevicesToLocationForApiAsync(terminalIds, locationAreaId, groupId);
        }

        public async Task<PagingResponse<DeviceAssociatedResponseApiModel>> GetAssignedDeviceAsync(AssignedDeviceParamsApiModel paramsModel, PagingParameter pagingParameter, string searchValue, SearchAssignDeviceType searchType)
        {
            var companyGroupMapping = _companyGroupMappingRepository.GetById(paramsModel.CompanyGroupMappingId.GetValueOrDefault());

            var companyIds = new List<int>();

            if (companyGroupMapping is not null)
            {
                companyIds = await _companyGroupMappingRepository.GetAllChildIdsByHierarchyAsync(companyGroupMapping.HierarchyKey);
            }

            if (paramsModel.CompanyId != 0)
            {
                companyIds.Add(paramsModel.CompanyId);
            }

            var locationItemRegionIds = await _locationItemSelectRepository.GetLocationItemRegionIdsAsync(paramsModel.GroupMappingId);

            var locationAreaIds = new List<int>();

            if (paramsModel.LocationIdsSelected == null)
            {
                locationAreaIds = await _locationItemRepository.GetLocationAreaIdsForAllRegionIdsAsync(locationItemRegionIds);
            }
            else
            {
                locationAreaIds = (await _locationItemRepository.GetLocationAreaIdByStringHierarchy(paramsModel.LocationIdsSelected)).ToList();
            }

            var deviceIndustryIds = await _locationDeviceMappingRepository.GetDeviceIndustryIdAsync(paramsModel.LocationCategoryId, paramsModel.DeviceTypeId, paramsModel.DeviceIndustryId);

            var result = await _terminalMasterRepository.GetDeviceAssignedAsync(
                locationAreaIds,
                deviceIndustryIds,
                UtilConvert.SeparateStringToInt(paramsModel.DeviceBrandId),
                UtilConvert.SeparateStringToInt(paramsModel.DeviceModelId),
                UtilConvert.SeparateStringToInt(paramsModel.MerchantId),
                companyIds,
                paramsModel.GroupId,
                searchValue,
                searchType,
                pagingParameter,
                paramsModel.IsActivate);

            return result;
        }

        public async Task UnassignDevicesToLocationForApiAsync(IList<int> terminalIds)
        {
            await _terminalMasterRepository.UnassignDevicesToLocationForApiAsync(terminalIds);
        }

        public async Task<bool> HasAnyDeviceAssignedToGroup(int groupId)
        {
            var hasDeviceAssigned = await _terminalMasterRepository.HasAnyDeviceAssignedToGroup(groupId);

            return hasDeviceAssigned;
        }
    }
}
