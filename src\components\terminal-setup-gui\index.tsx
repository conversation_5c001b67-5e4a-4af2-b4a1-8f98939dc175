import React, { useEffect, useRef, useState } from 'react';
import { Spin } from 'antd';
import { TerminalSetupApi } from '../../apis/terminal-setup.api';
import './index.scss';

interface TerminalSetupGUIProps {
  terminalMasterId: number;
  iframeRef?: React.RefObject<HTMLIFrameElement>;
  onFirstDiscard?: () => void;
  isAutoDiscard?: boolean;
}

const TerminalSetupGUI: React.FC<TerminalSetupGUIProps> = ({
  terminalMasterId,
  iframeRef: externalIframeRef,
  onFirstDiscard,
  isAutoDiscard = false,
}) => {
  const internalIframeRef = useRef<HTMLIFrameElement>(null);
  const iframeRef = externalIframeRef || internalIframeRef;
  const [loading, setLoading] = useState(true);

  const [cookieSet, setCookieSet] = useState(false);
  const [iframeUrl, setIframeUrl] = useState<string | null>(null);
  const [hasDiscarded, setHasDiscarded] = useState(false);

  useEffect(() => {
    TerminalSetupApi.setAuthCookie();

    const url = TerminalSetupApi.getTerminalSetupUrl(terminalMasterId);

    setIframeUrl(url);
    setCookieSet(true);

    // Clean up when component unmounts
    return () => {
      TerminalSetupApi.iframeSignout(iframeRef);
    };
  }, [terminalMasterId, iframeRef]);

  useEffect(() => {
    if (!cookieSet || !iframeUrl) {
      return;
    }

    const handleIframeLoad = () => {
      setLoading(false);
      if (!hasDiscarded && iframeRef.current?.contentWindow && isAutoDiscard) {
        onFirstDiscard?.();
        TerminalSetupApi.sendDiscardAction(iframeRef.current.contentWindow);
        setHasDiscarded(true);
      }
    };

    const iframe = iframeRef.current;
    if (iframe) {
      iframe.addEventListener('load', handleIframeLoad);
    }

    return () => {
      if (iframe) {
        iframe.removeEventListener('load', handleIframeLoad);
      }
    };
  }, [cookieSet, iframeUrl, hasDiscarded, onFirstDiscard]);

  return (
    <div className="terminal-setup-gui-container">
      {loading && (
        <div className="loading-overlay">
          <Spin size="large" tip="Loading Terminal Setup..." />
        </div>
      )}

      {cookieSet && iframeUrl && (
        <iframe
          ref={iframeRef}
          src={iframeUrl}
          title="Terminal Setup GUI"
          className="terminal-setup-iframe"
          sandbox="allow-forms allow-scripts allow-same-origin allow-popups allow-modals"
          onError={(e) => console.error('Iframe error:', e)}
        />
      )}
    </div>
  );
};

export default TerminalSetupGUI;
