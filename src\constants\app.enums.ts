export enum IntegrationStatus {
  None,
  Success,
  Error,
  SyncSuc<PERSON>,
  SyncError,
  ConnectionLostError,
  ConnectionGALostError,
  ConnectionZohoLostError,
}

export enum CompanyItemType {
  Company = 1,
  Subsidiary = 2,
  Group = 3,
}

export enum FilterKey {
  ALL = 'all',
  SELECTED = 'selected',
  NOT_SELECTED = 'notSelected',
}

export enum LocationLevelId {
  Industry = 1,
  Chain = 2,
  Region = 3,
  Country = 4,
  State = 5,
  City = 6,
  Suburb = 7,
  SuburbArea = 14,
  Locations = 8,
  LocationAreas = 9,
}

export enum ScheduleOption {
  None = 0,
  Immediately = 1,
  ForceUpgrade = 2,
}

export enum SyncType {
  PartialSync = 1,
  FullSync = 2,
}

export enum TemplateUpgradeType {
  None = 0,
  Existing = 1,
  New = 2,
}

export enum DeviceSetupType {
  None = 0,
  BaseTemplate = 1,
  VirtualDevice = 2,
  VirtualTemplateDevice = 3,
}

export enum CompanyGroupMappingType {
  Company = 1,
  Subsidiary = 2,
  PartnerAccess = 4,
  Group = 3,
  SubPartner = 5,
  App = 6,
  AppVersion = 7,
}

export enum ColumnWidthCssClass {
  Three = 'three-columns-width',
  Nine = 'nine-columns-width',
}

export enum ColumnHeightCssClass {
  Big = 'columm-big-size',
  Small = 'columm-small-size',
}

export enum UploadType {
  Person = 1,
  Company = 2,
  Charities = 3,
  Processors = 4,
}

export enum SearchPersonOption {
  None,
  Name,
  Email,
}

export enum PreferredLanguage {
  None,
  English,
  Chinese,
  Vietnamese,
}

export enum PasswordRequirementKey {
  DiffFromPreviousPass,
  Length,
  Uppercase,
  Lowercase,
  SpecialChar,
  NotSameAsMmsId,
}

export enum PersonSetupTab {
  PersonInfo = 'person-info',
  MmsLogon = 'mms-logon',
}

export enum LocationLevel {
  Industry = 1,
  Chain = 2,
  Region = 3,
  Country = 4,
  State = 5,
  City = 6,
  Suburb = 7,
  SuburbArea = 8,
  Locations = 9,
  LocationAreas = 10,
}