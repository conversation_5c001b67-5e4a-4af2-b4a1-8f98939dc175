import './index.scss';
import { useRef, useEffect, useState, useCallback, useMemo } from 'react';
import { useBreadcrumb } from '../../hooks/useBreadcrumb';
import { ROUTE_PATHS } from '../../constants/router.constants';
import { useTranslation } from 'react-i18next';
import LocalUtils from '../../utils/local.utils';
import { AppTemplateApi } from '../../apis/app-template.api';
import { AppBaseTemplateApi } from '../../apis/app-base-template.api';
import {
  ColumnWidthCssClass,
  CompanyGroupMappingType,
  CompanyItemType,
  ColumnHeightCssClass,
} from '../../constants/app.enums';
import { DataItemResponse } from '../../models/common.model';
import useCompanyTree from '../../hooks/useCompanyTree';
import DataColumnItems, { DataItem } from '../../components/shared/column';
import { Empty, Spin } from 'antd';
import MerchantSettings from '../../components/template-data';
import { LOCAL_STORAGE_KEY } from '../../constants/app-constants';
import { useNavigate } from 'react-router-dom';
import showModal from '../../components/shared/notification-modal';
import showNotification from '../../components/shared/notification';
import { MenuProps } from 'antd';
import ValidateTemplate from '../../components/shared/template-validation';

interface SelectedItems {
  application?: DataItem;
  appVersion?: DataItem;
}

function AppBaseTemplatePage() {
  const appBaseTemplateRef = useRef<HTMLDivElement>(null);
  const { updateBreadcrumb } = useBreadcrumb();
  const { t } = useTranslation();
  const navigate = useNavigate();

  const { lastSelectedItem, parentItem } = useCompanyTree(false);
  const [applications, setApplications] = useState<DataItem[]>([]);
  const [appVersions, setAppVersions] = useState<DataItem[]>([]);
  const [baseTemplates, setBaseTemplates] = useState<DataItem[]>([]);
  const [isLoading, setLoading] = useState(false);
  const [templateData, setTemplateData] = useState<TemplateDataType>({});
  const [activeTab, setActiveTab] = useState<string>();
  const [tabSettings, setTabSettings] = useState<Record<string, JsonValue>>({});
  const [loadingApplications, setLoadingApplications] = useState(false);
  const [loadingAppVersions, setLoadingAppVersions] = useState(false);
  const [loadingBaseTemplates, setLoadingBaseTemplates] = useState(false);
  const [selectedItems, setSelectedItems] = useState<SelectedItems>({});

  type JsonValue =
    | string
    | number
    | boolean
    | null
    | { [key: string]: JsonValue }
    | JsonValue[];

  type TemplateDataType = {
    [key: string]: Record<string, JsonValue>;
  };

  const [selectedApplicationId, setSelectedApplicationId] = useState<
    number | null
  >(null);
  const [selectedAppVersionId, setSelectedAppVersionId] = useState<
    number | null
  >(null);
  const [selectedBaseTemplateId, setSelectedBaseTemplateId] = useState<
    number | null
  >(null);

  const resetDataFunctions = useMemo(
    () => [setApplications, setAppVersions, setBaseTemplates],
    [],
  );

  const resetSelectedFunctions = useMemo(
    () => [
      setSelectedApplicationId,
      setSelectedAppVersionId,
      setSelectedBaseTemplateId,
    ],
    [],
  );

  const resetFromIndex = useCallback(
    (startIndex: number) => {
      resetDataFunctions.slice(startIndex + 1).forEach((reset) => reset([]));
      resetSelectedFunctions.slice(startIndex).forEach((reset) => reset(null));
    },
    [resetDataFunctions, resetSelectedFunctions],
  );

  const convertToDataItem = (dataItemRes: DataItemResponse[]): DataItem[] => {
    return dataItemRes.map((item) => ({
      key: item.id,
      label: item.name || '',
      active: item.isActive,
    }));
  };

  const loadSavedSelections = useCallback(async (items: SelectedItems) => {
    try {
      if (items.application) {
        await handleSelectApplication(items.application);

        if (items.appVersion) {
          await handleSelectAppVersion(items.appVersion);
        }
      }
    } catch (error) {
      console.error('Error loading saved selections:', error);
    }
  }, []);

  const loadInitialData = useCallback(async () => {
    if (lastSelectedItem?.itemType === CompanyItemType.Group) {
      setLoadingApplications(true);
      let apps: DataItem[] = [];
      try {
        resetFromIndex(0);

        const response = await AppBaseTemplateApi.getCompanyGroupMapping(
          lastSelectedItem.id,
          CompanyGroupMappingType.App,
        );

        apps = convertToDataItem(response.data);
        setApplications(apps);
      } catch (error) {
        console.error('Error loading applications:', error);
        setApplications([]);
      } finally {
        setLoadingApplications(false);
      }

      const savedItems = LocalUtils.get(
        LOCAL_STORAGE_KEY.BASE_TEMPLATE_SELECTIONS,
      );
      if (savedItems) {
        try {
          const parsedItems = JSON.parse(savedItems) as SelectedItems;
          if (parsedItems.application) {
            const savedApp = apps.find(
              (app: DataItem) => app.key === parsedItems.application?.key,
            );
            if (savedApp) {
              setSelectedItems(parsedItems);
              await loadSavedSelections(parsedItems);
            }
          }
        } catch (error) {
          console.error('Error loading saved selections:', error);
        }
      }
    } else {
      resetTemplateData();
      setApplications([]);
      resetFromIndex(0);
    }
  }, [lastSelectedItem, resetFromIndex, loadSavedSelections]);

  useEffect(() => {
    loadInitialData();
  }, [loadInitialData]);

  const handleClick = async (item: DataItem, columnIndex: number) => {
    let newSelectedItems: SelectedItems = { ...selectedItems };

    switch (columnIndex) {
      case 1: // Application
        newSelectedItems = {
          application: item,
          appVersion: undefined,
        };
        await handleSelectApplication(item);
        break;

      case 2: // App Version
        if (!selectedItems.application) return;
        newSelectedItems = {
          ...selectedItems,
          appVersion: item,
        };
        await handleSelectAppVersion(item);
        break;

      case 3: // Base Template
        if (!selectedItems.application || !selectedItems.appVersion) return;
        newSelectedItems = {
          ...selectedItems,
        };
        await handleSelectBaseTemplate(item);
        break;
    }

    setSelectedItems(newSelectedItems);
    saveSelectedItems(newSelectedItems);
  };

  const handleSelectApplication = async (item: DataItem) => {
    setSelectedApplicationId(item.key);
    resetFromIndex(1);
    resetTemplateData();

    try {
      setLoadingAppVersions(true);

      const response = await AppBaseTemplateApi.getCompanyGroupMapping(
        item.key,
        CompanyGroupMappingType.AppVersion,
      );

      setAppVersions(convertToDataItem(response.data));
    } catch (error) {
      console.error('Error loading app versions:', error);
      setAppVersions([]);
    } finally {
      setLoadingAppVersions(false);
    }
  };

  const handleSelectAppVersion = async (item: DataItem) => {
    setSelectedAppVersionId(item.key);
    resetFromIndex(2);
    resetTemplateData();

    try {
      setLoadingBaseTemplates(true);

      const response = await AppBaseTemplateApi.getAppBaseTemplate(item.key);

      setBaseTemplates(convertToDataItem(response.data));
    } catch (error) {
      console.error('Error loading base template:', error);
      setBaseTemplates([]);
    } finally {
      setLoadingBaseTemplates(false);
    }
  };

  const handleSelectBaseTemplate = async (item: DataItem) => {
    setSelectedBaseTemplateId(item.key);
    resetTemplateData();

    await fetchTemplateDataById(item.key);
  };

  const resetTemplateData = () => {
    setTemplateData({});
    setTabSettings({});
    setActiveTab(undefined);
  };

  useEffect(() => {
    if (templateData && activeTab && templateData[activeTab]) {
      setTabSettings(templateData[activeTab]);
    }
  }, [activeTab, templateData]);

  const fetchTemplateDataById = async (templateId: number) => {
    try {
      setLoading(true);
      const { data } = await AppTemplateApi.getTemplateData(templateId);

      let templateData: TemplateDataType = {};
      try {
        if (typeof data === 'string') {
          templateData = JSON.parse(data) as TemplateDataType;
        } else {
          templateData = data as TemplateDataType;
        }

        await ValidateTemplate(templateId, t);

        setTemplateData(templateData);

        const savedActiveTab = LocalUtils.get(
          LOCAL_STORAGE_KEY.ACTIVE_BASE_TEMPLATE_SELECTED_TAB,
        );

        if (savedActiveTab && templateData[savedActiveTab]) {
          setActiveTab(savedActiveTab);
        } else if (activeTab && templateData[activeTab]) {
          setActiveTab('Terminal');
        } else {
          const validTabKey =
            Object.keys(templateData).find((key) => {
              const value = templateData[key];
              return (
                value !== null &&
                (typeof value === 'object' || Array.isArray(value))
              );
            }) || Object.keys(templateData)[0];

          setActiveTab(validTabKey);
          setTabSettings(templateData[validTabKey]);
          LocalUtils.set(
            LOCAL_STORAGE_KEY.ACTIVE_BASE_TEMPLATE_SELECTED_TAB,
            validTabKey,
          );
        }
      } catch (error) {
        console.error('Error processing template data:', error);
      }
    } catch (error) {
      console.error('Error fetching template data by ID:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTabChangeModal = (tabKey: string) => {
    setActiveTab(tabKey);
    localStorage.setItem(
      LOCAL_STORAGE_KEY.ACTIVE_BASE_TEMPLATE_SELECTED_TAB,
      tabKey,
    );

    if (templateData && templateData[tabKey]) {
      setTabSettings(templateData[tabKey]);
    }
  };

  const handleEdit = async () => {
    if (selectedBaseTemplateId) {
      try {
        const companyId = parentItem?.orgId || 0;
        const response = await AppBaseTemplateApi.getTemplateInfoById(
          selectedBaseTemplateId,
          companyId,
        );

        const data = response.data;
        navigate(
          `${ROUTE_PATHS.AppBaseTemplateSetup}/${data.terminalMasterId}`,
          {
            state: {
              companyName: data.companyName,
              templateName: data.templateName,
              templateId: selectedBaseTemplateId,
              isNewTemplate: false,
              companyId: parentItem?.id,
            },
          },
        );
      } catch (error) {
        console.error('Error fetching terminal ID:', error);
      }
    }
  };

  const handleAdd = async () => {
    if (selectedAppVersionId) {
      try {
        const response =
          await AppBaseTemplateApi.addAppBaseTemplate(selectedAppVersionId);

        const newTemplateId = response.data;
        const companyId = parentItem?.orgId || 0;
        const templateInfo = await AppBaseTemplateApi.getTemplateInfoById(
          newTemplateId,
          companyId,
        );

        const data = templateInfo.data;
        navigate(
          `${ROUTE_PATHS.AppBaseTemplateSetup}/${data.terminalMasterId}`,
          {
            state: {
              companyName: data.companyName,
              templateName: data.templateName,
              templateId: newTemplateId,
              isNewTemplate: true,
              companyId: parentItem?.id,
            },
          },
        );
      } catch (error) {
        console.error('Error adding new template:', error);
      }
    }
  };

  const handleDelete = async () => {
    if (selectedBaseTemplateId) {
      const selectedTemplate = baseTemplates.find(
        (template) => template.key === selectedBaseTemplateId,
      );
      showModal(
        'delete',
        t('appBaseTemplate.actions.deleteTitle'),
        `${t('appBaseTemplate.actions.deleteContent')} <strong>"${selectedTemplate?.label}"</strong>?`,
        {
          okText: t('common.delete'),
          onOk: () => confirmDeleteTemplate(),
          cancelText: t('common.cancel'),
        },
      );
    }
  };

  const confirmDeleteTemplate = async () => {
    if (!selectedBaseTemplateId) return;

    try {
      await AppBaseTemplateApi.deleteAppBaseTemplate(selectedBaseTemplateId);

      showNotification('success', t('appBaseTemplate.actions.deleteSuccess'));

      if (selectedAppVersionId) {
        const response =
          await AppBaseTemplateApi.getAppBaseTemplate(selectedAppVersionId);
        setBaseTemplates(convertToDataItem(response.data));
      }
      setSelectedBaseTemplateId(null);
      resetTemplateData();
    } catch (error) {
      console.error('Error deleting template:', error);
    }
  };

  const handleActivate = async () => {
    if (selectedBaseTemplateId) {
      const selectedTemplate = baseTemplates.find(
        (template) => template.key === selectedBaseTemplateId,
      );

      if (selectedTemplate?.active) {
        showNotification('warning', t('appBaseTemplate.actions.alreadyActive'));
        return;
      }

      showModal(
        'confirm',
        t('appBaseTemplate.actions.activateTitle'),
        `${t('appBaseTemplate.actions.activateContent')} <strong>"${selectedTemplate?.label}"</strong>?`,
        {
          okText: t('common.active'),
          onOk: () => confirmActivateTemplate(),
          cancelText: t('common.cancel'),
        },
      );
    }
  };

  const confirmActivateTemplate = async () => {
    if (!selectedBaseTemplateId) return;

    try {
      await AppBaseTemplateApi.activateDeactivateBaseTemplate(
        selectedBaseTemplateId,
        true,
      );
      showNotification('success', t('appBaseTemplate.actions.activateSuccess'));

      if (selectedAppVersionId) {
        const response =
          await AppBaseTemplateApi.getAppBaseTemplate(selectedAppVersionId);
        setBaseTemplates(convertToDataItem(response.data));
      }
    } catch (error) {
      console.error('Error activating template:', error);
    }
  };

  const handleDeactivate = async () => {
    if (selectedBaseTemplateId) {
      const selectedTemplate = baseTemplates.find(
        (template) => template.key === selectedBaseTemplateId,
      );

      if (!selectedTemplate?.active) {
        showNotification(
          'warning',
          t('appBaseTemplate.actions.alreadyInactive'),
        );
        return;
      }

      showModal(
        'confirm',
        t('appBaseTemplate.actions.deactivateTitle'),
        `${t('appBaseTemplate.actions.deactivateContent')} <strong>"${selectedTemplate?.label}"</strong>?`,
        {
          okText: t('common.deactivate'),
          onOk: () => confirmDeactivateTemplate(),
          cancelText: t('common.cancel'),
        },
      );
    }
  };

  const confirmDeactivateTemplate = async () => {
    if (!selectedBaseTemplateId) return;

    try {
      await AppBaseTemplateApi.activateDeactivateBaseTemplate(
        selectedBaseTemplateId,
        false,
      );
      showNotification(
        'success',
        t('appBaseTemplate.actions.deactivateSuccess'),
      );

      if (selectedAppVersionId) {
        const response =
          await AppBaseTemplateApi.getAppBaseTemplate(selectedAppVersionId);
        setBaseTemplates(convertToDataItem(response.data));
      }
    } catch (error) {
      console.error('Error deactivating template:', error);
    }
  };

  const saveSelectedItems = (items: SelectedItems) => {
    LocalUtils.set(
      LOCAL_STORAGE_KEY.BASE_TEMPLATE_SELECTIONS,
      JSON.stringify(items),
    );
  };

  const getBaseTemplateMenuItems = () => {
    const items: MenuProps['items'] = [
      {
        key: 'add',
        label: t('common.add'),
        onClick: handleAdd,
      },
      {
        key: 'edit',
        label: t('common.edit'),
        onClick: handleEdit,
      },
      {
        key: 'activate',
        label: t('common.active'),
        onClick: handleActivate,
      },
      {
        key: 'deactivate',
        label: t('common.deactivate'),
        onClick: handleDeactivate,
      },
      {
        key: 'remove',
        label: t('common.remove'),
        onClick: handleDelete,
      },
    ];

    return items;
  };

  useEffect(() => {
    updateBreadcrumb(
      {
        title: t('breadcrumbs.appBaseTemplate'),
        path: ROUTE_PATHS.AppBaseTemplate,
      },
      true,
    );
  }, [t]);

  return (
    <div className="app-base-template-page" ref={appBaseTemplateRef}>
      <div className="app-base-template">
        <DataColumnItems
          name={t('appBaseTemplate.columnTitles.application')}
          data={applications}
          loading={loadingApplications}
          onSelect={(item) => handleClick(item, 1)}
          selectedKey={selectedApplicationId}
          columnWidthClass={ColumnWidthCssClass.Three}
          columnHeightClass={ColumnHeightCssClass.Big}
          menuItems={[]}
        />
        <DataColumnItems
          name={t('appBaseTemplate.columnTitles.appVersion')}
          data={appVersions}
          onSelect={(item) => handleClick(item, 2)}
          loading={loadingAppVersions}
          selectedKey={selectedAppVersionId}
          columnWidthClass={ColumnWidthCssClass.Three}
          columnHeightClass={ColumnHeightCssClass.Big}
          menuItems={[]}
        />
        <DataColumnItems
          name={t('appBaseTemplate.columnTitles.baseTemplate')}
          data={baseTemplates}
          onSelect={(item) => handleClick(item, 3)}
          loading={loadingBaseTemplates}
          selectedKey={selectedBaseTemplateId}
          columnWidthClass={ColumnWidthCssClass.Three}
          columnHeightClass={ColumnHeightCssClass.Big}
          menuItems={getBaseTemplateMenuItems()}
        />
      </div>
      <div className="app-base-template-data">
        <Spin spinning={isLoading}>
          {selectedBaseTemplateId ? (
            <MerchantSettings
              key={`template-${selectedBaseTemplateId}-tab-${activeTab}`}
              initialTabKey={activeTab}
              tabSettings={tabSettings}
              onTabChange={handleTabChangeModal}
              templateData={templateData}
              arrayItemDisplayConfigs={{
                Merchants: 'lszMerchantName',
                Charities: 'lszCharityName',
              }}
              arrayItemLabels={{
                Processors: 'Processor',
                PaymentModes: 'Payment Mode',
                CharityAmounts: 'Charity Amount',
                'CharityAmounts/Amounts': 'Amount',
              }}
            />
          ) : (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={t('appBaseTemplate.actions.pleaseSelectTemplate')}
            />
          )}
        </Spin>
      </div>
    </div>
  );
}

export default AppBaseTemplatePage;
