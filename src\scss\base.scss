@use './helpers/variables' as *;

body *,
input,
textarea,
select {
  font-family: 'Poppins', sans-serif !important;
}

::placeholder {
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 160%;
}

.h2-bold {
  font-size: 32px;
  font-style: normal;
  font-weight: 600;
  line-height: 138%;
}

.h4-bold {
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: 138%;
}

.h5-bold {
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
  line-height: 120%;
}

.body-semiBold {
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 160%;
}

.body-1-regular {
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 120%;
}

.body-1-bold {
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 120%;
}

.body-2-regular {
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 122%;
}

.body-2-bold {
  font-size: 12px;
  font-style: normal;
  font-weight: 600;
  line-height: 122%;
}

.btn-base {
  height: 32px;
  width: 135px;
  gap: 8px;
}

.text-center {
  text-align: center;
}

.color-nobel {
  color: $nobel-color;
}

.color-green {
  color: $green-color;
}

.color-sunset {
  color: $sunset-color;
}

.des-color {
  color: $mid-night;
}

.color-blue {
  color: var(--main-color);
}

.color-secondary {
  color: $secondaryColor;
}

.mr-bottom-0 {
  margin-bottom: 0px;
}

.mr-bottom-5 {
  margin-bottom: 5px;
}

.pd-bottom-5 {
  padding-bottom: 5px;
}

.font-w-medium {
  font-weight: 500;
}

.no-padding {
  padding: 0 !important;
}

.no-margin {
  margin: 0 !important;
}

.padding-20 {
  padding: 20px;
}

.mr-bottom-24 {
  margin-bottom: 24px !important;
}

.mr-top-24 {
  margin-top: 24px !important;
}

.mr-top-36 {
  margin-top: 36px !important;
}

.mr-right-4 {
  margin-right: 4px;
}

.mr-right-12 {
  margin-right: 12px;
}

.mr-right-16 {
  margin-right: 16px;
}

.mr-bottom-8 {
  margin-bottom: 8px !important;
}

.text-success {
  color: $secondaryColor;
}

.text-warning {
  color: #f59f00;
}

.text-error {
  color: #f35848;
}

.mr-bottom-20 {
  margin-bottom: 20px;
}

.padding-bottom-0 {
  padding-bottom: 0px !important;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: $black-color;
}

.mid-night-border {
  border-color: $mid-night;
}

.fw-500 {
  font-weight: 500;
}

.some-class {
  color: var(--main-color);
}

.opacity-80 {
  opacity: 0.8;
}

@media only screen and (max-width: 576px) {
  .mobile-hidden {
    display: none;
  }

  .mobile-mr-bottom-20 {
    margin-bottom: 20px;
  }
}

@media only screen and (min-width: 576px) {
  .desktop-hidden {
    display: none;
  }
}

$scrollbar-width: 1px;
$scrollbar-width-slim: 2px;
$color-border: #d9d9d9;
// Mixins
@mixin scrollbar($width: $scrollbar-width, $thumb-color: $color-border) {
  scrollbar-width: thin;
  scrollbar-color: $thumb-color transparent;

  &::-webkit-scrollbar {
    width: $width;
  }

  &::-webkit-scrollbar-thumb {
    background-color: $thumb-color;
    border-radius: calc($width / 2);
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }
}

* {
  @include scrollbar();
}

::-webkit-scrollbar {
  width: 2px !important;
}

div::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(203, 81, 81, 0.3) !important;
}

div::-webkit-scrollbar-thumb {
  background-color: darkgrey;
  outline: 1px solid slategrey;
}

.main-color {
  color: var(--main-1);
}

.alert-animation {
  animation: slideDown 0.3s ease-in-out;
  opacity: 1;
  transform-origin: top center;
  display: inline-block;
  width: auto;
  text-align: center;

  .ant-alert-content {
    flex: none;
    display: inline-block;
  }

  .ant-alert-message {
    text-align: center;
  }
}

.alert-animation-wrapper {
  width: 100%;
  display: flex;
  justify-content: center;
}

@keyframes slideDown {
  0% {
    opacity: 0;
    transform: translateY(-10px) scale(0.98);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.text-overflow {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}
