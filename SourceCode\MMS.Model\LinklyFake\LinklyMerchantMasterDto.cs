using Newtonsoft.Json;

namespace MMS.Model.LinklyFake
{
    /// <summary>
    /// DTO for merchant master data from the LinklyFake API
    /// </summary>
    public class LinklyMerchantMasterDto
    {
        /// <summary>
        /// Merchant ID
        /// </summary>
        [JsonProperty("id")]
        public int Id { get; set; }

        /// <summary>
        /// Merchant name
        /// </summary>
        [JsonProperty("merchantName")]
        public string MerchantName { get; set; } = string.Empty;

        /// <summary>
        /// Email address
        /// </summary>
        [JsonProperty("emailAddress")]
        public string EmailAddress { get; set; } = string.Empty;

        /// <summary>
        /// Logo
        /// </summary>
        [JsonProperty("logo")]
        public string? Logo { get; set; }

        /// <summary>
        /// Merchant ID
        /// </summary>
        [JsonProperty("merchantID")]
        public string MerchantID { get; set; } = string.Empty;

        /// <summary>
        /// Phone number
        /// </summary>
        [JsonProperty("phone")]
        public string Phone { get; set; } = string.Empty;

        /// <summary>
        /// Mobile phone number
        /// </summary>
        [JsonProperty("mobilePhone")]
        public string MobilePhone { get; set; } = string.Empty;

        /// <summary>
        /// Company detail ID
        /// </summary>
        [JsonProperty("companyDetailId")]
        public int? CompanyDetailId { get; set; }

        /// <summary>
        /// Personal detail ID
        /// </summary>
        [JsonProperty("personalDetailId")]
        public int? PersonalDetailId { get; set; }

        /// <summary>
        /// Business hour ID
        /// </summary>
        [JsonProperty("businessHourId")]
        public int? BusinessHourId { get; set; }

        /// <summary>
        /// Merchant company ID
        /// </summary>
        [JsonProperty("merchantCompanyId")]
        public int? MerchantCompanyId { get; set; }

        /// <summary>
        /// Latitude
        /// </summary>
        [JsonProperty("latitude")]
        public decimal Latitude { get; set; }

        /// <summary>
        /// Longitude
        /// </summary>
        [JsonProperty("longitude")]
        public decimal Longitude { get; set; }

        /// <summary>
        /// Is roaming
        /// </summary>
        [JsonProperty("isRoaming")]
        public bool IsRoaming { get; set; }

        /// <summary>
        /// Picture
        /// </summary>
        [JsonProperty("picture")]
        public string? Picture { get; set; }

        /// <summary>
        /// Street address geo
        /// </summary>
        [JsonProperty("streetAddressGeo")]
        public string StreetAddressGeo { get; set; } = string.Empty;

        /// <summary>
        /// IP geo
        /// </summary>
        [JsonProperty("ipGeo")]
        public string IPGeo { get; set; } = string.Empty;

        /// <summary>
        /// Mobile location
        /// </summary>
        [JsonProperty("fMobileLocation")]
        public bool FMobileLocation { get; set; }

        /// <summary>
        /// Use sort business cate
        /// </summary>
        [JsonProperty("useSortBusinessCate")]
        public bool UseSortBusinessCate { get; set; }

        /// <summary>
        /// Use sort contact persons
        /// </summary>
        [JsonProperty("useSortContactPersons")]
        public bool UseSortContactPersons { get; set; }

        /// <summary>
        /// Use sort merchant users
        /// </summary>
        [JsonProperty("useSortMerchantUsers")]
        public bool UseSortMerchantUsers { get; set; }

        /// <summary>
        /// Is user changes allowed
        /// </summary>
        [JsonProperty("isUserChangesAllowed")]
        public bool IsUserChangesAllowed { get; set; }

        /// <summary>
        /// Is merchant template
        /// </summary>
        [JsonProperty("isMerchantTemplate")]
        public bool IsMerchantTemplate { get; set; }

        /// <summary>
        /// Linkly ID
        /// </summary>
        [JsonProperty("linklyId")]
        public string LinklyId { get; set; } = string.Empty;

        /// <summary>
        /// Location
        /// </summary>
        [JsonProperty("location")]
        public string Location { get; set; } = string.Empty;

        /// <summary>
        /// Location area
        /// </summary>
        [JsonProperty("locationArea")]
        public string LocationArea { get; set; } = string.Empty;

        /// <summary>
        /// System ID
        /// </summary>
        [JsonProperty("systemId")]
        public int SystemId { get; set; }

        /// <summary>
        /// Status
        /// </summary>
        [JsonProperty("status")]
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Created date
        /// </summary>
        [JsonProperty("created")]
        public DateTime Created { get; set; }

        /// <summary>
        /// Modified date
        /// </summary>
        [JsonProperty("modified")]
        public DateTime Modified { get; set; }

        /// <summary>
        /// Last updated date
        /// </summary>
        [JsonProperty("lastUpdated")]
        public DateTime LastUpdated { get; set; }
    }
}
