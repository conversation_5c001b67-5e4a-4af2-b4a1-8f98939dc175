.app-header {
  background: #fff;
  padding: 0 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 72px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  border-radius: 6px;
  flex-grow: 1;

  .header-left {
    .ant-breadcrumb {
      margin-top: 3px;
      .ant-breadcrumb-separator {
        color: #00a26d;
      }
      li:last-child {
        .ant-breadcrumb-link a {
          color: #00a26d;
        }
      }
    }
  }

  .header-right {
    display: flex;
    align-items: center;

    .ant-space {
      display: flex;
      align-items: center;

      .notification-icon {
        font-size: 20px;
        color: #8c8c8c;
        margin-right: 16px;
      }

      .ant-avatar {
        margin-right: 8px;
      }

      .user-info {
        display: flex;
        flex-direction: column;
        margin-right: 8px;

        .user-name {
          font-weight: 500;
          color: #000;
        }

        .user-role {
          font-size: 12px;
          color: #8c8c8c;
        }
      }

      .more-icon {
        font-size: 20px;
        color: #8c8c8c;
        cursor: pointer;
      }
    }
  }
}

.header-content {
  display: flex;
  gap: 16px;

  .company-collapse {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #fff;
    border-radius: 6px;
    gap: 20px;
    width: 240px;
    height: 72px;
    padding: 12px 16px;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

    .body-2-regular {
      font-size: 12px;
      color: #8c8c8c;
      margin-top: 3px;
      display: inline-block;
      width: 100px;
    }

    .company-collapse-menu {
      display: flex;
      align-items: center;
      gap: 12px;
      height: 100%;

      [class^='icon-'] {
        cursor: pointer;
        color: #8c8c8c;

        &:hover {
          color: #1890ff;
        }
      }
    }
  }
}

.company-collapse {
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 6px;
  gap: 20px;
  width: 240px;
  height: 72px;
  padding: 12px 16px;

  .body-2-bold {
    font-weight: bold;
  }

  .body-2-regular {
    font-size: 12px;
    margin-top: 3px;
  }

  .company-collapse-menu {
    display: flex;
    align-items: center;
    gap: 3px;
    height: 72px;
  }

  div {
    line-height: 1;
  }
}
