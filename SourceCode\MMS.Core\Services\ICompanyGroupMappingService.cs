﻿using MMS.Core.CoreUTI.Enum;
using MMS.Core.Entities;
using MMS.Core.Services.Base;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MMS.Core.Services
{
    public interface ICompanyGroupMappingService : IBaseService<CompanyGroupMapping>
    {
        /// <summary>
        /// Get item by id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<CompanyGroupMapping> GetByIdAsync(int id);

        /// <summary>
        /// Get item by ids
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        Task<List<CompanyGroupMapping>> GetByIdsAsync(IList<int> ids);

        /// <summary>
        /// Get Hierarchy Ids
        /// </summary>
        /// <param name="parentId"></param>
        /// <returns></returns>
        Task<List<int>> GetHierarchyIdsAsync(int parentId);

        /// <summary>
        /// Get Selected items
        /// </summary>
        /// <param name="type"></param>
        /// <param name="parentId"></param>
        /// <param name="screenType"></param>
        /// <param name="includeDeactivatedOrg"></param>
        /// <returns></returns>
        Task<List<CompanyGroupMapping>> GetSelectedAsync(CompanyGroupMappingType type, int? parentId, ScreenType screenType, bool includeDeactivatedOrg = false);

        /// <summary>
        /// Get children items of a mapping with parent id
        /// </summary>
        /// <param name="parentId"></param>
        /// <param name="isIncludeGroup"></param>
        /// <returns></returns>
        Task<List<CompanyGroupMapping>> GetCompanyTreeChildrenMappingsAsync(int? parentId, bool isIncludeGroup = true);

        /// <summary>
        /// Get children items of a mapping with parent id  partner access
        /// </summary>
        /// <param name="parentId"></param>
        /// <returns></returns>
        Task<List<CompanyGroupMapping>> GetPartnerAccessTreeChildrenMappingsAync(int? parentId);

        /// <summary>
        /// Get Selected Items By Parent Ids
        /// </summary>
        /// <param name="type"></param>
        /// <param name="parentIds"></param>
        /// <param name="includeDeactivatedOrg"></param>
        /// <returns></returns>
        Task<List<CompanyGroupMapping>> GetSelectedByParentIdsAsync(CompanyGroupMappingType type, IList<int> parentIds, bool includeDeactivatedOrg = false);

        /// <summary>
        /// Get By Type
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        Task<List<CompanyGroupMapping>> GetByTypeAsync(CompanyGroupMappingType type);

        /// <summary>
        ///  Update Selected Company Group Mapping
        /// </summary>
        /// <param name="selectedIdsint"></param>
        /// <param name="parentId"></param>
        /// <param name="type"></param>
        /// <param name="useSelectSort"></param>
        /// <param name="screenType"></param>
        /// <returns></returns>
        Task UpdateSelectedAsync(IList<int> selectedIdsint, int? parentId, CompanyGroupMappingType type, ScreenType screenType, bool useSelectSort = false);

        /// <summary>
        /// Activate or Deactivate multi groups
        /// </summary>
        /// <param name="ids"></param>
        /// <param name="isActivate"></param>
        void ActivateDeactivateByListIds(IList<int> ids, bool isActivate);

        /// <summary>
        /// Update IsHideDeactivated by Parent id
        /// </summary>
        /// <param name="parentId"></param>
        /// <returns></returns>
        Task UpdateIsHideDeactivatedByParentIdAsync(int parentId);

        /// <summary>
        /// Update Configure by type
        /// </summary>
        /// <param name="type"></param>
        bool UpdateConfigureByType(CompanyGroupMappingType type);

        /// <summary>
        /// Get Configure by type
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        bool GetConfigByType(CompanyGroupMappingType type);

        /// <summary>
        /// Get Configure by Parent id
        /// </summary>
        /// <param name="parentId"></param>
        /// <returns></returns>
        bool GetConfigByParentId(int parentId);

        /// <summary>
        /// Get group sources for sub partner
        /// </summary>
        /// <param name="accessPartnerId"></param>
        /// <returns></returns>
        Task<List<CompanyGroup>> GetGroupSourcesForSubPartnerAsync(int accessPartnerId);

        /// <summary>
        /// Retrieves a <see cref="CompanyGroupMapping"/> entity based on the specified company ID and screen type
        /// by delegating the request to the repository layer.
        /// </summary>
        /// <param name="companyId">The ID of the company to filter by. Can be null.</param>
        /// <param name="screenType">The screen type to filter by.</param>
        /// <returns>
        /// An instance of <see cref="CompanyGroupMapping"/> if a matching entity is found; otherwise, null.
        /// </returns>
        /// <remarks>
        /// This method serves as a service layer wrapper for the repository method <see cref="Repository.ICompanyGroupMappingRepository.GetCompanyByCompanyIdAndScreenType(int?, ScreenType)"/>.
        /// It ensures separation of concerns between business logic and data access.
        /// </remarks>
        Task<CompanyGroupMapping> GetCompanyByCompanyIdAndScreenType(int? companyId, ScreenType screenType);

        /// <summary>
        /// Get selected groups by company or subsidiary
        /// </summary>
        /// <param name="parentId"></param>
        /// <returns></returns>
        Task<List<CompanyGroupMapping>> GetSelectedGroupsByCompanyOrSubsidiaryAsync(int parentId);

        /// <summary>
        /// Get group sources for sub partner
        /// </summary>
        /// <param="parentId"></param>
        /// <returns></returns>
        Task<List<int>> GetExistingCompanyIdsInTreeAsync(int? parentId);

        /// <summary>
        /// Get selected Application by Company id or Subsidiary id
        /// </summary>
        /// <param name="companyMasterId"></param>
        /// <param name="groupId"></param>
        /// <returns></returns>
        Task<List<CompanyGroupMapping>> GetSelectedAppByCompanyOrSubsidiaryAsync(int companyMasterId, int groupId);

        /// <summary>
        /// Get selected app version id
        /// </summary>
        /// <returns></returns>
        Task<List<int>> GetSelectedAppVersionIdsAsync();

        /// <summary>
        /// Get all company tree mappings at once instead of by parentId
        /// </summary>
        /// <returns>Complete list of partner access mappings</returns>
        Task<List<CompanyGroupMapping>> GetAllCompanyForSearchTreeMappingsAsync();

        /// <summary>
        /// Get all partner access tree mappings at once instead of by parentId
        /// </summary>
        /// <returns>Complete list of partner access mappings</returns>
        Task<List<CompanyGroupMapping>> GetAllPartnerAccessTreeMappingsAsync();

        /// <summary>
        /// Get all ids by hierarchy
        /// </summary>
        /// <param name="hierarchy"></param>
        /// <returns></returns>
        Task<List<int>> GetAllParentIdsByHierarchy(string hierarchy);

        /// <summary>
        /// Insert company group in company group mapping
        /// </summary>
        Task InsertGroupAsync(int groupId, int parentId);
    }
}
