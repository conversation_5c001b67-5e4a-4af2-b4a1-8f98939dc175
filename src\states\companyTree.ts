import { atom } from 'recoil';
import { APP_STATE_KEYS } from '../constants/app-state.constants';
import { CompanyItem } from '../models/company-tree.model';

interface CompanyTreeState {
  selectedItems: CompanyItem[];
  lastSelectedItem: CompanyItem | null;
  parentItem: CompanyItem | null;  // Added parentItem property  in case this is company
  isCollapse: boolean;
  isIncludeGroup?: boolean;
}

const showCompanyTreeState = atom<boolean | undefined>({
  key: APP_STATE_KEYS.SHOW_COMPANY_TREE_STATE,
  default: undefined,
});

const collapseCompanyTreeState = atom<boolean | undefined>({
  key: APP_STATE_KEYS.COLLAPSE_COMPANY_TREE_STATE,
  default: undefined,
});

const companyTreeDataState = atom<CompanyTreeState>({
  key: APP_STATE_KEYS.COMPANY_TREE_DATA_STATE,
  default: {
    selectedItems: [],
    lastSelectedItem: null,
    parentItem: null,  // Initialize parentItem as null
    isCollapse: false,
    isIncludeGroup: true,
  },
});

export { showCompanyTreeState, companyTreeDataState, collapseCompanyTreeState };
