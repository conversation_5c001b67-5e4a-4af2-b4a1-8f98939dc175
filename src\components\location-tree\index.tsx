import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useRecoilState } from 'recoil';
import DataColumnItems, { DataItem } from '../shared/column/index';
import SelectLocationsModal from './select-locations';
import './index.scss';
import useCompanyTree from '../../hooks/useCompanyTree';
import useStoredLocations from '../../hooks/useStoredLocations';
import {
  ColumnWidthCssClass,
  CompanyItemType,
  LocationLevelId,
  ColumnHeightCssClass,
  LocationLevel,
} from '../../constants/app.enums';
import { LocationTreeApi } from '../../apis/location-tree.api';
import {
  LocationGlobalList,
  LocationStorageItem,
} from '../../models/location-tree.model';
import { selectedLocationItemState } from '../../states/location-tree.state';
import LocalUtils from '../../utils/local.utils';
import { LOCAL_STORAGE_KEY } from '../../constants/app-constants';
import SelectMerchantsModal from './select-merchants';

export interface LocationTreeItem extends DataItem {
  locationLevel?: number;
}

interface LocationSelectDataModal {
  parentItemId: number | null;
  columnName: string;
  selectLevel: LocationLevel;
}

const convertToDataItem = (
  locationList: LocationGlobalList,
): LocationTreeItem[] => {
  return locationList.locationItems.map((item) => ({
    key: item.id,
    label: item.name || '',
    locationLevel: locationList.location.locationLevel,
  }));
};

const getNextLocationLevel = (currentLevel: number): number => {
  switch (currentLevel) {
    case LocationLevelId.Region:
      return LocationLevelId.Country;
    case LocationLevelId.Country:
      return LocationLevelId.State;
    case LocationLevelId.State:
      return LocationLevelId.City;
    case LocationLevelId.City:
      return LocationLevelId.Suburb;
    case LocationLevelId.Suburb:
      return LocationLevelId.SuburbArea;
    case LocationLevelId.SuburbArea:
      return LocationLevelId.Locations;
    case LocationLevelId.Locations:
      return LocationLevelId.LocationAreas;
    case LocationLevelId.LocationAreas:
      return LocationLevelId.LocationAreas;
    default:
      return currentLevel + 1;
  }
};

const LocationTree: React.FC = () => {
  const { lastSelectedItem } = useCompanyTree();
  const [, setSelectedLocation] = useRecoilState(selectedLocationItemState);
  const [regions, setRegions] = useState<LocationTreeItem[]>([]);
  const [countries, setCountries] = useState<LocationTreeItem[]>([]);
  const [states, setStates] = useState<LocationTreeItem[]>([]);
  const [cities, setCities] = useState<LocationTreeItem[]>([]);
  const [suburbs, setSuburbs] = useState<LocationTreeItem[]>([]);
  const [suburbAreas, setSuburbAreas] = useState<LocationTreeItem[]>([]);
  const [locations, setLocations] = useState<LocationTreeItem[]>([]);
  const [locationAreas, setLocationAreas] = useState<LocationTreeItem[]>([]);
  const [merchants, setMerchants] = useState<LocationTreeItem[]>([]);

  // Add selected states for each column
  const [selectedRegionId, setSelectedRegionId] = useState<number | null>(null);
  const [selectedCountryId, setSelectedCountryId] = useState<number | null>(
    null,
  );
  const [selectedStateId, setSelectedStateId] = useState<number | null>(null);
  const [selectedCityId, setSelectedCityId] = useState<number | null>(null);
  const [selectedSuburbId, setSelectedSuburbId] = useState<number | null>(null);
  const [selectedSuburbAreaId, setSelectedSuburbAreaId] = useState<
    number | null
  >(null);
  const [selectedLocationId, setSelectedLocationId] = useState<number | null>(
    null,
  );
  const [selectedLocationAreaId, setSelectedLocationAreaId] = useState<
    number | null
  >(null);
  const [selectedMerchantId, setSelectedMerchantId] = useState<number | null>(
    null,
  );

  // Array of reset functions for data states
  const resetDataFunctions = useMemo(
    () => [
      setRegions,
      setCountries,
      setStates,
      setCities,
      setSuburbs,
      setSuburbAreas,
      setLocations,
      setLocationAreas,
      setMerchants,
    ],
    [],
  );

  // Array of reset functions for selected states
  const resetSelectedFunctions = useMemo(
    () => [
      setSelectedRegionId,
      setSelectedCountryId,
      setSelectedStateId,
      setSelectedCityId,
      setSelectedSuburbId,
      setSelectedSuburbAreaId,
      setSelectedLocationId,
      setSelectedLocationAreaId,
      setSelectedMerchantId,
    ],
    [],
  );

  // Function to reset both data and selected states from a specific index
  const resetFromIndex = useCallback(
    (startIndex: number) => {
      // Reset data states
      resetDataFunctions.slice(startIndex + 1).forEach((reset) => reset([]));
      // Reset selected states
      resetSelectedFunctions.slice(startIndex).forEach((reset) => reset(null));
    },
    [resetDataFunctions, resetSelectedFunctions],
  );

  useEffect(() => {
    if (regions.length === 0) {
      setSelectedLocation({ key: 0, label: '', locationLevel: 0 });
    }
  }, [regions, setSelectedLocation]);

  useEffect(() => {
    if (lastSelectedItem?.itemType !== CompanyItemType.Group) {
      setSelectedLocation({ key: 0, label: '', locationLevel: 0 });
    }
    clearLocations();
  }, [lastSelectedItem, setSelectedLocation]);

  const [loadingRegions, setLoadingRegions] = useState(false);
  const [loadingCountries, setLoadingCountries] = useState(false);
  const [loadingStates, setLoadingStates] = useState(false);
  const [loadingCities, setLoadingCities] = useState(false);
  const [loadingSuburbs, setLoadingSuburbs] = useState(false);
  const [loadingSuburbAreas, setLoadingSuburbAreas] = useState(false);
  const [loadingLocations, setLoadingLocations] = useState(false);
  const [loadingLocationAreas, setLoadingLocationAreas] = useState(false);
  const [loadingMerchants, setLoadingMerchants] = useState(false);

  const [pendingClicks, setPendingClicks] = useState<LocationStorageItem[]>([]);
  const [processingLevel, setProcessingLevel] = useState<number | null>(null);

  // State for SelectLocationsModal
  const [isSelectModalVisible, setIsSelectModalVisible] = useState(false);
  const [modalKey, setModalKey] = useState(0);
  const [modalSelectedItem, setModalSelectedItem] =
    useState<LocationSelectDataModal>();

  const [isSelectMerchantsModalVisible, setIsSelectMerchantsModalVisible] =
    useState(false);
  const [selectedMerchantLocationId, setSelectedMerchantLocationId] = useState<
    number | null
  >(null);

  useEffect(() => {
    if (pendingClicks.length > 0 && processingLevel === null) {
      const processNextClick = async () => {
        const location = pendingClicks[0];
        const remainingClicks = pendingClicks.slice(1);

        let items: LocationTreeItem[] = [];
        let selectedId: number | null;
        let handleSelect: (item: LocationTreeItem) => Promise<void>;
        let isLoading: boolean;

        switch (location.level) {
          case LocationLevelId.Region:
            items = regions;
            selectedId = selectedRegionId;
            handleSelect = handleSelectRegion;
            isLoading = loadingRegions;
            break;
          case LocationLevelId.Country:
            items = countries;
            selectedId = selectedCountryId;
            handleSelect = handleSelectCountry;
            isLoading = loadingCountries;
            break;
          case LocationLevelId.State:
            items = states;
            selectedId = selectedStateId;
            handleSelect = handleSelectState;
            isLoading = loadingStates;
            break;
          case LocationLevelId.City:
            items = cities;
            selectedId = selectedCityId;
            handleSelect = handleSelectCity;
            isLoading = loadingCities;
            break;
          case LocationLevelId.Suburb:
            items = suburbs;
            selectedId = selectedSuburbId;
            handleSelect = handleSelectSuburb;
            isLoading = loadingSuburbs;
            break;
          case LocationLevelId.SuburbArea:
            items = suburbAreas;
            selectedId = selectedSuburbAreaId;
            handleSelect = handleSelectSuburbArea;
            isLoading = loadingSuburbAreas;
            break;
          case LocationLevelId.Locations:
            items = locations;
            selectedId = selectedLocationId;
            handleSelect = handleSelectLocation;
            isLoading = loadingLocations;
            break;
          case LocationLevelId.LocationAreas:
            items = locationAreas;
            selectedId = selectedLocationAreaId;
            handleSelect = handleSelectLocationArea;
            isLoading = loadingLocationAreas;
            break;
          default:
            setPendingClicks(remainingClicks);
            setProcessingLevel(null);
            return;
        }

        // If data is still loading, wait for next effect run
        if (isLoading) {
          return;
        }

        setProcessingLevel(location.level);

        const item = items.find((item) => item.key === location.id);

        if (item && selectedId !== item.key) {
          await handleSelect(item);
          setPendingClicks(remainingClicks);
          setProcessingLevel(null);
        } else if (!item) {
          setPendingClicks([]);
          setProcessingLevel(null);
        }
      };

      processNextClick();
    }
  }, [pendingClicks, processingLevel]);

  useEffect(() => {
    const isAnyLoading =
      loadingRegions ||
      loadingCountries ||
      loadingStates ||
      loadingCities ||
      loadingSuburbs ||
      loadingSuburbAreas ||
      loadingLocations ||
      loadingLocationAreas ||
      loadingMerchants;

    const isProcessingAuto =
      pendingClicks.length > 0 || processingLevel !== null;

    if (!isAnyLoading && !isProcessingAuto) {
      LocalUtils.set(LOCAL_STORAGE_KEY.IS_LOCATION_TREE_LOADED, 'true');
    } else {
      LocalUtils.set(LOCAL_STORAGE_KEY.IS_LOCATION_TREE_LOADED, 'false');
    }
  }, [
    loadingRegions,
    loadingCountries,
    loadingStates,
    loadingCities,
    loadingSuburbs,
    loadingSuburbAreas,
    loadingLocations,
    loadingLocationAreas,
    loadingMerchants,
    pendingClicks,
    processingLevel,
  ]);

  const loadInitialData = useCallback(async () => {
    if (lastSelectedItem?.itemType === CompanyItemType.Group) {
      LocalUtils.set(LOCAL_STORAGE_KEY.IS_LOCATION_TREE_LOADED, 'false');
      try {
        resetFromIndex(0);
        setLoadingRegions(true);
        const locations = LocalUtils.getLocationSelections();
        const lastSelectedLocations: Array<{ key: number; value: number }> = [];

        if (locations) {
          for (const item of locations) {
            lastSelectedLocations.push({ key: item.level, value: item.id });
          }
        }

        const response = await LocationTreeApi.getLocationTree(
          {
            groupMappingId: lastSelectedItem.id,
            screenType: '1',
            isHasLocationZone: false,
            cloudSecurityAccessMappingId: 0,
          },
          lastSelectedLocations,
        );

        // Only set regions data
        response.data.locationGlobalLists.forEach(
          (locationList: LocationGlobalList) => {
            const items = convertToDataItem(locationList);
            if (locationList.location.id === LocationLevelId.Region) {
              setRegions(items);
            }
          },
        );

        // After loading region data, set up pending clicks
        if (locations) {
          setPendingClicks(locations);
        } else {
          LocalUtils.set(LOCAL_STORAGE_KEY.IS_LOCATION_TREE_LOADED, 'true');
        }
      } catch (error) {
        console.error('Error loading location tree:', error);
        setRegions([]);
      } finally {
        setLoadingRegions(false);
      }
    } else {
      setRegions([]);
      resetFromIndex(0);
    }
  }, [lastSelectedItem, resetFromIndex]);

  const { addLocation, clearLocations } = useStoredLocations();

  const handleSelectRegion = async (item: LocationTreeItem) => {
    updateStoredLocations(LocationLevelId.Region, item.key);
    setSelectedLocation(item);
    setSelectedRegionId(item.key);
    resetFromIndex(1); // Reset from Country onwards
    await loadLocationBelow(
      item.key,
      LocationLevelId.Region,
      setLoadingCountries,
      setCountries,
    );
  };

  const handleSelectCountry = async (item: LocationTreeItem) => {
    updateStoredLocations(LocationLevelId.Country, item.key);
    setSelectedLocation(item);
    setSelectedCountryId(item.key);
    resetFromIndex(2); // Reset from State onwards
    await loadLocationBelow(
      item.key,
      LocationLevelId.Country,
      setLoadingStates,
      setStates,
    );
  };

  const handleSelectState = async (item: LocationTreeItem) => {
    updateStoredLocations(LocationLevelId.State, item.key);
    setSelectedLocation(item);
    setSelectedStateId(item.key);
    resetFromIndex(3); // Reset from City onwards
    await loadLocationBelow(
      item.key,
      LocationLevelId.State,
      setLoadingCities,
      setCities,
    );
  };

  const handleSelectCity = async (item: LocationTreeItem) => {
    updateStoredLocations(LocationLevelId.City, item.key);
    setSelectedLocation(item);
    setSelectedCityId(item.key);
    resetFromIndex(4); // Reset from Suburb onwards
    await loadLocationBelow(
      item.key,
      LocationLevelId.City,
      setLoadingSuburbs,
      setSuburbs,
    );
  };

  const handleSelectSuburb = async (item: LocationTreeItem) => {
    updateStoredLocations(LocationLevelId.Suburb, item.key);
    setSelectedLocation(item);
    setSelectedSuburbId(item.key);
    resetFromIndex(5); // Reset from Suburb Area onwards
    await loadLocationBelow(
      item.key,
      LocationLevelId.Suburb,
      setLoadingSuburbAreas,
      setSuburbAreas,
    );
  };

  const handleSelectSuburbArea = async (item: LocationTreeItem) => {
    updateStoredLocations(LocationLevelId.SuburbArea, item.key);
    setSelectedLocation(item);
    setSelectedSuburbAreaId(item.key);
    resetFromIndex(6); // Reset from Location onwards
    await loadLocationBelow(
      item.key,
      LocationLevelId.SuburbArea,
      setLoadingLocations,
      setLocations,
    );
  };

  const handleSelectLocation = async (item: LocationTreeItem) => {
    updateStoredLocations(LocationLevelId.Locations, item.key);
    setSelectedLocation(item);
    setSelectedLocationId(item.key);
    resetFromIndex(7); // Reset from Location Area onwards
    await loadLocationBelow(
      item.key,
      LocationLevelId.Locations,
      setLoadingLocationAreas,
      setLocationAreas,
    );
  };

  const handleSelectLocationArea = async (item: LocationTreeItem) => {
    updateStoredLocations(LocationLevelId.LocationAreas, item.key);
    setSelectedLocation(item);
    setSelectedLocationAreaId(item.key);
    resetFromIndex(8); // Reset from Merchants onwards
    try {
      setLoadingMerchants(true);
      const response = await LocationTreeApi.getMerchantLocationAreas({
        locationAreaId: item.key,
        cloudSecurityAccessMappingId: 0,
      });

      if (response.data) {
        const merchants = response.data.map((merchant) => ({
          key: merchant.id,
          label: merchant.name,
          locationLevel: LocationLevelId.LocationAreas,
          locationItemId: merchant.id,
          parentId: item.key,
          hierarchy: '',
          isSelected: false,
          deActivate: !merchant.isActive,
        }));
        setMerchants(merchants);
      } else {
        setMerchants([]);
      }
    } catch (error) {
      console.error('Error loading merchants:', error);
      setMerchants([]);
    } finally {
      setLoadingMerchants(false);
    }
  };

  const updateStoredLocations = useCallback(
    (level: number, id: number) => {
      const currentLocations = LocalUtils.getLocationSelections();
      const existingIndex = currentLocations.findIndex(
        (item) => item.level === level,
      );

      const newLocations = currentLocations.slice(
        0,
        existingIndex !== -1 ? existingIndex : currentLocations.length,
      );
      newLocations.push({ level, id });

      LocalUtils.setLocationSelections(newLocations);

      clearLocations();

      newLocations.forEach((location) => {
        addLocation(location);
      });
    },
    [addLocation, clearLocations],
  );

  const handleOpenSelectModal = useCallback(
    (locationSelectData: LocationSelectDataModal) => {
      setModalSelectedItem(locationSelectData);
      setIsSelectModalVisible(true);
    },
    [],
  );

  const handleCloseSelectModal = useCallback(() => {
    setIsSelectModalVisible(false);
  }, []);

  const handleAfterSave = useCallback(() => {
    if (modalSelectedItem && modalSelectedItem.parentItemId !== null) {
      const { selectLevel } = modalSelectedItem;

      let currentItem: LocationTreeItem | undefined;
      let handleSelect: (
        item: LocationTreeItem,
      ) => Promise<void> = async () => {};

      switch (selectLevel) {
        case LocationLevel.Region:
          loadInitialData();
          break;
        case LocationLevel.Country:
          currentItem = regions.find((item) => item.key === selectedRegionId);
          handleSelect = handleSelectRegion;
          break;
        case LocationLevel.State:
          currentItem = countries.find(
            (item) => item.key === selectedCountryId,
          );
          handleSelect = handleSelectCountry;
          break;
        case LocationLevel.City:
          currentItem = states.find((item) => item.key === selectedStateId);
          handleSelect = handleSelectState;
          break;
        case LocationLevel.Suburb:
          currentItem = cities.find((item) => item.key === selectedCityId);
          handleSelect = handleSelectSuburb;
          break;
        case LocationLevel.SuburbArea:
          currentItem = suburbs.find((item) => item.key === selectedSuburbId);
          handleSelect = handleSelectSuburbArea;
          break;
        case LocationLevel.Locations:
          currentItem = suburbAreas.find(
            (item) => item.key === selectedSuburbAreaId,
          );
          handleSelect = handleSelectLocation;
          break;
        case LocationLevel.LocationAreas:
          currentItem = locations.find(
            (item) => item.key === selectedLocationId,
          );
          handleSelect = handleSelectLocationArea;
          break;
      }

      if (currentItem && handleSelect) {
        setIsSelectModalVisible(false);
        setModalKey((prev) => prev + 1);
        handleSelect(currentItem);
      } else {
        setIsSelectModalVisible(false);
      }
    } else {
      setIsSelectModalVisible(false);
    }
  }, [modalSelectedItem]);

  const createLocationMenuItems = (
    locationSelectData: LocationSelectDataModal,
  ) => {
    if (
      locationSelectData.parentItemId !== null &&
      lastSelectedItem?.itemType === CompanyItemType.Group
    )
      return [
        {
          key: 'select',
          label: 'Select',
          onClick: () => handleOpenSelectModal(locationSelectData),
        },
      ];
  };

  const getColumnName = useCallback(
    (locationLevel: number | undefined): string => {
      switch (locationLevel) {
        case LocationLevelId.Region:
          return 'Region';
        case LocationLevelId.Country:
          return 'Country';
        case LocationLevelId.State:
          return 'State';
        case LocationLevelId.City:
          return 'City';
        case LocationLevelId.Suburb:
          return 'Suburb';
        case LocationLevelId.SuburbArea:
          return 'Suburb Area';
        case LocationLevelId.Locations:
          return 'Location';
        case LocationLevelId.LocationAreas:
          return 'Location Area';
        default:
          return '';
      }
    },
    [],
  );

  const loadLocationBelow = async (
    locationItemId: number,
    locationLevel: number,
    setLoading: (loading: boolean) => void,
    setData: (data: LocationTreeItem[]) => void,
  ) => {
    if (!lastSelectedItem) return;

    try {
      setLoading(true);
      const nextLevel = getNextLocationLevel(locationLevel);
      const response = await LocationTreeApi.getLocationBelow({
        locationItemId: locationItemId,
        locationId: locationLevel,
        locationIdsToLoad: [nextLevel],
        screenType: '1',
        groupMappingId: lastSelectedItem.id,
        cloudSecurityAccessMappingId: 0,
        isMerchantLocation: true,
        isEcommerce: true,
        locationParentIdsString: '',
        parentId: 0,
        locationParentId: 0,
        usedItemId: 0,
      });

      const locationList = response.data.value.locationGlobalLists[0];
      if (locationList) {
        const items = convertToDataItem(locationList);
        setData(items);
      } else {
        setData([]);
      }
    } catch (error) {
      console.error('Error loading location below:', error);
      setData([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadInitialData();
  }, [loadInitialData]);

  const handleOpenSelectMerchantsModal = useCallback((locationId: number) => {
    setSelectedMerchantLocationId(locationId);
    setIsSelectMerchantsModalVisible(true);
  }, []);

  const handleCloseSelectMerchantsModal = useCallback(() => {
    setIsSelectMerchantsModalVisible(false);
    setSelectedMerchantLocationId(null);
  }, []);

  const handleAfterSaveMerchants = useCallback(() => {
    if (selectedMerchantLocationId) {
      const currentItem = locationAreas.find(
        (item) => item.key === selectedLocationAreaId,
      );
      if (currentItem) {
        handleSelectLocationArea(currentItem);
      }
    }
    setIsSelectMerchantsModalVisible(false);
    setSelectedMerchantLocationId(null);
  }, [
    selectedMerchantLocationId,
    selectedLocationAreaId,
    locationAreas,
    handleSelectLocationArea,
  ]);

  const createMerchantMenuItems = useCallback(
    (locationId: number) => {
      if (lastSelectedItem?.itemType === CompanyItemType.Group) {
        return [
          {
            key: 'select',
            label: 'Select',
            onClick: () => handleOpenSelectMerchantsModal(locationId),
          },
        ];
      }
      return [];
    },
    [lastSelectedItem],
  );

  return (
    <div className="location-items-tree">
      <DataColumnItems
        name={getColumnName(LocationLevelId.Region)}
        data={regions}
        loading={loadingRegions}
        onSelect={handleSelectRegion}
        selectedKey={selectedRegionId}
        columnWidthClass={ColumnWidthCssClass.Three}
        columnHeightClass={ColumnHeightCssClass.Small}
        menuItems={createLocationMenuItems({
          parentItemId: 0,
          columnName: getColumnName(LocationLevelId.Region),
          selectLevel: LocationLevel.Region,
        })}
      />
      <DataColumnItems
        name={getColumnName(LocationLevelId.Country)}
        data={countries}
        onSelect={handleSelectCountry}
        loading={loadingCountries}
        selectedKey={selectedCountryId}
        columnWidthClass={ColumnWidthCssClass.Three}
        columnHeightClass={ColumnHeightCssClass.Small}
        menuItems={createLocationMenuItems({
          parentItemId: selectedRegionId,
          columnName: getColumnName(LocationLevelId.Country),
          selectLevel: LocationLevel.Country,
        })}
      />
      <DataColumnItems
        name={getColumnName(LocationLevelId.State)}
        data={states}
        onSelect={handleSelectState}
        loading={loadingStates}
        selectedKey={selectedStateId}
        columnWidthClass={ColumnWidthCssClass.Three}
        columnHeightClass={ColumnHeightCssClass.Small}
        menuItems={createLocationMenuItems({
          parentItemId: selectedCountryId,
          columnName: getColumnName(LocationLevelId.State),
          selectLevel: LocationLevel.State,
        })}
      />
      <DataColumnItems
        name={getColumnName(LocationLevelId.City)}
        data={cities}
        onSelect={handleSelectCity}
        loading={loadingCities}
        selectedKey={selectedCityId}
        columnWidthClass={ColumnWidthCssClass.Three}
        columnHeightClass={ColumnHeightCssClass.Small}
        menuItems={createLocationMenuItems({
          parentItemId: selectedStateId,
          columnName: getColumnName(LocationLevelId.City),
          selectLevel: LocationLevel.City,
        })}
      />
      <DataColumnItems
        name={getColumnName(LocationLevelId.Suburb)}
        data={suburbs}
        onSelect={handleSelectSuburb}
        loading={loadingSuburbs}
        selectedKey={selectedSuburbId}
        columnWidthClass={ColumnWidthCssClass.Three}
        columnHeightClass={ColumnHeightCssClass.Small}
        menuItems={createLocationMenuItems({
          parentItemId: selectedCityId,
          columnName: getColumnName(LocationLevelId.Suburb),
          selectLevel: LocationLevel.Suburb,
        })}
      />
      <DataColumnItems
        name={getColumnName(LocationLevelId.SuburbArea)}
        data={suburbAreas}
        onSelect={handleSelectSuburbArea}
        loading={loadingSuburbAreas}
        selectedKey={selectedSuburbAreaId}
        columnWidthClass={ColumnWidthCssClass.Three}
        columnHeightClass={ColumnHeightCssClass.Small}
        menuItems={createLocationMenuItems({
          parentItemId: selectedSuburbId,
          columnName: getColumnName(LocationLevelId.SuburbArea),
          selectLevel: LocationLevel.SuburbArea,
        })}
      />
      <DataColumnItems
        name={getColumnName(LocationLevelId.Locations)}
        data={locations}
        onSelect={handleSelectLocation}
        loading={loadingLocations}
        selectedKey={selectedLocationId}
        columnWidthClass={ColumnWidthCssClass.Three}
        columnHeightClass={ColumnHeightCssClass.Small}
        menuItems={createLocationMenuItems({
          parentItemId: selectedSuburbAreaId,
          columnName: getColumnName(LocationLevelId.Locations),
          selectLevel: LocationLevel.Locations,
        })}
      />
      <DataColumnItems
        name={getColumnName(LocationLevelId.LocationAreas)}
        data={locationAreas}
        onSelect={handleSelectLocationArea}
        loading={loadingLocationAreas}
        selectedKey={selectedLocationAreaId}
        columnWidthClass={ColumnWidthCssClass.Three}
        columnHeightClass={ColumnHeightCssClass.Small}
        menuItems={createLocationMenuItems({
          parentItemId: selectedLocationId,
          columnName: getColumnName(LocationLevelId.LocationAreas),
          selectLevel: LocationLevel.LocationAreas,
        })}
      />
      <DataColumnItems
        name="Merchants"
        data={merchants}
        onSelect={(item: LocationTreeItem) => {
          setSelectedMerchantId(item.key);
        }}
        loading={loadingMerchants}
        selectedKey={selectedMerchantId}
        columnWidthClass={ColumnWidthCssClass.Three}
        columnHeightClass={ColumnHeightCssClass.Small}
        menuItems={
          selectedLocationAreaId
            ? createMerchantMenuItems(selectedLocationAreaId)
            : []
        }
      />

      <SelectLocationsModal
        key={modalKey}
        visible={isSelectModalVisible}
        onCancel={handleCloseSelectModal}
        onAfterSave={handleAfterSave}
        parentItemId={modalSelectedItem?.parentItemId || 0}
        companyGroupMappingId={lastSelectedItem?.id || 0}
        columnName={modalSelectedItem?.columnName || ''}
        selectLevel={modalSelectedItem?.selectLevel || LocationLevel.Region}
      />

      <SelectMerchantsModal
        visible={isSelectMerchantsModalVisible}
        onCancel={handleCloseSelectMerchantsModal}
        onAfterSave={handleAfterSaveMerchants}
        locationId={selectedMerchantLocationId || 0}
      />
    </div>
  );
};

export default LocationTree;
