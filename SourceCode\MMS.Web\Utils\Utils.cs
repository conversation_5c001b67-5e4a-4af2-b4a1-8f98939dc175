﻿using Microsoft.AspNetCore.Html;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using MMS.Core.Entities;
using MMS.Core.Utils;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Drawing;
using System.Globalization;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Xml;
using System.Xml.Linq;
using HttpContext = MMS.Core.dbContext.HttpContext;

namespace MMS.Web.Utils
{
    public static class WebUtils
    {
        private static readonly Random RandomNumber;
        private const string SALT_PASS = "~!3J*E+Y_2#34=SH?JK-21^Y8~JJ";
        
        public static bool IsTokenExpired(string token)
        {
            var handler = new JwtSecurityTokenHandler();
            var jwtToken = handler.ReadJwtToken(token);

            var expClaim = jwtToken.Claims.FirstOrDefault(claim => claim.Type == "exp");
            if (expClaim != null)
            {
                var expTimestamp = long.Parse(expClaim.Value);
                var expDateTime = DateTimeOffset.FromUnixTimeSeconds(expTimestamp).UtcDateTime;

                // Compare expiration time to current UTC time
                return expDateTime < DateTime.UtcNow;
            }
            else
            {
                throw new Exception("Expiration claim not found in token.");
            }
        }

        public static async Task<string> CompressZip(string webRootPath, List<string> filepaths, string folderZip)
        {
            var directoryPath = Path.Combine(webRootPath, Constants.PATH_USERDATA, folderZip);
            var zipFileName = $"{DateTime.Now.Ticks}.zip";
            var zipFilePath = Path.Combine(directoryPath, zipFileName);

            if (!Directory.Exists(directoryPath))
            {
                Directory.CreateDirectory(directoryPath);
            }

            using (FileStream zipToOpen = new FileStream(zipFilePath, FileMode.Create))
            {
                using (ZipArchive archive = new ZipArchive(zipToOpen, ZipArchiveMode.Create))
                {
                    foreach (var filepath in filepaths)
                    {
                        if (File.Exists(filepath))
                        {
                            FileInfo fileInfo = new FileInfo(filepath);
                            string fileExtension = Path.GetExtension(fileInfo.Name).ToLower();
                            string entryName;

                            if (filepath.Contains("TerminaMasterSetupUpgradeRecord") || filepath.Contains(Constants.PATH_PARAMETER))
                            {
                                // Use template.json for JSON files, template.xml for XML files
                                entryName = fileExtension == ".json" ? Constants.API_SYNC_JSON_NAME : Constants.API_SYNC_XML_NAME;
                            }
                            else
                            {
                                entryName = fileInfo.Name;
                            }

                            // Create a new entry in the ZIP archive
                            ZipArchiveEntry entry = archive.CreateEntry(entryName);

                            // Open the source file to read its contents
                            using (FileStream sourceFileStream = new FileStream(filepath, FileMode.Open, FileAccess.Read))
                            {
                                using (Stream entryStream = entry.Open())
                                {
                                    // Copy the contents of the source file to the ZIP entry
                                    sourceFileStream.CopyTo(entryStream);
                                }
                            }
                        }
                    }
                }
            }

            return zipFilePath;
        }

        public static string GetDisplayEnum(this Enum GenericEnum)
        {
            Type genericEnumType = GenericEnum.GetType();
            MemberInfo[] memberInfo = genericEnumType.GetMember(GenericEnum.ToString());
            if ((memberInfo != null && memberInfo.Length > 0))
            {
                var _Attribs = memberInfo[0].GetCustomAttributes(typeof(DisplayAttribute), false);
                if ((_Attribs != null && _Attribs.Length > 0))
                {
                    return ((DisplayAttribute)_Attribs[0]).Name;
                }
            }

            return GenericEnum.ToString();
        }

        /// <summary>
        /// Removes the "Controller" suffix from the given controller name.
        /// </summary>
        /// <param name="controllerName">The name of the controller, typically ending with "Controller".</param>
        /// <returns>The controller name without the "Controller" suffix.</returns>
        public static string GetControllerName(string controllerName)
        {
            return controllerName.Replace("Controller", "");
        }

        public static List<SelectListItem> ToSelectListItem<T>(this T value, bool includeNone = true) where T : System.Enum
        {
            var result = new List<SelectListItem>();

            foreach (var each in Enum.GetValues(typeof(T)))
            {
                var displayName = GetDisplayEnum((Enum)each);
                result.Add(new SelectListItem(displayName, each.ToString()));
            }

            if (!includeNone)
                result.RemoveAt(0); // Remove none;

            return result;
        }

        public static IList<ViewItemModel> GetListViewItemInEnum<T>(T value, bool includeNone = true) where T : System.Enum
        {
            var result = new List<ViewItemModel>();

            foreach (var each in Enum.GetValues(typeof(T)))
            {
                var displayName = GetDisplayEnum((Enum)each);
                result.Add(new ViewItemModel(displayName, (long)each, value.HasFlag((Enum)each)));
            }

            if (!includeNone)
                result.RemoveAt(0); // Remove none;

            return result;
        }

        public static T ViewItemModelToEnum<T>(List<ViewItemModel> entities) where T : System.Enum
        {
            long result = 0;

            foreach (var each in entities)
            {
                if (each.Enabled) result = result | each.ValueEnum;
            }

            return (T)Enum.ToObject(typeof(T), result);
        }


        public static async Task<string> GetTerminalLogName(long date)
        {
            var fileName = $"{date.ToString()}.json";

            return fileName;
        }

        public static string GenerateKey(ViewContext viewContext, string label)
        {
            return "ScreenKey" + viewContext.ActionDescriptor.RouteValues["controller"] + viewContext.ActionDescriptor.RouteValues["action"] + "_" + label;
        }


        public static async Task<string> CreateTerminalLogFile(string filePath, string fileData)
        {

            // Create the file, or overwrite if the file exists.
            using (FileStream fs = System.IO.File.Create(filePath))
            {
                byte[] info = new System.Text.UTF8Encoding(true).GetBytes(fileData);
                // Add some information to the file.
                fs.Write(info, 0, info.Length);
            }

            return filePath;
        }

        public static async Task<string> ReadTerminalLogFile(string filePath)
        {
            string json = string.Empty;

            using (StreamReader r = new StreamReader(filePath))
            {
                json = r.ReadToEnd();
                // List<Item> items = JsonConvert.DeserializeObject<List<Item>>(json);
            }


            return json;
        }


        /// <summary>
        /// Get TerminalMaster Folder, each TerminalMasterId have their own folder.
        /// </summary>
        /// <param name="terminalMasterId"></param>
        /// <returns></returns>
        public static async Task<string> GetTerminalMasterFolder(int terminalMasterId)
        {
            var loggingPath = await MMS.Web.Utils.WebUtils.GetLoggingFolder();
            var folderName = $"terminalMasterId_{terminalMasterId}";
            var termianlMasterFolder = CreateDirectory(Path.Combine(loggingPath, folderName));

            if (Directory.Exists(termianlMasterFolder)) return termianlMasterFolder;

            Directory.CreateDirectory(termianlMasterFolder);

            return termianlMasterFolder;
        }

        public static async Task<string> GetFileLogPath(string fileName, int terminalMasterId)
        {
            var terminalFolder = await GetTerminalMasterFolder(terminalMasterId);
            var filePath = Path.Combine(terminalFolder, fileName);

            return filePath;
        }

        /// <summary>
        /// Get LoggingFolder Path, create if not existed.
        /// </summary>
        /// <returns></returns>
        public static async Task<string> GetLoggingFolder()
        {
            var loggingPath = PathHelper.GetFullPath(Constants.PATH_LOGGING_FILE);

            if (Directory.Exists(loggingPath)) return loggingPath;

            Directory.CreateDirectory(loggingPath);

            return loggingPath;
        }

        public static HtmlString SerializeObject(object value)
        {
            using (var stringWriter = new StringWriter())
            using (var jsonWriter = new JsonTextWriter(stringWriter))
            {
                var serializer = new JsonSerializer
                {
                    // Let's use camelCasing as is common practice in JavaScript
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                };

                // We don't want quotes around object names
                jsonWriter.QuoteName = false;
                serializer.Serialize(jsonWriter, value);

                return new HtmlString(stringWriter.ToString());
            }
        }

        public static string GetSecondsDifferenceBetweenTwoDays(DateTime startDate, DateTime endDate)
        {
            TimeSpan span = endDate.Subtract(startDate);
            return (span.TotalMilliseconds / 1000).ToString("##.###");
        }
        public static string CreateChangeCode(string action, string function, int id)
        {
            return action + "." + function + id.ToString("D");
        }

        public static string PathImagesFolder(string fileName)
        {
            var folder = Path.Combine(Directory.GetCurrentDirectory(), Constants.PATH_IMAGE);
            if (!Directory.Exists(folder))
            {
                Directory.CreateDirectory(folder);
            }
            var path = Path.Combine(folder, fileName);
            return path;
        }

        public static bool IsNumeric(string input)
        {
            try
            {
                int.Parse(input);
            }
            catch
            {
                return false;
            }
            return true;
        }

        public static string GenerateMessageString(string message, string beginTextTag, string endTextTag
            , string beginParamTag, string endParamTag, string paramLabel, int paramNumber)
        {
            if (paramNumber == 0)
            {
                return string.IsNullOrWhiteSpace(message)
                    ? GetTextTagInHtml("...", beginTextTag, endTextTag)
                    : GetTextTagInHtml(message, beginTextTag, endTextTag);
            }
            if (string.IsNullOrWhiteSpace(message))
            {
                for (var i = 0; i < paramNumber; i++)
                {
                    message += "..." + "{" + i + "}";
                }
            }
            //check if message start with {index}
            var startCheckPattern = "\\A{[0-9]}";
            var matchStart = Regex.Match(message, startCheckPattern);
            if (matchStart.Success)
            {
                message = "_____" + message;
            }
            var endCheckPattern = "{[0-9]}\\z";
            var matchEnd = Regex.Match(message, endCheckPattern);
            if (matchEnd.Success)
            {
                message = message + "_____";
            }
            //check pattern
            var pattern = "{[0-9]}";
            string[] resultArray = Regex.Split(message, pattern,
                RegexOptions.IgnoreCase);
            var match = Regex.Match(message, pattern);
            if (match.Success)
            {
                for (var i = 0; i < resultArray.Length; i++)
                {
                    var text = resultArray[i] == "_____" ? "..." : resultArray[i];
                    message = message.Replace(resultArray[i],
                        GetTextTagInHtml(text, beginTextTag, endTextTag));
                    if (i < resultArray.Length - 1)
                    {
                        message = message.Replace("{" + i + "}",
                            GetParamTagInHtml(i, paramLabel + (i + 1), beginParamTag, endParamTag));
                    }
                }
            }
            else
            {
                return GetTextTagInHtml(string.IsNullOrWhiteSpace(message) ? "..." : message, beginTextTag, endTextTag);
            }
            return message;
        }

        public static string GetTextTagInHtml(string text, string beginTag, string endTag)
        {
            return beginTag + text + endTag;
        }

        public static string GetParamTagInHtml(int paramIndex, string paramLabel, string beginTag, string endTag)
        {
            return string.Format(beginTag + paramLabel + endTag, paramIndex);
        }


        public static string ScreenKey()
        {
            if (HttpContext.Current == null || HttpContext.Current.Request.HttpContext.Request == null) return string.Empty;

            var routeData = HttpContext.Current.Request.HttpContext.Request.RouteValues;
            var actionName = routeData.ContainsKey("action") ? routeData["action"].ToString() : string.Empty;
            var controllerName = routeData.ContainsKey("controller") ? routeData["controller"].ToString() : string.Empty;
            var currentKey = GenerateScreenKey(actionName, controllerName);

            return currentKey;
        }

        public static string GenerateScreenKey(string actionName, string controllerName)
        {
            return $"ScreenKey{controllerName}{actionName}";
        }

        public static void UpdateMenuBar(string defaultLabel, string LabelKey)
        {
            if (HttpContext.Current == null || HttpContext.Current.Session == null || HttpContext.Current.Request.HttpContext.Request == null) return;

            var screenKey = ScreenKey();

            var menuBarList = WebUtils.GetSession<List<HeaderMenuBar>>(Constants.MenuHistorySession) ?? new List<HeaderMenuBar>();

            var menu = menuBarList.FirstOrDefault(p => p.ScreenKey == screenKey);
            if (menu == null)
            {
                RemoveNullableMenus();
                return;
            }

            menu.DefaultLabel = defaultLabel;
            menu.LabelKey = LabelKey;



            WebUtils.SetSession<List<HeaderMenuBar>>(Constants.MenuHistorySession, menuBarList);

        }

        public static void UpdateMenuBarAddScreen(string url, string key)
        {
            if (HttpContext.Current == null || HttpContext.Current.Session == null || HttpContext.Current.Request.HttpContext.Request == null) return;

            var screenKey = ScreenKey();

            var menuBarList = WebUtils.GetSession<List<HeaderMenuBar>>(Constants.MenuHistorySession) ?? new List<HeaderMenuBar>();

            var menu = menuBarList.FirstOrDefault(p => p.ScreenKey == screenKey);
            if (menu == null)
            {
                RemoveNullableMenus();
                return;
            }

            menu.Url = url;
            menu.ScreenKey = key;

            menuBarList = menuBarList.Where(p => p.DefaultLabel != null).ToList();
            WebUtils.SetSession<List<HeaderMenuBar>>(Constants.MenuHistorySession, menuBarList);
        }

        public static string GetUrgentUrl()
        {
            if (HttpContext.Current == null || HttpContext.Current.Session == null) return null;
            HttpContext.Current.Request.Cookies.TryGetValue(Constants.RedirectUrgentCookieKey, out var urgentUrl);

            return urgentUrl;
        }

        private static void RemoveNullableMenus()
        {
            if (HttpContext.Current == null || HttpContext.Current.Session == null || HttpContext.Current.Request.HttpContext.Request == null) return;


            var menuBarList = WebUtils.GetSession<List<HeaderMenuBar>>(Constants.MenuHistorySession) ?? new List<HeaderMenuBar>();

            menuBarList = menuBarList.Where(p => p.DefaultLabel != null).ToList();
            WebUtils.SetSession<List<HeaderMenuBar>>(Constants.MenuHistorySession, menuBarList);
        }

        public static void UpdateMenuBarLanguage()
        {
            //TODO: Call this function when change the Language interface.
        }

        public static UserMaster GetCurrentUser()
        {
            var userByte = HttpContext.Current.Session.Get(Constants.USERID);

            var userMaster = ByteArrayToObject<UserMaster>(userByte);

            return userMaster;
        }

        public static int GetCurrCompanyId()
        {
            //get on Session
            var currCompany = HttpContext.Current.Session.Get(Constants.COMPANY_SESSION);
            var companyId = ByteArrayToObject<int>(currCompany);

            return companyId;
        }

        public static int GetCurrAccessStatusId()
        {
            var currAccessStatus = HttpContext.Current.Session.Get(Constants.ACCESS_STATUS_SESSION);
            var accessStatusId = ByteArrayToObject<int>(currAccessStatus);

            return accessStatusId;
        }

        public static string GetCurrentCompany()
        {
            //TODO DungPham handle get Company before go to main screen

            var company = "Worldline";

            return company;
        }


        public static string GetAvatarUrl()
        {
            string path = "";
            var userByte = HttpContext.Current.Session.Get(Constants.USERID);

            var current = ByteArrayToObject<UserMaster>(userByte);

            if (current != null)
            {
                return !string.IsNullOrWhiteSpace(current.PersonalPhoto) ? Constants.PATH_CLOUD_USER.Replace("~", "") + "/" + current.PersonalPhoto :
                                                                           Constants.PATH_IMAGE_RESOURCE_GET + Constants.NO_IMAGE;
            }
            return path;
        }
        public static string GetTerminalImageUrl(string image = null, string path = "")
        {
            var imageUrl = Constants.PATH_NO_IMAGE;

            if (Constants.PATH_NO_IMAGE.Equals(image))
                return image;

            if (string.IsNullOrWhiteSpace(path))
                path = Constants.PATH_CLOUD_USER;

            if (!string.IsNullOrWhiteSpace(image))
            {
                var filePath = Path.Combine(PathHelper.GetFullPath(path), image);

                if (File.Exists(filePath))
                    imageUrl = Path.Combine(path, image).Replace("~", "");
            }

            return imageUrl;
        }

        public static IEnumerable<HeaderMenuBar> GetmenuBar()
        {
            if (HttpContext.Current == null || HttpContext.Current.Session == null) return new List<HeaderMenuBar>();


            return WebUtils.GetSession<List<HeaderMenuBar>>(Constants.MenuHistorySession) ?? new List<HeaderMenuBar>();
        }
        public static string SaveFileAt(string folderDir, IFormFile uploadedFile, string oldName = "", bool isDelete = false, string newName = "")
        {
            folderDir = PathHelper.GetFullPath(folderDir);
            var path = folderDir;

            if (!string.IsNullOrWhiteSpace(oldName) && Directory.Exists(folderDir))
            {
                //delete old file first before create new one
                var oldpath = Path.Combine(path, oldName);
                if (File.Exists(oldpath))
                    File.Delete(oldpath);
            }

            //set empty file when delete
            if (uploadedFile == null) return string.Empty;

            //create new folder if not exist
            if (!Directory.Exists(folderDir))
                Directory.CreateDirectory(folderDir);

            //generate new name by uid
            var fileName = newName;
            if (string.IsNullOrWhiteSpace(fileName))
            {
                var extension = uploadedFile.FileName.Split('.').LastOrDefault();
                fileName = $"{Guid.NewGuid()}.{extension}";
            }

            //save file to folder
            var filePath = Path.Combine(folderDir, fileName);

            using var fileStream = new FileStream(filePath, FileMode.Create);
            uploadedFile.CopyTo(fileStream);

            return fileName;
        }

        private static string DeleteFile(string folderDir, string oldName)
        {
            var path = Path.Combine(Directory.GetCurrentDirectory(), folderDir);
            var fileOld = Path.Combine(path, oldName);
            if (File.Exists(fileOld))
                File.Delete(fileOld);
            return string.Empty;
        }

        public static async Task<string> SaveSupportFile(string oldFile, IFormFile file, string newName = "")
        {
            return await FileUploadUtility.TerminalGuiUploadFile(Constants.PATH_SUPPORT, file, oldFile, newName);
        }

        public static async Task<string> PathMerchantFolder(string oldFile, IFormFile file)
        {
            return await FileUploadUtility.TerminalGuiUploadFile(Constants.PATH_MERCHANT, file, oldFile);
        }
        public static string CreatePathMerchatFolder(string fileName)
        {
            string path = "";

            if (!string.IsNullOrWhiteSpace(fileName))
            {
                if (fileName != Constants.PATH_IMAGE_RESOURCE.Replace("~/", "/") + Constants.NO_IMAGE)
                    path = Constants.PATH_MERCHANT.Replace("~/", "/") + "/" + fileName;
            }
            else
            {
                path = Constants.PATH_NO_IMAGE;
            }

            return path;
        }

        public static string GetImagePath(string sourceIconId)
        {
            string defaultNoImagePath = Constants.PATH_IMAGE_RESOURCE.Replace("~/", "/") + Constants.NO_IMAGE;
            string merchantPathPrefix = Constants.PATH_MERCHANT.Replace("~/", "/");

            if (!string.IsNullOrWhiteSpace(sourceIconId) && sourceIconId != defaultNoImagePath)
            {
                return $"{merchantPathPrefix}/{sourceIconId}";
            }

            return defaultNoImagePath;
        }

        public static async Task<string> PathCloudUserFolder(string oldFile, IFormFile file, string newName = "", UploadType uploadType = UploadType.Image)
        {
            return await FileUploadUtility.TerminalGuiUploadFile(Constants.PATH_CLOUD_USER, file, oldFile, newName, uploadType);
        }

        public static string PathCloudUserFolder(string fileName)
        {
            var folder = Path.Combine(Directory.GetCurrentDirectory(), Constants.PATH_CLOUD_USER);
            if (!Directory.Exists(folder))
            {
                Directory.CreateDirectory(folder);
            }
            var path = Path.Combine(folder, fileName);
            return path;
        }

        public static async Task<string> PathCloudCompanyFolder(string oldFile, IFormFile file)
        {
            return await FileUploadUtility.TerminalGuiUploadFile(Constants.PATH_CLOUD_COMPANY, file, oldFile);
        }

        public static string PathCloudCompanyFolder(string fileName)
        {
            var folder = PathHelper.GetFullPath(Constants.PATH_CLOUD_COMPANY);
            if (!Directory.Exists(folder))
            {
                Directory.CreateDirectory(folder);
            }
            var path = Path.Combine(folder, fileName);
            return path;
        }

        public static async Task<string> PathCharityFolder(string oldFile, IFormFile file)
        {
            return await FileUploadUtility.TerminalGuiUploadFile(Constants.PATH_MERCHANT, file, oldFile);
        }

        public static string PathCharityFolder(string fileName)
        {
            var folder = PathHelper.GetFullPath(Constants.PATH_MERCHANT);
            if (!Directory.Exists(folder))
            {
                Directory.CreateDirectory(folder);
            }
            var path = Path.Combine(folder, fileName);
            return path;
        }

        public static async Task<string> PathCloudModelFolder(string oldFile, IFormFile file)
        {
            return await FileUploadUtility.TerminalGuiUploadFile(Constants.PATH_CLOUD_MODEL, file, oldFile);
        }

        public static string PathCloudModelFolder(string fileName)
        {
            var folder = PathHelper.GetFullPath(Constants.PATH_CLOUD_MODEL);

            if (!Directory.Exists(folder) && !Directory.Exists(folder))
                Directory.CreateDirectory(folder);

            var path = Path.Combine(folder, fileName);

            return path;
        }

        public static string GetPathCloud(string fileName, string filePath)
        {
            var folder = PathHelper.GetFullPath(filePath);
            if (!Directory.Exists(folder))
            {
                Directory.CreateDirectory(folder);
            }
            var path = Path.Combine(folder, fileName);
            return path;
        }

        public static async Task<string> PathProcessorsFolder(string oldFile, IFormFile file)
        {
            return await FileUploadUtility.TerminalGuiUploadFile(Constants.PATH_PROCESSOR, file, oldFile);
        }

        public static string PathProcessorsFolder(string fileName)
        {
            var folder = PathHelper.GetFullPath(Constants.PATH_PROCESSOR);
            if (!Directory.Exists(folder))
            {
                Directory.CreateDirectory(folder);
            }
            var path = Path.Combine(folder, fileName);
            return path;
        }

        public static async Task<string> PathSoundsFolder(string oldFile, IFormFile file)
        {
            return await FileUploadUtility.TerminalGuiUploadFile(Constants.PATH_SOUND, file, oldFile);
        }

        public static string PathSoundsFolder(string fileName)
        {
            var folder = PathHelper.GetFullPath(Constants.PATH_SOUND);
            if (!Directory.Exists(folder))
            {
                Directory.CreateDirectory(folder);
            }
            var path = Path.Combine(folder, fileName);
            return path;
        }

        public static async Task<string> PathGalleryImageFolder(IFormFile file)
        {
            return await FileUploadUtility.TerminalGuiUploadFile(Constants.PATH_GALLERY_IMAGE, file);
        }

        public static async Task<string> PathBinTableFolder(string oldFile, IFormFile file)
        {
            return await FileUploadUtility.TerminalGuiUploadFile(Constants.PATH_BINTABLE_SERVER, file, oldFile);
        }

        public static async Task<string> PathApplicationFolder(string oldFile, IFormFile file)
        {
            return await FileUploadUtility.TerminalGuiUploadFile(Constants.PATH_APPLICATION, file, oldFile);
        }

        public static string PathApplicationFolder(string fileName)
        {
            if (fileName == null) return "";
            var folder = PathHelper.GetFullPath(Constants.PATH_APPLICATION);
            if (!Directory.Exists(folder))
            {
                Directory.CreateDirectory(folder);
            }
            var path = Path.Combine(folder, fileName);
            return path;
        }

        public static string PathDownloadApplicationFolder(string folderName)
        {
            if (folderName == null) return "";
            var folder = PathHelper.GetFullPath(Constants.PATH_DOWNLOAD_APPLICATION);
            if (!Directory.Exists(folder))
            {
                Directory.CreateDirectory(folder);
            }

            var path = Path.Combine(folder, folderName);

            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }
            else
            {
#if !DEMO
                Directory.Delete(path, true);
                Directory.CreateDirectory(path);
#endif
            }

            return path;
        }

        public static string PathParameterDownload(string folderName)
        {
            if (folderName == null) return "";
            var folder = PathHelper.GetFullPath(Constants.PATH_PARAMETER_DOWNLOAD_SERVER);
            if (!Directory.Exists(folder))
            {
                Directory.CreateDirectory(folder);
            }

            var path = Path.Combine(folder, folderName);

            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }
            else
            {
                try
                {
#if !DEMO
                    var files = Directory.GetFiles(path);

                    foreach (var item in files)
                    {
                        File.Delete(item);
                    }

                    var dics = Directory.GetDirectories(path);

                    foreach (var item in dics)
                    {
                        Directory.Delete(item, true);
                    }
#endif
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                }
            }

            return path;
        }
        public static string CreateDirectory(string path)
        {
            if (path == null) return "";

            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }
            return path;
        }
        public static string PathFirmwareFolder(string fileName)
        {
            var folder = PathHelper.GetFullPath(Constants.PATH_FIRMWARE);
            if (!Directory.Exists(folder))
            {
                Directory.CreateDirectory(folder);
            }
            var path = Path.Combine(folder, fileName);
            return path;
        }

        public static string GetMessage(string param, string message)
        {
            return string.Format(message.Replace("[0]", "{0}"), param);
        }

        public static string GetMessage(string param1, string param2, string message)
        {
            return string.Format(message.Replace("[0]", "{0}").Replace("[1]", "{1}"), param1, param2);
        }

        public static string GetMessage(string message, string[] param)
        {
            if (param == null)
            {
                return message;
            }
            else
            {
                for (var i = 0; i < param.Length; i++)
                {
                    message = message.Replace("{" + i + "}", param.GetValue(i).ToString());
                }
                return message;
                //return message;
            }
        }

        public static bool IsValidIp(string addr)
        {
            IPAddress ip;
            var check = IPAddress.TryParse(addr, out ip);
            return check;
        }

        public static bool IsValidMacAddress(string address)
        {
            var r = new Regex("^([0-9a-fA-F]{2}(?:[:-]?[0-9a-fA-F]{2}){5})$");
            return r.IsMatch(address);
        }

        public static bool IsValidDateFormat(string date, string partern)
        {
            if (string.IsNullOrWhiteSpace(date)) return false;
            date = date.Replace("-", "").Replace("/", "").Replace(" ", "").Replace(":", "");
            try
            {
                var result = DateTime.ParseExact(date, partern, System.Globalization.CultureInfo.CurrentCulture);
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                return false;
            }
            return true;
        }

        public static bool IsValidPhoneNumber(string phone)
        {
            var r = new Regex("^([0-9\\(\\)\\/\\+ \\-]*)$");
            return r.IsMatch(phone);
        }

        public static bool IsValidName(string name)
        {
            //var r = new Regex(@"^[\p{L}\s'.-]+$");
            //return r.IsMatch(name);
            return true;
        }

        public static bool IsValidEmailAddress(String email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        public static bool IsValidZipCode(String zipcode)
        {
            var r = new Regex(@"^\d{4}$");
            return r.IsMatch(zipcode);
        }

        public static bool IsValidAddress(String address)
        {
            var r = new Regex(@"^[0-9]+(\s[a-zA-Z]+)+$");
            return r.IsMatch(address);
        }

        public static bool IsValidUrl(String url)
        {
            var r =
                new Regex(
                    @"^(http|https|ftp)\://[a-zA-Z0-9\-\.]+\.[a-zA-Z]{2,3}(:[a-zA-Z0-9]*)?/?([a-zA-Z0-9\-\._\?\,\'/\\\+&amp;%\$#\=~])*$");
            return r.IsMatch(url);
        }

        public static string GetTimeInDateTime(DateTime dateTime)
        {
            var timeString = dateTime.TimeOfDay.ToString();

            return timeString;
        }

        public static DateTime SetTimeToDateTime(string time = null)
        {
            return DateTime.Parse(Constants.DefaultDateTimeValue.ToShortDateString() + " " + time);
        }

        public static DateTime? ConvertStringToDateTime(String datetime, String partern)
        {
            if (string.IsNullOrWhiteSpace(datetime)) return null;
            try
            {
                datetime = datetime.Replace("-", "").Replace("/", "").Replace(" ", "").Replace(":", "").Replace(".", "");
                var result = DateTime.ParseExact(datetime, partern, null);
                return result;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public static String ConvertDateTimeToString(DateTime? datetime, String partern)
        {
            if (datetime == null) return "";
            return string.IsNullOrWhiteSpace(partern) ? "" : datetime.GetValueOrDefault().ToString(partern);
        }

        public static int ConvertBoolToInt(bool input)
        {
            return input ? 1 : 0;
        }

        public static bool ConvertIntToBool(int input)
        {
            return input != 0;
        }

        public static string GetDefaultStyleString(Style style)
        {
            var result = "";

            if (string.IsNullOrWhiteSpace(style.CssClass))
            {
                //style for main
                result += Constants.FormCSSSelector + "{";
                result += Constants.StyleBackGroundColor + ": #" + style.BackGroundColor + ";";
                result += Constants.StyleTextColor + ": #" + style.DescriptionTextFontColor + ";";
                result += Constants.StyleTextFontSize + ":" + style.DescriptionTextFontSize + "px ;";
                result += Constants.StyleTextFontStyle + ":" + style.DescriptionTextFontStyle + ";";
                result += "}";
                //style for input text field
                //result += Constants.FormCSSSelector + " " + Constants.SelectorInput + "{";
                //result += Constants.StyleTextColor + ": #" + style.EntryTextFontColor + ";";
                //result += Constants.StyleTextFontSize + ":" + style.EntryTextFontSize + "px;";
                //result += Constants.StyleTextFontStyle + ":" + style.EntryTextFontStyle + ";";
                //result += Constants.StyleBackGroundColor + ": #" + style.EntryFieldColor + ";";
                //result += "}";
                //style for input text field
                //result += "input,select " + "{";
                //result += Constants.StyleTextColor + ": #" + style.EntryTextFontColor + ";";
                //result += Constants.StyleTextFontSize + ":" + style.EntryTextFontSize + "px;";
                //result += Constants.StyleTextFontStyle + ":" + style.EntryTextFontStyle + ";";
                //result += Constants.StyleBackGroundColor + ": #" + style.EntryFieldColor + ";";
                //result += Constants.StyleBorder + ": #" + style.Border + ";";
                //result += "}";
                //style for border 
                result += Constants.SelectorInput + ", " + Constants.SelectorSelectButton + "{";
                result += Constants.StyleBorder + ": #" + style.Border + ";";
                result += "}";
                //style for button text color
                result += Constants.SelectorSelectButton + "{";
                //result += Constants.StyleTextColor + ": #" + style.DescriptionTextFontColor + ";";
                result += "}";

            }
            else
            {
                if (style.CssClass.Equals(Constants.FormInputItem))
                {

                    //style for input text field
                    result += Constants.FormInputItem + "{";
                    result += Constants.StyleTextColor + ": #" + style.DescriptionTextFontColor + ";";
                    result += Constants.StyleTextFontSize + ":" + style.DescriptionTextFontSize + "px;";
                    result += Constants.StyleTextFontStyle + ":" + style.DescriptionTextFontStyle + ";";
                    //result += Constants.StyleBackGroundColor + ": #" + style. + ";";
                    result += "}";
                    result += Constants.SelectorHeader + "{";
                    result += Constants.StyleTextFontStyle + ":" + style.DescriptionTextFontStyle + ";";
                    //result += Constants.StyleBackGroundColor + ": #" + style. + ";";
                    result += "}";
                    //style for input text field
                    result += Constants.SelectorInput + "{";
                    result += Constants.StyleTextColor + ": #" + style.EntryTextFontColor + ";";
                    result += Constants.StyleTextFontSize + ":" + style.EntryTextFontSize + "px;";
                    result += Constants.StyleTextFontStyle + ":" + style.EntryTextFontStyle + ";";
                    result += Constants.StyleBackGroundColor + ": #" + style.EntryFieldColor + ";";
                    result += Constants.StyleBorder + ": #" + style.Border + ";";
                    result += "}";
                    //style for border 
                }
                else
                {
                    result += style.CssClass + "{";
                    if (!string.IsNullOrWhiteSpace(style.BackGroundColor))
                    {
                        result += Constants.StyleBackGroundColor + ": #" + style.BackGroundColor + ";";
                    }
                    if (!string.IsNullOrWhiteSpace(style.DescriptionTextFontColor))
                    {
                        result += Constants.StyleTextColor + ": #" + style.DescriptionTextFontColor + ";";
                    }
                    if (!string.IsNullOrWhiteSpace(style.DescriptionTextFontSize))
                    {
                        result += Constants.StyleTextFontSize + ": " + style.DescriptionTextFontSize + "px;";
                    }
                    if (!string.IsNullOrWhiteSpace(style.DescriptionTextFontStyle))
                    {
                        result += Constants.StyleTextFontStyle + ": " + style.DescriptionTextFontStyle + ";";
                    }
                    if (!string.IsNullOrWhiteSpace(style.EntryTextFontColor))
                    {
                        result += Constants.StyleTextColor + ": #" + style.EntryTextFontColor + ";";
                    }
                    if (!string.IsNullOrWhiteSpace(style.EntryTextFontSize))
                    {
                        result += Constants.StyleTextFontSize + ": " + style.EntryTextFontSize + "px;";
                    }
                    if (!string.IsNullOrWhiteSpace(style.EntryTextFontStyle))
                    {
                        result += Constants.StyleTextFontStyle + ": " + style.EntryTextFontStyle + ";";
                    }
                    if (!string.IsNullOrWhiteSpace(style.EntryFieldColor))
                    {
                        result += Constants.StyleBackGroundColor + ": #" + style.EntryFieldColor + ";";
                    }
                    if (!string.IsNullOrWhiteSpace(style.Border))
                    {
                        result += Constants.StyleBorder + ": #" + style.Border + ";";
                    }
                    result += "}";
                }
            }
            return result;
        }

        public static string GetCustomizedStyleString(Style style)
        {
            var result = "";
            if (style.CssClass.Contains(".form-header"))
            {
                if (!style.TagType.Equals(Constants.TagTypeIcon))
                {
                    //style for header text
                    result += style.CssClass + " span {";
                    if (!string.IsNullOrWhiteSpace(style.DescriptionTextFontColor))
                    {
                        result += Constants.StyleTextColor + ": #" + style.DescriptionTextFontColor + ";";
                    }
                    if (!string.IsNullOrWhiteSpace(style.DescriptionTextFontSize))
                    {
                        result += Constants.StyleTextFontSize + ": " + style.DescriptionTextFontSize + "px;";
                    }
                    if (!string.IsNullOrWhiteSpace(style.DescriptionTextFontStyle))
                    {
                        result += Constants.StyleTextFontStyle + ": " + style.DescriptionTextFontStyle + ";";
                    }
                    result += "}";
                    //style for back ground of header
                    result += style.CssClass + " {";
                    if (!string.IsNullOrWhiteSpace(style.BackGroundColor))
                    {
                        result += Constants.StyleBackGroundColor + ": #" + style.BackGroundColor + ";";
                    }
                    result += "}";
                    //style for header icon

                    if (!string.IsNullOrEmpty(style.IconColor))
                    {
                        result += style.CssClass + " i {";
                        result += Constants.StyleTextColor + ": #" + style.IconColor + ";";
                        result += "}";
                    }

                }

            }
            else if (!string.IsNullOrWhiteSpace(style.TagType))
            {
                if (style.TagType.Equals(Constants.TagTypeButton))
                {
                    result += style.CssClass + "{";

                    if (!string.IsNullOrWhiteSpace(style.DescriptionTextFontColor))
                    {
                        result += Constants.StyleTextColor + ": #" + style.DescriptionTextFontColor + ";";
                    }
                    if (!string.IsNullOrWhiteSpace(style.DescriptionTextFontSize))
                    {
                        result += Constants.StyleTextFontSize + ": " + style.DescriptionTextFontSize + "px;";
                    }
                    if (!string.IsNullOrWhiteSpace(style.DescriptionTextFontStyle))
                    {
                        result += Constants.StyleTextFontStyle + ": " + style.DescriptionTextFontStyle + ";";
                    }
                    result += "}";

                    result += style.CssClass + " ." + Constants.SelectorButtonSelect + "{";

                    if (!string.IsNullOrWhiteSpace(style.EntryFieldColor))
                    {
                        result += Constants.StyleBackGroundColor + ": #" + style.EntryFieldColor + ";";
                    }
                    if (!string.IsNullOrWhiteSpace(style.Border) && style.Border != "fff")
                    {
                        result += Constants.StyleBorder + ": #" + style.Border + ";";
                    }
                    result += "}";

                }
                if (style.TagType.Equals(Constants.TagTypeButtonOnOf))
                {
                    result += style.CssClass + "{";

                    if (!string.IsNullOrWhiteSpace(style.BackGroundColor))
                    {
                        result += Constants.StyleBackGroundColor + ": #" + style.BackGroundColor + ";";
                    }
                    if (!string.IsNullOrWhiteSpace(style.Border) && style.Border != "fff")
                    {
                        result += Constants.StyleBorder + ": #" + style.Border + ";";
                    }
                    result += "}";
                }

            }
            else
            {
                result += style.CssClass + "{";

                if (!string.IsNullOrWhiteSpace(style.DescriptionTextFontColor))
                {
                    result += Constants.StyleTextColor + ": #" + style.DescriptionTextFontColor + ";";
                }
                if (!string.IsNullOrWhiteSpace(style.DescriptionTextFontSize))
                {
                    result += Constants.StyleTextFontSize + ": " + style.DescriptionTextFontSize + "px;";
                }
                if (!string.IsNullOrWhiteSpace(style.DescriptionTextFontStyle))
                {
                    result += Constants.StyleTextFontStyle + ": " + style.DescriptionTextFontStyle + ";";
                }
                if (!string.IsNullOrWhiteSpace(style.BackGroundColor))
                {
                    result += Constants.StyleBackGroundColor + ": #" + style.BackGroundColor + ";";
                }
                result += "}";

                result += style.CssClass + " " + Constants.SelectorTextInput +
                          "," + style.CssClass + " " + Constants.SelectorSelect +
                          "," + style.CssClass + " " + Constants.SelectorButton + "{";
                if (!string.IsNullOrWhiteSpace(style.EntryTextFontColor))
                {
                    result += Constants.StyleTextColor + ": #" + style.EntryTextFontColor + ";";
                }
                if (!string.IsNullOrWhiteSpace(style.EntryTextFontSize))
                {
                    result += Constants.StyleTextFontSize + ": " + style.EntryTextFontSize + "px;";
                }
                if (!string.IsNullOrWhiteSpace(style.EntryTextFontStyle))
                {
                    result += Constants.StyleTextFontStyle + ": " + style.EntryTextFontStyle + ";";
                }
                if (!string.IsNullOrWhiteSpace(style.EntryFieldColor))
                {
                    result += Constants.StyleBackGroundColor + ": #" + style.EntryFieldColor + ";";
                }
                if (!string.IsNullOrWhiteSpace(style.Border))
                {
                    result += Constants.StyleBorder + ": #" + style.Border + ";";
                }
                result += "}";
                if (!string.IsNullOrWhiteSpace(style.EntryTextFontColor))
                {
                    // style color for placeholder
                    result += style.CssClass + " input::-webkit-input-placeholder {";
                    result += Constants.StyleTextColor + ": #" + style.EntryTextFontColor + ";";
                    result += "}";
                    result += style.CssClass + " input::-moz-placeholder {";
                    result += Constants.StyleTextColor + ": #" + style.EntryTextFontColor + ";";
                    result += "}";
                    result += style.CssClass + " input:-ms-input-placeholder {";
                    result += Constants.StyleTextColor + ": #" + style.EntryTextFontColor + ";";
                    result += "}";
                    result += style.CssClass + " input.form-control:-webkit-autofill {"
                              + "-webkit-box-shadow: 0 0 0px 1000px #" + style.EntryFieldColor + " inset;"
                              + "-webkit-text-fill-color: #" + style.EntryTextFontColor + " !important;";
                    result += "}";
                }

            }

            return result;
        }

        public static string GetDefaultStyleStringByLang(Style style)
        {
            string result = "";
            //style for Text inptut
            result += style.CssClass + "{";
            result += Constants.StyleTextFontSize + ": " + style.DescriptionTextFontSize + "px ;";
            result += Constants.StyleTextFontStyle + ": " + style.DescriptionTextFontStyle + ";";
            result += "}";
            return result;
        }

        public static string GetDefaultString(IList<Style> styles, IList<Style> customStyles, IList<Style> stylesByLang)
        {
            var styleStr = "";
            foreach (var style in styles)
            {
                styleStr += GetDefaultStyleString(style);
            }
            foreach (var style in stylesByLang)
            {
                styleStr += GetDefaultStyleStringByLang(style);
            }
            foreach (var style in customStyles)
            {
                styleStr += GetCustomizedStyleString(style);
            }
            return styleStr;
        }

        public static string ChangeIconColor(string imagePath, string savePath, string changeColor, string iconName)
        {
            //string pathTest = ControllerContext.HttpContext.Server.MapPath("~/Images/icon-avtive-off.png");
            // string savePath = ControllerContext.HttpContext.Server.MapPath("~/Images/icon-avtive-off-after.png");
            Image image1 = Image.FromFile(imagePath);
            Bitmap bmp = new Bitmap(image1);
            Color change = ColorTranslator.FromHtml("#" + changeColor);
            var folderExist = Directory.Exists(savePath + changeColor);
            savePath = savePath + "\\" + changeColor;
            if (!folderExist)
            {
                Directory.CreateDirectory(savePath);
            }
            for (int i = 0; i < bmp.Width; i++)
            {
                for (int j = 0; j < bmp.Height; j++)
                {
                    if (bmp.GetPixel(i, j).B < 20)
                    {
                        Color actualColor = bmp.GetPixel(i, j);
                        // > 150 because.. Images edges can be of low pixel colr. if we set all pixel color to new then there will be no smoothness left.
                        if (actualColor.A != 0)
                            bmp.SetPixel(i, j, change);
                        else
                            bmp.SetPixel(i, j, actualColor);
                        //bmp.SetPixel(i, j, Color.DarkGreen);
                    }
                }
            }
            bmp.Save(savePath + "\\" + iconName, System.Drawing.Imaging.ImageFormat.Png);
            return changeColor + "/" + iconName;
        }

        public static string HashToStoreInDatabase(string password)
        {
            var pwdToHash = password + SALT_PASS; // ~!3JEY_234=SHJK-21^Y8~JJ is my hard-coded salt
            return BCrypt.Net.BCrypt.HashPassword(pwdToHash, BCrypt.Net.BCrypt.GenerateSalt(12));
        }

        public static bool IsMatchHash(string hashedPwdFromDatabase, string enteredPassword)
        {
            try
            {
                return BCrypt.Net.BCrypt.Verify(enteredPassword + SALT_PASS, hashedPwdFromDatabase);
            }
            catch (Exception)
            {
                return false;
            }

        }

        public static int ConvertStringToInt(string input)
        {
            try
            {
                return Int32.Parse(input);
            }
            catch
            {
                return 0;
            }
        }

        public static IList<int> ConvertStringToInts(string input)
        {
            try
            {
                var reuslt = new List<int>();
                var list = input.Split(',');
                foreach (var item in list)
                {
                    reuslt.Add(Int32.Parse(item));
                }
                return reuslt;
            }
            catch
            {
                return new List<int>();
            }
        }

        public static IList<string> ConvertStringToListString(string input)
        {
            var reuslt = new List<string>();
            if (input == null)
                return reuslt;
            try
            {
                var list = input.Split(',');
                foreach (var item in list)
                {
                    reuslt.Add(item);
                }
                return reuslt;
            }
            catch
            {
                return reuslt;
            }
        }

        public static long ConvertStringToLong(string input)
        {
            try
            {
                return long.Parse(input);
            }
            catch
            {
                return 0;
            }
        }
        public static void AddRange<T>(this IList<T> list, IEnumerable<T> items)
        {
            if (list == null) throw new ArgumentNullException("list");
            if (items == null) throw new ArgumentNullException("items");

            if (list is List<T>)
            {
                ((List<T>)list).AddRange(items);
            }
            else
            {
                foreach (var item in items)
                {
                    list.Add(item);
                }
            }
        }
        // public static int ToInt32(this object input)
        // {
        //     try
        //     {
        //         return Int32.Parse(input.ToString());
        //     }
        //     catch
        //     {
        //         return 0;
        //     }
        // }

        /// <summary>
        /// convert oject to string and return string empty if object null
        /// </summary>
        /// <param name="input">object</param>
        /// <returns>string</returns>
        public static string ToStr(this object input)
        {
            try
            {
                return input.ToString();
            }
            catch
            {
                return string.Empty;
            }
        }

        public static string ToStringFormatDate(this DateTime input, string format)
        {
            try
            {
                return input.ToString(format, CultureInfo.InvariantCulture);
            }
            catch
            {
                return string.Empty;
            }
        }

        public static string ConvertToTitleCase(string text)
        {
            if (string.IsNullOrEmpty(text))
                return text;

            TextInfo textInfo = CultureInfo.CurrentCulture.TextInfo;
            return textInfo.ToTitleCase(text.ToLower());
        }
        
        public static double ConvertStringToDouble(string input)
        {
            double result;
            return double.TryParse(input, out result) ? result : 0;
        }

        public static DateTime ToDateTimeFormat(this string input, string format)
        {
            DateTime newDate;
            return DateTime.TryParseExact(input, format, null, DateTimeStyles.None, out newDate) ? newDate : DateTime.MinValue;
        }

        public static string GetStringDateFormat(int type)
        {
            string result;
            switch (type)
            {
                case Constants.DATEFMT_DD_MM_YY: result = Constants.STRING_DATEFMT_DD_MM_YY; break;
                case Constants.DATEFMT_YY_MM_DD: result = Constants.STRING_DATEFMT_YY_MM_DD; break;
                case Constants.DATEFMT_MM_DD_YY: result = Constants.STRING_DATEFMT_MM_DD_YY; break;

                default: result = Constants.STRING_DATEFMT_DD_MM_YY; break;
            }
            return result;
        }

        public static string FormatString(string text)
        {
            var result = Regex.Replace(text, "([a-z](?=[A-Z]|[0-9])|[A-Z](?=[A-Z][a-z]|[0-9])|[0-9](?=[^0-9]))", "$1 ");
            return result;
        }

        public static void ConvertDataAttributes(ViewDataDictionary viewData)
        {
            var data = viewData.ToList();
            foreach (var item in data)
            {
                if (!item.Key.StartsWith("data_", StringComparison.OrdinalIgnoreCase)) continue;

                viewData.Add(item.Key.Replace("_", "-"), item.Value);
                viewData.Remove(item.Key);
            }
        }
        public static byte[] ObjToByteArray<T>(T obj)
        {
            var json = JsonConvert.SerializeObject(obj);
            var bytes = Encoding.UTF8.GetBytes(json);

            return bytes;
        }

        public static T ByteArrayToObject<T>(byte[] cipher)
        {
            if (cipher == null) return default(T);

            var json = Encoding.UTF8.GetString(cipher);

            return JsonConvert.DeserializeObject<T>(json);
        }
        public static T FromByteArray<T>(byte[] data)
        {
            if (data == null) return default(T);
            string json = System.Text.Encoding.UTF8.GetString(data);
            return JsonConvert.DeserializeObject<T>(json);
        }

        public static void SetSession<T>(string key, T value)
        {
            var bytes = ObjToByteArray<T>(value);

            HttpContext.Current.Session.Set(key, bytes); // Store in session
        }

        public static T GetSession<T>(string key)
        {
            try
            {
                if (HttpContext.Current?.Session == null)
                    return default;

                // Retrieve byte array from session
                var bytes = HttpContext.Current.Session.Get(key);

                if (bytes == null)
                    return default;

                return ByteArrayToObject<T>(bytes);
            }
            catch
            {
                return default;
            }
        }


        public static void RemoveSession(string key)
        {
            HttpContext.Current.Session.Remove(key);
        }

        public static void SetCookie(string key, string value)
        {
            HttpContext.Current.Response.Cookies.Append(key, value);
        }

        public static void SetCookie(string key, string value, CookieOptions option)
        {
            HttpContext.Current.Response.Cookies.Append(key, value, option);
        }
        public static string GetCookie(string key)
        {
            HttpContext.Current.Request.Cookies.TryGetValue(key, out var value);
            return value;
        }

        public static int GetRandomNumber()
        {
            return RandomNumber.Next();
        }

        /// <summary>
        /// convert int? to string decimal 0
        /// </summary>
        /// <param name="input">an object</param>
        /// <returns></returns>
        public static string ToStringXML(this object input)
        {
            try
            {
                if (input.GetType() == typeof(int))
                {
                    return ((int)input).ToString("D");
                }

                return input.ToString();
            }
            catch
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// Get Prop Value from an object
        /// </summary>
        /// <param name="src"></param>
        /// <param name="propName"></param>
        /// <returns></returns>
        public static object GetPropValue(this object src, string propName)
        {
            return src.GetType().GetProperty(propName).GetValue(src, null);
        }

        /// <summary>
        /// Check type object is a Simple Type
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        public static bool IsSimpleType(this Type type)
        {
            return
                type.IsPrimitive ||
                new Type[] {
            typeof(string),
            typeof(decimal),
            typeof(DateTime),
            typeof(DateTimeOffset),
            typeof(TimeSpan),
            typeof(Guid)
                }.Contains(type) ||
                type.IsEnum ||
                Convert.GetTypeCode(type) != TypeCode.Object ||
                (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>) && IsSimpleType(type.GetGenericArguments()[0]))
                ;
        }

        /// <summary>
        /// compare and whrite xml change
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="entity"></param>
        /// <param name="oldEntity"></param>
        /// <param name="isAdd"></param>
        /// <returns></returns>
        public static string GetXmlChange<T>(this T entity, T oldEntity, string action) where T : BaseEntity
        {
            var builder = new StringBuilder();
            var setting = new XmlWriterSettings() { OmitXmlDeclaration = true };
            using (var writer = XmlWriter.Create(builder, setting))
            {

                writer.WriteStartDocument();
                writer.WriteStartElement(action);
                writer.WriteStartElement(nameof(entity));

                foreach (PropertyInfo propertyInfo in entity.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance))
                {
                    var propName = propertyInfo.Name;
                    var IsSimpleType = propertyInfo.PropertyType.IsSimpleType();
                    if (!IsSimpleType) continue;
                    var value = entity.GetPropValue(propName);
                    var oldValue = oldEntity.GetPropValue(propName);

                    if (value != oldValue)
                    {
                        writer.WriteStartElement(propName);
                        writer.WriteString(value.ToStringXML());
                        writer.WriteEndElement();
                    }
                }

                writer.WriteEndElement();
                writer.WriteEndElement();
                writer.WriteEndDocument();
            }

            return builder.ToString();
        }
        
        public static string GetTerminalSerialNumber(string fullString)
        {
            int separatorIndex = fullString.LastIndexOf('-');

            return (separatorIndex != -1) 
                ? fullString.Substring(0, separatorIndex) 
                : fullString;
        }
    }

    public static class CustomizeByteArray
    {
        public static byte CalculateLRC(this byte[] bytes)
        {
            byte LRC = 0;
            for (int i = 0; i < bytes.Length; i++)
            {
                LRC ^= bytes[i];
            }
            return LRC;
        }
    }
}
