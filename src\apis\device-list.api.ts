import { AxiosResponse } from 'axios';
import { getAsync, buildQueryParams, postAsync, putAsync } from './http-client';
import { DataItemResponse, PagingResponse } from '../models/common.model';
import {
  AssignDevicesModel,
  AssignedDeviceParamsApi,
  AvailableDeviceParams,
  AvailableDevicesApiModel,
  DeviceAssociatedResponseApiModel,
  DeviceBrand,
  DeviceModel,
  SyncLinklyDeviceResponse,
} from '../models/device-list.model';

const baseUrl = import.meta.env.VITE_MMS_API_URL;

const getAvailableDevices = (
  availableDeviceParams: AvailableDeviceParams,
): Promise<AxiosResponse<PagingResponse<AvailableDevicesApiModel>>> => {
  const url = `${baseUrl}/location-device-setup/available-devices`;
  return getAsync(url, availableDeviceParams, true, true, false, 'repeat');
};

function getDeviceAssociated(
  params: AssignedDeviceParamsApi,
): Promise<AxiosResponse<PagingResponse<DeviceAssociatedResponseApiModel>>> {
  const queryString = buildQueryParams(params);
  const url = `${baseUrl}/location-device-setup/devices-associated?${queryString}`;
  return getAsync(url);
}

function getDeviceBrands(): Promise<AxiosResponse<DeviceBrand[]>> {
  const url = `${baseUrl}/location-device-setup/device-brands`;
  return getAsync(url);
}

function getDeviceModels(
  brandIds: number[],
): Promise<AxiosResponse<DeviceModel[]>> {
  const url = `${baseUrl}/location-device-setup/device-models`;
  return getAsync(url, { brandIds }, true, true, false, 'repeat');
}

function assignDevicesToLocation(
  model: AssignDevicesModel,
): Promise<AxiosResponse<void>> {
  const url = `${baseUrl}/location-device-setup/assign-devices`;
  return postAsync(url, model);
}

function syncLinklyDevices(
  companyId?: number,
): Promise<AxiosResponse<SyncLinklyDeviceResponse>> {
  const url = `${baseUrl}/location-device-setup/sync-linkly-devices/${companyId}`;
  return getAsync(url);
}

function unassignDevicesFromLocation(
  terminalMasterIds: number[],
): Promise<AxiosResponse<void>> {
  const url = `${baseUrl}/location-device-setup/unassign-device`;
  return putAsync(url, terminalMasterIds, true, false);
}

const getAllMerchants = (): Promise<AxiosResponse<DataItemResponse[]>> => {
  const url = `${baseUrl}/location-device-setup/all-activate-merchants`;
  return getAsync(url);
};

export const DeviceListApi = {
  getAvailableDevices,
  getDeviceAssociated,
  getDeviceBrands,
  getDeviceModels,
  assignDevicesToLocation,
  syncLinklyDevices,
  unassignDevicesFromLocation,
  getAllMerchants,
};
