using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MMS.Core.Attributes;
using MMS.Core.CoreUTI;
using MMS.Core.CoreUTI.Enum;
using MMS.Core.Entities.EntityInterface;
using MMS.Core.Entities.Locations;
using MMS.Core.Entities.TerminalSetupGUI;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text.Json.Serialization;
using System.Xml.Serialization;

namespace MMS.Core.Entities
{

    [Display(Name = "Terminal")]
    [MMSOutputFileName(FileName = "TMaster")]
    [XmlRoot("Terminal")]
    public class TerminalMaster : TerminalBaseEntity, IViewItem
    {
        public TerminalMaster InitDefaultData()
        {
            this.lszTerminalName = "XCR Terminal";
            this.cPINCharacters = 12;
            this.fContactlessEnabled = true;
            this.TerminalWAN.fEnable_WAN = true;
            this.TerminalWAN.DHCP_Activation = true;
            this.ContactlessTimeDelayEnabled = false;
            this.ContactlessTimeDelay = 10;

            return this;
        }

        #region Device new

        [CompareIgnore]
        public DeviceStatus DeviceStatusSelected { get; set; }

        [CompareIgnore]
        public bool IsMobile { get; set; }

        [CompareIgnore]
        public bool UseSortMutipleMerchant { get; set; }

        [CompareIgnore]
        public bool UseSortDeviceSetupAccess { get; set; }

        public int? CopyDeviceTemplateId { get; set; }
        [ForeignKey("CopyDeviceTemplateId")]
        [CompareIgnore]
        public virtual DeviceTemplate DeviceTemplate { get; set; }

        [CompareIgnore]
        public TemplateOptions TemplateOptions { get; set; }

        [CompareIgnore]
        public DeviceSetupType DeviceSetupType { get; set; }

        #endregion

        #region  TEM XML TEMPLATE - NEW

        [CompareIgnore]
        public string lszTerminalName { get; set; }

        [CompareIgnore]
        public InternetOption iInternetOption { get; set; }

        // Terminal Wifi
        public int? TerminalWifiId { get; set; }

        [TMSTemplateField(TypeUI = TMSTemplateUIConst.Separator)]
        [NotMapped]
        [Display(Name = "Terminal Wifi")]
        [CompareIgnore]
        public bool SeparatorTerminalWifi { get; set; }

        [ForeignKey("TerminalWifiId")]
        [TMSTemplateField(TypeUI = TMSTemplateUIConst.ChildNode)]
        [CompareIgnore]
        public virtual TerminalWifi TerminalWifi { get; set; }

        public int? TerminalEthernetId { get; set; }

        [TMSTemplateField(TypeUI = TMSTemplateUIConst.Separator)]
        [NotMapped]
        [Display(Name = "Terminal Ethernet")]
        [CompareIgnore]
        public bool SeparatorTerminalEthernet { get; set; }

        [ForeignKey("TerminalEthernetId")]
        [TMSTemplateField(TypeUI = TMSTemplateUIConst.ChildNode)]
        [CompareIgnore]
        public virtual TerminalWAN TerminalWAN { get; set; }

        [TMSTemplateField(TypeUI = TMSTemplateUIConst.Separator)]
        [NotMapped]
        [Display(Name = "System Setup")]
        [CompareIgnore]
        public bool Separator1 { get; set; }

        [TMSTemplateField]
        [Display(Name = "PIN Character")]
        [CopyField]
        public int cPINCharacters { get; set; }

        [TMSTemplateField]
        [CopyField]
        public bool fContactlessEnabled { get; set; }

        [TMSTemplateField]
        [Display(Name = "Contactless Time Delay Enabled")]
        [CompareIgnore]
        public bool ContactlessTimeDelayEnabled { get; set; }

        [TMSTemplateField]
        [Display(Name = "Contactless Time Delay (seconds)")]
        [CompareIgnore]
        public int ContactlessTimeDelay { get; set; }

        [NotMapped]
        [CompareIgnore]
        public virtual ICSSetup ICSSetup { get; set; }

        #endregion  TEM XML TEMPLATE - NEWS

        [CompareIgnore]
        public bool ShowDate { get; set; }

        [CompareIgnore]
        public bool ShowTime { get; set; }

        //Using for PAXSTORE
        [CompareIgnore]
        public long TerminalPaxStoreId { get; set; }

        //--------------------------------------------------------------------
        // Support and Processor
        //--------------------------------------------------------------------
        public int? SupportId { get; set; }

        [ForeignKey("SupportId")]
        public virtual SupportTerminal Support { get; set; }

        //--------------------------------------------------------------------
        // System
        //--------------------------------------------------------------------
        public int? DateId { get; set; }

        [ForeignKey("DateId")]
        public virtual DateTimeZoneTerminal Date { get; set; }

        //--------------------------------------------------------------------
        // Terminal Function
        //--------------------------------------------------------------------

        public int? SoundId { get; set; }

        [ForeignKey("SoundId")]
        public virtual SoundTerminal Sound { get; set; }

        public int? FallBackId { get; set; }

        [ForeignKey("FallBackId")]
        public virtual FallBackTerminal FallBack { get; set; }

        public int? PosRequestId { get; set; }

        [ForeignKey("PosRequestId")]
        [CompareIgnore]
        public virtual PosRequestTerminal PosRequest { get; set; }

        public int? CountryTerminalId { get; set; }

        [ForeignKey("CountryTerminalId")]
        public virtual CountryTerminal CountryTerminal { get; set; }

        public int? CashBackId { get; set; }

        [ForeignKey("CashBackId")]
        [CompareIgnore]
        public virtual DeditCardBackTerminal CashBack { get; set; }

        public int? ExtraModudleId { get; set; }

        [ForeignKey("ExtraModudleId")]
        [CompareIgnore]
        public virtual ExtraModudle ExtraModudle { get; set; }

        public int? PayplusProcessorId { get; set; }

        [ForeignKey("PayplusProcessorId")]
        [CompareIgnore]
        public virtual PayplusProcessor PayplusProcessor { get; set; }

        public int? ByTerminalTypeId { get; set; }

        [ForeignKey("ByTerminalTypeId")]
        [CompareIgnore]
        public virtual ICSSetupByTerminalType ICSSetupByTerminalType { get; set; }

        public int? ByCardDataInputId { get; set; }

        [ForeignKey("ByCardDataInputId")]
        [CompareIgnore]
        public virtual ICSSetupByCardDataInput ICSSetupByCardDataInput { get; set; }

        public int? ByCVMCapabilityId { get; set; }

        [ForeignKey("ByCVMCapabilityId")]
        [CompareIgnore]
        public virtual ICSSetupByCVMCapability ICSSetupByCVMCapability { get; set; }

        public int? BySecurityCapabilityId { get; set; }

        [ForeignKey("BySecurityCapabilityId")]
        [CompareIgnore]
        public virtual ICSSetupBySecurityCapability ICSSetupBySecurityCapability { get; set; }

        public int? ByTmDataInputId { get; set; }

        [ForeignKey("ByTmDataInputId")]
        [CompareIgnore]
        public virtual ICSSetupByTmDataInput ICSSetupByTmDataInput { get; set; }

        public int? ByTmDataOutputId { get; set; }

        [ForeignKey("ByTmDataOutputId")]
        [CompareIgnore]
        public virtual ICSSetupByTmDataOutput ICSSetupByTmDataOutput { get; set; }

        public int? ByCDAModeId { get; set; }

        [ForeignKey("ByCDAModeId")]
        [CompareIgnore]
        public virtual ICSSetupByCDAMode ICSSetupByCDAMode { get; set; }

        public virtual TerminalRebootSchedule TerminalRebootSchedule { get; set; }

        //--------------------------------------------------------------------
        // Properties of Terminal
        //--------------------------------------------------------------------
        public int? GroupMasterId { get; set; }
        [ForeignKey("GroupMasterId")]
        [CompareIgnore]
        public virtual GroupMaster GroupMaster { get; set; }

        /// <summary>
        /// Terminal Brand
        /// </summary>
        /// <value></value>
        public int? TerminalModelTypeId { get; set; }
        [ForeignKey("TerminalModelTypeId")]
        [CompareIgnore]
        public virtual TerminalModelMaster TerminalModelType { get; set; }

        //set on server only
        [CompareIgnore]
        public bool IsUsedTerminalParameterTemplate { get; set; }

        public int? ApplicationId { get; set; }
        [ForeignKey("ApplicationId")]
        [CompareIgnore]
        public virtual TerminalApplication TerminalApplication { get; set; }

        public virtual List<ApplicationDeviceMapping> ApplicationList { get; set; }

        public int? TerminalParameterLocationId { get; set; }
        [ForeignKey("TerminalParameterLocationId")]
        [CompareIgnore]
        public virtual TerminalParameterLocation TerminalParameterLocation { get; set; }

        [CompareIgnore]
        public string TerminalSerialNumber { get; set; }

        [CompareIgnore]
        public string TerminalServiceNumber { get; set; }

        [CompareIgnore]
        public string SmartClaimLink { get; set; }

        public int? IoTDeviceTypeId { get; set; }
        [ForeignKey("IoTDeviceTypeId")]
        [CompareIgnore]
        public virtual IOTDeviceTypes IoTDeviceType { get; set; }

        public int? IoTDeviceCateId { get; set; }
        [ForeignKey("IoTDeviceCateId")]
        [CompareIgnore]
        public virtual DeviceCategories IoTDeviceCate { get; set; }

        public int? IoTDeviceId { get; set; }
        [ForeignKey("IoTDeviceId")]
        [CompareIgnore]
        /// <summary>
        /// Device Model
        /// </summary>
        /// <value></value>
        public virtual IOTDevices IoTDevice { get; set; }

        public int? TerminalBrandId { get; set; }
        [ForeignKey(nameof(TerminalBrandId))]
        public virtual TerminalBrandMaster TerminalBrandMaster { get; set; }

        public int? StreetNameId { get; set; }
        [ForeignKey("StreetNameId")]
        [CompareIgnore]
        public virtual AddressStreetName StreetName { get; set; }

        public int? AssignedLocationAreaId { get; set; }
        [ForeignKey("AssignedLocationAreaId")]
        [CompareIgnore]
        public virtual LocationItem LocationArea { get; set; }

        public int? AssignedDeviceIndustryId { get; set; }

        [ForeignKey("AssignedDeviceIndustryId")]
        [CompareIgnore]
        public virtual DeviceIndustry DeviceIndustry { get; set; }

        public int? AssignedGroupId { get; set; }

        [ForeignKey(nameof(AssignedGroupId))]
        [CompareIgnore]
        public virtual CompanyGroup CompanyGroup { get; set; }

        [CompareIgnore]
        public bool IsAssignedDevice { get; set; }

        [CompareIgnore]
        public string StreetNo { get; set; }

        [CompareIgnore]
        public string ApartmentSuiteNo { get; set; }

        [CompareIgnore]
        public string FloorNo { get; set; }

        [CompareIgnore]
        public string SpecialInstructions { get; set; }

        [CompareIgnore]
        public string AccessCode { get; set; }

        [CompareIgnore]
        public string MapLocationGoogle { get; set; }

        [CompareIgnore]
        public string GpsLocation { get; set; }

        public int? TerminalSetupTemplateId { get; set; }
        [ForeignKey("TerminalSetupTemplateId")]
        [JsonIgnore]
        [CompareIgnore]
        public virtual TerminalSetupTemplate TerminalSetupTemplate { get; set; }


        public int? FirmwareUpgradeTemplateId { get; set; }
        [ForeignKey("FirmwareUpgradeTemplateId")]
        [JsonIgnore]
        [CompareIgnore]
        public virtual FirmwareUpgradeTemplate FirmwareUpgradeTemplate { get; set; }

        public int? ApplicationUpgradeTemplateId { get; set; }
        [ForeignKey("ApplicationUpgradeTemplateId")]
        [JsonIgnore]
        [CompareIgnore]
        public virtual ApplicationUpgradeTemplate ApplicationUpgradeTemplate { get; set; }

        [CompareIgnore]
        public string TerminalComment { get; set; }

        [CompareIgnore]
        public DateTime? ActiveAccessDate { get; set; }

        [CompareIgnore]
        public DateTime? ActivateAccessTime { get; set; }

        [CompareIgnore]
        public DateTime? DeActiveAccessDate { get; set; }

        [CompareIgnore]
        public DateTime? DeActiveAccessTime { get; set; }

        public int? ApplicationVersionId { get; set; }
        [ForeignKey("ApplicationVersionId")]
        [JsonIgnore]
        [CompareIgnore]
        public virtual ApplicationVersion ApplicationVersion { get; set; }

        public int? FirmwareVersionId { get; set; }
        [ForeignKey("FirmwareVersionId")]
        [JsonIgnore]
        [CompareIgnore]
        public virtual FirmwareVersion FirmwareVersion { get; set; }

        [CompareIgnore]
        public DateTime? TerminalInstallDate { get; set; }

        [CompareIgnore]
        public DateTime? TerminalInstallTime { get; set; }

        public int? TimeZoneMasterId { get; set; }
        [ForeignKey("TimeZoneMasterId")]
        [JsonIgnore]
        public virtual TimeZoneMaster TimeZoneMaster { get; set; }

        [CompareIgnore]
        public DateTime? BusinessStartTime { get; set; }

        [CompareIgnore]
        public DateTime? BusinessFinishTime { get; set; }

        [CompareIgnore]
        public string StoreName { get; set; }

        [CompareIgnore]
        public string TerminalLocation { get; set; }

        [CompareIgnore]
        public string LogonIpAddress { get; set; }

        [CompareIgnore]
        public string LogonMacAddress { get; set; }

        [CompareIgnore]
        public bool DeactiveSetupUpdate { get; set; }

        [CompareIgnore]
        public bool DeactivateAppUpgrade { get; set; }

        [CompareIgnore]
        public bool DeactivateFirmwareUpgrade { get; set; }

        [CompareIgnore]
        public bool DeactiveCloudAccess { get; set; }

        /// <summary>
        /// Indicates the source of the terminal data (MMS or Linkly)
        /// </summary>
        [CompareIgnore]
        public TerminalSources TerminalSource { get; set; } = TerminalSources.None;

        [CompareIgnore]
        public string IdAccessPoint { get; set; }

        public int? MerchantTerminalId { get; set; }

        [CompareIgnore]
        public int IdLanguageDefault { get; set; }

        public virtual IList<CountryCardSetupTerminal> CountryCardSetupTerminals { get; set; }

        public virtual IList<HostInterfaces> HostInterfaces { get; set; }

        public virtual IList<Charity> CharityTerminals { get; set; }

        public virtual IList<MerchantCardFallback> MerchantCardFallbacks { get; set; }

        [JsonIgnore]
        public virtual IList<UserTerminal> UserTerminals { get; set; }

        [JsonIgnore]
        public virtual IList<MerchantTerminal> MerchantTerminals { get; set; }

        [JsonIgnore]
        public virtual IList<ProcessorTerminal> ProcessorTerminals { get; set; }

        public virtual IList<TerminalUserSecurityAccess> TerminalSecurityAccesses { get; set; }

        [JsonIgnore]
        public virtual IList<UserAccessTerminal> UsersAccessTerminal { get; set; }

        [JsonIgnore]
        [CompareIgnore]
        public virtual IList<TerminalCurrency> TerminalCurrencies { get; set; }
        //--------------------------------------------------------------------
        // Setup Update  of Terminal
        //--------------------------------------------------------------------
        [JsonIgnore]
        [CompareIgnore]
        public virtual TerminalSetupUpdate TerminalSetupUpdate { get; set; }

        public int? ApplicationUpgradeTerminalId { get; set; }

        [ForeignKey("ApplicationUpgradeTerminalId")]
        [JsonIgnore]
        [CompareIgnore]
        public virtual ApplicationUpgradeTerminal ApplicationUpgradeTerminal { get; set; }

        public int? FirmwareUpgradeTerminalId { get; set; }

        [JsonIgnore]
        [ForeignKey("FirmwareUpgradeTerminalId")]
        [CompareIgnore]
        public virtual FirmwareUpgradeTerminal FirmwareUpgradeTerminal { get; set; }

        //--------------------------------------------------------------------
        // Terminal Setup Access
        //--------------------------------------------------------------------
        public int? TerminalSetupAccessDefault { get; set; }
        [ForeignKey("TerminalSetupAccessDefault")]
        [JsonIgnore]
        [CompareIgnore]
        public virtual SecurityAccessLevelMaster TerminalSetupAccessDefaultEntity { get; set; }

        public int? TerminalServiceDetail { get; set; }
        [ForeignKey("TerminalServiceDetail")]
        [JsonIgnore]
        [CompareIgnore]
        public virtual CompanyDetails TerminalServiceDetailCompany { get; set; }

        //[JsonIgnore]
        [NotMapped]
        [CompareIgnore]
        //[InverseProperty("AssociatedTerminals")]
        public virtual IList<MerchantCompany> AssociatedCompanies { get; set; }

        //Is Future Data Required
        [CompareIgnore]
        public virtual bool IsFutureDataRequired { get; set; }

        [ForeignKey("AutoLogin")]
        [CompareIgnore]
        public int? AutoLoginId { get; set; }

        public virtual AutoLoginTerminal AutoLogin { get; set; }

        [ForeignKey("Training")]
        [CompareIgnore]
        public int? TrainingId { get; set; }

        [CompareIgnore]
        public virtual TrainingTerminal Training { get; set; }

        [CompareIgnore]
        public virtual IList<TerminalChange> TerminalChanges { get; set; }

        [CompareIgnore]
        public virtual IList<TerminalUpdateSchedule> Schedules { get; set; }

        [CompareIgnore]
        public virtual IList<BinTable> BinTables { get; set; }

        [CompareIgnore]
        public virtual IList<IoTDeviceAssociatedStatus> IoTDeviceAssociatedStatus { get; set; }

        [CompareIgnore]
        public virtual IList<IoTDeviceCompanyAssigned> IoTDeviceCompanyAssigneds { get; set; }

        public int? LangById { get; set; }
        [ForeignKey("LangById")]
        [CompareIgnore]
        public virtual LanguageBy LanguageBy { get; set; }

        public int? TerminalSetupId { get; set; }
        [ForeignKey("TerminalSetupId")]
        public virtual TerminalSetup TerminalSetup { get; set; }

        public int? TerminalCloudId { get; set; }

        [ForeignKey("TerminalCloudId")]
        public virtual TerminalCloud TerminalCloud { get; set; }

        public int? MerchantGeneralSetupId { get; set; }
        [ForeignKey("MerchantGeneralSetupId")]
        [CompareIgnore]
        public virtual MerchantGeneralSetup MerchantGeneralSetup { get; set; }

        public int? PosIntegrationId { get; set; }
        [ForeignKey(nameof(PosIntegrationId))]
        public virtual PosIntegrationTerminal PosIntegration { get; set; }

        public int? CloudPosIntegrationId { get; set; }
        [ForeignKey(nameof(CloudPosIntegrationId))]
        public virtual CloudPosIntegrationTerminal CloudPosIntegration { get; set; }

        public int? TerminalStyleId { get; set; }
        [ForeignKey(nameof(TerminalStyleId))]
        public virtual TerminalStyle TerminalStyle { get; set; }

        //--------------------------------------------------------------------
        // Store json for future update
        //--------------------------------------------------------------------
        [NotMapped]
        [CompareIgnore]
        public IList<int> MerchantTerminalIds { get; set; }

        [NotMapped]
        [CompareIgnore]
        public IList<int> ProcessorTerminalIds { get; set; }

        [NotMapped]
        [CompareIgnore]
        public IList<int> UserTerminalIds { get; set; }

        //--------------------------------------------------------------------
        // Contructor  of Terminal
        //--------------------------------------------------------------------

        [CompareIgnore]
        public virtual IList<POSInterface> POSInterfaces { get; set; }

        [CompareIgnore]
        public virtual IList<POSConnection> POSConnections { get; set; }

        [CompareIgnore]
        public string Name => TerminalSerialNumber;

        public TerminalMaster()
        {
            TerminalWifi = new TerminalWifi();
            TerminalWAN = new TerminalWAN();
            ApplicationUpgradeTerminal = new ApplicationUpgradeTerminal();
        }

        public TerminalMaster(int companyId, int brandId, int modelId) : this()
        {
            TerminalServiceDetail = companyId;
            TerminalBrandId = brandId;
            TerminalModelTypeId = modelId;
            IsActive = true;
        }

        public TerminalMaster(string terminalName)
        {

            Support = new SupportTerminal();
            Date = new DateTimeZoneTerminal();
            Sound = new SoundTerminal();
            AutoLogin = new AutoLoginTerminal();
            Training = new TrainingTerminal();
            FallBack = new FallBackTerminal();
            PosRequest = new PosRequestTerminal();
            PosRequest.Sale = new SystemPosSaleType();
            PosRequest.Bar = new SystemPosSaleType();
            PosRequest.Customer = new SystemPosSaleType();
            PosRequest.Table = new SystemPosSaleType();
            PosRequest.Room = new SystemPosSaleType();
            CountryTerminal = new CountryTerminal();
            CashBack = new DeditCardBackTerminal();

            TerminalSecurityAccesses = new List<TerminalUserSecurityAccess>
            {
                new TerminalUserSecurityAccess(){ Level = 0},
                new TerminalUserSecurityAccess(){ Level = 1},
                new TerminalUserSecurityAccess(){ Level = 2},
                new TerminalUserSecurityAccess(){ Level = 3}
            };

            TerminalSerialNumber = "";
            TerminalServiceNumber = "";

            TerminalComment = "";
            ActiveAccessDate = DateTime.Now;
            ActivateAccessTime = DateTime.Now;
            DeActiveAccessDate = DateTime.Now;
            DeActiveAccessTime = DateTime.Now;

            TerminalInstallDate = DateTime.Now;
            TerminalInstallTime = DateTime.Now;

            BusinessStartTime = DateTime.Now;
            BusinessFinishTime = DateTime.Now;
            StoreName = "";
            TerminalLocation = "";
            LogonIpAddress = "";
            LogonMacAddress = "";
            DeactiveSetupUpdate = false;
            DeactivateAppUpgrade = false;
            DeactivateFirmwareUpgrade = false;
            DeactiveCloudAccess = false;
            IsActive = true;

            TerminalSetupUpdate = new TerminalSetupUpdate();
            ApplicationUpgradeTerminal = new ApplicationUpgradeTerminal();
            FirmwareUpgradeTerminal = new FirmwareUpgradeTerminal();
            LanguageBy = new LanguageBy();
            TerminalSetup = new TerminalSetup();
            TerminalCloud = new TerminalCloud();

            TerminalSetupAccessDefault = null;
            PosIntegration = new PosIntegrationTerminal();
            CloudPosIntegration = new CloudPosIntegrationTerminal();
        }

        public TerminalMaster Copy(TerminalMaster terminalTmp)
        {
            var terminalMaster = new TerminalMaster();
            terminalMaster.AutoLogin = this.AutoLogin;
            terminalMaster.AutoLoginId = this.AutoLoginId;
            terminalMaster.BusinessFinishTime = this.BusinessFinishTime;
            terminalMaster.BusinessStartTime = this.BusinessStartTime;
            terminalMaster.CashBack = this.CashBack;
            terminalMaster.CountryTerminal = this.CountryTerminal;
            terminalMaster.Date = this.Date;
            terminalMaster.FallBack = this.FallBack;
            terminalMaster.PosRequest = this.PosRequest;
            terminalMaster.Id = this.Id;
            terminalMaster.IsActive = this.IsActive;
            terminalMaster.IsStatus = this.IsStatus;
            terminalMaster.IsUpdated = this.IsUpdated;
            terminalMaster.Modified = DateTime.MinValue;
            terminalMaster.Created = DateTime.MinValue;
            terminalMaster.MerchantTerminalIds = terminalTmp.MerchantTerminals
                .Where(m => m.IsStatus != Constants.DELETE_RECORD && m.IsStatus != Constants.PRE_DELETE_RECORD)
                .Select(m => m.Id).ToList();
            terminalMaster.ProcessorTerminalIds = terminalTmp.ProcessorTerminals
                .Where(m => m.IsStatus != Constants.DELETE_RECORD && m.IsStatus != Constants.PRE_DELETE_RECORD)
                .Select(m => m.Id).ToList();
            terminalMaster.UserTerminalIds = terminalTmp.UserTerminals
                .Where(m => m.IsStatus != Constants.DELETE_RECORD && m.IsStatus != Constants.PRE_DELETE_RECORD)
                .Select(m => m.Id).ToList();
            terminalMaster.TerminalCloud = this.TerminalCloud;
            terminalMaster.TimeZoneMaster = this.TimeZoneMaster;
            terminalMaster.TimeZoneMasterId = this.TimeZoneMasterId;
            terminalMaster.Training = this.Training;
            terminalMaster.TrainingId = this.TrainingId;
            terminalMaster.Sound = this.Sound;
            terminalMaster.Support = this.Support;
            terminalMaster.TerminalSetup = this.TerminalSetup;
            terminalMaster.TerminalSecurityAccesses = this.TerminalSecurityAccesses.ToList();
            terminalMaster.UserTerminalIds = terminalTmp.UserTerminals
                .Where(m => m.IsStatus != Constants.DELETE_RECORD && m.IsStatus != Constants.PRE_DELETE_RECORD)
                .Select(m => m.Id).ToList();
            return terminalMaster;
        }

        public TerminalMaster Clone()
        {
            return (TerminalMaster)this.MemberwiseClone();
        }

        public bool IsDeviceTemplate()
        {
            return CopyDeviceTemplateId != null && DeviceSetupType == DeviceSetupType.BaseTemplate;
        }
    }

    public record AssignedDeviceParamsModel(string LocationIdsSelected, int LocationCategoryId, int DeviceTypeId, int DeviceIndustryId, int DeviceBrandId, int DeviceModelId, int CompanyId, int GroupId, int GroupMappingId);

    public record AssignedDeviceParamsApiModel(string LocationIdsSelected, int LocationCategoryId, int DeviceTypeId, int DeviceIndustryId, string DeviceBrandId, string DeviceModelId, string MerchantId, int CompanyId, int GroupId, int GroupMappingId, bool? IsActivate, int? CompanyGroupMappingId);

    public record MerchantSwitchImpactModel(bool hasImpact, List<int> terminalMasterIds = null);

    public class TerminalMasterMap : IEntityTypeConfiguration<TerminalMaster>
    {
        public void Configure(EntityTypeBuilder<TerminalMaster> builder)
        {
            builder.ToTable(Constants.TerminalMaster);
            builder.HasKey(reg => reg.Id);

            builder.HasMany(tm => tm.HostInterfaces)
                .WithOne(c => c.TerminalMaster)
                .HasForeignKey(c => c.TerminalMasterId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasMany(tm => tm.MerchantTerminals)
               .WithOne(mt => mt.Terminal)
               .HasForeignKey(mt => mt.TerminalId)
               .OnDelete(DeleteBehavior.Cascade);

            builder.HasMany(tm => tm.UserTerminals)
                .WithOne(mc => mc.Terminal)
                .HasForeignKey(mt => mt.TerminalId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasMany(tm => tm.TerminalSecurityAccesses)
                .WithOne(mc => mc.Terminal)
                .HasForeignKey(mt => mt.TerminalId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasMany(tm => tm.CharityTerminals)
                .WithOne(c => c.TerminalMaster)
                .HasForeignKey(c => c.TerminalId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasMany(tm => tm.CountryCardSetupTerminals)
                .WithOne(cc => cc.TerminalMaster)
                .HasForeignKey(cc => cc.TerminalMasterId)
                .OnDelete(DeleteBehavior.Cascade);
        }
    }
}
