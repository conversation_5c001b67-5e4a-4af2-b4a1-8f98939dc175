import { AxiosResponse } from 'axios';
import { DataItemResponse, PagingResponse } from '../models/common.model';
import {
  CompanyDetailsRequest,
  CompanyDetailsResponse,
  CompanyItemResponse,
} from '../models/company.model';
import {
  deleteAsync,
  getAsync,
  patchAsync,
  postAsync,
  postFormDataAsync,
  putFormDataAsync,
} from './http-client';
import {
  CompanyUserSelectResponse,
  SelectedCompanyUsers,
} from '../models/company-user.model';

const baseUrl = import.meta.env.VITE_MMS_API_URL;
const controller = 'company-details';

const getCompanyList = (
  pageNumber: number = 1,
  pageSize: number = 10,
  searchKey?: string,
): Promise<AxiosResponse<PagingResponse<CompanyItemResponse>>> => {
  const url = `${baseUrl}/${controller}/get-companies`;
  const param = `pageNumber=${pageNumber}&pageSize=${pageSize}&searchKey=${searchKey}`;
  return getAsync(`${url}?${param}`);
};

const getCompanyDetails = (
  id: number,
): Promise<AxiosResponse<CompanyDetailsResponse>> => {
  const url = `${baseUrl}/${controller}/get-company-details/${id}`;
  return getAsync(url);
};

const addNewCompany = (
  company: CompanyDetailsRequest,
): Promise<AxiosResponse> => {
  const url = `${baseUrl}/${controller}/add-new-company`;
  return postFormDataAsync(url, company);
};

const updateCompany = (
  id: number,
  company: CompanyDetailsRequest,
): Promise<AxiosResponse> => {
  const url = `${baseUrl}/${controller}/update-company/${id}`;
  return putFormDataAsync(url, company);
};

const deleteCompany = (id: number): Promise<AxiosResponse> => {
  const url = `${baseUrl}/${controller}/delete-company/${id}`;
  return deleteAsync(url);
};

const activateDeactivateCompany = (
  id: number,
  isActive: boolean,
): Promise<AxiosResponse> => {
  const url = `${baseUrl}/${controller}/activate-deactivate-company/${id}`;
  const param = `isActive=${isActive}`;
  return patchAsync(`${url}?${param}`);
};

function getCompanyUsersSelect(
  pageNumber: number,
  pageSize: number,
  companyId: number,
  filter: string,
  searchKey?: string,
): Promise<AxiosResponse<CompanyUserSelectResponse>> {
  const url = `${baseUrl}/${controller}/get-company-users/${companyId}?pageNumber=${pageNumber}&pageSize=${pageSize}&filter=${filter}&searchKey=${searchKey}`;
  return getAsync(url);
}

function getSelectedCompanyUsers(
  companyId: number,
  searchKey?: string,
): Promise<AxiosResponse<SelectedCompanyUsers>> {
  const url = `${baseUrl}/${controller}/get-selected-company-users/${companyId}?searchKey=${searchKey}`;
  return getAsync(url);
}

const activateDeactivateCompanyUser = (
  id: number,
  isActive: boolean,
): Promise<AxiosResponse> => {
  const url = `${baseUrl}/${controller}/activate-deactivate-company-user/${id}`;
  const param = `isActive=${isActive}`;
  return patchAsync(`${url}?${param}`);
};


function selectUsers(
  companyId: number,
  userIds: number[],
): Promise<AxiosResponse<void>> {
  const url = `${baseUrl}/${controller}/select-company-users/${companyId}`;
  return postAsync(url, userIds);
}

const getAppForCopyBaseTemplate = (
  companyId: number,
): Promise<AxiosResponse<DataItemResponse[]>> => {
  const url = `${baseUrl}/${controller}/get-app-for-copy-base-template/${companyId}`;
  return getAsync(url);
};

export const CompanyApi = {
  getCompanyList,
  getCompanyDetails,
  addNewCompany,
  updateCompany,
  deleteCompany,
  activateDeactivateCompany,
  getCompanyUsersSelect,
  getSelectedCompanyUsers,
  selectUsers,
  activateDeactivateCompanyUser,
  getAppForCopyBaseTemplate,
};
