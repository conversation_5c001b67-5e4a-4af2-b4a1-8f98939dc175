using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MMS.Core.Entities.Commons;
using MMS.Core.CoreUTI;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using MMS.Core.Attributes;
using System.ComponentModel.DataAnnotations;
using System.Xml.Serialization;
using MMS.Core.CoreUTI.Enum;
using MMS.Core.Entities.EntityInterface;

namespace MMS.Core.Entities
{
    public class MerchantMaster : BaseEntity, IViewItem
    {
        [MultiLanguageSupport]
        public string Name => lszMerchantName;
        public string lszMerchantName { get; set; }
        public string lszEmailAddress { get; set; }
        public string lszLogo { get; set; }


        public string MerchantID { get; set; }

        public string lszPhone { get; set; }
        public string lszMobilePhone { get; set; }
        public int? CompanyDetailId { get; set; }
        [ForeignKey("CompanyDetailId")]
        public virtual CompanyDetails CompanyDetail { get; set; }

        public int? PersonalDetailId { get; set; }
        [ForeignKey("PersonalDetailId")]
        public virtual UserMaster PersonalDetails { get; set; }

        public int? BusinessHourId { get; set; }
        [ForeignKey("BusinessHourId")]
        public virtual BusinessHour BusinessHour { get; set; }

        public int? MerchantCompanyId { get; set; }
        [ForeignKey("MerchantCompanyId")]
        public virtual MerchantCompany MerchantCompany { get; set; }

        public virtual IList<CompanyDetails> CompanyDetailses { get; set; }

        public virtual IList<MerchantTerminal> MerchantTerminals { get; set; }

        [Column(TypeName = "decimal(18, 8)")]
        public decimal Latitude { get; set; }
        [Column(TypeName = "decimal(18, 8)")]
        public decimal Longitude { get; set; }
        public bool IsRoaming { get; set; }
        public string lszPicture { get; set; }
        public string lszStreetAddressGeo { get; set; }
        public string lszIPGeo { get; set; }
        public bool fMobileLocation { get; set; }

        public bool UseSortBusinessCate { get; set; }
        public bool UseSortContactPersons { get; set; }
        public bool UseSortMerchantUsers { get; set; }
        public bool IsUserChangesAllowed { get; set; }
        public bool IsMerchantTemplate { get; set; }

        public string LinklyId { get; set; }
    }

    public class MerchantMasterMap : IEntityTypeConfiguration<MerchantMaster>
    {
        public void Configure(EntityTypeBuilder<MerchantMaster> builder)
        {
            builder.ToTable(Constants.MerchantMaster);
            builder.HasKey(reg => reg.Id);
        }
    }
}
