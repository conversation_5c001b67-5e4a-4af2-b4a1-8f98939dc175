using MMS.Model.LinklyFake;

namespace MMS.Infrastructure.Services.LinklyFake
{
    /// <summary>
    /// Interface for LinklyFake service to interact with the LinklyFake API
    /// </summary>
    public interface ILinklyFakeService
    {
        /// <summary>
        /// Get all terminals from the LinklyFake API
        /// </summary>
        /// <returns>List of LinklyTerminalDto objects</returns>
        Task<List<LinklyTerminalDto>> GetAllTerminalsAsync();

        /// <summary>
        /// Get all companies from the LinklyFake API
        /// </summary>
        /// <returns>List of LinklyCompanyDto objects</returns>
        Task<List<LinklyCompanyDto>> GetAllCompaniesAsync();

        /// <summary>
        /// Get all merchant masters from the LinklyFake API
        /// </summary>
        /// <returns>List of LinklyMerchantMasterDto objects</returns>
        Task<List<LinklyMerchantMasterDto>> GetAllMerchantMastersAsync();
    }
}
