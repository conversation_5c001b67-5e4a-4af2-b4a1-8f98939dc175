import { useRecoilState } from 'recoil';
import { breadcrumbsState, BreadcrumbItem } from '../states/breadcrumb';
import { useNavigate, useLocation } from 'react-router-dom';

export type { BreadcrumbItem };

export const useBreadcrumb = () => {
  const [breadcrumbs, setBreadcrumbs] = useRecoilState(breadcrumbsState);
  const navigate = useNavigate();
  const location = useLocation();

  /**
   * Resets breadcrumbs to only show Home
   */
  const resetToHome = () => {
    setBreadcrumbs([{ path: '/', title: 'Home' }]);
  };

  /**
   * Adds a breadcrumb item to the current breadcrumbs.
   * If the breadcrumb already exists, it removes all breadcrumbs after it.
   * If the page is independent, it resets the breadcrumbs to Home > Current Page.
   * @param item The breadcrumb item to add
   * @param isIndependent Whether this page should ignore previous navigation history
   */
  const updateBreadcrumb = (
    item: BreadcrumbItem,
    isIndependent: boolean = false,
  ) => {
    if (isIndependent) {
      // For independent pages, reset to Home > Current Page
      setBreadcrumbs([{ path: '/', title: 'Home' }, item]);
      return;
    }

    const existingIndex = breadcrumbs.findIndex(
      (crumb) => crumb.path === item.path,
    );

    if (existingIndex >= 0) {
      if (existingIndex === breadcrumbs.length - 1) {
        // Last item existed, no need to do anything
        return;
      } else {
        // If exists and it's not the last one, keep only breadcrumbs up to this index
        setBreadcrumbs((current) => current.slice(0, existingIndex + 1));
      }
    } else {
      // If doesn't exist, add it to the end
      setBreadcrumbs((current) => [...current, item]);
    }
  };

  /**
   * Updates breadcrumb for dynamic routes
   * @param path The dynamic route path
   * @param title The dynamic title for the breadcrumb
   * @param isIndependent Whether this page should ignore previous navigation history
   */
  const updateDynamicBreadcrumb = (
    path: string,
    title: string,
    isIndependent: boolean = false,
  ) => {
    updateBreadcrumb({ path, title }, isIndependent);
  };

  /**
   * Navigates to the previous breadcrumb in the history.
   * Removes the current breadcrumb from the array.
   * @param state Optional state to pass to the navigation. If not provided, will use the state from the breadcrumb item.
   * @returns true if navigation was successful, false otherwise
   */
  const navigateToPreviousBreadcrumb = (
    state?: Record<string, any>,
  ): boolean => {
    try {
      // Need at least 2 breadcrumbs to navigate back (current + previous)
      if (breadcrumbs.length < 2) {
        return false;
      }

      // Get the previous breadcrumb (second to last), not the current one (last)
      const previousBreadcrumb = breadcrumbs[breadcrumbs.length - 2];

      if (!previousBreadcrumb.path) {
        console.warn('Previous breadcrumb has no path to navigate to');
        return false;
      }

      // Use provided state or fall back to the state stored in the breadcrumb
      const navigationState = state || previousBreadcrumb.state;

      navigate(previousBreadcrumb.path, { state: navigationState });

      return true;
    } catch (error) {
      console.error('Error navigating to previous breadcrumb:', error);
      return false;
    }
  };

  return {
    breadcrumbs,
    setBreadcrumbs,
    updateBreadcrumb,
    updateDynamicBreadcrumb,
    navigateToPreviousBreadcrumb,
    resetToHome,
  };
};
