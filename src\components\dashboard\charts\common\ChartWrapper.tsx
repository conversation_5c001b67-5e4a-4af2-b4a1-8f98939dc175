import React from 'react';
import { <PERSON><PERSON>, Spin } from 'antd';

interface ChartWrapperProps {
  isLoading: boolean;
  children: React.ReactNode;
  className?: string;
}

const ChartWrapper: React.FC<ChartWrapperProps> = ({
  isLoading,
  children,
  className = '',
}) => {
  return (
    <>
      {isLoading && (
        <div className="chart-loading">
          <Spin tip="Loading...">
            <Alert description="Loading Chart..." type="info" />
          </Spin>
        </div>
      )}
      {!isLoading && (
        <div className={`chart-wrapper ${className}`}>{children}</div>
      )}
    </>
  );
};

export default ChartWrapper;
