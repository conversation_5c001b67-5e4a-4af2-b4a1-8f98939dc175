import React, { useCallback, useEffect, useState } from 'react';
import { Button, Dropdown, Input, Table } from 'antd';
import { useTranslation } from 'react-i18next';
import CommonModal from '../../shared/modal';
import FontIcon from '../../shared/icons/font-icon';
import PaginationComponent from '../../shared/pagination';
import { getMerchantColumns } from '../columns/merchant-columns';
import { FilterKey } from '../../../constants/app.enums';
import { DEFAULT_PAGE_SIZE } from '../../../constants/device-list.constants';
import { AvailableMerchantApiModel } from '../../../models/merchant-list.model';
import { MerchantListApi } from '../../../apis/merchant.api';
import { DeviceAssociatedResponseApiModel } from '../../../models/device-list.model';
import './index.scss';

interface MerchantAssignModalProps {
  visible: boolean;
  onCancel: () => void;
  onSave: () => Promise<void>;
  selectedDevice: DeviceAssociatedResponseApiModel | null;
  merchantRowSelection: any;
}

const MerchantAssignModal: React.FC<MerchantAssignModalProps> = ({
  visible,
  onCancel,
  onSave,
  selectedDevice,
  merchantRowSelection,
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [merchantData, setMerchantData] = useState<AvailableMerchantApiModel[]>(
    [],
  );
  const [totalRecordsMerchant, setTotalRecordsMerchant] = useState<number>(0);
  const [pageSizeMerchant, setPageSizeMerchant] = useState(DEFAULT_PAGE_SIZE);
  const [pageNumberMerchant, setPageNumberMerchant] = useState(1);
  const [merchantFilter, setMerchantFilter] = useState<FilterKey>(
    FilterKey.ALL,
  );
  const [searchValue, setSearchValue] = useState<string>('');
  const [searchKey, setSearchKey] = useState<string>('');
  const [isSearchVisible, setIsSearchVisible] = useState(false);
  const [selectedMerchants, setSelectedMerchants] = useState<number[]>([]);

  useEffect(() => {
    if (visible) {
      setSelectedMerchants([]);
    }
  }, [selectedDevice?.id, visible]);

  const fetchMerchantData = useCallback(
    async (deviceId: number) => {
      try {
        setLoading(true);
        const response = await MerchantListApi.getMerchantSelect(
          pageNumberMerchant,
          pageSizeMerchant,
          deviceId,
          merchantFilter.toLowerCase(),
          searchKey,
        );

        setMerchantData(response.data.merchantsSource.data);
        setTotalRecordsMerchant(response.data.merchantsSource.totalRecords);

        if (selectedMerchants.length === 0) {
          setSelectedMerchants(response.data.selectedIds);
          merchantRowSelection.onChange(response.data.selectedIds);
        }
      } catch (error) {
        console.error('Error fetching merchants:', error);
      } finally {
        setLoading(false);
      }
    },
    [
      pageNumberMerchant,
      pageSizeMerchant,
      merchantFilter,
      searchKey,
      selectedMerchants.length,
    ],
  );

  useEffect(() => {
    if (selectedDevice && visible) {
      fetchMerchantData(selectedDevice.id);
    }
  }, [selectedDevice?.id, visible, fetchMerchantData]);

  useEffect(() => {
    if (selectedDevice?.id && visible) {
      fetchMerchantData(selectedDevice.id);
    }
  }, [merchantFilter]);

  const handleModalCancel = () => {
    setMerchantFilter(FilterKey.ALL);
    setPageNumberMerchant(1);
    setSearchValue('');
    setSearchKey('');
    onCancel();
  };

  useEffect(() => {
    if (!visible) {
      setMerchantFilter(FilterKey.ALL);
      setPageNumberMerchant(1);
      setSearchValue('');
      setSearchKey('');
    }
  }, [visible]);

  const handleSearch = () => {
    if (!isSearchVisible) {
      setIsSearchVisible(true);
      return;
    }
    if (selectedDevice?.id) {
      setSearchKey(searchValue);
      setIsSearchVisible(false);
    }
  };

  const handleClearSearch = () => {
    setSearchValue('');
    setSearchKey('');
    setMerchantFilter(FilterKey.ALL);
    setPageNumberMerchant(1);
    if (selectedDevice?.id) {
      fetchMerchantData(selectedDevice.id);
    }
  };

  const updatedMerchantRowSelection = {
    ...merchantRowSelection,
    selectedRowKeys: selectedMerchants,
    preserveSelectedRowKeys: true, // This ensures selections are preserved across pages
    onChange: (selectedRowKeys: number[]) => {
      setSelectedMerchants(selectedRowKeys);
      merchantRowSelection.onChange(selectedRowKeys);
    },
  };

  return (
    <CommonModal
      visible={visible}
      onCancel={handleModalCancel} // Use handleModalCancel instead of onCancel
      onSave={onSave}
      title={`${t('device.assignMerchant')} ${selectedDevice?.serialNumber || ''}`}
      width={560}
    >
      <div className="selected-devices-section">
        <div className="selected-devices-header">
          <span className="section-title">{t('device.merchantSelected')}</span>
          <div className="section-actions">
            <div className="search-wrapper">
              <Button
                type="text"
                onClick={handleSearch}
                size="small"
                icon={<FontIcon size={16} className="icon-search main-color" />}
              >
                {t('common.search')}
              </Button>
              {isSearchVisible && (
                <div className="search-input">
                  <Input
                    autoFocus
                    placeholder={t('common.searchByName')}
                    value={searchValue}
                    onChange={(e) => setSearchValue(e.target.value)}
                    onPressEnter={() => {
                      if (selectedDevice?.id) {
                        setSearchKey(searchValue);
                        setIsSearchVisible(false);
                      }
                    }}
                    onBlur={() => setIsSearchVisible(false)}
                    style={{ width: 200 }}
                  />
                </div>
              )}
            </div>
            <Button
              type="text"
              icon={<FontIcon size={16} className="icon-close main-color" />}
              size="small"
              onClick={handleClearSearch}
            >
              {t('common.clearAll')}
            </Button>
            <Dropdown
              menu={{
                items: [
                  { key: FilterKey.ALL, label: t('common.all') },
                  { key: FilterKey.SELECTED, label: t('common.selected') },
                  {
                    key: FilterKey.NOT_SELECTED,
                    label: t('common.notSelected'),
                  },
                ],
                onClick: ({ key }) => {
                  setMerchantFilter(key as FilterKey);
                },
              }}
              trigger={['click']}
              placement="bottomRight"
            >
              <Button
                type="text"
                icon={<FontIcon size={16} className="icon-filter main-color" />}
                size="small"
              >
                Filter
              </Button>
            </Dropdown>
          </div>
        </div>
        <div className="selected-devices-table">
          <Table
            rowSelection={{
              type: 'checkbox',
              ...updatedMerchantRowSelection,
            }}
            className="device-assigned"
            columns={getMerchantColumns()}
            dataSource={merchantData}
            rowKey="id"
            loading={loading}
            pagination={false}
            scroll={{ y: 230 }}
          />
          <PaginationComponent
            totalRecords={totalRecordsMerchant}
            pageSize={pageSizeMerchant}
            setPageSize={setPageSizeMerchant}
            pageNumber={pageNumberMerchant}
            setPageNumber={setPageNumberMerchant}
          />
        </div>
      </div>
    </CommonModal>
  );
};

export default MerchantAssignModal;
