server {
    listen 80;
    server_name _;
    server_tokens off;

    root /usr/share/nginx/html;
    index index.html;

    # Security headers
    add_header X-Content-Type-Options "nosniff";
    # Removed X-Frame-Options to allow cross-domain iframe embedding
    add_header X-XSS-Protection "1; mode=block";

    # CORS headers for cross-domain requests
    # We need to use a variable for Access-Control-Allow-Origin to support multiple origins
    # This is handled by the global nginx config, so we'll just use * here for development
    add_header Access-Control-Allow-Origin "*";
    add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
    add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization";
    # Note: When using Access-Control-Allow-Origin: *, credentials cannot be true
    # This is handled by the global nginx config with proper origin checking
    # Comprehensive CSP that allows specific cross-domain connections for MVC
    add_header Content-Security-Policy "default-src 'self'; connect-src 'self' https://mms-api.projectjumpstart.site https://mms-mvc.projectjumpstart.site; frame-src 'self' http://localhost:8002 https://mms-mvc.projectjumpstart.site; frame-ancestors 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https://mms-api.projectjumpstart.site http://localhost:8002 https://mms-mvc.projectjumpstart.site;";

    # Cache static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
    }

    # Route all requests to index.html for SPA
    location / {
        try_files $uri $uri/ /index.html;
    }
}