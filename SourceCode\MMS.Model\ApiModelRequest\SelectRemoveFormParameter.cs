﻿namespace MMS.Model.ApiModelRequest
{
    [Flags]
    public enum FilterType
    {
        None = 0,
        All = 1 << 0,
        Selected = 1 << 1,
        NotSelected = 1 << 2
    }

    public class SelectRemoveFormParameter : PagingParameter
    {
        public string? SearchString { get; set; }

        public FilterType SelectedFilterType { get; set; } = FilterType.None;

        public SelectRemoveFormParameter()
        {
        }

        public SelectRemoveFormParameter(int pageNumber, int pageSize, FilterType filterType, string searchString) : this()
        {
            SearchString = searchString;
            SelectedFilterType = filterType;
            PageNumber = pageNumber;
            PageSize = pageSize;
        }
    }

    public class SelectRemoveRequest
    {
        public List<int> SelectedIds { get; set; }

        public SelectRemoveRequest()
        {
            SelectedIds = new List<int>();
        }
    }
}
