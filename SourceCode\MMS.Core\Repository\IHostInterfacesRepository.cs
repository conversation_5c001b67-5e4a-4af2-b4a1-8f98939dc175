﻿using MMS.Core.Entities.TerminalSetupGUI;
using MMS.Core.Repository;
using System;
using System.Collections.Generic;
using System.Text;

namespace MMS.Core.Repository
{
   public interface IHostInterfacesRepository : IBaseRepository<HostInterfaces>
    {
        IList<HostInterfaces> GetHostInterfaceByMerchantTerminalId(int merchantTemninalId);
        IList<HostInterfaces> GetHostInterfacesByTerminalMasterId(int terminalMasterId);
        IList<HostInterfaces> GetHostInterfacesNotSelectedByMerchantId(int merchantTemninalId, int terminalMasterId);
        IList<HostInterfaces> GetHostInterfacesByMerchantTerminalAndSurchargeRule(int merchantTerminalId, int surchargeRuleId);
        IList<HostInterfaces> GetHostInterfacesNotSelectedByMerchantAndSurchargeRule(int merchantTerminalId, int terminalMasterId, int surchargeRuleId);

        /// <summary>
        /// Gets host interfaces not assigned to the specified terminal, excluding deleted or pre-deleted records.
        /// </summary>
        /// <param name="terminalMasterId">The ID of the terminal master to exclude.</param>
        /// <returns>A list of unassigned, valid host interfaces.</returns>
        IList<HostInterfaces> GetHostInterfacesNotSelectedByTerminal(int terminalMasterId);

        /// <summary>
        /// Retrieves a list of HostInterfaces based on the provided IDs
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        IList<HostInterfaces> GetHostInterfacesByIds(IList<int> ids);

        /// <summary>
        /// Retrieves a list of global HostInterfaces
        /// </summary>
        /// <returns></returns>
        IList<HostInterfaces> GetGlobalHostInterfaces();

        /// <summary>
        /// Retrieves a list of global terminal HostInterfaces by TerminalID
        /// </summary>
        /// <param name="terminalMasterId">The ID of the terminal master to exclude.</param>
        /// <returns></returns>
        IList<HostInterfaces> GetTerminalHostInterfaces(int terminalMasterId);
    }

}
