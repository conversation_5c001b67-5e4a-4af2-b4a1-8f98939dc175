import React from 'react';
import './font-icon.scss';

interface IconProps extends React.HTMLAttributes<HTMLElement> {
  className?: string;
  size?: 12 | 14 | 16 | 20 | 24 | 28 | 32 | 36;
  hoverEffect?: boolean;
}

const FontIcon: React.FC<IconProps> = ({
  className = '',
  size,
  hoverEffect = false,
  ...rest
}) => {
  const sizeClass = size ? `font-icon-${size}` : '';
  const hoverClass = hoverEffect ? 'font-icon-hover' : '';
  const combinedClassName =
    `font-icon ${sizeClass} ${hoverClass} ${className}`.trim();

  return <i className={combinedClassName} {...rest} />;
};

export default FontIcon;
