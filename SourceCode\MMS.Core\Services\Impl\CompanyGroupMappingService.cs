﻿using System;
using MMS.Core.Entities;
using MMS.Core.Repository;
using MMS.Core.Services.Impl.Base;
using System.Collections.Generic;
using System.Threading.Tasks;
using MMS.Core.CoreUTI.Enum;

namespace MMS.Core.Services.Impl
{
    public class CompanyGroupMappingService : BaseService<CompanyGroupMapping>, ICompanyGroupMappingService
    {
        private readonly ICompanyGroupMappingRepository _companyGroupMappingRepository;
        private readonly IConfigureHideDeactivatedRepository _configureHideDeactivatedRepository;
        public CompanyGroupMappingService(IBaseRepository<CompanyGroupMapping> repository,
            ILanguageExecuteService languageExecuteService,
            ICompanyGroupMappingRepository companyGroupMappingRepository,
            IConfigureHideDeactivatedRepository configureHideDeactivatedRepository)
            : base(repository, languageExecuteService)
        {
            _companyGroupMappingRepository = companyGroupMappingRepository;
            _configureHideDeactivatedRepository = configureHideDeactivatedRepository;
        }

        public async Task<CompanyGroupMapping> GetByIdAsync(int id)
        {
            return await _companyGroupMappingRepository.GetByIdAsync(id);
        }

        public async Task<List<CompanyGroupMapping>> GetByIdsAsync(IList<int> ids)
        {
            return await _companyGroupMappingRepository.GetByIdsAsync(ids);
        }

        public async Task<List<int>> GetHierarchyIdsAsync(int parentId)
        {
            return await _companyGroupMappingRepository.GetHierarchyIdsAsync(parentId);
        }

        public async Task<List<CompanyGroupMapping>> GetSelectedAsync(CompanyGroupMappingType type, int? parentId, ScreenType screenType, bool includeDeactivatedOrg = false)
        {
            var listSelected = await _companyGroupMappingRepository.GetSelectedAsync(type, parentId, includeDeactivatedOrg);

            return listSelected;
        }

        public async Task<List<CompanyGroupMapping>> GetCompanyTreeChildrenMappingsAsync(int? parentId, bool isIncludeGroup = true)
        {
            if (parentId is not null)
            {
                _ = await _companyGroupMappingRepository.GetByIdAsync(parentId.GetValueOrDefault())
                    ?? throw new KeyNotFoundException($"Parent company with ID={parentId} not found.");
            }

            return await _companyGroupMappingRepository.GetCompanyTreeChildrenMappingsAync(parentId, isIncludeGroup);
        }

        public async Task<List<CompanyGroupMapping>> GetPartnerAccessTreeChildrenMappingsAync(int? parentId)
        {
            if (parentId is not null)
            {
                _ = await _companyGroupMappingRepository.GetByIdAsync(parentId.GetValueOrDefault())
                    ?? throw new KeyNotFoundException($"Parent company with ID={parentId} not found.");
            }

            return await _companyGroupMappingRepository.GetPartnerAccessTreeChildrenMappingsAync(parentId);
        }

        public async Task<List<CompanyGroupMapping>> GetSelectedByParentIdsAsync(CompanyGroupMappingType type, IList<int> parentIds, bool includeDeactivatedOrg = false)
        {
            var listSelected = await _companyGroupMappingRepository.GetSelectedByParentIdsAsync(type, parentIds, includeDeactivatedOrg);

            return listSelected;
        }

        public async Task<List<CompanyGroupMapping>> GetByTypeAsync(CompanyGroupMappingType type)
        {
            return await _companyGroupMappingRepository.GetByTypeAsync(type);
        }

        public async Task UpdateSelectedAsync(IList<int> selectedIdsint, int? parentId, CompanyGroupMappingType type, ScreenType screenType, bool useSelectSort = false)
        {
            await _companyGroupMappingRepository.UpdateSelectedAsync(selectedIdsint, parentId, type, screenType, useSelectSort);
        }

        public void ActivateDeactivateByListIds(IList<int> ids, bool isActivate)
        {
            var items = GetById(ids, includeDeactivated: true);

            foreach (var item in items)
            {
                item.IsActive = isActivate;
            }

            UpdateAll(items);
        }

        public async Task UpdateIsHideDeactivatedByParentIdAsync(int parentId)
        {
            await _companyGroupMappingRepository.UpdateIsHideDeactivatedByParentIdAsync(parentId);
        }

        public bool UpdateConfigureByType(CompanyGroupMappingType type)
        {
            var configureHideDeactivated = _configureHideDeactivatedRepository.GetFirst();
            bool result;
            switch (type)
            {
                case CompanyGroupMappingType.Company:
                    result = !configureHideDeactivated.MerchantLocationCompanyColumn;
                    configureHideDeactivated.MerchantLocationCompanyColumn = result;
                    break;
                case CompanyGroupMappingType.Group:
                    result = !configureHideDeactivated.MerchantLocationGroupColumn;
                    configureHideDeactivated.MerchantLocationGroupColumn = result;
                    break;
                default:
                    throw new ArgumentException($"Not support configure for this {type}");
            }

            _configureHideDeactivatedRepository.UpdateConfig(configureHideDeactivated);

            return result;
        }

        public bool GetConfigByType(CompanyGroupMappingType type)
        {
            var config = _configureHideDeactivatedRepository.GetFirst();
            switch (type)
            {
                case CompanyGroupMappingType.Company:
                    return config.MerchantLocationCompanyColumn;
                case CompanyGroupMappingType.Group:
                    return config.MerchantLocationGroupColumn;
                default:
                    throw new ArgumentException($"Not support configure for this {type}");
            }
        }

        public bool GetConfigByParentId(int parentId)
        {
            var parent = _companyGroupMappingRepository.GetById(parentId);

            return parent.IsHideDeactivated;
        }

        public async Task<List<CompanyGroup>> GetGroupSourcesForSubPartnerAsync(int accessPartnerId)
        {
            return await _companyGroupMappingRepository.GetGroupSourcesForSubPartnerAsync(accessPartnerId);
        }

        public async Task<CompanyGroupMapping> GetCompanyByCompanyIdAndScreenType(int? companyId, ScreenType screenType)
        {
            return await _companyGroupMappingRepository.GetCompanyByCompanyIdAndScreenType(companyId, screenType);
        }

        public async Task<List<CompanyGroupMapping>> GetSelectedGroupsByCompanyOrSubsidiaryAsync(int parentId)
        {
            return await _companyGroupMappingRepository.GetSelectedGroupsByCompanyOrSubsidiaryAsync(parentId);
        }

        public async Task<List<int>> GetExistingCompanyIdsInTreeAsync(int? parentId)
        {
            var ids = new List<int>();
            var currentId = parentId;

            while (currentId != null)
            {
                var currentCompanyGroupMapping = await _companyGroupMappingRepository.GetByIdAsync(currentId.Value);

                if (currentCompanyGroupMapping == null)
                    break;

                int? idToAdd = currentCompanyGroupMapping.Type switch
                {
                    CompanyGroupMappingType.SubPartner => currentCompanyGroupMapping.SubPartnerId,
                    CompanyGroupMappingType.PartnerAccess => currentCompanyGroupMapping.PartnerAccessId,
                    CompanyGroupMappingType.Subsidiary => currentCompanyGroupMapping.CompanySubsidiaryId,
                    CompanyGroupMappingType.Company => currentCompanyGroupMapping.CompanyId,
                    _ => null
                };

                if (idToAdd.HasValue)
                {
                    ids.Add(idToAdd.Value);
                }

                currentId = currentCompanyGroupMapping.ParentId;
            }

            return ids;
        }

        public async Task<List<CompanyGroupMapping>> GetSelectedAppByCompanyOrSubsidiaryAsync(int companyMasterId, int groupId)
        {
            return await _companyGroupMappingRepository.GetSelectedAppByCompanyOrSubsidiaryAsync(companyMasterId, groupId);
        }

        public async Task<List<int>> GetSelectedAppVersionIdsAsync()
        {
            return await _companyGroupMappingRepository.GetSelectedAppVersionIdsAsync();
        }

        public async Task<List<int>> GetAllParentIdsByHierarchy(string hierarchy)
        {
            return await _companyGroupMappingRepository.GetAllParentIdsByHierarchy(hierarchy);
        }

        public async Task<List<CompanyGroupMapping>> GetAllCompanyForSearchTreeMappingsAsync()
        {
            return await _companyGroupMappingRepository.GetAllCompanyForSearchTreeMappingsAsync();
        }

        public async Task<List<CompanyGroupMapping>> GetAllPartnerAccessTreeMappingsAsync()
        {
            return await _companyGroupMappingRepository.GetAllPartnerAccessTreeMappingsAsync();
        }

        public async Task InsertGroupAsync(int groupId, int parentId)
        {
            await _companyGroupMappingRepository.InsertGroupAsync(groupId, parentId);
        }
    }
}
