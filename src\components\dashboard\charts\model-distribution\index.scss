.chart-col {
  .apexcharts-canvas {
    .apexcharts-legend {
      display: grid;
      grid-template-columns: repeat(2, max-content);
    }
  }
}

.chart-col {
  .apexcharts-canvas {
    .apexcharts-legend {
      display: grid;
      grid-template-columns: repeat(2, max-content);
    }
  }
}

.chart-col .apexcharts-canvas .apexcharts-legend::after {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  height: 100%;
  width: 1px;
  background-color: #ccc;
  transform: translateX(-50%);
}

.model-distribution-chart {
  .apexcharts-legend {
    position: absolute;
    left: 0;
    top: 24px;
    display: flex;
    flex-direction: column;
    width: 400px;
  }

  .apexcharts-legend-series {
    display: flex;
    align-items: center;
    margin: 5px 30px;
    width: 80%;
  }

  .apexcharts-legend-marker {
    position: relative;
    height: 16px;
    width: 16px;
    display: inline-block;
  }

  .apexcharts-legend-marker svg {
    width: 100%;
    height: 100%;
  }

  .apexcharts-legend-text {
    margin-left: 0;
    color: #373d3f;
    font-size: 12px;
    font-weight: 400;
    font-family: Helvetica, Arial, sans-serif;
    display: flex;
    align-items: center;
    width: 100%;
  }

  .custom-legend-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding-right: 20px;
  }

  .legend-label {
    color: #666666;
    font-size: 12px;
    display: block;
  }

  .legend-value {
    color: inherit;
    font-size: 12px;
    font-weight: 600;
    text-align: right;
    width: 20px;
  }
}
