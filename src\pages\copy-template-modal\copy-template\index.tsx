import './index.scss';
import { useRef, useEffect, useState, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import DataColumnItems, { DataItem } from '../../../components/shared/column';
import { DataItemResponse } from '../../../models/common.model';
import {
  ColumnHeightCssClass,
  ColumnWidthCssClass,
  CompanyGroupMappingType,
} from '../../../constants/app.enums';
import { LOCAL_STORAGE_KEY } from '../../../constants/app-constants';
import { AppBaseTemplateApi } from '../../../apis/app-base-template.api';
import LocalUtils from '../../../utils/local.utils';
import { CompanyApi } from '../../../apis/company.api';

interface SelectedItems {
  application?: DataItem;
  appVersion?: DataItem;
  baseTemplate?: DataItem;
}

interface CopyTemplateProps {
  companyId: number;
  onTemplateSelect?: (selectedTemplate: DataItem | null) => void;
}

const CopyTemplate = ({ companyId, onTemplateSelect }: CopyTemplateProps) => {
  const appBaseTemplateRef = useRef<HTMLDivElement>(null);
  const { t } = useTranslation();

  const [applications, setApplications] = useState<DataItem[]>([]);
  const [appVersions, setAppVersions] = useState<DataItem[]>([]);
  const [baseTemplates, setBaseTemplates] = useState<DataItem[]>([]);
  const [selectedItems, setSelectedItems] = useState<SelectedItems>({});
  const [loadingApplications, setLoadingApplications] = useState(false);
  const [loadingAppVersions, setLoadingAppVersions] = useState(false);
  const [loadingBaseTemplates, setLoadingBaseTemplates] = useState(false);

  const [selectedApplicationId, setSelectedApplicationId] = useState<
    number | null
  >(null);
  const [selectedAppVersionId, setSelectedAppVersionId] = useState<
    number | null
  >(null);
  const [selectedBaseTemplateId, setSelectedBaseTemplateId] = useState<
    number | null
  >(null);

  const resetDataFunctions = useMemo(
    () => [setApplications, setAppVersions, setBaseTemplates],
    [],
  );

  const resetSelectedFunctions = useMemo(
    () => [
      setSelectedApplicationId,
      setSelectedAppVersionId,
      setSelectedBaseTemplateId,
    ],
    [],
  );

  const resetFromIndex = useCallback(
    (startIndex: number) => {
      resetDataFunctions.slice(startIndex + 1).forEach((reset) => reset([]));
      resetSelectedFunctions.slice(startIndex).forEach((reset) => reset(null));
    },
    [resetDataFunctions, resetSelectedFunctions],
  );

  const convertToDataItem = (dataItemRes: DataItemResponse[]): DataItem[] => {
    return dataItemRes.map((item) => ({
      key: item.id,
      label: item.name || '',
      active: item.isActive,
    }));
  };

  const loadSavedSelections = useCallback(async (items: SelectedItems) => {
    try {
      if (items.application) {
        await handleSelectApplication(items.application);

        if (items.appVersion) {
          await handleSelectAppVersion(items.appVersion);

          if (items.baseTemplate) {
            await handleSelectBaseTemplate(items.baseTemplate);
          }
        }
      }
    } catch (error) {
      console.error('Error loading saved selections:', error);
    }
  }, []);

  const loadInitialData = useCallback(async () => {
    setLoadingApplications(true);
    let apps: DataItem[] = [];
    try {
      resetFromIndex(0);

      const response = await CompanyApi.getAppForCopyBaseTemplate(companyId);

      apps = convertToDataItem(response.data);
      setApplications(apps);
    } catch (error) {
      console.error('Error loading applications:', error);
      setApplications([]);
    } finally {
      setLoadingApplications(false);
    }

    const savedItems = LocalUtils.get(
      LOCAL_STORAGE_KEY.COPY_TEMPLATE_SELECTIONS,
    );
    if (savedItems) {
      try {
        const parsedItems = JSON.parse(savedItems) as SelectedItems;
        if (parsedItems.application) {
          const savedApp = apps.find(
            (app: DataItem) => app.key === parsedItems.application?.key,
          );
          if (savedApp) {
            setSelectedItems(parsedItems);
            await loadSavedSelections(parsedItems);
          }
        }
      } catch (error) {
        console.error('Error loading saved selections:', error);
      }
    }
  }, [resetFromIndex, loadSavedSelections]);

  useEffect(() => {
    loadInitialData();
  }, [loadInitialData]);

  useEffect(() => {
    if (!selectedBaseTemplateId) {
      onTemplateSelect?.(null);
    }
  }, [selectedBaseTemplateId, onTemplateSelect]);

  const handleClick = async (item: DataItem, columnIndex: number) => {
    let newSelectedItems: SelectedItems = { ...selectedItems };

    switch (columnIndex) {
      case 1: // Application
        newSelectedItems = {
          application: item,
          appVersion: undefined,
          baseTemplate: undefined,
        };
        await handleSelectApplication(item);
        break;

      case 2: // App Version
        if (!selectedItems.application) return;
        newSelectedItems = {
          ...selectedItems,
          appVersion: item,
          baseTemplate: undefined,
        };
        await handleSelectAppVersion(item);
        break;

      case 3: // Base Template
        if (!selectedItems.application || !selectedItems.appVersion) return;
        newSelectedItems = {
          ...selectedItems,
          baseTemplate: item,
        };
        await handleSelectBaseTemplate(item);
        break;
    }

    setSelectedItems(newSelectedItems);
    saveSelectedItems(newSelectedItems);
  };

  const saveSelectedItems = (items: SelectedItems) => {
    LocalUtils.set(
      LOCAL_STORAGE_KEY.COPY_TEMPLATE_SELECTIONS,
      JSON.stringify(items),
    );
  };

  const handleSelectApplication = async (item: DataItem) => {
    setSelectedApplicationId(item.key);
    resetFromIndex(1);

    try {
      setLoadingAppVersions(true);

      const response = await AppBaseTemplateApi.getCompanyGroupMapping(
        item.key,
        CompanyGroupMappingType.AppVersion,
      );

      setAppVersions(convertToDataItem(response.data));
    } catch (error) {
      console.error('Error loading app versions:', error);
      setAppVersions([]);
    } finally {
      setLoadingAppVersions(false);
    }
  };

  const handleSelectAppVersion = async (item: DataItem) => {
    setSelectedAppVersionId(item.key);
    resetFromIndex(2);

    try {
      setLoadingBaseTemplates(true);

      const response = await AppBaseTemplateApi.getAppBaseTemplate(item.key);

      setBaseTemplates(convertToDataItem(response.data));
    } catch (error) {
      console.error('Error loading base template:', error);
      setBaseTemplates([]);
    } finally {
      setLoadingBaseTemplates(false);
    }
  };

  const handleSelectBaseTemplate = async (item: DataItem) => {
    setSelectedBaseTemplateId(item.key);
    onTemplateSelect?.(item);
  };

  return (
    <div className="copy-template" ref={appBaseTemplateRef}>
      <div className="copy-template-content">
        <DataColumnItems
          name={t('appBaseTemplate.columnTitles.application')}
          data={applications}
          loading={loadingApplications}
          onSelect={(item) => handleClick(item, 1)}
          selectedKey={selectedApplicationId}
          columnWidthClass={ColumnWidthCssClass.Three}
          columnHeightClass={ColumnHeightCssClass.Big}
          menuItems={[]}
          showMenuIcon={false}
        />
        <DataColumnItems
          name={t('appBaseTemplate.columnTitles.appVersion')}
          data={appVersions}
          onSelect={(item) => handleClick(item, 2)}
          loading={loadingAppVersions}
          selectedKey={selectedAppVersionId}
          columnWidthClass={ColumnWidthCssClass.Three}
          columnHeightClass={ColumnHeightCssClass.Big}
          menuItems={[]}
          showMenuIcon={false}
        />
        <DataColumnItems
          name={t('appBaseTemplate.columnTitles.baseTemplate')}
          data={baseTemplates}
          onSelect={(item) => handleClick(item, 3)}
          loading={loadingBaseTemplates}
          selectedKey={selectedBaseTemplateId}
          columnWidthClass={ColumnWidthCssClass.Three}
          columnHeightClass={ColumnHeightCssClass.Big}
          menuItems={[]}
          showMenuIcon={false}
        />
      </div>
    </div>
  );
};

export default CopyTemplate;
