import { Spin } from 'antd';
import { useEffect, useState } from 'react';
import Chart from 'react-apexcharts';
import './index.scss';
import { ApexOptions } from 'apexcharts';

interface ChartData {
  series: number[];
  labels: string[];
}

interface ChartState {
  options: ApexOptions;
}

const CHART_COLORS = [
  '#1E90FF',
  '#32CD32',
  '#4682B4',
  '#FFA500',
  '#1E90FF',
  '#FF4500',
  '#32CD32',
  '#B22222',
  '#A9A9A9',
  '#8A2BE2',
];

const INITIAL_CHART_DATA: ChartData = {
  series: [29, 20, 10, 8, 6, 5, 4, 4, 4, 17],
  labels: [
    'A920 Pro',
    'A920',
    'Others',
    'A77',
    'A3700',
    'A30',
    'A35',
    'A910S',
    'A6650',
    'Others',
  ],
};

const TOTAL_NON_ACTIVE = 107;

const formatLegendItem = (seriesName: string, opts: any) => {
  const value = opts.w.globals.series[opts.seriesIndex];
  const color = opts.w.globals.colors[opts.seriesIndex];
  return `
    <div class="custom-legend-item" style="display: flex; align-items: center;">
      <span class="legend-label" style="color: #666666; font-size: 12px; display: block;">${seriesName}</span>
      <span class="legend-value" style="color: ${color}; font-size: 12px; font-weight: 600; margin-left: 10px; ">${value}</span>
    </div>
  `;
};

const getChartOptions = (chartData: ChartData): ApexOptions => ({
  series: chartData.series,
  labels: chartData.labels,
  dataLabels: {
    enabled: false,
  },
  plotOptions: {
    pie: {
      expandOnClick: true,
      customScale: 1,
      donut: {
        size: '60%',
        labels: {
          show: true,
          name: {
            show: true,
          },
          value: {
            show: true,
            fontSize: '14px',
            fontWeight: 600,
            color: '#000000',
            offsetY: 0,
            formatter: () => TOTAL_NON_ACTIVE.toString(),
          },
          total: {
            show: true,
            label: 'Total',
            fontSize: '12px',
            fontWeight: 600,
            color: '#666666e7',
            formatter: () => TOTAL_NON_ACTIVE.toString(),
          },
        },
      },
    },
  },
  colors: CHART_COLORS,
  chart: {
    width: 50,
  },
  legend: {
    position: 'left',
    horizontalAlign: 'left',
    floating: false,
    fontSize: '12px',
    formatter: formatLegendItem,
    markers: {},
    itemMargin: {
      horizontal: 30,
      vertical: 5,
    },
  },
});

function ModelDistribution() {
  const [isLoading, setLoading] = useState(true);
  const [chartState, setChartState] = useState<ChartState>({
    options: getChartOptions(INITIAL_CHART_DATA),
  });

  useEffect(() => {
    // Simulating data fetching delay
    const timer = setTimeout(() => {
      setLoading(false);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <>
      {isLoading && <Spin className="loading-container" />}
      {!isLoading && (
        <div className="chart-col model-distribution-chart">
          <Chart
            options={chartState.options}
            series={chartState.options.series}
            type="donut"
            width={'100%'}
            height={200}
          />
        </div>
      )}
    </>
  );
}

export default ModelDistribution;
