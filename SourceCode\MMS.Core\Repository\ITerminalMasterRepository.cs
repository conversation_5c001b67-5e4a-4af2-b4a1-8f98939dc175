using MMS.Core.Entities;
using MMS.Model.ApiModelRequest;
using MMS.Model.ApiModelResponse;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;
using MMS.Core.CoreUTI.Enum;
using MMS.Infrastructure.Commons;

namespace MMS.Core.Repository
{
    public interface ITerminalMasterRepository : IBaseRepository<TerminalMaster>
    {
        IList<TerminalMaster> TerminalByGroupCompanyUserAndLevel();
        T GetNaviProperty<T>(int id, Expression<Func<TerminalMaster, object>> select, params Expression<Func<TerminalMaster, object>>[] includes) where T : class;
        IList<T> GetNaviPropertyList<T>(int id, Expression<Func<TerminalMaster, object>> select, params Expression<Func<TerminalMaster, object>>[] includes) where T : class;
        int LastMaster();
        TerminalMaster GetTerminalByTerminalId(int terId);
        TerminalMaster GetMerchantCardFallback(int terId);
        IList<TerminalMaster> TerminalByGroupCompanyModelAndFilter(int modelId);
        IList<TerminalMaster> GetAllTerminalsByApplicationTemplate(int id);
        IList<TerminalMaster> TerminalActiveByGroupId(int id, string search, int index, int limit, IEnumerable<int> _groupTerminals);

        bool SetIsStatus(string table, List<int> ids, int isStatus);
        bool SetIsStatus(string table, string whereStatus, int isStatus);
        bool SetIsClientId(string table, List<int> ids, int clientId);

        List<int> GetAllTerminalSetupUpdateByTemplateId(int id);
        TerminalMaster GetTerminalBySerialNumber(string serialNumber, string macAddress, int modelId);

        /// <summary>
        /// Support API api/synchronize/resyncfile/prepare without MacAddress and TerminalModelId
        /// </summary>
        /// <param name="serialNumber"></param>
        /// <returns></returns>
        TerminalMaster GetTerminalBySerialNumber(string serialNumber);

        string GetAllXmlChanges(int terminalMasterId);

        TerminalMaster GetForDeviceSetupEdit(int terminalMasterId);

        /// <summary>
        /// Retrieves the list of registered applications associated with a specific terminal master.
        /// </summary>
        /// <param name="terminalMaterId">The unique identifier of the terminal master.</param>
        /// <returns>
        /// A task that represents the asynchronous operation.
        /// The task result contains a list of <see cref="ApplicationDeviceMapping"/> objects associated with the terminal master.
        /// If no applications are found or the terminal master is not valid, an empty list is returned.
        /// </returns>
        Task<List<ApplicationDeviceMapping>> GetSelectedApplicationListByTerminalMasterIdAsync(int terminalMaterId, List<int> source);

        List<TerminalMaster> GetTerminalForPaxStore(Expression<Func<TerminalMaster, bool>> whereClause = null);

        /// <summary>
        /// Update IsStatus for api sync up restore
        /// </summary>
        /// <param name="table"></param>
        /// <param name="whereStatus"></param>
        /// <param name="isStatus"></param>
        /// <param name="columnName"></param>
        /// <returns></returns>
        Task<bool> SetIsStatusWithColumnGroupAsync(string table, string whereStatus, int isStatus, string columnName);

        /// <summary>
        /// Retrieves a list of unassigned <see cref="TerminalMaster"/> devices based on various filters.
        /// </summary>
        /// <param name="companyIdOrg">Filter by original company ID.</param>
        /// <param name="brandIdOrg">Filter by original brand ID.</param>
        /// <param name="modelIdOrg">Filter by original model ID.</param>
        /// <param name="isAssignedDevice">Whether to include assigned devices.</param>
        /// <param name="searchKey">Search term for the terminal serial number.</param>
        /// <param name="companyId">Filter by company ID for assigned devices.</param>
        /// <param name="accessStatusId">Filter by access status ID for assigned devices.</param>
        /// <returns>A list of matching <see cref="TerminalMaster"/> entries.</returns>
        Task<List<TerminalMaster>> GetAllTerminalUnAssignedDeviceAsync(int companyIdOrg, int brandIdOrg, int modelIdOrg, bool isAssignedDevice = false, string searchKey = "", int companyId = 0, int accessStatusId = 0);

        /// <summary>
        /// Assign Device to Location Area and Device Industry
        /// </summary>
        /// <param name="terminalId"></param>
        /// <param name="locationAreaId"></param>
        /// <param name="deviceIndustryId"></param>
        /// <param name="groupId"></param>
        /// <returns></returns>
        Task AssignDeviceToLocationAsync(int terminalId, int locationAreaId, int deviceIndustryId, int groupId);

        /// <summary>
        /// Asynchronously assigns a device industry to a terminal.
        /// </summary>
        /// <param name="terminalId">The ID of the terminal to which the device industry will be assigned.</param>
        /// <param name="deviceIndustryId">The ID of the device industry to assign to the terminal.</param>
        /// <returns>A Task representing the asynchronous operation. The Task will complete when the device industry is successfully assigned.</returns>
        /// <exception cref="ArgumentException">Thrown when the terminalId or deviceIndustryId is invalid.</exception>
        /// <exception cref="InvalidOperationException">Thrown when the device industry cannot be assigned due to a system error or incompatible data.</exception>
        Task AssignDeviceIndustryAsync(int terminalId, int deviceIndustryId);

        /// <summary>
        /// Get Assigned Device and filter by Device Model id
        /// </summary>
        /// <param name="locationAreaIds"></param>
        /// <param name="deviceIndustryIds"></param>
        /// <param name="deviceModelId"></param>
        /// <param name="companyId"></param>
        /// <param name="groupId"></param>
        /// <returns></returns>
        Task<List<TerminalMaster>> GetDeviceAssignedAsync(IList<int> locationAreaIds, IList<int> deviceIndustryIds, int deviceBrandId, int deviceModelId, int companyId, int groupId);

        /// <summary>
        /// Asynchronously retrieves a list of devices based on a list of device IDs.
        /// </summary>
        /// <param name="ids">A list of device IDs to query the devices.</param>
        /// <returns>
        /// A task representing the asynchronous operation. The task result contains a list of
        /// <see cref="TerminalMaster"/> objects that match the provided device IDs.
        /// </returns>
        Task<IList<TerminalMaster>> GetListDeviceByListIdAsync(IList<int> ids);

        /// <summary>
        /// Asynchronously retrieves a list of devices based on the specified filter criteria.
        /// </summary>
        /// <param name="groupId">The ID of the device group to filter the devices by.</param>
        /// <param name="deviceIndustryIds">A list of device industry IDs to filter the devices by.</param>
        /// <param name="deviceBrandId">The ID of the device brand to filter the devices by.</param>
        /// <param name="deviceModelId">The ID of the device model to filter the devices by.</param>
        /// <param name="companyId">The ID of the partner company or sub partner whose devices are to be retrieved.</param>
        /// <param name="locationAreaIds">The IDs of the location area id whose devices are to be retrieved.</param>
        /// <returns>
        /// A task representing the asynchronous operation. The task result is a list of <see cref="TerminalMaster"/> objects that match the specified filter criteria.
        /// </returns>
        /// <remarks>
        /// This method allows for filtering devices by multiple criteria, including industry, brand, model, and company.
        /// It is useful in scenarios where you need to retrieve devices that match specific conditions, such as when displaying a list of available devices for a particular company or when managing devices based on industry and brand.
        /// </remarks>
        Task<List<TerminalMaster>> GetDeviceListByFiltersAsync(int groupId, IList<int> deviceIndustryIds, int deviceBrandId, int deviceModelId, int companyId, IList<int> locationAreaIds);

        /// <summary>
        /// Asynchronously retrieves a list of terminal brands that are not associated with any LocationAreaId
        /// for the specified device industries.
        /// </summary>
        /// <param name="deviceIndustryIds">A list of device industry IDs to filter the terminal brands by.</param>
        /// <returns>
        /// A task representing the asynchronous operation. The task result is a list of <see cref="TerminalBrandMaster"/>
        /// objects representing the terminal brands that are not associated with any LocationAreaId for the specified industries.
        /// </returns>
        /// <remarks>
        /// This method is useful when you need to retrieve terminal brands that are available for specific device industries, but do not have any association with a specific location area. This can be useful in scenarios where brands are not tied to any physical location or region.
        /// </remarks>
        Task<List<TerminalBrandMaster>> GetTerminalMasterBrandWithoutLocationAreaId(IList<int> deviceIndustryIds);

        /// <summary>
        /// Get Brand Master of Assigned Location Device
        /// </summary>
        /// <param name="locationAreaIds"></param>
        /// <param name="deviceIndustryIds"></param>
        /// <param name="deviceBrandIds"></param>
        /// <param name="companyId"></param>
        /// <param name="groupId"></param>
        /// <returns></returns>
        Task<List<TerminalBrandMaster>> GetTerminalBrandMasterOfAssignedDeviceAsync(IList<int> locationAreaIds, IList<int> deviceIndustryIds, IList<int> deviceBrandIds, int companyId, int groupId);

        Task<List<TerminalModelMaster>> GetTerminalMasterModelWithoutLocationAreaId(IList<int> deviceIndustryIds, int deviceBrandId, int companyId, int groupId);

        /// <summary>
        /// Get Model Master Of Assigned Location Device and filter by Device Brand id
        /// </summary>
        /// <param name="locationAreaIds"></param>
        /// <param name="deviceIndustryIds"></param>
        /// <param name="deviceBrandId"></param>
        /// <param name="companyId"></param>
        /// <param name="groupId"></param>
        /// <returns></returns>
        Task<List<TerminalModelMaster>> GetTerminalModelMasterOfAssignedDeviceAsync(IList<int> locationAreaIds, IList<int> deviceIndustryIds, int deviceBrandId, int companyId, int groupId);

        Task<List<int>> GetMatchingLocationAreaIdsAsync(List<int> locationAreaIds);

        Task<List<int>> GetMatchingDeviceIndustryIdsAsync(List<int> deviceIndustryIds, int? locationAreaId);

        /// <summary>
        /// Get Terminal with include
        /// </summary>
        /// <param name="terminalMasterId"></param>
        /// <returns></returns>
        TerminalMaster GetTerminalMasterIncludedById(int terminalMasterId);

        /// <summary>
        /// Get Terminal By Device Template Id
        /// </summary>
        /// <param name="templateId"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        Task<TerminalMaster> GetTerminalByDeviceTemplateId(int templateId, DeviceSetupType type);

        /// <summary>
        /// Get Terminal By Serial Number not include
        /// </summary>
        /// <param name="serialNumber"></param>
        /// <returns></returns>
        Task<TerminalMaster> GetTerminalBySerialNumberNotInclude(string serialNumber);

        /// Asynchronously retrieves a list of <see cref="TerminalMaster"/> objects that have a non-null <see cref="TerminalMaster.CopyDeviceTemplateId"/> and are associated with a device template that has a status of <see cref="Constants.DELETE_RECORD"/>.
        /// </summary>
        /// <returns>
        /// A task that represents the asynchronous operation. The task result contains a list of <see cref="TerminalMaster"/> objects matching the specified criteria.
        /// </returns>
        Task<List<TerminalMaster>> GetDeletedTemplateTerminalsAsync();

        /// <summary>
        /// Get Terminal by device template id
        /// </summary>
        /// <param name="templateId"></param>
        /// <returns></returns>
        Task<TerminalMaster> GetTerminalByDeviceTemplateId(int templateId);

        /// <summary>
        /// Get assigned device ids by location
        /// </summary>
        /// <param name="locationItemId"></param>
        /// <returns></returns>
        Task<List<int>> GetAssignedDeviceIdsByLocationAsync(int locationItemId, int groupId);

        /// <summary>
        /// Get all available devices for api
        /// </summary>
        /// <param name="companyIdOrg"></param>
        /// <param name="brandIdOrg"></param>
        /// <param name="modelIdOrg"></param>
        /// <param name="isAssignedDevice"></param>
        /// <param name="searchKey"></param>
        /// <param name="companyId"></param>
        /// <param name="accessStatusId"></param>
        /// <returns></returns>
        Task<PagingResponse<AvailableDevicesApiModel>> GetAllTerminalUnAssignedDeviceForApiAsync(int companyId, List<int> brandIds, List<int> modelIds, string keySearch, PagingParameter pagingParameter);

        /// <summary>
        /// Assign Device to Location Area and Group Id
        /// </summary>
        /// <param name="terminalId"></param>
        /// <param name="locationAreaId"></param>
        /// <param name="deviceIndustryId"></param>
        /// <param name="groupId"></param>
        /// <returns></returns>
        Task AssignDeviceToLocationForApiAsync(int terminalId, int locationAreaId, int groupId);

        /// <summary>
        /// Assign multiple Device to Location Area and Group Id
        /// </summary>
        /// <param name="terminalIds"></param>
        /// <param name="locationAreaId"></param>
        /// <param name="groupId"></param>
        /// <returns></returns>
        Task AssignDevicesToLocationForApiAsync(IList<int> terminalIds, int locationAreaId, int groupId);

        /// <summary>
        /// Gets terminals by source flag
        /// </summary>
        /// <param name="source">The terminal source flag to filter by</param>
        /// <param name="includeDeleted">Whether to include deleted terminals</param>
        /// <param name="includeDeactivated">Whether to include deactivated terminals</param>
        /// <returns>List of terminals matching the source flag</returns>
        Task<IList<TerminalMaster>> GetTerminalsBySourceAsync(TerminalSources source, bool includeDeleted = false, bool includeDeactivated = true);


        /// <summary>
        /// Get device asigned include pagingParameter
        /// </summary>
        /// <param name="locationAreaIds"></param>
        /// <param name="deviceIndustryIds"></param>
        /// <param name="deviceBrandId"></param>
        /// <param name="deviceModelId"></param>
        /// <param name="companyId"></param>
        /// <param name="groupId"></param>
        /// <param name="pagingParameter"></param>
        /// <returns></returns>
        Task<PagingResponse<DeviceAssociatedResponseApiModel>> GetDeviceAssignedAsync(IList<int> locationAreaIds, IList<int> deviceIndustryIds, List<int> deviceBrandIds, List<int> deviceModelIds, List<int> merchantIds, List<int> companyIds, int groupId, string searchValue, SearchAssignDeviceType searchType, PagingParameter pagingParameter, bool? isActivate);

        /// <summary>
        /// Unassigns the specified devices from their associated location asynchronously.
        /// </summary>
        /// <param name="terminalIds"></param>
        /// <returns></returns>
        Task UnassignDevicesToLocationForApiAsync(IList<int> terminalIds);

        /// <summary>
        /// Check has any device assigned to group
        /// </summary>
        /// <param name="groupId"></param>
        /// <returns>boolean</returns>
        Task<bool> HasAnyDeviceAssignedToGroup(int groupId);
    }
}
