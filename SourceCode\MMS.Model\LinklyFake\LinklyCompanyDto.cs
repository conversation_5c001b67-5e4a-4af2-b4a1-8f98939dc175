using Newtonsoft.Json;

namespace MMS.Model.LinklyFake
{
    /// <summary>
    /// DTO for company data from the LinklyFake API
    /// </summary>
    public class LinklyCompanyDto
    {
        /// <summary>
        /// Company ID
        /// </summary>
        [JsonProperty("id")]
        public int Id { get; set; }

        /// <summary>
        /// Linkly ID
        /// </summary>
        [JsonProperty("linklyId")]
        public string LinklyId { get; set; } = string.Empty;

        /// <summary>
        /// Company ID number
        /// </summary>
        [JsonProperty("companyIdNumber")]
        public int CompanyIdNumber { get; set; }

        /// <summary>
        /// Company registered name
        /// </summary>
        [JsonProperty("companyRegisteredName")]
        public string CompanyRegisteredName { get; set; } = string.Empty;

        /// <summary>
        /// Company trading name
        /// </summary>
        [JsonProperty("companyTradingName")]
        public string CompanyTradingName { get; set; } = string.Empty;

        /// <summary>
        /// Tax ID
        /// </summary>
        [JsonProperty("taxID")]
        public int TaxID { get; set; }

        /// <summary>
        /// Company photo
        /// </summary>
        [JsonProperty("companyPhoto")]
        public string? CompanyPhoto { get; set; }

        /// <summary>
        /// Formation date
        /// </summary>
        [JsonProperty("formationDate")]
        public DateTime FormationDate { get; set; }

        /// <summary>
        /// Started doing business date
        /// </summary>
        [JsonProperty("startedDoingBusiness")]
        public DateTime StartedDoingBusiness { get; set; }

        /// <summary>
        /// Finished doing business date
        /// </summary>
        [JsonProperty("finishedDoingBusiness")]
        public DateTime? FinishedDoingBusiness { get; set; }

        /// <summary>
        /// Business formation date
        /// </summary>
        [JsonProperty("businessFormationDate")]
        public string BusinessFormationDate { get; set; } = string.Empty;

        /// <summary>
        /// Turnover per annum
        /// </summary>
        [JsonProperty("turnoverPerAnnum")]
        public string TurnoverPerAnnum { get; set; } = string.Empty;

        /// <summary>
        /// Service supplier
        /// </summary>
        [JsonProperty("serviceSupplier")]
        public bool ServiceSupplier { get; set; }

        /// <summary>
        /// Company registration
        /// </summary>
        [JsonProperty("companyRegistration")]
        public string CompanyRegistration { get; set; } = string.Empty;

        /// <summary>
        /// After hours phone
        /// </summary>
        [JsonProperty("afterHoursPhone")]
        public string AfterHoursPhone { get; set; } = string.Empty;

        /// <summary>
        /// After hours extension
        /// </summary>
        [JsonProperty("afterHoursExtension")]
        public string AfterHoursExtension { get; set; } = string.Empty;

        /// <summary>
        /// User changes allowed
        /// </summary>
        [JsonProperty("userChangesAllowed")]
        public bool UserChangesAllowed { get; set; }

        /// <summary>
        /// Email
        /// </summary>
        [JsonProperty("email")]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// Phone number
        /// </summary>
        [JsonProperty("phoneNumber")]
        public string PhoneNumber { get; set; } = string.Empty;

        /// <summary>
        /// Address
        /// </summary>
        [JsonProperty("address")]
        public string Address { get; set; } = string.Empty;

        /// <summary>
        /// Status
        /// </summary>
        [JsonProperty("status")]
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Created date
        /// </summary>
        [JsonProperty("created")]
        public DateTime Created { get; set; }

        /// <summary>
        /// Modified date
        /// </summary>
        [JsonProperty("modified")]
        public DateTime Modified { get; set; }

        /// <summary>
        /// Last updated date
        /// </summary>
        [JsonProperty("lastUpdated")]
        public DateTime LastUpdated { get; set; }

        /// <summary>
        /// Has sub partner
        /// </summary>
        [JsonProperty("hasSubPartner")]
        public bool HasSubPartner { get; set; }

        /// <summary>
        /// Has linked company
        /// </summary>
        [JsonProperty("hasLinkedCompany")]
        public bool HasLinkedCompany { get; set; }

        /// <summary>
        /// Parent ID
        /// </summary>
        [JsonProperty("parentId")]
        public int? ParentId { get; set; }

        /// <summary>
        /// Is active
        /// </summary>
        [JsonProperty("isActive")]
        public int IsActive { get; set; }
    }
}
