import { Button, Card, Form, Input, Tabs, Image, Alert } from 'antd';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import './index.scss';
import LanguageDropdown from '../../components/shared/language';
import showNotification from '../../components/shared/notification';
import {
  LOCAL_STORAGE_KEY,
  VALIDATION_MESSAGE_CONFIG,
} from '../../constants/app-constants';
import { useNavigate } from 'react-router-dom';
import { ROUTE_PATHS } from '../../constants/router.constants';
import { AuthApi } from '../../apis/auth.api';
import { CompanyAccessApi } from '../../apis/company-access.api';
import LocalUtils from '../../utils/local.utils';
import { getThemeAsset } from '../../utils/assets';
import FontIcon from '../../components/shared/icons/font-icon';

function SignInPage() {
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const { t } = useTranslation();
  const navigator = useNavigate();
  const [errorMessage, setErrorMessage] = useState('');

  const items = [
    {
      key: '1',
      label: (
        <span>
          <FontIcon size={16} className="icon-tabs icon-email" />
          <span className="body-2-regular tab-text-color">
            {t('tabs.email')}
          </span>
        </span>
      ),
      children: (
        <Form
          validateMessages={VALIDATION_MESSAGE_CONFIG}
          onFinish={SignIn}
          form={form}
          className="hiden-required-mark login-form"
          layout="vertical"
        >
          {errorMessage && (
            <>
              <div className="alert-animation-wrapper">
                <Alert
                  message={errorMessage}
                  type="error"
                  showIcon
                  closable
                  className="alert-animation"
                />
              </div>
            </>
          )}
          <Form.Item
            label={t('login.email')}
            name="email"
            rules={[
              {
                required: true,
                message: t('login.emailRequired'),
                type: 'email',
              },
            ]}
          >
            <Input placeholder={t('login.emailPlaceholder')} />
          </Form.Item>
          <Form.Item
            label={t('login.password')}
            name="password"
            rules={[{ required: true, message: t('login.passwordRequired') }]}
          >
            <Input.Password placeholder={t('placeholders.password')} />
          </Form.Item>
          <Form.Item className="submit-button">
            <Button type="primary" htmlType="submit" block loading={loading}>
              {t('login.loginButton')}
            </Button>
          </Form.Item>
          <div className="dont-have-account body-2-regular text-center">
            <span style={{ color: '#202224', opacity: 0.65 }}>
              {t('login.dontHaveAccount')}
            </span>
            <a
              href="/create-account"
              style={{ color: '#202224', textDecoration: 'underline' }}
            >
              {t('login.createAccount')}
            </a>
          </div>

          <LanguageDropdown className="mr-top-24" />
        </Form>
      ),
    },
    {
      key: '2',
      label: (
        <span>
          <FontIcon size={16} className="icon-tabs icon-phone" />
          <span className="body-2-regular tab-text-color">
            {t('tabs.cellNumber')}
          </span>
        </span>
      ),
    },
  ];

  async function SignIn() {
    setLoading(true);
    setErrorMessage('');

    try {
      const value = form.getFieldsValue();
      const result = await AuthApi.signIn(value.email, value.password);

      if (result.status === 200) {
        LocalUtils.set(LOCAL_STORAGE_KEY.ACCESS_TOKEN, result.data.token);

        if (result.data.isAdmin) {
          const sendOtpResp = await AuthApi.sendOtp();
          if (sendOtpResp.status === 200) {
            showNotification(
              'success',
              t('login.sendOtpSuccessTitle'),
              t('login.sendOtpSuccess'),
            );

            navigator(ROUTE_PATHS.ConfirmCode, {
              state: { isAdmin: result.data.isAdmin },
            });
          }
        } else {
          handleNavigator(result.data.isAdmin);
        }
      }
    } catch (error: any) {
      setErrorMessage(error.response.data.message);
    } finally {
      setLoading(false);
    }
  }

  const checkAccess = async (
    companyId: number,
    accessStatus: number,
    isPartnerAccess: boolean,
  ) => {
    try {
      const checkAccess = await CompanyAccessApi.accessToCompany(
        companyId,
        accessStatus,
        isPartnerAccess,
      );
      if (checkAccess.status === 200) {
        return true;
      }
    } catch (error) {
      console.error('Error accessing company:', error);
      return false;
    }
  };

  const handleNavigator = async (isAdmin: boolean) => {
    try {
      const companyAccessList = await CompanyAccessApi.getCompanyAccessList();
      if (companyAccessList.data && companyAccessList.data.length > 1) {
        navigator(ROUTE_PATHS.CompanyAccess);
      } else if (companyAccessList.data.length === 1) {
        const checkAccessResp = await checkAccess(
          companyAccessList.data[0].id,
          companyAccessList.data[0].accessStatusId,
          companyAccessList.data[0].isPartnerAccess,
        );
        if (checkAccessResp) {
          const sendOtpResp = await AuthApi.sendOtp();
          if (sendOtpResp.status === 200) {
            showNotification(
              'success',
              t('login.sendOtpSuccess') || 'OTP sent successfully!',
            );
            navigator(ROUTE_PATHS.ConfirmCode, {
              state: {
                isAdmin,
                selectedCompany: companyAccessList.data[0].id,
                accessStatus: companyAccessList.data[0].accessStatusId,
                isPartnerAccess: companyAccessList.data[0].isPartnerAccess,
              },
            });
          }
        } else {
          showNotification(
            'warning',
            'You have no company to access! Please contact Administrator to get the access permission!',
          );
        }
      }
    } catch (error) {}
  };

  return (
    <div className="sign-in-page">
      <Card className="card-sign-in">
        <div className="header-sign-in">
          <div className="logo-image">
            <Image
              width={160}
              preview={false}
              src={getThemeAsset('images', 'logo-auth.svg')}
            />
          </div>
          <h2 className="h2-bold mr-bottom-8">{t('login.loginToAccount')}</h2>
          <p className="body-2-regular opacity-80">
            {t('login.selectCompany')}
          </p>
        </div>

        <div className="tab-container">
          <Tabs defaultActiveKey="1" items={items} centered />
        </div>
      </Card>
    </div>
  );
}

export default SignInPage;
