@using MMS.Web
@using MMS.Web.Models
@using MMS.Web.Utils
@model MMS.Web.Models.TerminalModel
<head>
    <style>
@Html.Raw(@ViewBag.Style)
    </style>
</head>

<div class="@CssConstants.Form1ColTerminal @CssConstants.Middle @CssConstants.Color">

    <input type="hidden" value="@Model.Id" name="model.TerminalId" />

    @await Html.PartialAsync(PartialViewConstants.TerminalHeaderPartialView, new TerminalHeaderModel()
    {
        Label = @_helper.GetLanguageText("Global Setup")
    })

    <div id="loadingGroup" class="@CssConstants.FormDetails @CssConstants.BorderForm @CssConstants.Color @CssConstants.TerminalContent">

        <div class="row">
            <mms-button-gui asp-action="HotInterfaces" asp-controller="TerminalSetupGUI" asp-route-terminalMasterId="@Model.Id">Host Interfaces</mms-button-gui>
        </div>

        <div class="row">
            <mms-button-gui asp-action="ListGlobalHostInterfaces" asp-controller="TerminalSetupGUI" asp-route-terminalMasterId="@Model.Id">Select Host Interfaces</mms-button-gui>
        </div>

        <div class="row">
            <mms-button-gui asp-action="ECRInterface" asp-controller="TerminalSetupGUI">ECR Interfaces
            </mms-button-gui>
        </div>

        <div class="row">
            <mms-button-gui asp-action="ECRRequests" asp-controller="TerminalSetupGUI">ECR Requests
            </mms-button-gui>
        </div>

        <div class="row">
            <mms-button-gui asp-action="CountrySetup" asp-controller="TerminalSetupGUI">Country
            </mms-button-gui>
        </div>

        <div class="row">
            <mms-button-gui asp-action="DateTimeZone" asp-controller="TerminalSetupGUI" asp-route-id="@Model.Id">Date Timezone
            </mms-button-gui>
        </div>

        <div class="row">
            <mms-button-gui asp-action="LanguageSupport" asp-controller="TerminalSetupGUI">Language Used
            </mms-button-gui>
        </div>

        <div class="row">
            <mms-button-gui asp-action="IconImages" asp-controller="TerminalSetupGUI">Icon Images
            </mms-button-gui>
        </div>


        <div class="row">
            <mms-button-gui asp-action="StatusBar" asp-controller="TerminalSetupGUI">Status Bar
            </mms-button-gui>
        </div>

        <div class="row">
            <mms-button-gui>Health Care
            </mms-button-gui>
        </div>

        <div class="row">
            <mms-button-gui asp-action="TerminalSecurityAccess" asp-controller="TerminalSetupGUI">Security Terminal Access
            </mms-button-gui>
        </div>

        <div class="row">
            <mms-button-gui asp-action="UserTerminals" asp-controller="TerminalSetupGUI" asp-route-id="@Model.Id">Terminal Users
            </mms-button-gui>
        </div>

        <div class="row">
            <mms-button-gui asp-action="FallBack" asp-controller="TerminalSetupGUI">Fall Back
            </mms-button-gui>
        </div>

        <div class="row">
            <mms-button-gui asp-action="CharitySetup" asp-controller="TerminalSetupGUI" asp-route-id="@Model.Id">Charity Setup
            </mms-button-gui>
        </div>

        <div class="@CssConstants.ClearBoth">
        </div>

    </div>

    @await Html.PartialAsync(PartialViewConstants.TerminalFooterPartialView, new TerminalFooterModel()
    {
    })

</div>
