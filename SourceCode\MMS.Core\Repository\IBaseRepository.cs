﻿using MMS.Core.dbContext;
using MMS.Core.Entities;
using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace MMS.Core.Repository
{
    /// <summary>
    ///
    /// </summary>
    /// <typeparam name="T"></typeparam>
    public interface IBaseRepository<T>
        where T : BaseEntity
    {
        void UsingSessionContext();
        MMSContext GetContext();
        void CtxDisposeOrNot(MMSContext context);
        IList<T> GetAll(bool includeDeleted = false, bool includeDeactivated = true, Expression<Func<T, bool>> whereClause = null, params Expression<Func<T, object>>[] includes);

        /// <summary>
        ///  Retrieves all entities, prioritizing ChangeTracker entries if available.
        /// </summary>
        /// <returns></returns>
        IList<T> GetAllV2(bool includeDeleted = false, bool includeDeactivated = true, Expression<Func<T, bool>> whereClause = null, params Expression<Func<T, object>>[] includes);

        T FirstOrDefault(Expression<Func<T, bool>> whereClauses = null, bool includeDeleted = false, params Expression<Func<T, object>>[] includes);
        T LastOrDefault(Expression<Func<T, bool>> whereClauses = null, bool includeDeleted = false, params Expression<Func<T, object>>[] includes);
        T GetById(int id ,params Expression<Func<T, object>>[] includes);
        void Delete(T entity, bool isDeleteFromData = false);
        bool Delete(int id);
        bool Delete(int id, bool isDeleteFromData = false);

        /// <summary>
        /// Add Or Update
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="isCommit"></param>
        void AddOrUpdate(T entity, bool isCommit = true);

        /// <summary>
        /// Add Or Update the list of entities
        /// </summary>
        /// <param name="entities"></param>
        /// <param name="isCommit"></param>
        void AddOrUpdateAll(IEnumerable<T> entities, bool isCommit = true);
        T Insert(T entity, bool commit = true);
        void Insert(IEnumerable<T> entities, bool commit = true);
        T Update(T entity, bool commit = true);
        /// <summary>
        /// Update the list of entities
        /// </summary>
        /// <param name="entities">The IEnumerable</param>
        void Update(IEnumerable<T> entities, bool commit = true);
        T GetValueByLang(T entity, IList<Dictionary> dics);
        bool SetIsUpdated(string table, List<int> ids, bool isUpdated);
        bool SetTemplateForTerminal(int id, List<int> terminalIds, string templateColumn);
        List<TU> RawSqlQuery<TU>(string query, Func<DbDataReader, TU> map);
        T SingleOrDefault(Expression<Func<T, bool>> whereClauses = null, bool includeDeleted = false, params Expression<Func<T, object>>[] includes);
        IList<object> Select(Expression<Func<T, object>> selectClause = null, Expression<Func<T, bool>> whereClauses = null, bool includeDeleted = false, params Expression<Func<T, object>>[] includes);
        void UndoAll();
        void SaveChanges();
        Task<int> SaveChangesAsync();
        bool IsExisted(int id);
        IQueryable<T> GetPage(IQueryable<T> query, int pageSize, int pageIndex, Expression<Func<T, object>> sortExpression = null);
    }
}
