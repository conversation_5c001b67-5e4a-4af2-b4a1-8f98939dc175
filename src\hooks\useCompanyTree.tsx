import { useEffect } from 'react';
import { useRecoilState } from 'recoil';
import {
  showCompanyTreeState,
  companyTreeDataState,
} from '../states/companyTree';
import { useLocation } from 'react-router-dom';

function useCompanyTree(isIncludeGroup: boolean = true) {
  const [showCompanyTree, setShowCompanyTree] =
    useRecoilState(showCompanyTreeState);
  const [companyTreeData, setCompanyTreeData] =
    useRecoilState(companyTreeDataState);
  const { selectedItems, lastSelectedItem, parentItem } = companyTreeData;
  const location = useLocation();

  const toggleCompanyTree = () => {
    setShowCompanyTree(!showCompanyTree);
  };

  // Update isIncludeGroup when the prop changes or route changes
  useEffect(() => {
    setCompanyTreeData((prev) => ({
      ...prev,
      isIncludeGroup: isIncludeGroup,
    }));
  }, [isIncludeGroup, setCompanyTreeData, location.pathname]);

  // Handle show/hide company tree
  useEffect(() => {
    setShowCompanyTree(true);
    return () => {
      setShowCompanyTree(undefined);
    };
  }, [setShowCompanyTree]);

  return {
    selectedItems,
    lastSelectedItem,
    parentItem,
    showCompanyTree,
    toggleCompanyTree,
    isIncludeGroup: companyTreeData.isIncludeGroup,
  };
}
export default useCompanyTree;
