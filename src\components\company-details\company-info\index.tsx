import { Col, Input, Row, UploadFile } from 'antd';
import Form from 'antd/es/form';
import { CompanyDetailsResponse } from '../../../models/company.model';
import { useEffect } from 'react';
import '../index.scss';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';
import DefaultCompanyImage from '../../../assets/images/default-company.svg';
import UploadImage from '../../shared/upload-image';
import { UploadType } from '../../../constants/app.enums';
import { useTranslation } from 'react-i18next';

const CompanyDetailsContent = ({
  company,
  isEdit,
  form,
}: {
  company: CompanyDetailsResponse | undefined;
  isEdit: boolean;
  form: any;
}) => {
  const baseUrl = import.meta.env.VITE_MMS_API_URL;
  const { t } = useTranslation();

  useEffect(() => {
    if (company) {
      form.setFieldsValue(company);
    }
  }, [company, form]);

  const defaultFile = [
    {
      url: company?.profileImage
        ? `${baseUrl}/${company?.profileImage}`
        : DefaultCompanyImage,
      uid: '1',
      name: 'image.png',
      status: 'done',
    },
  ];

  return (
    <div className="company-details-page">
      <div className="content-wrapper">
        <div className="company-image-section">
          <div className="body-2-regular">{t('companyManagement.logo')}</div>
          {company && (
            <UploadImage
              value={defaultFile as UploadFile[]}
              isDisabled={true}
              uploadType={UploadType.Company}
            />
          )}
        </div>

        <div className="company-info-section">
          <Form
            form={form}
            layout="vertical"
            disabled={!isEdit}
            initialValues={company}
          >
            <Row gutter={[24, 16]}>
              <Col span={24} md={8}>
                <Form.Item
                  name="id"
                  label="MMS System ID"
                  className="body-2-regular form-item"
                >
                  <Input disabled={true} />
                </Form.Item>
              </Col>
              <Col span={24} md={8}>
                <Form.Item
                  name="linklyId"
                  label="Linkly System ID"
                  className="body-2-regular form-item"
                >
                  <Input disabled={true} />
                </Form.Item>
              </Col>
              <Col span={24} md={8}>
                <Form.Item
                  name="companyIdNumber"
                  label="Company ID Number"
                  className="body-2-regular form-item"
                >
                  <Input />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={[24, 16]}>
              <Col span={24} md={8}>
                <Form.Item
                  name="companyRegisteredName"
                  label="Company Registered Name"
                  className="body-2-regular form-item"
                >
                  <Input />
                </Form.Item>
              </Col>
              <Col span={24} md={8}>
                <Form.Item
                  name="companyTradingName"
                  label="Company Trading Name"
                  className="body-2-regular form-item"
                >
                  <Input />
                </Form.Item>
              </Col>
              <Col span={24} md={8}>
                <Form.Item
                  name="email"
                  label="Email Address"
                  className="body-2-regular form-item"
                  rules={[
                    {
                      type: 'email',
                      message: 'The input is not valid E-mail!',
                    },
                  ]}
                >
                  <Input />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={[24, 16]}>
              <Col span={24} md={8}>
                <Form.Item
                  name="phoneNumber"
                  label="Phone Number"
                  className="body-2-regular form-item"
                >
                  <PhoneInput
                    containerClass={isEdit ? '' : 'dropdown-no-border'}
                    disabled={!isEdit}
                    country={'us'}
                    value={company?.phoneNumber}
                  />
                </Form.Item>
              </Col>
              <Col span={24} md={8}>
                <Form.Item
                  name="address"
                  label="HQ Address"
                  className="body-2-regular form-item"
                >
                  <Input />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default CompanyDetailsContent;
