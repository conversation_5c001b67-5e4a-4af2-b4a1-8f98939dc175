﻿<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
	</PropertyGroup>
	<ItemGroup>
		<PackageReference Include="AutoMapper" Version="13.0.1" />
		<PackageReference Include="BCrypt.Net-Core" Version="1.6.0" />
		<PackageReference Include="CompareNETObjects" Version="4.83.0" />
		<PackageReference Include="ES.Net.Module" Version="1.0.6" />
		<PackageReference Include="Hangfire.AspNetCore" Version="1.8.14" />
		<PackageReference Include="Hangfire.Mongo" Version="1.10.9" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.10" />
		<PackageReference Include="Microsoft.AspNetCore.Http" Version="2.2.2" />
		<PackageReference Include="Microsoft.AspNetCore.SignalR.Core" Version="1.0.15" />
		<PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.3" />
		<PackageReference Include="MySql.Data" Version="9.0.0" />
		<PackageReference Include="MySql.Data.EntityFrameworkCore" Version="8.0.22" />
		<PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="8.0.2" />
		<PackageReference Include="protobuf-net" Version="3.2.30" />
		<PackageReference Include="SendGrid" Version="9.29.2" />
		<PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
		<PackageReference Include="Toolbelt.EntityFrameworkCore.IndexAttribute" Version="5.0.1.1" />
		<PackageReference Include="Microsoft.AspNetCore.SignalR.Common" Version="8.0.8" />
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.8" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.8" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.8" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.8" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="System.Runtime.Caching" Version="9.0.3" />
	</ItemGroup>
	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning" Version="5.1.0" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning.ApiExplorer" Version="5.1.0" />
		<PackageReference Include="Microsoft.Identity.Client" Version="4.62.0" />
		<PackageReference Include="MimeKit" Version="4.7.1" />
		<PackageReference Include="NetCore.AutoRegisterDi" Version="2.2.1" />
		<PackageReference Include="Serilog" Version="4.2.0" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.0" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.3" />
	</ItemGroup>
	<ItemGroup>
		<Folder Include="Entities\EntityInterface\" />
		<Folder Include="Exceptions\" />
		<Folder Include="Services\Users\impl\" />
	</ItemGroup>
	<ItemGroup>
		<ProjectReference Include="..\MMS.Infrastructure\MMS.Infrastructure.csproj" />
		<ProjectReference Include="..\MMS.Model\MMS.Model.csproj" />
		<ProjectReference Include="..\MMS.Paxstore\MMS.Paxstore.csproj" />
	</ItemGroup>
	<PropertyGroup>
		<GenerateAssemblyInfo>false</GenerateAssemblyInfo>
	</PropertyGroup>

	<!-- Conditionally include migrations based on build configuration -->
	<!-- Only exclude Designer files from migrations to improve build performance while keeping migration functionality -->
	<ItemGroup Condition="'$(IncludeMigrations)' != 'true'">
		<Compile Remove="Migrations\**\*.Designer.cs" />
	</ItemGroup>
</Project>