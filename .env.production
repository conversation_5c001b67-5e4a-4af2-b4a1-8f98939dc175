MODE=production

# Terminal source
VITE_TERMINAL_SOURCE=linkly

# API configuration - direct URL to API
# For local development with Docker
# VITE_MMS_API_URL=http://localhost:8003
# For production
VITE_MMS_API_URL=/api

# MMS.Web MVC application URL - direct URL to MVC
# For local development with Docker
# VITE_MMS_WEB_URL=http://localhost:8002
# For production
VITE_MMS_WEB_URL=https://mms-mvc.projectjumpstart.site

# Terminal Setup Authentication
VITE_TERMINAL_SETUP_AUTH_COOKIE_NAME=X-API-KEY
VITE_TERMINAL_SETUP_AUTH_COOKIE_VALUE=CB-API-KEY-12345
VITE_TERMINAL_SETUP_AUTH_COOKIE_PATH=/

# Theme
# Available options:
# - bright-blue
# - forest-green
VITE_APP_THEME=forest-green

# Docker configuration
MMS_FE_VERSION=1.12.0