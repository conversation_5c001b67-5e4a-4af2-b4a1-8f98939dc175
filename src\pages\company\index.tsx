import {
  Table,
  Avatar,
  Button,
  Space,
  Popconfirm,
  message,
  Tag,
  Input,
  Select,
} from 'antd';
import { CompanyItemResponse } from '../../models/company.model';
import './index.scss';
import { useEffect, useState } from 'react';
import { useBreadcrumb } from '../../hooks/useBreadcrumb';
import FontIcon from '../../components/shared/icons/font-icon';
import PaginationComponent from '../../components/shared/pagination';
import { ROUTE_PATHS } from '../../constants/router.constants';
import { useNavigate, useLocation, useParams } from 'react-router-dom';
import showNotification from '../../components/shared/notification';
import { useTranslation } from 'react-i18next';
import { CompanyApi } from '../../apis/company.api';
import DefaultCompanyImage from '../../assets/images/default-company.svg';

const CompanyPage = () => {
  const baseUrl = import.meta.env.VITE_MMS_API_URL;
  const TERMINAL_SOURCE = import.meta.env.VITE_TERMINAL_SOURCE;
  const location = useLocation();
  const navigate = useNavigate();
  const { t } = useTranslation();

  const getPageFromUrl = (): number => {
    const { page } = useParams<{ page: string }>();
    return page ? parseInt(page, 10) : 1;
  };

  const columns = [
    {
      title: 'Company Name',
      dataIndex: 'name',
      key: 'name',
      width: TERMINAL_SOURCE === 'linkly' ? '35%' : '25%',
      render: (text: string, record: CompanyItemResponse) => (
        <Space>
          <Avatar
            size={44}
            src={
              record.profileImage
                ? `${baseUrl}/${record.profileImage}`
                : DefaultCompanyImage
            }
          />
          <span className="body-1-bold">{text}</span>
        </Space>
      ),
    },
    {
      title: 'Phone Number',
      dataIndex: 'phoneNumber',
      key: 'phoneNumber',
      width: TERMINAL_SOURCE === 'linkly' ? '30%' : '20%',
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      width: TERMINAL_SOURCE === 'linkly' ? '35%' : '25%',
    },
    // Conditionally add Status column only if TERMINAL_SOURCE is not 'linkly'
    ...(TERMINAL_SOURCE !== 'linkly'
      ? [
          {
            title: 'Status',
            dataIndex: 'status',
            key: 'status',
            width: '20%',
            render: (_: string, record: CompanyItemResponse) => (
              <div className="status-tag">
                <Tag color={record.isActive ? '#87d068' : '#f50'}>
                  {record.isActive ? 'Active' : 'Inactive'}
                </Tag>
              </div>
            ),
          },
        ]
      : []),
    ...(TERMINAL_SOURCE !== 'linkly'
      ? [
          {
            title: <div style={{ textAlign: 'left' }}>Action</div>,
            key: 'action',
            align: 'right' as const,
            width: '10%',
            render: (_: any, record: CompanyItemResponse) => (
              <Space size="middle">
                <Popconfirm
                  title="Activate/Deactivate the company"
                  description={
                    record.isActive
                      ? 'Are you sure to deactivate this company?'
                      : 'Are you sure to activate this company?'
                  }
                  onConfirm={() =>
                    handleActivateDeactivateCompany(record.id, !record.isActive)
                  }
                  onCancel={() => {}}
                  okText="Yes"
                  cancelText="No"
                >
                  <Button
                    type="text"
                    icon={<FontIcon size={16} className="icon-clock" />}
                    className="action-btn btn-lock"
                  />
                </Popconfirm>

                <Button
                  type="text"
                  icon={<FontIcon size={16} className="icon-edit-1" />}
                  className="action-btn btn-edit"
                  onClick={() => handleViewCompanyDetails(record.id)}
                />
                <Popconfirm
                  title="Delete the company"
                  description="Are you sure to delete this company?"
                  onConfirm={() => handleDeleteCompany(record.id)}
                  onCancel={() => {}}
                  okText="Yes"
                  cancelText="No"
                >
                  <Button
                    type="text"
                    icon={<FontIcon size={16} className="icon-_close" />}
                    className="action-btn btn-delete"
                  />
                </Popconfirm>
              </Space>
            ),
          },
        ]
      : []),
  ];

  const { updateBreadcrumb } = useBreadcrumb();

  const defaultPageSize = 10;
  const [pageNumber, setPageNumber] = useState(getPageFromUrl());
  const [pageSize, setPageSize] = useState(defaultPageSize);
  const [totalRecord, setTotalRecord] = useState(50);
  const [companies, setCompanies] = useState<CompanyItemResponse[]>([]);
  const [activateState, setActivateState] = useState(false);
  const [searchKey, setSearchKey] = useState<string>('');
  const [inputValue, setInputValue] = useState<string>('');

  useEffect(() => {
    updateBreadcrumb(
      {
        title: 'Company Management',
        path: ROUTE_PATHS.MerchantLocations,
      },
      true,
    );
  }, [t]);

  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);

    if (pageNumber === 1) {
      queryParams.delete('page');
    } else {
      queryParams.set('page', pageNumber.toString());
    }

    const newSearch = queryParams.toString();
    const newPath = `${location.pathname}${newSearch ? `?${newSearch}` : ''}`;

    if (location.search !== (newSearch ? `?${newSearch}` : '')) {
      navigate(newPath, { replace: true });
    }
  }, [pageNumber, location.pathname, location.search, navigate]);

  const handleViewCompanyDetails = (id: number) => {
    navigate(`${ROUTE_PATHS.Companies}/${id}`);
  };

  const handleActivateDeactivateCompany = async (
    id: number,
    isActive: boolean,
  ) => {
    try {
      await CompanyApi.activateDeactivateCompany(id, isActive);
      showNotification(
        'success',
        isActive
          ? 'Activate company successfully'
          : 'Deactivate company successfully',
      );
      setActivateState(!activateState);
    } catch (error) {
      console.log(error);
    }
  };

  const handleDeleteCompany = async (id: number) => {
    try {
      await CompanyApi.deleteCompany(id);
      message.success('Delete company successfully');
      const { data } = await CompanyApi.getCompanyList(pageNumber, pageSize);
      if (data && data.data.length === 0 && pageNumber > 1) {
        setPageNumber(pageNumber - 1);
      } else {
        getCompanies();
      }
    } catch (error) {
      console.log(error);
    }
  };

  const getCompanies = async () => {
    try {
      const { data } = await CompanyApi.getCompanyList(
        pageNumber,
        pageSize,
        searchKey,
      );
      if (data) {
        setCompanies(data.data);
        setTotalRecord(data.totalRecords);
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getCompanies();
  }, [pageNumber, pageSize, activateState, searchKey]);

  const calculateTotalPages = () => {
    return Math.ceil(totalRecord / pageSize);
  };

  const handleSearch = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      setSearchKey(inputValue);
    }
  };

  return (
    <div className="company-container">
      <div className="company-header">
        <span className="body-2-bold">
          {t('companyManagement.companyList')}
        </span>
        <Space
          className="company-header-action-container"
          direction="horizontal"
        >
          <Space.Compact>
            <Input
              className="company-header-input"
              placeholder={t('common.searchByName')}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleSearch}
              value={inputValue}
            />
          </Space.Compact>
          {TERMINAL_SOURCE !== 'linkly' && (
            <Button
              type="primary"
              icon={<FontIcon size={16} className="icon-plus" />}
              onClick={() => {
                navigate(ROUTE_PATHS.Companies + '/0');
              }}
              className="add-btn"
            >
              {t('companyManagement.addCompany')}
            </Button>
          )}
        </Space>
      </div>
      <Table
        dataSource={companies}
        columns={columns}
        rowKey="id"
        pagination={false}
        onRow={(record: CompanyItemResponse) => ({
          onClick: () => handleViewCompanyDetails(record.id),
          style: { cursor: 'pointer' },
        })}
        rowClassName={(_, index) =>
          index % 2 === 0 ? 'table-row-light' : 'table-row-dark'
        }
        className="company-table"
      />

      <div className="company-pagination">
        {companies && (
          <PaginationComponent
            totalRecords={totalRecord}
            pageSize={pageSize}
            setPageSize={(newPageSize) => {
              const totalPages = calculateTotalPages();
              if (pageNumber > Math.ceil(totalRecord / newPageSize)) {
                setPageNumber(1);
              }
              setPageSize(newPageSize);
            }}
            pageNumber={pageNumber}
            setPageNumber={setPageNumber}
          />
        )}
      </div>
    </div>
  );
};

export default CompanyPage;
