﻿using MMS.Core.CoreUTI.Enum;
using MMS.Core.Entities.Locations;
using MMS.Model.ApiModelRequest;
using MMS.Model.ApiModelResponse;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace MMS.Core.Repository
{
    public interface ILocationItemRepository : IBaseRepository<LocationItem>
    {
        /// <summary>
        /// Get location item global for select
        /// </summary>
        /// <param name="parentId"></param>
        /// <returns></returns>
        Task<IList<int>> GetLocationAreaIdByParentIdAsync(int parentId);

        /// <summary>
        /// Get location item include location and level
        /// </summary>
        /// <param name="locationItemId"></param>
        /// <returns></returns>
        Task<LocationItem> GetLocationItemIncludeLevelAsync(int locationItemId);

        /// <summary>
        /// Get all Location Area id
        /// </summary>
        /// <returns></returns>
        Task<IList<int>> GetAllLocationAreaIdsAsync();

        /// <summary>
        /// Get Location Area id by string hierarchy id
        /// </summary>
        /// <param name="locationIds"></param>
        /// <returns></returns>
        Task<IList<int>> GetLocationAreaIdByStringHierarchy(string locationIds);
        
        Task<IList<LocationItem>> GetLocationItemSource(int parentId, LocationLevel locationLevel);

        Task<Dictionary<int, List<int>>> GetLocationAreaIdsByParentIdsAsync(List<int> parentIds);

        /// <summary>
        /// Get Location Area Ids for all Regions
        /// </summary>
        /// <param name="locationItemRegionIds"></param>
        /// <returns></returns>
        Task<List<int>> GetLocationAreaIdsForAllRegionIdsAsync(List<int> locationItemRegionIds);
        Task<PagingResponse<DataItemResponse>> GetSourceForSelectLocationItemAsync(int parentLocationId, LocationLevel locationLevel, List<int> selectedIds, SelectRemoveFormParameter pagingParameter);
    }
}
