import React, { useState } from 'react';
import { Upload, Image } from 'antd';
import type { GetProp, UploadFile, UploadProps } from 'antd';
import ImgCrop from 'antd-img-crop';
import './index.scss';
import { UploadType } from '../../../constants/app.enums';
import { FileManagementApi } from '../../../apis/file-management';

type FileType = Parameters<GetProp<UploadProps, 'beforeUpload'>>[0];

interface UploadImageProps {
  uploadType: UploadType;
  value?: UploadFile[];
  onChange?: (fileList: UploadFile[]) => void;
  onRemove?: (file: UploadFile) => void;
  listType?: 'text' | 'picture' | 'picture-card';
  showPreview?: boolean;
  cropProps?: {
    rotationSlider?: boolean;
  };
  className?: string;
  isDisabled?: boolean;
}

const getBase64 = (file: FileType): Promise<string> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
  });

const UploadImage: React.FC<UploadImageProps> = ({
  value,
  onChange,
  onRemove,
  listType = 'picture-card',
  showPreview = true,
  cropProps = { rotationSlider: true },
  className = 'default',
  isDisabled = false,
  uploadType = UploadType.Person,
}) => {
  const [fileList, setFileList] = useState<UploadFile[]>(value || []);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');

  const handleChange: UploadProps['onChange'] = ({ fileList: newFileList }) => {
    const latestFile = newFileList.slice(-1);
    setFileList(latestFile);
    onChange?.(latestFile);
  };

  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as FileType);
    }

    setPreviewImage(file.url || (file.preview as string));
    setPreviewOpen(true);
  };

  const handleRemove = (file: UploadFile) => {
    onRemove?.(file);
    setFileList([]);
    onChange?.([]);
  };

  const uploadImage = async (options: any) => {
    const { onSuccess, onError, file } = options;
    try {
      const res = await FileManagementApi.uploadFile(file, uploadType);
      onSuccess(res.data);
    } catch (err) {
      onError({ err });
    }
  };

  return (
    <div className={`upload-image-container ${className}`}>
      <ImgCrop {...cropProps}>
        <Upload
          disabled={isDisabled}
          accept="image/*"
          customRequest={uploadImage}
          listType={listType}
          fileList={fileList}
          multiple={false}
          onChange={handleChange}
          onPreview={handlePreview}
          maxCount={1}
          onRemove={handleRemove}
        >
          {fileList.length === 0 ? '+ Upload' : null}
        </Upload>
      </ImgCrop>
      {previewImage && showPreview && (
        <Image
          wrapperStyle={{ display: 'none' }}
          preview={{
            visible: previewOpen,
            onVisibleChange: (visible) => setPreviewOpen(visible),
            afterOpenChange: (visible) => !visible && setPreviewImage(''),
          }}
          src={previewImage}
        />
      )}
    </div>
  );
};

export default UploadImage;
