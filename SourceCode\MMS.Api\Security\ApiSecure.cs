using MMS.Core.CoreUTI.Enum;

namespace MMS.Api.Security
{
    /// <summary>
    /// API-specific permission constants
    /// </summary>
    public static class ApiSecure
    {
        // Action constants (same values as Secure class)
        public const int ViewAction = (int)Actions.ViewAction;
        public const int AddAction = (int)Actions.AddAction;
        public const int EditAction = (int)Actions.EditAction;
        public const int ActiveAction = (int)Actions.ActiveAction;
        public const int RemoveAction = (int)Actions.RemoveAction;
        public const int SelectRemoveAction = (int)Actions.SelectRemoveAction;

        public const Actions FullAction = Actions.ViewAction | Actions.AddAction | Actions.EditAction |
                                         Actions.ActiveAction | Actions.SelectRemoveAction | Actions.RemoveAction;

        // New API permission function constants based on requirements
        public const int ApiPersonManagement = 10006;
        public const int ApiCompanyDetails = 10007;
        public const int ApiCompanyGroupMapping = 10008;
        public const int ApiDeviceManagement = 10009;
        public const int ApiLocationManagement = 10010;
        public const int ApiBaseTemplateManagement = 10011;
        public const int ApiMerchantManagement = 10012;
    }
}
