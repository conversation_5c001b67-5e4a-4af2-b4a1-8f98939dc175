using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MMS.Api.ApiModels;
using MMS.Api.Utils;
using MMS.Core.CoreUTI;
using MMS.Core.CoreUTI.Enum;
using MMS.Core.Entities;
using MMS.Core.Services;
using MMS.Core.Services.Companies;
using MMS.Core.Services.Locations;
using MMS.Core.Services.SyncTerminalSetup;
using MMS.Model.ApiModelRequest;
using MMS.Model.ApiModelResponse;
using MMS.Model.ApiModelResponse.ApplicationsSetup;
using Newtonsoft.Json.Linq;
using Serilog;
using System.Net;
using System.Net.Http.Headers;
using System.Xml.Linq;

namespace MMS.Api.Controllers
{
    [Authorize]
    [ApiController]
    [Route("app-template")]
    public class AppTemplateController : ControllerBase
    {
        private readonly ITerminalService _terminalMasterService;
        private readonly ILocationItemService _locationItemService;
        private readonly IDeviceSetupUpgradeService _deviceSetupUpgradeService;
        private readonly IRegisteredAppService _registeredAppService;
        private readonly ISyncTerminalSetupService _syncTerminalSetupService;
        private readonly IApplicationDeviceMappingService _applicationDeviceMappingService;
        private readonly IDeviceTemplatesService _deviceTemplatesService;
        private readonly IMerchantTerminalService _merchantTerminalService;
        private readonly ITerminalService _terminalService;
        private readonly ICompanyDetailsService _companyDetailsService;
        private readonly ICompanyGroupMappingService _companyGroupMappingService;

        public AppTemplateController(
            ITerminalService terminalMasterService,
            ILocationItemService locationItemService,
            IDeviceSetupUpgradeService deviceSetupUpgradeService,
            IRegisteredAppService registeredAppService,
            ISyncTerminalSetupService syncTerminalSetupService,
            IDeviceTemplatesService deviceTemplatesService,
            IMerchantTerminalService merchantTerminalService,
            ITerminalService terminalService,
            IApplicationDeviceMappingService applicationDeviceMappingService,
            ICompanyDetailsService companyDetailsService,
            ICompanyGroupMappingService companyGroupMappingService)
        {
            _terminalMasterService = terminalMasterService;
            _locationItemService = locationItemService;
            _deviceSetupUpgradeService = deviceSetupUpgradeService;
            _registeredAppService = registeredAppService;
            _syncTerminalSetupService = syncTerminalSetupService;
            _deviceTemplatesService = deviceTemplatesService;
            _merchantTerminalService = merchantTerminalService;
            _terminalService = terminalService;
            _applicationDeviceMappingService = applicationDeviceMappingService;
            _companyDetailsService = companyDetailsService;
            _companyGroupMappingService = companyGroupMappingService;
        }

        [HttpGet("device-info/{terminalMasterId}")]
        public IActionResult DeviceInfo(int terminalMasterId)
        {
            var terminalMaster = _terminalMasterService.GetForDeviceSetupEdit(terminalMasterId) ?? throw new ArgumentNullException(nameof(terminalMasterId), "TerminalMasterId cannot be null.");
            var deviceInfoModel = terminalMaster.ToDeviceInfoModel();

            var assignedLocation = _locationItemService.GetById(terminalMaster.AssignedLocationAreaId.GetValueOrDefault(), includes: p => p.Parent);
            if (assignedLocation != null)
            {
                deviceInfoModel.Location = assignedLocation.Parent?.Name;
                deviceInfoModel.LocationArea = assignedLocation.Name;
            }

            return Ok(deviceInfoModel);
        }

        [HttpGet("setup-upgrade-task/{terminalMasterId}")]
        public IActionResult PendingSetupUpgrade(int terminalMasterId)
        {
            var scheduleTasks = _deviceSetupUpgradeService.GetScheduleUpgradeTasks(terminalMasterId)
                .OrderByDescending(p => p.ScheduleType == ScheduleType.Install)
                .ThenByDescending(p => p.DateDownload)
                .ToList();

            var vms = scheduleTasks.Select(p => new DeviceSetupUpgradeModel(p)).ToList();
            return Ok(vms);
        }

        [HttpGet("show-template-by-task/{deviceSetupUpgradeId}")]
        public async Task<IActionResult> ShowDeviceTemplateDataByTask(int deviceSetupUpgradeId)
        {
            var deviceSetupUpgrade = _deviceSetupUpgradeService.GetById(deviceSetupUpgradeId);

            if (deviceSetupUpgrade is null)
            {
                throw new ArgumentNullException(nameof(deviceSetupUpgrade));
            }

            return await ShowDeviceTemplateDataByTemplateId(deviceSetupUpgrade.DeviceTemplateId);
        }

        [HttpGet("show-template-by-id/{terminalMasterTemplateId}")]
        public async Task<IActionResult> ShowDeviceTemplateDataByTemplateId(int terminalMasterTemplateId)
        {
            var terminalMasterTemplate = await _terminalMasterService.GetTerminalByDeviceTemplateId(terminalMasterTemplateId, DeviceSetupType.VirtualTemplateDevice)
                ?? await _terminalMasterService.GetTerminalByDeviceTemplateId(terminalMasterTemplateId, DeviceSetupType.BaseTemplate);

            if (terminalMasterTemplate is null)
                throw new ArgumentNullException(nameof(terminalMasterTemplate));

            var templateData = await GetRawDataFromTemplate(terminalMasterTemplate);
            if (templateData is null)
                throw new ArgumentNullException(nameof(templateData));

            return Ok(templateData);
        }

        [HttpGet("get-base-template-info-by-id")]
        public async Task<IActionResult> GetTerminalIdByTemplateId(int templateId, int companyId)
        {
            var terminalMasterTemplate = await _terminalMasterService.GetTerminalByDeviceTemplateId(templateId, DeviceSetupType.BaseTemplate);
            var baseTemplates = _deviceTemplatesService.GetById(templateId);
            var company = _companyDetailsService.GetById(companyId);

            if (terminalMasterTemplate is null)
                throw new ArgumentNullException(nameof(terminalMasterTemplate));

            if (baseTemplates is null)
                throw new ArgumentNullException(nameof(baseTemplates));

            if (company is null)
                throw new ArgumentNullException(nameof(company));

            var reponse = new AppBaseTemplateInfoResponse
            {
                CompanyName = company.Name,
                TerminalMasterId = terminalMasterTemplate.Id,
                TemplateName = baseTemplates.Name
            };

            return Ok(reponse);
        }

        [HttpPut("update-template-name")]
        public IActionResult UpdateAppBaseTemplateName(EditBaseTemplateNameRequest request)
        {
            var baseTemplates = _deviceTemplatesService.GetById(request.TemplateId);

            if (baseTemplates is null)
                throw new ArgumentNullException(nameof(baseTemplates));

            if (request.IsNewTemplate)
            {
                baseTemplates.IsStatus = Constants.NOTCHANGE_RECORD;
            }

            baseTemplates.Name = request.TemplateName.Trim();
            _deviceTemplatesService.Update(baseTemplates);

            return Ok();
        }

        [HttpDelete("cancel-task-setup-upgrade/{setupUpgradeId}")]
        public async Task<ActionResult> CancelAction(int setupUpgradeId)
        {
            var setupUpgrade = _deviceSetupUpgradeService.GetById(setupUpgradeId);

            if (setupUpgrade == null)
                throw new ArgumentNullException(nameof(setupUpgrade));


            _deviceSetupUpgradeService.Delete(setupUpgrade);
            if (setupUpgrade.Status == DeviceSetupUpgradeStatuses.DownloadPending)
            {
                _deviceSetupUpgradeService.DeleteJob(setupUpgrade);
            }
            else
            {
                const DeviceSetupUpgradeStatuses sendMessageFlags = DeviceSetupUpgradeStatuses.Delivered | DeviceSetupUpgradeStatuses.Sent | DeviceSetupUpgradeStatuses.Downloaded;
                if (sendMessageFlags.HasFlag(setupUpgrade.Status))
                {
                    var terminalMaster = _terminalMasterService.GetById(setupUpgrade.TerminalMasterId);
                    var appID = _registeredAppService.GetAppIDById(setupUpgrade.RegisteredAppId);
                    await _syncTerminalSetupService.SentDeviceSetupMessageToClient(setupUpgrade.VersionName, appID, terminalMaster.TerminalSerialNumber, "Cancel Action", "Cancel");
                }
            }

            return Ok();
        }

        [HttpGet("get-app-information")]
        [ProducesResponseType<AppInformationResponse>(StatusCodes.Status200OK)]
        public async Task<IActionResult> GetAppInformation(int terminalMasterId)
        {
            var (applicationMappingId, _) = await _applicationDeviceMappingService.GetMappingIdAndAppIdByTerminalMasterIdAsync(terminalMasterId);
            var response = await _applicationDeviceMappingService.GetAppNameAndVersionAsync(applicationMappingId);
            return Ok(response);
        }

        [HttpGet("get-assigned-app-template")]
        [ProducesResponseType<string>(StatusCodes.Status200OK)]
        public async Task<IActionResult> GetAssignedAppTemplate(int terminalMasterId)
        {
            var (_, appId) = await _applicationDeviceMappingService.GetMappingIdAndAppIdByTerminalMasterIdAsync(terminalMasterId);
            var currentDeviceTemplate = await _deviceSetupUpgradeService.GetAssignedAppTemplateAsync(terminalMasterId, appId);
            if (!currentDeviceTemplate.Item1)
            {
                return BadRequest(currentDeviceTemplate.Item2);
            }

            return Ok(currentDeviceTemplate.Item2);
        }

        [HttpGet("template-upgrade/{terminalMasterId}")]
        public async Task<IActionResult> TemplateUpgrade(int terminalMasterId)
        {
            var applicationDeviceMapping = _applicationDeviceMappingService.FirstOrDefault(
                        whereClauses: o => o.TerminalMasterId == terminalMasterId,
                        includes:
                        [
                            o => o.Application,
                            o => o.ApplicationVersion
                        ]);

            if (applicationDeviceMapping is null)
                throw new ArgumentNullException(nameof(applicationDeviceMapping));

            var currentDeviceTemplate = await _deviceSetupUpgradeService.GetAssignedAppTemplateAsync(applicationDeviceMapping.TerminalMasterId, applicationDeviceMapping.ApplicationId);

            var model = applicationDeviceMapping.TemplateUpgradeResponse();
            model.CurrentTemplateName = currentDeviceTemplate.Item1 ? currentDeviceTemplate.Item2 : string.Empty;
            model.RedirectUrlTemplate = Url.Action(
                    nameof(TemplateUpgrade),
                    nameof(AppTemplateController).Replace("Controller", ""),
                    new { terminalMasterId });

            //var newTemplateDeviceInfor = WebUtils.GetSession<NewTemplateDeviceInforSessionModel>(NewTemplateDeviceInfor);
            //if (newTemplateDeviceInfor is not null)
            //{
            //    viewModel.NewTemplateId = newTemplateDeviceInfor.NewTemplateId;
            //    viewModel.NewTemplateName = newTemplateDeviceInfor.NewTemplateName;
            //    viewModel.NewTemplateDeviceId = newTemplateDeviceInfor.NewTemplateDeviceId;
            //    viewModel.TemplateType = newTemplateDeviceInfor.TemplateType;
            //    viewModel.TemplateSelectedOption = (int)newTemplateDeviceInfor.TemplateSelectedOption;
            //    viewModel.CopiedTemplateName = newTemplateDeviceInfor.CopiedTemplateName;
            //}

            return Ok(model);
        }

        [HttpGet("existing-templates")]
        [ProducesResponseType<IList<DataItemResponse>>(StatusCodes.Status200OK)]
        public async Task<IActionResult> GetExistingTemplates(int terminalMasterId)
        {
            var (applicationDeviceMappingId, _) = await _applicationDeviceMappingService.GetMappingIdAndAppIdByTerminalMasterIdAsync(terminalMasterId);
            var existingTemplate = await _deviceTemplatesService.GetExistingTemplatesAsync(applicationDeviceMappingId);
            return Ok(existingTemplate);
        }

        [HttpGet("base-templates/{terminalMasterId}")]
        public async Task<IActionResult> GetBaseTemplateList(int terminalMasterId)
        {
            var (_, appId) = await _applicationDeviceMappingService.GetMappingIdAndAppIdByTerminalMasterIdAsync(terminalMasterId);
            var templateList = await _deviceTemplatesService.GetTemplateByRegisteredAppIdnAsync(appId);
            var listItem = templateList.Select(o => new DataItemResponse(o.Id, o.Name, o.IsActive)).ToList();

            return Ok(listItem);
        }

        [HttpGet("merchant-template-list/{terminalMasterId}")]
        public async Task<IActionResult> GetTemplateMerchantList(int terminalMasterId)
        {
            var merchants = await _merchantTerminalService.GetAssignedMerchantsAsync(terminalMasterId);
            var listItem = merchants.Select(o => new DataItemResponse(o.Id, o.Name, o.IsActive)).ToList();

            return Ok(listItem);
        }

        [HttpDelete("remove-template-history/{deviceTemplateId}")]
        public IActionResult RemoveTemplateHistory(int deviceTemplateId)
        {
            var deviceTemplate = _deviceTemplatesService.GetById(deviceTemplateId)
                ?? throw new KeyNotFoundException($"Device template not found with ID={deviceTemplateId}");

            _deviceTemplatesService.Delete(deviceTemplate);

            return Ok();
        }

        [HttpGet("is-fully-require-data/{terminalMasterTemplateId}")]
        public async Task<IActionResult> IsFullyRequireData(int terminalMasterTemplateId)
        {
            var terminalMasterTemplate = _terminalService.GetById(terminalMasterTemplateId);
            if (terminalMasterTemplate is not null)
            {
                var templateData = await GetRawDataFromTemplate(terminalMasterTemplate);
                if (!string.IsNullOrEmpty(templateData))
                {
                    XDocument xmlDoc = XDocument.Parse(templateData);
                    bool hasTerminalSetup = xmlDoc.Descendants("Terminal").Any(); // check Terminal Setup is setup or not

                    return Ok(new { isFullyRequireData = hasTerminalSetup });
                }
            }

            return Ok(new { isFullyRequireData = false });
        }


        /// <summary>
        /// Add the base template
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        [HttpPost("add-base-template")]
        public IActionResult AddAppBaseTemplate(AddAppBaseTemplateRequest request)
        {
            var appVersionMapping = _companyGroupMappingService.GetById(request.CompanyGroupMappingId)
                ?? throw new ArgumentException(nameof(request.CompanyGroupMappingId), "CompanyGroupMappingId invalid.");

            var entity = new DeviceTemplate()
            {
                CompanyGroupMappingId = request.CompanyGroupMappingId,
                AppVersionId = appVersionMapping.AppVersionId,
                IsActive = true,
                IsStatus = Constants.DELETE_RECORD,
            };

            _deviceTemplatesService.Insert(entity);

            var terminalMaster = new TerminalMaster()
            {
                DeviceSetupType = DeviceSetupType.BaseTemplate,
                CopyDeviceTemplateId = entity.Id
            };
            _terminalService.Insert(terminalMaster);

            int terminalMasterId = terminalMaster.Id;
            terminalMaster.TerminalSerialNumber = $"basetemplate-{terminalMasterId}";
            _terminalService.Update(terminalMaster);

            return Ok(entity.Id);
        }

        /// <summary>
        /// Delete the base template
        /// </summary>
        /// <param name="templateId"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        [HttpDelete("delete-base-template/{templateId}")]
        public IActionResult RemoveDeviceTemplates(int templateId)
        {
            var item = _deviceTemplatesService.GetById(templateId)
                ?? throw new ArgumentException("Invalid base template id");

            _deviceTemplatesService.Delete(item);

            return Ok();
        }

        /// <summary>
        /// Activate or Deactivate the base template
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        [HttpPatch("activate-deactivate-base-template")]
        public IActionResult ActivateAppBaseTemplate(ActivateDeactivateBaseTemplateRequest request)
        {
            var item = _deviceTemplatesService.GetById(request.TemplateId)
                ?? throw new ArgumentException("Invalid base template id");

            if (item.IsActive == request.IsActivate)
            {
                return BadRequest("The template is already in the same status.");
            }

            item.IsActive = request.IsActivate;
            _deviceTemplatesService.Update(item);

            return Ok();
        }

        /// <summary>
        /// Copy base template
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        /// <exception cref="InvalidOperationException"></exception>
        [HttpPost("copy-base-template")]
        public async Task<IActionResult> CopyTemplateToTemplate(CopyBaseTemplateRequest request)
        {
            _ = _deviceTemplatesService.GetById(request.SourceTemplateId) ?? throw new ArgumentException("Source template not found.", nameof(request.SourceTemplateId));
            _ = _deviceTemplatesService.GetById(request.TargetTemplateId) ?? throw new ArgumentException("Destination template not found.", nameof(request.TargetTemplateId));

            var terminalSrcTemplate = _terminalService.GetTerminalByTemplateId(request.SourceTemplateId);
            var terminalDestTemplate = _terminalService.GetTerminalByTemplateId(request.TargetTemplateId);

            var terminalSrcId = terminalSrcTemplate.Id;
            terminalSrcTemplate = await _terminalService.GetTerminalMasterForCopy(terminalSrcId, isBaseToBase: true)
                ?? throw new InvalidOperationException($"The source terminal was not found with ID={terminalSrcId}");

            await _terminalService.CopyTemplate(terminalDestTemplate.Id, terminalSrcTemplate);

            var copiedTerminal = await _terminalService.GetTerminalMasterForCopy(terminalDestTemplate.Id, isBaseToBase: true)
                ?? throw new InvalidOperationException($"The copied terminal was not found with ID={terminalDestTemplate.Id}");

            var isCopySuccess = ApiUtilCommon.ValidateTerminalTemplateCopy(terminalSrcTemplate, copiedTerminal);

            if (!isCopySuccess)
            {
                throw new ArgumentException("Copy failed.");
            }

            return Ok();
        }

        [HttpGet("validate-template/{templateId}")]
        public async Task<IActionResult> ValidateTemplate(int templateId)
        {
            var terminalMasterTemplate = await _terminalMasterService.GetTerminalByDeviceTemplateId(templateId, DeviceSetupType.BaseTemplate)
                ?? await _terminalMasterService.GetTerminalByDeviceTemplateId(templateId, DeviceSetupType.VirtualTemplateDevice);

            if (terminalMasterTemplate is null)
                throw new ArgumentNullException(nameof(terminalMasterTemplate));

            var (isValidTemplate, message) = await ValidateTemplate(terminalMasterTemplate);

            if (isValidTemplate)
                return Ok(new
                {
                    isValidTemplate
                });

            return Ok(new
            {
                isValidTemplate,
                message
            });
        }

        #region private method
        private static object ParseElement(XElement element)
        {
            if (!element.HasElements)
                return element.Value;

            var dict = new Dictionary<string, object>();

            var sortedElements = element.Elements()
                .OrderBy(e => e.HasElements ? 1 : 0)
                .ToList();

            foreach (var child in sortedElements)
            {
                if (dict.ContainsKey(child.Name.LocalName))
                {
                    if (dict[child.Name.LocalName] is List<object> list)
                        list.Add(ParseElement(child));
                    else
                        dict[child.Name.LocalName] = new List<object> { dict[child.Name.LocalName], ParseElement(child) };
                }
                else
                {
                    dict[child.Name.LocalName] = ParseElement(child);
                }
            }

            return dict;
        }

        private async Task<string> GetRawDataFromTemplate(TerminalMaster terminal)
        {
            try
            {
                var response = await ApiUtilCommon.ResyncPrepare(terminal, false);
                var responseContent = await response.Content.ReadAsStringAsync();
                Log.Debug("Response content: {0}", responseContent);
                JObject json = JObject.Parse(responseContent);

                if (response.StatusCode == HttpStatusCode.OK)
                {
                    var data = json["Data"]?.ToString();

                    if (data != null)
                    {
                        return data;
                    }
                    else
                    {
                        Log.Error("'Data' key is missing or null in the response for TerminalSerialNumber: {0}.", terminal.TerminalSerialNumber);
                        throw new InvalidOperationException("The 'Data' key is missing or null in the response.");
                    }
                }
                else
                {
                    var message = json["Message"]?.ToString() ?? "GetRawDataFromTemplate error";
                    Log.Error("Error occurred in GetRawDataFromTemplate for TerminalSerialNumber: {0}. StatusCode: {1}. Error: {2}", terminal.TerminalSerialNumber, response.StatusCode, message);
                    throw new InvalidOperationException(message);
                }
            }
            catch (HttpRequestException ex)
            {
                Log.Error("HTTP request failed in GetRawDataFromTemplate for TerminalSerialNumber: {0}. Error: {1}", terminal.TerminalSerialNumber, ex.Message);
                throw new InvalidOperationException("Failed to call PrepareFile API.", ex);
            }
        }

        private static async Task<(bool, string)> ValidateTemplate(TerminalMaster terminal)
        {
            try
            {
                var response = await ApiUtilCommon.ResyncPrepare(terminal, true);
                var responseContent = await response.Content.ReadAsStringAsync();
                Log.Debug("Response content: {0}", responseContent);
                JObject json = JObject.Parse(responseContent);

                if (response.StatusCode == HttpStatusCode.OK)
                {
                    return (true, string.Empty);
                }
                else
                {
                    return (false, json["Message"]?.ToString() ?? "GetRawDataFromTemplate error");
                }
            }
            catch (HttpRequestException ex)
            {
                return (false, ex.Message);
            }
        }
        #endregion
    }
}
