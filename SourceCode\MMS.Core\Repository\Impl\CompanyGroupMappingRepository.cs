﻿using Microsoft.EntityFrameworkCore;
using MMS.Core.CoreUTI;
using MMS.Core.CoreUTI.Enum;
using MMS.Core.dbContext;
using MMS.Core.Entities;
using MMS.Core.Repository.AppStore;
using MMS.Core.Utils;
using MMS.Infrastructure.Commons;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Threading.Tasks;

namespace MMS.Core.Repository.Impl
{
    public class CompanyGroupMappingRepository : BaseRepository<CompanyGroupMapping>, ICompanyGroupMappingRepository
    {
        private readonly MMSContext _context;
        private readonly IConfigureHideDeactivatedRepository _configureHideDeactivatedRepository;
        private readonly ICompanySubsidiaryMappingRepository _companySubsidiaryMappingRepository;
        private readonly IDeviceSetupUpgradeRepo _deviceSetupUpgradeRepo;
        private readonly IRegisteredAppVersionRepository _registeredAppVersionRepository;
        private readonly IApplicationDeviceMappingRepository _applicationDeviceMappingRepository;
        private readonly IDeviceTemplatesRepository _deviceTemplatesRepository;
        public CompanyGroupMappingRepository(DbContextFactory contextFactory,
            IConfigureHideDeactivatedRepository configureHideDeactivatedRepository,
            IDeviceSetupUpgradeRepo deviceSetupUpgradeRepo,
            IRegisteredAppVersionRepository registeredAppVersionRepository,
            IApplicationDeviceMappingRepository applicationDeviceMappingRepository,
            IDeviceTemplatesRepository deviceTemplatesRepository,
            ICompanySubsidiaryMappingRepository companySubsidiaryMappingRepository) : base(contextFactory)
        {
            _context = GetContext();
            _configureHideDeactivatedRepository = configureHideDeactivatedRepository;
            _companySubsidiaryMappingRepository = companySubsidiaryMappingRepository;
            _registeredAppVersionRepository = registeredAppVersionRepository;
            _applicationDeviceMappingRepository = applicationDeviceMappingRepository;
            _deviceTemplatesRepository = deviceTemplatesRepository;
            _deviceSetupUpgradeRepo = deviceSetupUpgradeRepo;
        }

        public async Task<CompanyGroupMapping> GetByIdAsync(int id)
        {
            var query = _context.Set<CompanyGroupMapping>()
                .Where(p => p.Id == id && p.IsStatus != Constants.DELETE_RECORD
                                                            && p.IsStatus != Constants.PRE_DELETE_RECORD)
                .Include(x => x.Company)
                .Include(x => x.CompanySubsidiary)
                        .ThenInclude(x => x.Subsidiary)
                .Include(x => x.Group)
                .Include(p => p.Parent)
                .Include(x => x.PartnerAccess);

            return await query.FirstOrDefaultAsync();
        }

        public async Task<List<CompanyGroupMapping>> GetByIdsAsync(IList<int> ids)
        {
            var query = _context.Set<CompanyGroupMapping>()
                .Where(p => ids.Contains(p.Id) && p.IsStatus != Constants.DELETE_RECORD
                                                            && p.IsStatus != Constants.PRE_DELETE_RECORD)
                .Include(x => x.Company)
                .Include(x => x.CompanySubsidiary)
                        .ThenInclude(x => x.Subsidiary)
                .Include(x => x.Group);

            return await query.ToListAsync();
        }

        public async Task<List<int>> GetHierarchyIdsAsync(int parentId)
        {
            var tablename = Constants.CompanyGroupMapping.ToLower();
            var sql = $@"
                        WITH RECURSIVE CompanyHierarchy AS (
                            SELECT id  FROM {tablename} WHERE Id = {{0}}
                            UNION ALL
                            SELECT c.id FROM {tablename} c
                            JOIN CompanyHierarchy ch ON c.ParentId = ch.Id
                        )
                        SELECT id FROM CompanyHierarchy";

            var result = await _context.Set<CompanyGroupMapping>().FromSqlRaw(sql, parentId).Select(c => c.Id).ToListAsync();
            return result;
        }

        public async Task<List<CompanyGroupMapping>> GetSelectedAsync(CompanyGroupMappingType type, int? parentId, bool includeDeactivatedOrg = false)
        {
            var query = _context.Set<CompanyGroupMapping>()
                .AsNoTracking()
                .Include(p => p.Group)
                .Include(p => p.Company)
                .Where(p => type == p.Type
                        && parentId == p.ParentId
                        && (p.Company == null || !string.IsNullOrWhiteSpace(p.Company.LinklyId))
                        && p.IsStatus != Constants.DELETE_RECORD
                        && p.IsStatus != Constants.PRE_DELETE_RECORD);

            query = UpdateGetSelectedQuery(query, type, includeDeactivatedOrg);

            bool isHideDeactivated;

            switch (type)
            {
                case CompanyGroupMappingType.Company:
                    isHideDeactivated = _configureHideDeactivatedRepository.GetFirst().MerchantLocationCompanyColumn;
                    break;
                case CompanyGroupMappingType.Subsidiary:
                    isHideDeactivated = GetIsHideDeactivatedByParentId(parentId!.Value);
                    break;
                case CompanyGroupMappingType.PartnerAccess:
                case CompanyGroupMappingType.SubPartner:
                case CompanyGroupMappingType.App:
                case CompanyGroupMappingType.AppVersion:
                    isHideDeactivated = false;
                    break;
                case CompanyGroupMappingType.Group:
                    isHideDeactivated = _configureHideDeactivatedRepository.GetFirst().MerchantLocationGroupColumn;
                    break;
                default:
                    throw new ArgumentException("Invalid type");
            }

            if (isHideDeactivated)
            {
                query = query.Where(p => p.IsActive);
            }

            return await query.ToListAsync();
        }

        public async Task<List<CompanyGroupMapping>> GetCompanyTreeChildrenMappingsAync(int? parentId, bool isIncludeGroup)
        {
            var query = _context.Set<CompanyGroupMapping>()
                .AsNoTracking()
                .Where(p =>
                    p.IsStatus != Constants.DELETE_RECORD &&
                    p.IsStatus != Constants.PRE_DELETE_RECORD);

            if (parentId == null)
            {
                query = query.Where(p =>
                    p.ParentId == null &&
                    !string.IsNullOrWhiteSpace(p.Company.LinklyId) &&
                    p.Type == CompanyGroupMappingType.Company);
            }
            else
            {
                query = query.Where(p =>
                    p.ParentId == parentId &&
                    (p.Company == null || !string.IsNullOrWhiteSpace(p.Company.LinklyId)) &&
                    (
                        p.Type == CompanyGroupMappingType.Company ||
                        (isIncludeGroup && p.Type == CompanyGroupMappingType.Group)
                    ));
            }

            query = query
                .Include(p => p.Company)
                .Include(p => p.Group);

            return await query.ToListAsync();
        }

        public async Task<Dictionary<int, bool>> GetCompanyTreeChildrenMappingsByIdsAync(List<int> parentIds, bool isIncludeGroup)
        {
            if (parentIds == null || !parentIds.Any())
            {
                return new Dictionary<int, bool>();
            }

            var types = new List<CompanyGroupMappingType>
            {
                CompanyGroupMappingType.Company,
            };

            if (isIncludeGroup)
            {
                types.Add(CompanyGroupMappingType.Group);
            }

            var query = _context.Set<CompanyGroupMapping>()
                .AsNoTracking()
                .Where(p =>
                    p.IsStatus != Constants.DELETE_RECORD &&
                    p.IsStatus != Constants.PRE_DELETE_RECORD &&
                    parentIds.Contains(p.ParentId.Value) &&
                    (p.Company == null || !string.IsNullOrWhiteSpace(p.Company.LinklyId)) &&
                    types.Contains(p.Type)
                );

            var parentIdsWithChildren = await query
                .Select(p => p.ParentId.Value)
                .Distinct()
                .ToListAsync();

            var result = new Dictionary<int, bool>();
            foreach (var parentId in parentIds)
            {
                result[parentId] = parentIdsWithChildren.Contains(parentId);
            }

            return result;
        }

        public async Task<List<CompanyGroupMapping>> GetPartnerAccessTreeChildrenMappingsAync(int? parentId)
        {
            if (parentId is null)
            {
                return await _context.Set<CompanyGroupMapping>()
                    .AsNoTracking()
                    .Where(p =>
                        p.ParentId == null &&
                        p.Type == CompanyGroupMappingType.Company &&
                        p.IsStatus != Constants.DELETE_RECORD &&
                        p.IsStatus != Constants.PRE_DELETE_RECORD)
                    .Include(p => p.Company)
                    .ToListAsync();
            }

            var query = _context.Set<CompanyGroupMapping>()
                .AsNoTracking()
                .Where(p =>
                    p.ParentId == parentId &&
                    (p.Type == CompanyGroupMappingType.Subsidiary || p.Type == CompanyGroupMappingType.PartnerAccess || p.Type == CompanyGroupMappingType.SubPartner) &&
                    p.IsStatus != Constants.DELETE_RECORD && p.IsStatus != Constants.PRE_DELETE_RECORD)
                .Include(p => p.CompanySubsidiary)
                    .ThenInclude(p => p.Subsidiary)
                .Include(p => p.Group).Include(p => p.PartnerAccess).Include(p => p.SubPartner);

            return await query.ToListAsync();
        }

        public async Task<List<CompanyGroupMapping>> GetSelectedByParentIdsAsync(CompanyGroupMappingType type, IList<int> parentIds, bool includeDeactivatedOrg = false)
        {
            var query = _context.Set<CompanyGroupMapping>()
                .AsNoTracking()
                .Where(p => type == p.Type && p.ParentId != null && parentIds.Contains(p.ParentId.Value) && p.IsStatus != Constants.DELETE_RECORD
                                                            && p.IsStatus != Constants.PRE_DELETE_RECORD && (!p.Parent.IsHideDeactivated || p.IsActive))
                .Include(p => p.Parent).AsQueryable();

            query = UpdateGetSelectedQuery(query, type, includeDeactivatedOrg);

            return await query.ToListAsync();
        }

        public async Task<CompanyGroupMapping> GetCompanyByCompanyIdAndScreenType(int? parentId, ScreenType screenType)
        {
            var query = await _context.Set<CompanyGroupMapping>()
                .FirstOrDefaultAsync(p => p.Id == parentId && p.ScreenType == screenType && p.IsStatus == Constants.NOTCHANGE_RECORD);
            if (query is null) return null;

            if (query.CompanySubsidiaryId.HasValue && query.CompanySubsidiaryId.Value != 0)
                return query;

            var companyId = Convert.ToInt32(query.HierarchyKey.Split(',')[0]);
            var mapping = await _context.Set<CompanyGroupMapping>()
                .FirstOrDefaultAsync(p => p.CompanyId == companyId && p.ScreenType == screenType && p.IsStatus == Constants.NOTCHANGE_RECORD);

            return mapping;
        }

        public async Task<List<CompanyGroupMapping>> GetByTypeAsync(CompanyGroupMappingType type)
        {
            var query = _context.Set<CompanyGroupMapping>()
                .AsNoTracking()
                .Where(p => type == p.Type
                        && p.IsStatus != Constants.DELETE_RECORD
                        && p.IsStatus != Constants.PRE_DELETE_RECORD);

            query = UpdateGetSelectedQuery(query, type, false);

            return await query.ToListAsync();
        }

        public async Task UpdateSelectedAsync(IList<int> selectedIds, int? parentId, CompanyGroupMappingType type, ScreenType screenType, bool useSelectSort = false)
        {
            await UpdateUseSelectSortAsync(type, parentId, useSelectSort);

            var configs = await GetConfigsAsync(type, parentId);
            var addableList = new List<CompanyGroupMapping>();

            var index = 1;
            var maxIndex = configs.Count != 0 ? configs.Max(p => p.OrderIndex) + 1 : index;

            foreach (var each in selectedIds)
            {
                var obj = FindConfigById(type, configs, each);

                if (obj == null)
                {
                    int orgId = each;

                    if (type == CompanyGroupMappingType.Subsidiary)
                    {
                        var subsidiaryMapping = _companySubsidiaryMappingRepository.GetById(each)
                            ?? throw new KeyNotFoundException($"Subsidiary mapping not found for ID: {each}");

                        orgId = subsidiaryMapping.SubsidiaryId;
                    }

                    var hierarchyKey = orgId.ToString();

                    if (parentId != null)
                    {
                        var parentHierarchyKey = (await _context.Set<CompanyGroupMapping>().FirstAsync(p => p.Id == parentId)).HierarchyKey;
                        hierarchyKey = $"{parentHierarchyKey},{hierarchyKey}";
                    }

                    obj = CreateNewConfig(each, parentId, type, ref maxIndex, screenType, hierarchyKey);
                    addableList.Add(obj);
                }

                if (useSelectSort)
                {
                    obj.OrderIndex = index++;
                }
            }

            var removeList = configs.Where(p => !selectedIds.Contains(GetId(type, p).Value)).ToList();

            await MarkConfigsForDeletion(type, removeList);

            if (addableList.Count > 0)
            {
                await _context.Set<CompanyGroupMapping>().AddRangeAsync(addableList);
            }

            _context.Set<CompanyGroupMapping>().UpdateRange(configs);
            await _context.SaveChangesAsync();
        }

        public async Task InsertGroupAsync(int groupId, int parentId)
        {
            var configs = await GetConfigsAsync(CompanyGroupMappingType.Group, parentId);
            var maxIndex = configs.Count != 0 ? configs.Max(p => p.OrderIndex) + 1 : 1;

            var hierarchyKey = groupId.ToString();

            var parent = await _context.Set<CompanyGroupMapping>()
                .FirstOrDefaultAsync(p => p.Id == parentId)
                ?? throw new ArgumentException($"Parent with ID {parentId} not found");

            hierarchyKey = $"{parent.HierarchyKey},{hierarchyKey}";

            var obj = CreateNewConfig(
                groupId,
                parentId,
                CompanyGroupMappingType.Group,
                ref maxIndex,
                ScreenType.MerchantLocation,
                hierarchyKey);

            await _context.Set<CompanyGroupMapping>().AddAsync(obj);
            await _context.SaveChangesAsync();
        }

        public async Task UpdateIsHideDeactivatedByParentIdAsync(int parentId)
        {
            var parent = await _context.Set<CompanyGroupMapping>().FirstOrDefaultAsync(p => p.Id == parentId);

            if (parent == null)
            {
                throw new ArgumentException("Invalid parent id");
            }

            parent.IsHideDeactivated = !parent.IsHideDeactivated;
            Update(parent);
        }

        public bool GetIsHideDeactivatedByParentId(int parentId)
        {
            var parent = _context.Set<CompanyGroupMapping>().FirstOrDefault(p => p.Id == parentId);

            if (parent == null)
            {
                throw new ArgumentException("Invalid parent id");
            }

            return parent.IsHideDeactivated;
        }

        private async Task UpdateUseSelectSortAsync(CompanyGroupMappingType type, int? parentId, bool useSelectSort)
        {
            if (type == CompanyGroupMappingType.Company)
            {
                var configuration = await _context.Set<Entities.Commons.Configuration>().FirstAsync();
                configuration.CompanyGroupUseSortCompany = useSelectSort;
                _context.Set<Entities.Commons.Configuration>().Update(configuration);
            }
            else if (parentId.HasValue)
            {
                var parent = await _context.Set<CompanyGroupMapping>().FindAsync(parentId.Value);
                parent.UseSelectSort = useSelectSort;
                _context.Set<CompanyGroupMapping>().Update(parent);
            }
        }

        private async Task<List<CompanyGroupMapping>> GetConfigsAsync(CompanyGroupMappingType type, int? parentId)
        {
            return await _context.Set<CompanyGroupMapping>()
                .Where(p => p.Type == type
                    && p.ParentId == parentId
                    && (p.Company == null || !string.IsNullOrWhiteSpace(p.Company.LinklyId))
                    && p.IsStatus != Constants.DELETE_RECORD
                    && p.IsStatus != Constants.PRE_DELETE_RECORD)
                    .Include(p => p.Company)
                .ToListAsync();
        }

        private static CompanyGroupMapping FindConfigById(CompanyGroupMappingType type, List<CompanyGroupMapping> configs, int id)
        {
            return configs.Find(p => GetId(type, p) == id);
        }

        private static CompanyGroupMapping CreateNewConfig(int id, int? parentId, CompanyGroupMappingType type, ref int maxIndex, ScreenType screenType, string hierarchyKey)
        {
            var obj = new CompanyGroupMapping
            {
                ParentId = parentId,
                IsActive = true,
                Type = type,
                ScreenType = screenType,
                OrderIndex = maxIndex++,
                HierarchyKey = hierarchyKey,
            };

            SetId(type, obj, id);
            return obj;
        }
        private async Task MarkConfigsForDeletion(CompanyGroupMappingType type, List<CompanyGroupMapping> removeList)
        {
            if (type == CompanyGroupMappingType.Company)
            {
                await ValidateCompanyDeletableAsync(removeList);
            }

            if (type == CompanyGroupMappingType.AppVersion)
            {
                await ValidateAppVersionDeletableAsync(removeList);
            }

            foreach (var each in removeList)
            {
                each.IsStatus = Constants.DELETE_RECORD;
            }
        }

        private static int? GetId(CompanyGroupMappingType type, CompanyGroupMapping mapping)
        {
            return type switch
            {
                CompanyGroupMappingType.Company => mapping.CompanyId,
                CompanyGroupMappingType.Subsidiary => mapping.CompanySubsidiaryId,
                CompanyGroupMappingType.Group => mapping.GroupId,
                CompanyGroupMappingType.PartnerAccess => mapping.PartnerAccessId,
                CompanyGroupMappingType.SubPartner => mapping.SubPartnerId,
                CompanyGroupMappingType.App => mapping.AppId,
                CompanyGroupMappingType.AppVersion => mapping.AppVersionId,
                _ => null
            };
        }

        private static void SetId(CompanyGroupMappingType type, CompanyGroupMapping mapping, int id)
        {
            switch (type)
            {
                case CompanyGroupMappingType.Company:
                    mapping.CompanyId = id;
                    break;
                case CompanyGroupMappingType.Subsidiary:
                    mapping.CompanySubsidiaryId = id;
                    break;
                case CompanyGroupMappingType.PartnerAccess:
                    mapping.PartnerAccessId = id;
                    break;
                case CompanyGroupMappingType.Group:
                    mapping.GroupId = id;
                    break;
                case CompanyGroupMappingType.SubPartner:
                    mapping.SubPartnerId = id;
                    break;
                case CompanyGroupMappingType.App:
                    mapping.AppId = id;
                    break;
                case CompanyGroupMappingType.AppVersion:
                    mapping.AppVersionId = id;
                    break;
            }
        }

        private static IQueryable<CompanyGroupMapping> UpdateGetSelectedQuery(IQueryable<CompanyGroupMapping> query, CompanyGroupMappingType type, bool includeDeactivatedOrg = false) //NOSONAR
        {
            switch (type)
            {
                case CompanyGroupMappingType.Company:
                    query = query.Where(x => x.Company.IsStatus != Constants.DELETE_RECORD
                                                            && x.Company.IsStatus != Constants.PRE_DELETE_RECORD)
                        .Include(x => x.Company);

                    if (!includeDeactivatedOrg)
                    {
                        query = query.Where(x => x.Company.IsActive);
                    }
                    break;
                case CompanyGroupMappingType.Subsidiary:
                    query = query.Where(x => x.CompanySubsidiary.IsStatus != Constants.DELETE_RECORD &&
                                                    x.CompanySubsidiary.IsStatus != Constants.PRE_DELETE_RECORD &&
                                                    x.CompanySubsidiary.Subsidiary.IsActive &&
                                                    x.CompanySubsidiary.Subsidiary.IsStatus != Constants.DELETE_RECORD &&
                                                    x.CompanySubsidiary.Subsidiary.IsStatus != Constants.PRE_DELETE_RECORD)
                        .Include(x => x.CompanySubsidiary)
                        .ThenInclude(x => x.Subsidiary);

                    if (!includeDeactivatedOrg)
                    {
                        query = query.Where(x => x.CompanySubsidiary.IsActive);
                    }
                    break;
                case CompanyGroupMappingType.PartnerAccess:
                    query = query.Where(x => x.PartnerAccess.IsStatus != Constants.DELETE_RECORD
                                        && x.PartnerAccess.IsStatus != Constants.PRE_DELETE_RECORD)
                                 .Include(x => x.PartnerAccess);

                    if (!includeDeactivatedOrg)
                    {
                        query = query.Where(x => x.PartnerAccess.IsActive);
                    }
                    break;
                case CompanyGroupMappingType.SubPartner:
                    query = query.Where(x => x.SubPartner.IsStatus != Constants.DELETE_RECORD
                                        && x.SubPartner.IsStatus != Constants.PRE_DELETE_RECORD)
                                 .Include(x => x.SubPartner);

                    if (!includeDeactivatedOrg)
                    {
                        query = query.Where(x => x.SubPartner.IsActive);
                    }
                    break;
                case CompanyGroupMappingType.Group:
                    query = query.Where(x => x.Group.IsStatus != Constants.DELETE_RECORD
                                                && x.Group.IsStatus != Constants.PRE_DELETE_RECORD)
                                    .Include(x => x.Group);
                    if (!includeDeactivatedOrg)
                    {
                        query = query.Where(x => x.Group.IsActive);
                    }
                    break;
                case CompanyGroupMappingType.App:
                    query = query.Where(x => x.App.IsStatus != Constants.DELETE_RECORD
                                          && x.App.IsStatus != Constants.PRE_DELETE_RECORD)
                                    .Include(x => x.App);

                    if (!includeDeactivatedOrg)
                    {
                        query = query.Where(x => x.App.IsActive);
                    }
                    break;
                case CompanyGroupMappingType.AppVersion:
                    query = query.Where(x => x.AppVersion.IsStatus != Constants.DELETE_RECORD
                                          && x.AppVersion.IsStatus != Constants.PRE_DELETE_RECORD)
                                    .Include(x => x.AppVersion);

                    if (!includeDeactivatedOrg)
                    {
                        query = query.Where(x => x.AppVersion.IsActive);
                    }
                    break;
                default:
                    break;
            }

            return query;
        }

        public async Task<List<CompanyGroup>> GetGroupSourcesForSubPartnerAsync(int accessPartnerId)
        {
            var query = _context.CompanyGroupMapping
                            .AsNoTracking()
                            .Include(p => p.Group)
                            .Where(p => p.Type == CompanyGroupMappingType.Group
                                     && p.IsStatus != Constants.DELETE_RECORD
                                     && p.IsActive
                                     && p.Group.IsStatus != Constants.DELETE_RECORD
                                     && p.Group.IsActive
                                     && p.ParentId == accessPartnerId);
            var groups = query.Select(p => p.Group);

            return await groups.ToListAsync();
        }

        public async Task<List<CompanyGroupMapping>> GetSelectedGroupsByCompanyOrSubsidiaryAsync(int parentId)
        {
            var parent = await GetByIdAsync(parentId);

            var mappingQuery = _context.CompanyGroupMapping
                .AsNoTracking()
                .Where(p => p.ScreenType == ScreenType.MerchantLocation
                            && p.IsActive
                            && p.IsStatus != Constants.DELETE_RECORD);

            mappingQuery = parent.Type switch
            {
                CompanyGroupMappingType.Company => mappingQuery
                    .Where(p => p.Type == CompanyGroupMappingType.Company
                             && p.CompanyId == parent.CompanyId),

                CompanyGroupMappingType.Subsidiary => mappingQuery
                    .Where(p => p.Type == CompanyGroupMappingType.Subsidiary
                             && p.CompanySubsidiaryId == parent.CompanySubsidiaryId),

                _ => mappingQuery.Where(_ => false)
            };

            var mappingId = await mappingQuery
                .Select(p => p.Id)
                .FirstOrDefaultAsync();

            if (mappingId == 0)
                return [];

            var groups = await _context.CompanyGroupMapping
                .AsNoTracking()
                .Where(p => p.Type == CompanyGroupMappingType.Group
                         && p.ParentId == mappingId
                         && p.IsActive
                         && p.IsStatus != Constants.DELETE_RECORD)
                .Include(p => p.Group)
                .ToListAsync();

            return groups;
        }

        public async Task<CompanyGroupMapping> GetByHierarchyKey(string hierarchyKey)
        {
            var result = await _context.Set<CompanyGroupMapping>().FirstOrDefaultAsync(p => p.HierarchyKey.Equals(hierarchyKey));

            if (result == null)
                throw new ArgumentException("Invalid Hierarchy Key");

            return result;
        }

        public async Task<Dictionary<int, bool>> HasDeletedParents(List<int> companyGroupMappingIds)
        {
            if (companyGroupMappingIds == null || !companyGroupMappingIds.Any())
            {
                return new Dictionary<int, bool>();
            }

            var tableName = Constants.CompanyGroupMapping.ToLower();

            var idsCsv = string.Join(",", companyGroupMappingIds);

            var sqlQuery = $@"
                WITH RECURSIVE RecursiveParent AS (
                    SELECT Id, ParentId, IsStatus, Id AS RootId
                    FROM {tableName}
                    WHERE Id IN ({idsCsv})

                    UNION ALL

                    SELECT cgm.Id, cgm.ParentId, cgm.IsStatus, rp.RootId
                    FROM {tableName} cgm
                    INNER JOIN RecursiveParent rp ON cgm.Id = rp.ParentId
                )
                SELECT RootId, COUNT(*) as DeletedCount
                FROM RecursiveParent
                WHERE IsStatus = 3
                GROUP BY RootId
            ";

            var deletedResults = await _context.Database
                                                .SqlQueryRaw<DeletedResult>(sqlQuery)
                                                .ToListAsync();

            var resultDict = companyGroupMappingIds.ToDictionary(id => id, id => false);
            foreach (var dr in deletedResults)
            {
                resultDict[dr.RootId] = dr.DeletedCount > 0;
            }

            return resultDict;
        }

        public async Task<List<int>> GetAllParentIdsByHierarchy(string hierarchy)
        {
            string firstId = hierarchy.Split(",")[0].Trim();

            var result = await _context.Set<CompanyGroupMapping>()
                .Where(o => hierarchy.Contains(o.HierarchyKey)
                    && o.HierarchyKey.StartsWith(firstId)
                    && o.IsStatus != Constants.DELETE_RECORD)
                .Select(o => o.Id)
                .ToListAsync();
            return result;
        }

        public async Task<List<int>> GetAllChildIdsByHierarchyAsync(string hierarchy)
        {
            var inputIds = hierarchy
                .Split(',', StringSplitOptions.RemoveEmptyEntries)
                .Select(id => int.Parse(id.Trim()))
                .ToHashSet();

            var result = await _context.Set<CompanyGroupMapping>()
                .Where(o => o.HierarchyKey.StartsWith(hierarchy)
                            && o.Type == CompanyGroupMappingType.Company
                            && o.Company.IsStatus != Constants.DELETE_RECORD
                            && o.IsStatus != Constants.DELETE_RECORD)
                .Select(o => o.HierarchyKey)
                .ToListAsync();

            var allIds = new HashSet<int>();

            foreach (var hierarchyKey in result)
            {
                var ids = hierarchyKey
                    .Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(id => int.Parse(id.Trim()));

                foreach (var id in ids)
                {
                    allIds.Add(id);
                }
            }

            allIds.ExceptWith(inputIds);

            return allIds.ToList();
        }


        public async Task<List<int>> GetByAppId(int appId)
        {
            var query = _context.CompanyGroupMapping
                .AsNoTracking()
                .Where(p => p.Type == CompanyGroupMappingType.App
                            && p.IsStatus != Constants.DELETE_RECORD
                            && p.AppId == appId);
            var ids = query.Select(p => p.Id);

            return await ids.ToListAsync();
        }

        public async Task<List<CompanyGroupMapping>> GetSelectedAppByCompanyOrSubsidiaryAsync(int companyMasterId, int groupId)
        {
            var condition = $"{companyMasterId},{groupId}";
            var result = await _context.CompanyGroupMapping
                .AsNoTracking()
                .Where(p => p.Type == CompanyGroupMappingType.App
                    && p.IsStatus != Constants.DELETE_RECORD
                    && p.IsActive
                    && p.AppId.HasValue
                    && p.App.IsStatus != Constants.DELETE_RECORD
                    && p.App.IsStatus != Constants.DEACTIVE_RECORD
                    && p.HierarchyKey.Contains(condition))
                .Include(p => p.App)
                .ToListAsync();

            return result;
        }

        public async Task<List<int>> GetSelectedAppVersionIdsAsync()
        {
            var result = _context.CompanyGroupMapping
                .AsNoTracking()
                .Where(p => p.Type == CompanyGroupMappingType.AppVersion
                    && p.IsStatus != Constants.DELETE_RECORD
                    && p.IsStatus != Constants.DEACTIVE_RECORD
                    && p.IsActive);

            return await result.Select(p => p.AppVersionId.Value).ToListAsync();
        }

        public async Task ValidateCompanyDeletableAsync(List<CompanyGroupMapping> removeList)
        {
            var companyGroupMappingIdDeletes = removeList.Select(p => p.Id).ToList();

            var hasChildrenDict = await GetCompanyTreeChildrenMappingsByIdsAync(companyGroupMappingIdDeletes, true);

            var idsWithChildren = hasChildrenDict.Where(kvp => kvp.Value).Select(kvp => kvp.Key).ToList();

            if (idsWithChildren.Count > 0)
            {
                var itemsWithChildren = removeList.Where(item => idsWithChildren.Contains(item.Id)).ToList();

                var itemNames = new List<string>();

                foreach (var item in itemsWithChildren)
                {
                    itemNames.Add(item.Name);
                }

                throw new ArgumentException($"ItemNames: {string.Join(", ", itemNames)}");
            }
        }

        public async Task ValidateAppVersionDeletableAsync(List<CompanyGroupMapping> removeList)
        {
            List<int> invalidDeleteIds;
            var orgId = removeList.Select(p => p.AppVersionId.Value).ToList();

            var upgradedAppVersionIds = await _deviceSetupUpgradeRepo.GetUgradedAppVersionIdsAsync();
            invalidDeleteIds = [.. orgId.Intersect(upgradedAppVersionIds)];

            if (invalidDeleteIds.Count > 0)
            {
                var appVerions = await _registeredAppVersionRepository.GetRegisteredAppVersionsByListIdsAsync(invalidDeleteIds);
                throw new ArgumentException(CommonConstant.GetCommonMessage(CommonMessage.Error_CannotDeleteUpgradedVersion) + " with name "
                    + string.Join(",", appVerions.Select(p => p.VersionNumber))
                    + " because this version is used in the upgrade history, please remove that device upgrade.");
            }

            var versionIdsUsedByApp = await _applicationDeviceMappingRepository.FilterUsedVersionIdsByAppAsync(orgId);
            await HandleVersionRemovalValidation(versionIdsUsedByApp, "it has an application using this version, please remove that Application.");

            var versionIdsUsedByBaseTemplate = await _deviceTemplatesRepository.FilterUsedAppVersionIdsByBaseTemplateAsync(removeList.Select(p => p.Id));
            await HandleVersionRemovalValidation(versionIdsUsedByBaseTemplate, "it has a base template using this version, please remove that base Template");
        }

        private async Task HandleVersionRemovalValidation(List<int> versionIds, string reason)
        {
            if (versionIds.Count == 0)
                return;

            var registeredAppVersions = await _registeredAppVersionRepository.GetRegisteredAppVersionsByListIdsAsync(versionIds);
            var message = string.Join(",", registeredAppVersions.Select(p => p.VersionNumber));

            throw new ArgumentException(CommonConstant.GetCommonMessage(CommonMessage.Error_CannotDeleteUpgradedVersion) + " with name "
                + message + " because " + reason);
        }

        public async Task<List<CompanyGroupMapping>> GetAllCompanyForSearchTreeMappingsAsync()
        {
            var rootCompanies = await _context.Set<CompanyGroupMapping>()
                .AsNoTracking()
                .Where(p =>
                    p.ParentId == null &&
                    p.Company.LinklyId != null &&
                    p.Type == CompanyGroupMappingType.Company &&
                    p.IsStatus != Constants.DELETE_RECORD &&
                    p.IsStatus != Constants.PRE_DELETE_RECORD)
                .Include(p => p.Company)
                .ToListAsync();

            var allMappings = await _context.Set<CompanyGroupMapping>()
                .AsNoTracking()
                .Where(p =>
                    p.ParentId != null &&
                    (p.CompanyId == null || p.Company.LinklyId != null) &&
                    (p.Type == CompanyGroupMappingType.Company ||
                     p.Type == CompanyGroupMappingType.Group) &&
                    p.IsStatus != Constants.DELETE_RECORD &&
                    p.IsStatus != Constants.PRE_DELETE_RECORD)
                .Include(p => p.Company)
                .Include(p => p.Group)
                .ToListAsync();

            return rootCompanies.Concat(allMappings).ToList();
        }

        public async Task<List<CompanyGroupMapping>> GetAllPartnerAccessTreeMappingsAsync()
        {
            var rootCompanies = await _context.Set<CompanyGroupMapping>()
                .AsNoTracking()
                .Where(p =>
                    p.ParentId == null &&
                    p.Type == CompanyGroupMappingType.Company &&
                    p.IsStatus != Constants.DELETE_RECORD &&
                    p.IsStatus != Constants.PRE_DELETE_RECORD)
                .Include(p => p.Company)
                .ToListAsync();

            var allMappings = await _context.Set<CompanyGroupMapping>()
                .AsNoTracking()
                .Where(p =>
                    p.ParentId != null &&
                    (p.Type == CompanyGroupMappingType.Subsidiary ||
                     p.Type == CompanyGroupMappingType.PartnerAccess ||
                     p.Type == CompanyGroupMappingType.SubPartner) &&
                    p.IsStatus != Constants.DELETE_RECORD &&
                    p.IsStatus != Constants.PRE_DELETE_RECORD)
                .Include(p => p.CompanySubsidiary)
                    .ThenInclude(p => p.Subsidiary)
                .Include(p => p.PartnerAccess)
                .Include(p => p.SubPartner)
                .Include(p => p.Group)
                .ToListAsync();

            return rootCompanies.Concat(allMappings).ToList();
        }

        public async Task<List<int>> GetAllCompanyIdSelected(int? parentId)
        {
            return await _context.Set<CompanyGroupMapping>()
                .AsNoTracking()
                .Where(p => p.Type == CompanyGroupMappingType.Company
                        && !string.IsNullOrWhiteSpace(p.Company.LinklyId)
                        && p.ParentId != parentId
                        && p.IsStatus != Constants.DELETE_RECORD
                        && p.IsStatus != Constants.PRE_DELETE_RECORD)
                .Include(p => p.Company)
                .Select(p => p.CompanyId.GetValueOrDefault())
                .ToListAsync();
        }
    }
}

[NotMapped]
public class DeletedResult
{
    public int RootId { get; set; }
    public int DeletedCount { get; set; }
}
