using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using MMS.Core.CoreUTI.Enum;
using MMS.Core.Entities;
using MMS.Core.Entities.TerminalSetupGUI;
using MMS.Core.Models;
using MMS.Core.Services;
using MMS.Core.Services.Base;
using MMS.Core.Services.Commons;
using MMS.Core.Services.Locations;
using MMS.Core.Services.SyncTerminalSetup;
using MMS.Core.Services.Users;
using MMS.Core.Utils;
using MMS.Web.Controllers.Apis;
using MMS.Web.Controllers.TerminalGUISetup;
using MMS.Web.Filters;
using MMS.Web.Models;
using MMS.Web.Models.TerminalSetupGUI;
using MMS.Web.Models.TerminalUpdateTemplate;
using MMS.Web.Models.ViewModels;
using MMS.Web.Utils;
using MMS.Web.Utils.Services;
using Newtonsoft.Json;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MMSConstants = MMS.Core.CoreUTI.Constants;
using Secure = MMS.Core.CoreUTI.Secure;
using UtilCommon = MMS.Web.Utils.UtilCommon;

namespace MMS.Web.Controllers
{

    public class TerminalSetupGUIController : TerminalGUIBaseController
    {
        public string _rootPath;

        #region service creator
        private readonly HostDomainSetting _hostDomainSetting;
        private readonly IWebHostEnvironment _webHostEnvironment;
        private readonly ITerminalService _terminalFixedService;
        private readonly ITerminalTypeMasterService _terminalTypeMasterService;
        private readonly IProcessorMasterService _processorService;
        private readonly ICompanyMasterService _companyMasterService;
        private readonly IApplicationVersionService _applicationVersionService;
        private readonly IFirmwareVersionService _firmwareVersionService;
        private readonly IAddressDetailsService _addressDetailService;
        private readonly IBaseService<VersionReleaseStatus> _versionReleaseStatusService;
        private readonly ISecurityAccessLevelService _securityAccessLevelService;
        private readonly IApplicationUpgradeTemplateService _applicationUpgradeTemplateService;
        private readonly IFirmwareUpgradeTemplateService _firmwareUpgradeTemplateService;
        private readonly IRS232CommService _rs232CommService;
        private readonly ITrainingTerminalService _trainingTerminalService;
        private readonly IBaseService<AutoLoginTerminal> _autoLoginTerminalService;
        private readonly ICloudImageService _cloudImageService;
        private readonly IBaseService<TenderTypeMerchant> _tenderTypesMerchantService;
        private readonly IFutureUpdateService _futureUpdateService;
        private readonly IContactDetailsService _contactDetailsService;
        private readonly IUserAccessMerchantService _userAccessMerchantService;
        private readonly ICurrencyService _currencyService;
        private readonly IHardCodeService _hardCodeService;
        private readonly IUserMerchantService _userMerchantService;
        private readonly ICharityService _charityService;
        private readonly IUserTerminalService _userTerminalService;
        private readonly IMerchantGeneralSetupService _merchantGeneralSetupService;
        private readonly IMerchantTerminalService _merchantTerminalService;
        private readonly ILanguageExecuteService _languageExecuteService;
        private readonly IEftPaymentsService _eftPaymentsService;
        private readonly ICharityTerminalService _charityTerminalService;
        private readonly IBaseService<MerchantTerminalHistory> _merchantTerminalHistoryService;
        private readonly IBaseService<SupportTerminal> _supportTerminalService;

        private readonly IBaseService<TipsTerminal> _tipsTerminalService;
        private readonly IBaseService<LanguageOptionTerminal> _languageOptionTerminalService;
        private readonly ICalculatePayService _calculatePayService;
        private readonly IServiceItemService _serviceItemService;
        private readonly ISurveyItemService _surveyItemService;
        private readonly IMerchantUserLogonService _merchantUserLogonService;
        private readonly IBaseService<BinTable> _binTableService;
        private readonly ICertificationUserService _certificationUserService;
        private readonly ILanguagesSupportService _languagesSupportService;

        private readonly IUserTerminalService _userTerminalSsCtxService;
        private readonly ICharityTerminalService _charityTerminalSsCtxService;
        private readonly IMerchantGeneralSetupService _merchantGeneralSetupSsCtxService;
        private readonly ICharityService _charitySsCtxService;
        private readonly IMerchantTerminalService _merchantTerminalSsCtxService;
        private readonly IBaseService<LanguageOptionTerminal> _languageOptionTerminalSsCtxService;
        private readonly IBaseService<SoundTerminal> _soundTerminalSsCtxService;
        private readonly IBaseService<SupportTerminal> _supportTerminalSsCtxService;
        private readonly IBaseService<TerminalUpdateSchedule> _terminalUpdateScheduleSsCtxService;
        private readonly IMerchantUserService _merchantUserSsCtxService;
        private readonly IHostInterfacesService _hostInterfacesService;
        private readonly IHostInterfacesService _hostInterfacesSsCtxService;
        private readonly IBaseService<BinTable> _binTableSsCtxService;
        private readonly ICertificationUserService _certificationUserSsCtxService;
        private readonly ILanguagesSupportService _languagesSupportSsCtxService;
        private readonly ITerminalSetupService _terminalSetupService;
        private readonly ITerminalSetupService _terminalSetupSsCtxService;
        private readonly ICountryTerminalService _countryTerminalService;
        private readonly ICountryTerminalService _countryTerminalSsCtxService;
        private readonly ICountryCardSetupTerminalService _countryCardSetupTerminalService;
        private readonly ICountryCardSetupTerminalService _countryCardSetupTerminalSsCtxService;
        private readonly ITimeZoneMasterService _timeZoneMasterService;
        private readonly IBaseService<DateTimeZoneTerminal> _dateTimeZoneTerminalSsCtxService;
        private readonly IBaseService<FallBackTerminal> _fallBackTerminalSsCtxService;
        private readonly IBaseService<FallBackTerminal> _fallBackTerminalService;
        private readonly IBaseService<TerminalUserSecurityAccess> _terminalUserSecurityAccessSsCtxService;
        private readonly IECRInterfaceService _ECRInterfaceService;
        private readonly IECRInterfaceService _ECRInterfaceSsCtxService;
        private readonly IECRRequestService _ECRRequestService;
        private readonly IECRRequestService _ECRRequestSsCtxService;
        private readonly IBaseService<AutoLoginTerminal> _autoLoginTerminalSsCtxService;
        private readonly IBaseService<SystemPosHttpServer> _systemPosHttpServerService;
        private readonly IBaseService<SystemPosHttpServer> _systemPosHttpServerSsCtxService;

        private readonly IBaseService<IconImageGroup> _iconImageGroupService;
        private readonly IBaseService<IconImageGroup> _iconImageGroupSsCtxService;
        private readonly IIconImageService _iconImageService;
        private readonly IIconImageService _iconImageSsCtxService;
        private readonly IBaseService<TerminalWifi> _terminalWifiService;
        private readonly IBaseService<TerminalWifi> _terminalWifiSsCtxService;
        private readonly IBaseService<TerminalWAN> _terminalWANService;
        private readonly IBaseService<TerminalWAN> _terminalWANSsCtxService;
        private readonly IBaseService<UserMaster> _userMasterSsCtxService;
        private readonly IBaseService<MMS.Core.Entities.alCoinDenominations> _alCoinDenominationsService;
        private readonly IBaseService<MMS.Core.Entities.alCoinDenominations> _alCoinDenominationsSsCtxService;
        private readonly IBaseService<MMS.Core.Entities.alNotesDenominations> _alNotesDenominationsService;
        private readonly IBaseService<MMS.Core.Entities.alNotesDenominations> _alNotesDenominationsSsCtxService;
        private readonly ILocationItemService _locationItemService;
        private readonly IBaseService<CharityAmount> _charityAmountService;
        private readonly IBaseService<CharityAmount> _charityAmountSsCtxService;
        private readonly IBaseService<TerminalRebootSchedule> _terminalRebootScheduleService;
        private readonly IBaseService<TerminalRebootSchedule> _terminalRebootScheduleSsCtxService;
        private readonly IBaseService<PosIntegrationTerminal> _posIntegrationTerminalService;
        private readonly IBaseService<PosIntegrationTerminal> _posIntegrationTerminalSsCtxService;
        private readonly IBaseService<CloudPosIntegrationTerminal> _cloudPosIntegrationTerminalService;
        private readonly IBaseService<CloudPosIntegrationTerminal> _cloudPosIntegrationTerminalSsCtxService;
        private readonly IBaseService<TerminalStyle> _terminalStyleServie;
        private readonly IBaseService<TerminalStyle> _terminalStyleSsCtxServie;
        private readonly ISyncTerminalSetupService _syncTerminalSetupService;
        private readonly IBaseService<MerchantCharity> _merchantCharityService;
        private readonly IBaseService<MerchantCharity> _merchantCharitySsCtxService;

        #endregion

        public TerminalSetupGUIController(
            ClientController clientController,
            IOptions<HostDomainSetting> setting,
            IWebHostEnvironment env,
            ITerminalService terminalFixedService,
            ITerminalTypeMasterService terminalTypeMasterService,
            IProcessorMasterService processorService,
            ICompanyMasterService companyMasterService,
            IApplicationVersionService applicationVersionService,
            IFirmwareVersionService firmwareVersionService,
            IAddressDetailsService addressDetailService,
            IActionOfFunctionService actionOfFunctionService,
            IBaseService<VersionReleaseStatus> versionReleaseStatusService,
            ILanguageMasterService languageMasterService,
            ISecurityAccessLevelService securityAccessLevelService,
            IApplicationUpgradeTemplateService applicationUpgradeTemplateService,
            IFirmwareUpgradeTemplateService firmwareUpgradeTemplateService,
            IRS232CommService rs232CommService,
            ITrainingTerminalService trainingTerminalService,
            IBaseService<AutoLoginTerminal> autoLoginTerminalService,
            ICloudImageService cloudImageService,
            IBaseService<TenderTypeMerchant> tenderTypesMerchantService,
            IFutureUpdateService futureUpdateService,
            IContactDetailsService contactDetailsService,
            IUserAccessMerchantService userAccessMerchantService,
            ICurrencyService currencyService,
            IHardCodeService hardCodeService,
            IUserMerchantService userMerchantService,
            ICharityService charityService,
            IUserMasterService userSystemService,
            ISystemHistoryService systemHistoryService,
            IStyleService styleService,
            IUserTerminalService userTerminalService,
            IUserAccessCloudService userAccessCloudService,
            ICommonMessageService commonMessageService,
            ILanguageExecuteService languageExecuteService,
            IMerchantGeneralSetupService merchantGeneralSetupService,
            IMerchantTerminalService merchantTerminalService,
            IEftPaymentsService eftPaymentsService,
            ICharityTerminalService charityTerminalService,
            IBaseService<MerchantTerminalHistory> merchantTerminalHistoryService,
            IBaseService<TipsTerminal> tipsTerminalService,
            IBaseService<LanguageOptionTerminal> languageOptionTerminalService,
            ICalculatePayService calculatePayService,
            IServiceItemService serviceItemService,
            ISurveyItemService surveyItemService,
            IMerchantUserLogonService merchantUserLogonService,
            IBaseService<SupportTerminal> supportTerminalService,
            IHostInterfacesService hostInterfacesService,
            IBaseService<BinTable> binTableService,
            ICertificationUserService certificationUserService,
            ILanguagesSupportService languagesSupportService,
            ITerminalSetupService terminalSetupService,
            ICountryTerminalService countryTerminalService,
            ICountryCardSetupTerminalService countryCardSetupTerminalService,
            IECRInterfaceService ECRInterfaceService,
            IECRRequestService ECRRequestService,
            IBaseService<SystemPosHttpServer> systemPosHttpServerService,
            IBaseService<MMS.Core.Entities.alCoinDenominations> alCoinDenominationsService,
            IBaseService<MMS.Core.Entities.alNotesDenominations> alNotesDenominationsService,
            ITMSSetupService tmsSetupService,

            ITerminalService terminalSsCtxService,
            ICharityTerminalService charityTerminalSsCtxService,
            IUserTerminalService userTerminalSsCtxService,
            IMerchantGeneralSetupService merchantGeneralSetupSsCtxService,
            ICharityService charitySsCtxService,
            IMerchantTerminalService merchantTerminalSsCtxService,
            IBaseService<LanguageOptionTerminal> languageOptionTerminalSsCtxService,
            IBaseService<SoundTerminal> soundTerminalSsCtxService,
            IBaseService<SupportTerminal> supportTerminalSsCtxService,
            IBaseService<TerminalUpdateSchedule> terminalUpdateScheduleSsCtxService,
            IMerchantUserService merchantUserSsCtxService,
            IHostInterfacesService hostInterfacesSsCtxService,
            IBaseService<BinTable> binTableSsCtxService,
            ILanguagesSupportService languagesSupportSsCtxService,
            ITerminalSetupService terminalSetupSsCtxService,
            ICertificationUserService certificationUserSsCtxService,
            ICountryTerminalService countryTerminalSsCtxService,
            ICountryCardSetupTerminalService countryCardSetupTerminalSsCtxService,
            ITimeZoneMasterService timeZoneMasterService,
            IBaseService<DateTimeZoneTerminal> dateTimeZoneTerminalSsCtxService,
            IBaseService<FallBackTerminal> fallBackTerminalSsCtxService,
            IBaseService<FallBackTerminal> fallBackTerminalService,
            IBaseService<TerminalUserSecurityAccess> terminalUserSecurityAccessSsCtxService,
            IECRInterfaceService ECRInterfaceSsCtxService,
            IECRRequestService ECRRequestSsCtxService,
            IBaseService<AutoLoginTerminal> autoLoginTerminalSsCtxService,
            IBaseService<SystemPosHttpServer> systemPosHttpServerSsCtxService,
            IBaseService<IconImageGroup> iconImageGroupService,
            IBaseService<IconImageGroup> iconImageGroupSsCtxService,
            IIconImageService iconImageService,
            IIconImageService iconImageSsCtxService,
            IBaseService<TerminalWifi> terminalWifiService,
            IBaseService<TerminalWifi> terminalWifiSsCtxService,
            IBaseService<TerminalWAN> terminalWANService,
            IBaseService<TerminalWAN> terminalWANSsCtxService,
            IBaseService<UserMaster> userMasterSsCtxService,
            IBaseService<MMS.Core.Entities.alNotesDenominations> alNotesDenominationsSsCtxService,
            IBaseService<MMS.Core.Entities.alCoinDenominations> alCoinDenominationsSsCtxService,
            ILocationItemService locationItemService,
            IIoTDeviceCompanyAssignedService ioTDeviceCompanyAssignedService,
            IIoTDeviceAssociatedStatusService ioTDeviceAssociatedStatusService,
            IBaseService<CharityAmount> charityAmountService,
            IBaseService<CharityAmount> charityAmountSsCtxService,
            IBaseService<TerminalRebootSchedule> terminalRebootScheduleService,
            IBaseService<TerminalRebootSchedule> terminalRebootScheduleSsCtxService,
            IBaseService<PosIntegrationTerminal> posIntegrationTerminalService,
            IBaseService<PosIntegrationTerminal> posIntegrationTerminalSsCtxService,
            IBaseService<CloudPosIntegrationTerminal> cloudPosIntegrationTerminalService,
            IBaseService<CloudPosIntegrationTerminal> cloudPosIntegrationTerminalSsCtxService,
            IBaseService<TerminalStyle> terminalStyleService,
            IBaseService<TerminalStyle> terminalStyleSsCtxService,
            ISyncTerminalSetupService syncTerminalSetupService,
            IBaseService<MerchantCharity> merchantCharityService,
            IBaseService<MerchantCharity> merchantCharitySsCtxService
        ) : base(
                systemHistoryService,
                styleService,
                userSystemService,
                languageMasterService,
                commonMessageService,
                userAccessCloudService,
                actionOfFunctionService,
                terminalSsCtxService,
                ioTDeviceCompanyAssignedService,
                ioTDeviceAssociatedStatusService)
        {
            base.ConfigClientController(clientController);
            _hostDomainSetting = setting.Value;
            _webHostEnvironment = env;
            _rootPath = env.WebRootPath;
            _terminalFixedService = terminalFixedService;
            _terminalTypeMasterService = terminalTypeMasterService;
            _processorService = processorService;
            _companyMasterService = companyMasterService;
            _applicationVersionService = applicationVersionService;
            _firmwareVersionService = firmwareVersionService;
            _addressDetailService = addressDetailService;
            _versionReleaseStatusService = versionReleaseStatusService;
            _securityAccessLevelService = securityAccessLevelService;
            _applicationUpgradeTemplateService = applicationUpgradeTemplateService;
            _firmwareUpgradeTemplateService = firmwareUpgradeTemplateService;
            _rs232CommService = rs232CommService;
            _trainingTerminalService = trainingTerminalService;
            _autoLoginTerminalService = autoLoginTerminalService;
            _cloudImageService = cloudImageService;
            _tenderTypesMerchantService = tenderTypesMerchantService;
            _futureUpdateService = futureUpdateService;
            _contactDetailsService = contactDetailsService;
            _userAccessMerchantService = userAccessMerchantService;
            _currencyService = currencyService;
            _hardCodeService = hardCodeService;
            _userMerchantService = userMerchantService;
            _charityService = charityService;
            _userTerminalService = userTerminalService;
            _merchantGeneralSetupService = merchantGeneralSetupService;
            _merchantTerminalService = merchantTerminalService;
            _languageExecuteService = languageExecuteService;
            _eftPaymentsService = eftPaymentsService;
            _charityTerminalService = charityTerminalService;
            _merchantTerminalHistoryService = merchantTerminalHistoryService;
            _tipsTerminalService = tipsTerminalService;
            _languageOptionTerminalService = languageOptionTerminalService;
            _calculatePayService = calculatePayService;
            _serviceItemService = serviceItemService;
            _surveyItemService = surveyItemService;
            _merchantUserLogonService = merchantUserLogonService;
            _supportTerminalService = supportTerminalService;
            _hostInterfacesService = hostInterfacesService;
            _binTableService = binTableService;
            _certificationUserService = certificationUserService;
            _languagesSupportService = languagesSupportService;
            _terminalSetupService = terminalSetupService;
            _countryTerminalService = countryTerminalService;
            _countryCardSetupTerminalService = countryCardSetupTerminalService;
            _timeZoneMasterService = timeZoneMasterService;
            _ECRInterfaceService = ECRInterfaceService;
            _ECRRequestService = ECRRequestService;
            _systemPosHttpServerService = systemPosHttpServerService;
            _iconImageGroupService = iconImageGroupService;
            _iconImageService = iconImageService;
            _terminalWifiService = terminalWifiService;
            _terminalWANService = terminalWANService;
            _alCoinDenominationsService = alCoinDenominationsService;
            _alNotesDenominationsService = alNotesDenominationsService;
            _fallBackTerminalService = fallBackTerminalService;
            _posIntegrationTerminalService = posIntegrationTerminalService;
            _cloudPosIntegrationTerminalService = cloudPosIntegrationTerminalService;
            _terminalStyleServie = terminalStyleService;
            _syncTerminalSetupService = syncTerminalSetupService;
            _merchantCharityService = merchantCharityService;

            _charityTerminalSsCtxService = (ICharityTerminalService)charityTerminalSsCtxService.UsingSessionContext();
            _userTerminalSsCtxService = userTerminalSsCtxService.UsingSessionContext();
            _merchantGeneralSetupSsCtxService = (IMerchantGeneralSetupService)merchantGeneralSetupSsCtxService.UsingSessionContext();
            _charitySsCtxService = charitySsCtxService.UsingSessionContext();
            _merchantTerminalSsCtxService = merchantTerminalSsCtxService.UsingSessionContext();
            _languageOptionTerminalSsCtxService = languageOptionTerminalSsCtxService.UsingSessionContext();
            _soundTerminalSsCtxService = soundTerminalSsCtxService.UsingSessionContext();
            _supportTerminalSsCtxService = supportTerminalSsCtxService.UsingSessionContext();
            _terminalUpdateScheduleSsCtxService = terminalUpdateScheduleSsCtxService.UsingSessionContext();
            _merchantUserSsCtxService = (IMerchantUserService)merchantUserSsCtxService.UsingSessionContext();
            _hostInterfacesSsCtxService = (IHostInterfacesService)hostInterfacesSsCtxService.UsingSessionContext();
            _binTableSsCtxService = binTableSsCtxService.UsingSessionContext();
            _certificationUserSsCtxService = certificationUserSsCtxService.UsingSessionContext();
            _languagesSupportSsCtxService = languagesSupportSsCtxService.UsingSessionContext();
            _terminalSetupSsCtxService = terminalSetupSsCtxService.UsingSessionContext();
            _countryTerminalSsCtxService = countryTerminalSsCtxService.UsingSessionContext();
            _countryCardSetupTerminalSsCtxService = countryCardSetupTerminalSsCtxService.UsingSessionContext();
            _dateTimeZoneTerminalSsCtxService = dateTimeZoneTerminalSsCtxService.UsingSessionContext();
            _fallBackTerminalSsCtxService = fallBackTerminalSsCtxService.UsingSessionContext();
            _terminalUserSecurityAccessSsCtxService = terminalUserSecurityAccessSsCtxService.UsingSessionContext();
            _ECRInterfaceSsCtxService = ECRInterfaceSsCtxService.UsingSessionContext();
            _ECRRequestSsCtxService = ECRRequestSsCtxService.UsingSessionContext();
            _autoLoginTerminalSsCtxService = autoLoginTerminalSsCtxService.UsingSessionContext();
            _systemPosHttpServerSsCtxService = systemPosHttpServerSsCtxService.UsingSessionContext();
            _iconImageGroupSsCtxService = iconImageGroupSsCtxService.UsingSessionContext();
            _iconImageSsCtxService = iconImageSsCtxService.UsingSessionContext();
            _terminalWifiSsCtxService = terminalWifiSsCtxService.UsingSessionContext();
            _terminalWANSsCtxService = terminalWANSsCtxService.UsingSessionContext();
            _userMasterSsCtxService = userMasterSsCtxService.UsingSessionContext();
            _alNotesDenominationsSsCtxService = alNotesDenominationsSsCtxService.UsingSessionContext();
            _alCoinDenominationsSsCtxService = alCoinDenominationsSsCtxService.UsingSessionContext();
            _locationItemService = locationItemService;
            _charityAmountService = charityAmountService;
            _charityAmountSsCtxService = charityAmountSsCtxService.UsingSessionContext();
            _terminalRebootScheduleService = terminalRebootScheduleService;
            _terminalRebootScheduleSsCtxService = terminalRebootScheduleSsCtxService.UsingSessionContext();
            _posIntegrationTerminalSsCtxService = posIntegrationTerminalSsCtxService.UsingSessionContext();
            _cloudPosIntegrationTerminalSsCtxService = cloudPosIntegrationTerminalSsCtxService.UsingSessionContext();
            _terminalStyleSsCtxServie = terminalStyleSsCtxService.UsingSessionContext();
            _merchantCharitySsCtxService = merchantCharitySsCtxService.UsingSessionContext();
        }

        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUISetupMenu, Action = Secure.ViewAction, ParentId = Secure.DeviceSetupListTerminal, IsRoot = true, ActionType = Secure.FullAction, GroupId = FilterGroupType.Terminal)]
        [SecurityCustomName("Device Setup")]
        public ActionResult Index(int id, int type = 0, string redirectUrlTemplate = null, bool isSimpleUI = false)
        {
            Log.Debug("TerminalSetupGUI Index called with id={Id}, isSimpleUI={IsSimpleUI}",
                id, isSimpleUI);

            TerminalId = id;

            //Set Session TerminalMasterId
            setTerminalMasterId(id);

            // Log SimpleUI mode parameters
            Log.Debug("SimpleUI mode: {IsSimpleUI}", isSimpleUI);

            if (type != 0)
            {
                Utils.WebUtils.SetSession<int>(Constants.TypeFutureUpdate, type);
                Log.Debug("Set TypeFutureUpdate session to {Type}", type);
            }

            var model = new TerminalModel { Id = id };
            var master = _terminalSsCtxService.GetById(id, p => p.GroupMaster);
            model.GroupMasterId = master.GroupMaster != null ? master.GroupMaster.Id : 0;
            model.RedirectUrlTemplate = redirectUrlTemplate;

            Log.Debug("Retrieved terminal master with GroupMasterId={GroupMasterId}", model.GroupMasterId);

            // TODO DUNG FIX THIS
            var masterOrg = _terminalFixedService.GetById(_terminalMasterOrgId, p => p.DeviceTemplate);
            model.IsLinkingTemplate = masterOrg?.TemplateOptions == TemplateOptions.TemplateLinked;
            model.LinkingTemplateName = masterOrg?.DeviceTemplate?.Name ?? string.Empty;
            model.TerminalMasterOrgId = masterOrg?.Id ?? 0;
            model.IsDeviceTemplate = master.IsDeviceTemplate();

            Log.Debug("Terminal linking info: IsLinkingTemplate={IsLinkingTemplate}, TemplateId={TemplateId}, IsDeviceTemplate={IsDeviceTemplate}",
                model.IsLinkingTemplate, model.TerminalMasterOrgId, model.IsDeviceTemplate);

            model.SetupMenuUrl = Url.Action("SetupMenu", "TerminalSetupGUI", new { id = id, type = type });

            var terminalMaster = _terminalFixedService.GetForDeviceSetupEdit(id);

            var assignedLocation = _locationItemService.GetById(terminalMaster.AssignedLocationAreaId.GetValueOrDefault(), includes: p => p.Parent);
            Log.Debug("Terminal assigned to location ID: {LocationId}", terminalMaster.AssignedLocationAreaId.GetValueOrDefault());

            model.HeaderName = "Device Setup";

            if (terminalMaster.lszTerminalName != null)
            {
                model.HeaderName += (" - " + terminalMaster.lszTerminalName);
                Log.Debug("Terminal name: {TerminalName}", terminalMaster.lszTerminalName);
            }

            // Set isSimpleUI in ViewBag to be used in the view
            ViewBag.IsSimpleUI = isSimpleUI;
            Log.Debug("Set ViewBag.IsSimpleUI = {IsSimpleUI}", isSimpleUI);

            return View(model);
        }

        [AppSetup]
        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUISetupMenu, Action = Secure.ViewAction)]
        public ActionResult SetupMenu(int id, int type = 0)
        {
            if (type != 0)
            {
                Utils.WebUtils.SetSession<int>(Constants.TypeFutureUpdate, type);

            }
            RemoveSession(Constants.MERCHANT_TERMINAL_ID);
            var model = new TerminalModel { Id = id };
            var master = _terminalSsCtxService.GetById(id, p => p.GroupMaster);
            model.GroupMasterId = master.GroupMaster != null ? master.GroupMaster.Id : 0;
            model.IsDeviceTemplate = master.IsDeviceTemplate();
            return PartialView(model);
        }

        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUIGlobalSetup, Action = Secure.ViewAction, ParentId = Secure.TerminalSetupGUISetupMenu, IsRoot = true, ActionType = Secure.FullAction, GroupId = FilterGroupType.Terminal)]
        [SecurityCustomName("Global Setup")]
        public ActionResult GlobalSetup(int terminalId)
        {
            RemoveSession(Constants.MERCHANT_TERMINAL_ID);

            var model = new TerminalModel { Id = terminalId };

            return PartialView(model);
        }

        #region User Terminal Setting
        [AuthorityFilterFactory(
            Function = Secure.TerminalSetupGUITerminalUsers,
            Action = Secure.ViewAction,
            ParentId = Secure.TerminalSetupGUIGlobalSetup,
            IsRoot = true,
            ActionType = Secure.FullAction,
            GroupId = FilterGroupType.Terminal)]
        [SecurityCustomName("Terminal Users")]
        public ActionResult UserTerminals(int id = 0)
        {
            var model = new UsersTerminalModel
            {
                TerminalId = id,
                UserList = new ViewItemsModel() { Items = _userTerminalSsCtxService.GetUserTerminalByTerminalId("", 0, Constants.LoadItem, id).ToList() }
            };
            return PartialView(model);
        }
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUsers, Action = Secure.ViewAction, ParentId = Secure.FunctionTerminalSetupFunction)]
        [SecurityCustomName("Terminal Users")]
        public ActionResult GetUserTerminals(int item = 0, string searchKey = "", int index = 0, int id = 0)
        {
            var skip = 0;
            if (index != 0)
            {
            }

            IList<ViewItemModel> items;

            items = _userTerminalSsCtxService.GetUserTerminalByTerminalId(searchKey, skip, Constants.LoadItem, id);

            if (items == null || items.Count <= 0 || items.Count < Constants.LoadItem)
            {
                ViewBag.Index = -1;
            }
            else
            {
                ViewBag.Index = index + 1;
            }
            var model = new ViewItemsModel()
            {
                Items = items,
            };
            return PartialView(PartialViewConstants.ListTerminalMultipleItemStandard, model);
        }

        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUITerminalUsers, Action = Secure.ViewAction)]
        public ActionResult UserTerminal(int userId = 0, int merchantId = 0, int terminalId = 0)
        {
            var userTerminal = _userTerminalSsCtxService.GetById(userId, p => p.User);
            var usersMaster = userTerminal.User;
            var idLanguagesTerminal = _languagesSupportSsCtxService.GetIdLanguageActiveByTerminalMasterId(_terminalMasterId);
            var languages = LanguageConstants.GetLanguages(idLanguagesTerminal);

            var model = new UserTerminalModel(userTerminal/*, userMerchant*/)
            {
                TerminalId = terminalId,
                MerchantId = merchantId,
                //Merchants = merchants,
                //Id = userId,
                LanguageMasters = languages.ToLanguageOptionTerminals(),
            };
            if (usersMaster != null)
            {
                model.TerminalId = terminalId;
                model.UserName = usersMaster.FullName;
                model.UserMasterId = usersMaster.Id;

            }

            model.RedirectUrl = Url.Action("UserTerminals", "TerminalSetupGUI", new { Id = model.TerminalId });
            return PartialView(model);
        }

        [AuthorityFilterFactory(
            Function = Secure.TerminalSetupGUITerminalUserEmailAddresses,
            Action = Secure.ViewAction,
            ParentId = Secure.TerminalSetupGUITerminalUsers,
            IsRoot = true,
            ActionType = Secure.FullAction,
            GroupId = FilterGroupType.Terminal)]
        [SecurityCustomName("Email Addresses")]
        public ActionResult UserEmailAddresses(int userId = 0, int terminalId = 0)
        {
            var userTerminal = _userTerminalSsCtxService.GetById(userId, p => p.User);
            var usersMaster = _languageExecuteService.GetLanguage(userTerminal.User);

            if (userTerminal == null)
                return RedirectToAction("UsersTerminal", "TerminalSetupGUI", new { Id = terminalId });

            var model = new UserTerminalModel(userTerminal)
            {
                TerminalId = terminalId,
                Id = userId,
                LanguageMasters = _languageOptionTerminalSsCtxService.GetAll(p => p.MerchantTerminalId == terminalId)
            };
            if (usersMaster != null)
            {
                model.TerminalId = terminalId;
                model.UserName = usersMaster.FullName;
                model.UserMasterId = usersMaster.Id;
            }
            return PartialView(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUITerminalUserEmailAddresses, Action = Secure.AddAction)]
        public ActionResult UserEmailAddresses(UserTerminalModel vm)
        {
            UserTerminal userTerminal = _userTerminalSsCtxService.GetById(vm.Id, p => p.User);
            if (userTerminal == null)
                return RedirectToAction("UserTerminals", "TerminalSetupGUI", new { Id = vm.TerminalId });

            userTerminal = vm.ToEmailEntity(userTerminal);
            _userTerminalSsCtxService.Update(userTerminal, false);

            return RedirectToAction("UserTerminal", new { userId = vm.Id, terminalId = vm.TerminalId });
        }

        [AuthorityFilterFactory(
            Function = Secure.TerminalSetupGUITerminalUserCellNumbers,
            Action = Secure.ViewAction,
            ParentId = Secure.TerminalSetupGUITerminalUsers,
            IsRoot = true,
            ActionType = Secure.FullAction,
            GroupId = FilterGroupType.Terminal)]
        [SecurityCustomName("Cell Numbers")]
        public ActionResult UserCellNumber(int userId = 0, int terminalId = 0)
        {
            var userTerminal = _userTerminalSsCtxService.GetById(userId, p => p.User);
            var usersMaster = _languageExecuteService.GetLanguage(userTerminal.User);

            if (userTerminal == null)
                return RedirectToAction("UsersTerminals", "TerminalSetupGUI", new { Id = terminalId });

            var model = new UserTerminalModel(userTerminal)
            {
                TerminalId = terminalId,
                Id = userId,
                LanguageMasters = _languageOptionTerminalSsCtxService.GetAll(p => p.MerchantTerminalId == terminalId)
            };
            if (usersMaster != null)
            {
                model.TerminalId = terminalId;
                model.UserName = usersMaster.FullName;
                model.UserMasterId = usersMaster.Id;
            }
            return PartialView(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUITerminalUserCellNumbers, Action = Secure.AddAction)]
        public ActionResult UserCellNumber(UserTerminalModel vm)
        {
            UserTerminal userTerminal = _userTerminalSsCtxService.GetById(vm.Id, p => p.User);
            if (userTerminal == null)
                return RedirectToAction("UserTerminals", "TerminalSetupGUI", new { Id = vm.TerminalId });

            userTerminal = vm.ToCellEntity(userTerminal);

            _userTerminalSsCtxService.Update(userTerminal, false);

            return RedirectToAction("UserTerminal", new { userId = vm.Id, terminalId = vm.TerminalId });

        }

        [AuthorityFilterFactory(
            Function = Secure.TerminalSetupGUITerminalUserM2mDevices,
            Action = Secure.ViewAction,
            ParentId = Secure.TerminalSetupGUITerminalUsers,
            IsRoot = true,
            ActionType = Secure.FullAction,
            GroupId = FilterGroupType.Terminal)]
        [SecurityCustomName("M2M Devices")]
        public ActionResult UserM2M(int userId = 0, int terminalId = 0)
        {
            var userTerminal = _userTerminalSsCtxService.GetById(userId, p => p.User);
            var usersMaster = _languageExecuteService.GetLanguage(userTerminal.User);

            if (userTerminal == null)
                return RedirectToAction("UserTerminals", "TerminalSetupGUI", new { Id = terminalId });

            var model = new UserTerminalModel(userTerminal)
            {
                TerminalId = terminalId,
                Id = userId,
                LanguageMasters = _languageOptionTerminalSsCtxService.GetAll(p => p.MerchantTerminalId == terminalId)
            };
            if (usersMaster != null)
            {
                model.TerminalId = terminalId;
                model.UserName = usersMaster.FullName;
                model.UserMasterId = usersMaster.Id;
            }
            return PartialView(model);
        }
        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUITerminalUserM2mDevices, Action = Secure.AddAction)]
        public ActionResult UserM2M(UserTerminalModel vm)
        {
            UserTerminal userTerminal = _userTerminalSsCtxService.GetById(vm.Id, p => p.User);
            if (userTerminal == null)
                return RedirectToAction("UserTerminals", "TerminalSetupGUI", new { Id = vm.TerminalId });

            userTerminal = vm.ToM2MEntity(userTerminal);

            _userTerminalSsCtxService.Update(userTerminal, false);

            return RedirectToAction("UserTerminal", new { userId = vm.Id, terminalId = vm.TerminalId });
        }

        [HttpPost]
        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUITerminalUsers, Action = Secure.RemoveAction)]
        public JsonResult UserDelete(int id)
        {
            UserTerminal userTerminal = _userTerminalSsCtxService.GetById(id);
            if (userTerminal == null)
                return Json(false);
            try
            {
                userTerminal.IsStatus = MMSConstants.PRE_DELETE_RECORD;
                _userTerminalSsCtxService.Update(userTerminal, false);
            }
            catch (Exception e)
            {
                return Json(false);
            }

            return Json(true);
        }

        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUITerminalUsers, Action = Secure.ViewAction)]
        public ActionResult UserSelect(int id)
        {
            var model = new UsersTerminalModel
            {
                TerminalId = id,
            };

            return PartialView(model);
        }

        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUITerminalUsers, Action = Secure.ViewAction)]
        public ActionResult UserGetItems(int terminalId, string searchKey = "", int index = 0)
        {
            var skip = 0;
            if (index != 0)
            {
                skip = index * Constants.LoadItem;
            }
            var items = _userTerminalSsCtxService.GetUserTerminalNotSelectByTerminalId(terminalId, searchKey, skip, Constants.LoadItem).ToList();

            if (items == null || items.Count <= 0 || items.Count < Constants.LoadItem)
            {
                ViewBag.Index = -1;
            }
            else
            {
                ViewBag.Index = index + 1;
            }
            var model = new ViewItemsModel()
            {
                Items = items,

            };
            return PartialView(PartialViewConstants.GetSelectItemsPartial, model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUITerminalUsers, Action = Secure.AddAction)]
        public ActionResult AddUser(int id, string IdSelectedList)
        {
            IList<int> listIds = Utils.WebUtils.ConvertStringToInts(IdSelectedList);

            var userTerminals = _userTerminalSsCtxService.GetUserTerminalByTerminalId(id);

            if (listIds == null || listIds.Count == 0) return RedirectToAction("UserTerminals", "TerminalSetupGUI", new { id = id });

            foreach (var tmp in from userId in listIds
                                let checkAddNew = !userTerminals.Any(user => userId.Equals(user.UserMasterId))
                                where checkAddNew
                                select new UserTerminal
                                {
                                    UserMasterId = userId,
                                    TerminalId = id,
                                    IsStatus = MMSConstants.PRE_INSERT_RECORD,
                                })
            {
                _userTerminalService.Insert(tmp);
            }

            Utils.WebUtils.SetSession<bool>(Constants.UsersSubmited, true);
            return RedirectToAction("UserTerminals", "TerminalSetupGUI", new { id = id });
        }

        public ActionResult UpdateUserTerminal(int userId = 0, int merchantId = 0, int terminalId = 0)
        {
            if (!IsAccessTerminalAccess(terminalId, Secure.FunctionTerminalUsers, Secure.ViewAction))
                return RedirectToAction("Index", "Error");
            //var usersMaster = _userService.GetById(userId, GetLanguage());
            var userTerminal = _userTerminalSsCtxService.GetById(userId, p => p.User);
            var usersMaster = userTerminal.User;

            var model = new UserTerminalModel(userTerminal/*, userMerchant*/)
            {
                TerminalId = terminalId,
                MerchantId = merchantId,
                //Merchants = merchants,
                //Id = userId,
                LanguageMasters = _languageOptionTerminalSsCtxService.GetAll(p => p.MerchantTerminalId == terminalId)
            };
            if (usersMaster != null)
            {
                model.TerminalId = terminalId;
                model.UserName = usersMaster.FullName;
                model.UserMasterId = usersMaster.Id;

            }
            return PartialView(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUITerminalUsers, Action = Secure.EditAction)]
        public ActionResult UpdateUserTerminal(UserTerminalModel model)
        {
            UserTerminal userTerminal = model.Id == 0
                ? new UserTerminal()
                : _userTerminalSsCtxService.GetById(model.Id, p => p.User);

            userTerminal = model.ToEntity(userTerminal);
            if (model.Id == 0)
            {
                userTerminal.IsStatus = MMSConstants.PRE_INSERT_RECORD;
                _userTerminalService.Insert(userTerminal);
            }

            Utils.WebUtils.SetSession<bool>(Constants.UsersSubmited, true);

            //if (model.UserMerchantId != 0)
            //{
            //    var usermerchant = _terminalService.GetUserMerchantById(model.UserMerchantId);
            //    usermerchant = model.GetUserMerchantTerminal(usermerchant, _terminalService);
            //    _terminalService.Update(usermerchant);
            //}
            //else if (model.Id != 0 && model.MerchantId != 0)
            //{
            //    var usermerchant = new UserMerchantTerminal() { MerchantId = model.MerchantId, UserId = model.Id };
            //    usermerchant = model.GetUserMerchantTerminal(usermerchant, _terminalService);
            //    _terminalService.Insert(usermerchant);
            //}



            return Redirect(model.RedirectUrl);

        }

        [AuthorityFilterFactory(Function = Secure.PersonCompanyPersonAndCompanies, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public JsonResult LoadUserMasterDetails(int id)
        {
            var user = _userMasterService.GetById(id);
            return Json(user);
        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUsers, Action = Secure.ViewAction)]
        public JsonResult CheckTerminalBeforeSettingUser(int id)
        {
            //var tmp = _terminalService.GetById(id);
            return Json(false);
        }

        #region CertificationUser
        [AuthorityFilterFactory(
            Function = Secure.TerminalSetupGUITerminalUserCertifications,
            Action = Secure.ViewAction,
            ParentId = Secure.TerminalSetupGUITerminalUsers,
            IsRoot = true,
            ActionType = Secure.FullAction,
            GroupId = FilterGroupType.Terminal)]
        [SecurityCustomName("Certifications")]
        public ActionResult CertificationUser(int userId)
        {

            var entity = _certificationUserSsCtxService.GetByUserId(userId);

            if (entity == null)
            {
                entity = new CertificationUser();
            }
            var items = MappingItemBoolean.ConvertToListBooleanItem(entity);

            items = items.Where(p => p.Value == true).ToList(); // Get only item active

            var userTerminal = _userTerminalSsCtxService.GetById(userId);

            var vm = new CertificationUserViewModel(items, userId, userTerminal.fEnabledCertification);

            vm.RedirectUrl = Url.Action("UserTerminal", "TerminalSetupGUI", new { userId, terminalId = _terminalMasterId });

            return PartialView(vm);
        }

        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUITerminalUserCertifications, Action = Secure.ViewAction)]
        public ActionResult CertificationFeatures(int userId)
        {
            var entity = _certificationUserSsCtxService.GetByUserId(userId);

            if (entity == null)
            {
                entity = new CertificationUser();
            }
            var items = MappingItemBoolean.ConvertToListBooleanItem(entity);

            var vm = new CertificationUserViewModel(items, userId);

            vm.RedirectUrl = Url.Action("CertificationUser", "TerminalSetupGUI", new { userId });

            return PartialView(vm);
        }

        [HttpPost]
        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUITerminalUserCertifications, Action = Secure.AddAction)]
        public ActionResult CertificationFeatures(CertificationUserViewModel vm)
        {
            if (vm.Type == TypeCertifications.Certifications)
            {
                var userTerminal = _userTerminalSsCtxService.GetById(vm.IdUser);
                userTerminal.fEnabledCertification = vm.Enabled;
                _userTerminalSsCtxService.Update(userTerminal, false);
            }

            var entity = _certificationUserSsCtxService.GetByUserId(vm.IdUser);

            if (entity == null)
            {
                entity = new CertificationUser(vm.IdUser, MMSConstants.NOTCHANGE_RECORD);
                _certificationUserSsCtxService.Insert(entity);
            }

            if (vm.Items != null)
                MappingItemBoolean.SetValueEntity(entity, vm.Items);

            _certificationUserSsCtxService.Update(entity, false);

            return Redirect(vm.RedirectUrl);
        }

        #endregion CertificationUser

        #endregion //User Terminal Setting

        #region Terminal Setup
        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUITerminalSetup, Action = Secure.ViewAction, ParentId = Secure.TerminalSetupGUISetupMenu, IsRoot = true, ActionType = Secure.FullAction, GroupId = FilterGroupType.Terminal)]
        [SecurityCustomName("Terminal Setup")]
        public ActionResult TerminalSetup(int terminalId)
        {
            var terminalMaster = _terminalSsCtxService.GetById(terminalId, p => p.UserTerminals);

            // TEM using first user
            var userTerminals = terminalMaster.UserTerminals.Where(x => x.IsStatus != MMSConstants.PRE_DELETE_RECORD && x.IsStatus != MMSConstants.DELETE_RECORD).ToList();
            var userTerminal = userTerminals.FirstOrDefault();

            if (terminalMaster == null) return RedirectToAction("Index", "Error");

            var terminalSetupId = terminalMaster.TerminalSetupId.GetValueOrDefault();

            var model = new TerminalSetupModel()
            {
                DefaultPaymentMode = DefaultPaymentMode.StandalonePay,
            };

            if (terminalSetupId > 0)
            {
                // old version using MerchantGeneralSetup
                var entity = _terminalSetupSsCtxService.GetById(terminalSetupId);
                model = new TerminalSetupModel(entity);

            }

            if (userTerminal != null)
            {
                var user = _userMasterSsCtxService.GetById(userTerminal.UserMasterId);
                model.logonUserId = user.logonUserId;
                model.logonPasscodeId = user.logonPasscodeId;
                model.HasUser = true;
            }

            model.TerminalId = terminalId;
            model.cPINCharacters = terminalMaster.cPINCharacters;
            model.fContactlessEnabled = terminalMaster.fContactlessEnabled;
            model.RedirectUrl = Url.Action("SetupMenu", new { id = model.TerminalId, type = Constants.ImmediateUpdate });

            return PartialView(model);
        }

        [HttpPost]
        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUITerminalSetup, Action = Secure.EditAction)]
        [ValidateAntiForgeryToken]
        public ActionResult TerminalSetup(TerminalSetupModel vm)
        {
            var tmp = new TerminalSetup();

            if (vm.Id == 0)
            {
                tmp = vm.ToEntity(tmp);

                tmp.ReadCtlsSwipeMode = Core.CoreUTI.Enum.CardReaderMode.CtlsDelay;
                tmp.PosIntegrationProtocol = PosIntegrationProtocol.LocalSocket;

                tmp.IsStatus = MMSConstants.PRE_INSERT_RECORD;
                _terminalSetupService.Insert(tmp);
                //_merchantGeneralSetupService.Insert(tmp);

                var terminal = _terminalSsCtxService.GetById(vm.TerminalId);

                terminal.TerminalSetupId = tmp.Id;

                _terminalSsCtxService.Update(terminal);
            }
            else
            {
                tmp = _terminalSetupSsCtxService.GetById(vm.Id);

                tmp = vm.ToEntity(tmp);

                _terminalSetupSsCtxService.Update(tmp, false);
            }

            var terminalMaster = _terminalSsCtxService.GetById(vm.TerminalId, p => p.UserTerminals);
            terminalMaster.cPINCharacters = vm.cPINCharacters;
            terminalMaster.fContactlessEnabled = vm.fContactlessEnabled;

            _terminalSsCtxService.Update(terminalMaster, false);

            // TEM using first user
            var userTerminals = terminalMaster.UserTerminals.Where(x => x.IsStatus != MMSConstants.PRE_DELETE_RECORD && x.IsStatus != MMSConstants.DELETE_RECORD).ToList();
            var userTerminal = userTerminals.FirstOrDefault();

            if (userTerminal != null)
            {
                var user = _userMasterSsCtxService.GetById(userTerminal.UserMasterId);
                user.logonUserId = vm.logonUserId;
                user.logonPasscodeId = vm.logonPasscodeId;

                _userMasterSsCtxService.Update(user, false);
            }

            return Redirect(vm.RedirectUrl);
        }

        #endregion //Terminal Setup

        #region Charities
        public ActionResult Charities(int selectedItem = 0, int terminalId = 0)
        {
            var model = new CharityTemplateModel()
            {
                SelectedItem = selectedItem,
                TerminalId = terminalId,
            };
            return PartialView(model);
        }

        public ActionResult GetCharityItems(int item = 0, int index = 0, int terminalId = 0)
        {

            var items = _charityTerminalService.GetCharitiesByTerminalId(terminalId).ToList().ToListItemModel();

            if (items == null || items.Count <= 0 || items.Count < Constants.LoadItem)
            {
                ViewBag.Index = -1;
            }
            else
            {
                ViewBag.Index = index + 1;
            }
            var model = new ViewItemsModel()
            {
                Items = items,
                SelectedId = item
            };
            return PartialView(PartialViewConstants.ListTerminalMultipleItemStandard, model);
        }

        public ActionResult CharitiesSelect(int id)
        {
            var model = new CharityTemplateModel()
            {
                TerminalId = id
            };
            return PartialView(model);
        }

        public ActionResult GetCharitiesDataForSelect(int terminalId, string searchKey = "", int index = 0)
        {
            var skip = 0;
            if (index != 0)
            {
                skip = index * Constants.LoadItem;
            }
            var items = _charityService.GetCharitiesNotSelectByTerminalId(terminalId, searchKey, skip, Constants.LoadItem).ToList();

            if (items == null || items.Count <= 0 || items.Count < Constants.LoadItem)
            {
                ViewBag.Index = -1;
            }
            else
            {
                ViewBag.Index = index + 1;
            }
            var model = new ViewItemsModel()
            {
                Items = items,

            };
            return PartialView("~/Views/TerminalSetupGUI/UserGetItems.cshtml", model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult CharitiesSelect(int id, string userIds)
        {
            IList<int> listIds = Utils.WebUtils.ConvertStringToInts(userIds);

            var charities = _charitySsCtxService.GetCharitiesByTerminalId(id);
            if (listIds == null || listIds.Count == 0) return RedirectToAction("UserTerminals", "TerminalSetupGUI", new { id = id });

            foreach (var tmp in from charityId in listIds
                                let checkAddNew = !charities.Any(c => id.Equals(c.Id))
                                where checkAddNew
                                select new CharityTerminal
                                {
                                    CharityId = charityId,
                                    TerminalId = id,
                                    IsStatus = MMSConstants.PRE_INSERT_RECORD,
                                })
            {
                _charityTerminalService.Insert(tmp);
            }

            Utils.WebUtils.SetSession<bool>(Constants.CharitiesSubmited, true);
            return RedirectToAction("Charities", "TerminalSetupGUI", new { terminalId = id });
        }

        #endregion //Charities

        #region Support Setting

        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUISupportSetup, Action = Secure.ViewAction, ParentId = Secure.TerminalSetupGUISetupMenu, IsRoot = true, ActionType = Secure.FullAction, GroupId = FilterGroupType.Terminal)]
        [SecurityCustomName("Terminal Support")]
        public ActionResult SupportSetup()
        {
            var terminalMaster = _terminalSsCtxService.GetById(TerminalId, x => x.Support);
            var support = terminalMaster.Support;
            if (support == null)
            {
                support = new SupportTerminal();
                _supportTerminalService.Insert(support);

                terminalMaster.Support = support;
                terminalMaster.SupportId = support.Id;

                _terminalFixedService.Update(terminalMaster);
            }
            var model = new SupportModel(support)
            {
                RedirectBackPage = Url.Action("SetupMenu", new { id = TerminalId }),
                TerminalId = TerminalId
            };

            return PartialView(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUISupportSetup, Action = Secure.EditAction)]
        public async Task<ActionResult> SupportSetup([FromForm] IFormFile logoImageFile, SupportModel viewModel, int deleteFile)
        {
            if (!string.IsNullOrWhiteSpace(viewModel.Phone) && !Utils.WebUtils.IsValidPhoneNumber(viewModel.Phone))
            {
                var message = Utils.WebUtils.GetMessage(viewModel.Phone,
                        GetStringByKey(Constants.ErrorMessageKeyPhoneNumberlIsInvalidFormat, "{0} is invalid phone number format"));
                return ModelInvalidRequest(message);
            }

            if (!string.IsNullOrWhiteSpace(viewModel.EmailFaults) && !Utils.WebUtils.IsValidEmailAddress(viewModel.EmailFaults))
            {
                var message = Utils.WebUtils.GetMessage(viewModel.EmailFaults,
                        GetStringByKey(Constants.ErrorMessageKeyEmailIsInvalidFormat, "{0} is invalid email format"));
                return ModelInvalidRequest(message);
            }

            if (!string.IsNullOrWhiteSpace(viewModel.EmailLogFile) && !Utils.WebUtils.IsValidEmailAddress(viewModel.EmailLogFile))
            {
                var message = Utils.WebUtils.GetMessage(viewModel.EmailLogFile,
                        GetStringByKey(Constants.ErrorMessageKeyEmailIsInvalidFormat, "{0} is invalid email format"));
                return ModelInvalidRequest(message);
            }

            var support = _supportTerminalSsCtxService.GetById(viewModel.Id);
            if (support != null)
            {
                if (logoImageFile != null)
                {
                    var name = support.Id + "." + logoImageFile.FileName.Split('.').LastOrDefault();
                    support.lszLogo = await WebUtils.SaveSupportFile(support.lszLogo, deleteFile == 1 ? null : logoImageFile, name);
                }

                if (deleteFile == 1 && !string.IsNullOrWhiteSpace(support.lszLogo))
                {
                    support.lszLogo = await WebUtils.SaveSupportFile(support.lszLogo, null);
                }

                support = viewModel.GetSupportTerminal(support);
                _supportTerminalSsCtxService.Update(support, false);
            }

            return Redirect(viewModel.RedirectBackPage);
        }

        #endregion

        #region Private

        #endregion Private

        #region Setup Update
        [AppSetup]
        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUISetupMenu, Action = Secure.EditAction)]
        public async Task<ActionResult> SetupUpdateNow(int terminalId, string url, bool isAjax = false)
        {
            var xmlChanges = _terminalSsCtxService.GetAllXmlChanges(terminalId);
            UtilCommon.TerminalDownloadSaveChanges(xmlChanges, _rootPath, terminalId);

            _terminalSsCtxService.SaveChanges();
            _terminalSsCtxService.UndoAll();
            //RemoveSession(Constants.TERMINAL_MASTER_ID);

            var terminalChangedXml = _terminalSsCtxService.GetTerminalChanges(terminalId, MMSConstants.CHANGE_RECORD);
            var xmlResponse = "<" + ApiLabelXmlConstants.Root + ">";
            string checkXml = "";
            if (terminalChangedXml != null)
            {
                foreach (var each in terminalChangedXml)
                {
                    xmlResponse += each.ChangedXml;
                    checkXml += each.ChangedXml;
                    each.IsClientUpdated = MMSConstants.UPDATED_RECORD;
                    _terminalSsCtxService.Update(each);
                }
            }
            xmlResponse += "</" + ApiLabelXmlConstants.Root + ">";
            var schedule = new TerminalUpdateSchedule()
            {
                TerminalId = terminalId,
                XmlChanges = xmlResponse,
                NextDownloadDate = DateTime.Now,
                NextDownloadTime = DateTime.Now,
                NextUpdateDate = DateTime.Now,
                NextUpdateTime = DateTime.Now
            };
            if (!string.IsNullOrWhiteSpace(checkXml))
            {
                _terminalUpdateScheduleSsCtxService.Insert(schedule);
            }

            string whereDeleteCondition = "where isStatus = " + MMSConstants.PRE_DELETE_RECORD;
            string whereInsertCondition = "where isStatus = " + MMSConstants.PRE_INSERT_RECORD;

            foreach (var tableName in MMSConstants.TerminalTablesName)
            {
                _terminalFixedService.SetIsStatus(tableName, whereDeleteCondition,
                    MMSConstants.DELETE_RECORD);
                _terminalFixedService.SetIsStatus(tableName, whereInsertCondition,
                    MMSConstants.NOTCHANGE_RECORD);
            }

            //if (isAjax)
            //{
            //    try
            //    {
            //        var terminaMaster = _terminalSsCtxService.GetById(terminalId);
            //        var zipPath = await GetDataFromTerminalSetup(terminaMaster, false, true);
            //        var result = await _syncTerminalSetupService.SyncTerminalSetupToClient(terminaMaster, zipPath, DateTime.Now, DateTime.Now);

            //        if (!result.IsSuccess)
            //        {
            //            return Json(new { success = false, message = $"{result.Message}" });
            //        }

            //    }
            //    catch (Exception ex)
            //    {
            //        Log.Error(ex.ToString());
            //        return Json(new { success = false, message = $"{ex.Message}" });
            //    }
            //}

            if (isAjax)
            {
                return Json(new { success = true, message = "Update successful" });
            }

            if (!string.IsNullOrEmpty(url))
            {
                return Redirect(url);
            }
            return RedirectToAction("FutureUpdate", "TerminalSetup", new { terminalId = terminalId });
        }



        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUISetupMenu, Action = Secure.EditAction)]
        public ActionResult EditFutureUpdate(int terminalId)
        {
            if (Utils.WebUtils.GetSession<int>(Constants.TypeFutureUpdate) == Constants.ReviewUpdate)
            {
                var futureUpdateId = Utils.WebUtils.GetSession<int>(Constants.FutureUpdateId);
                if (futureUpdateId == 0)
                {
                    return RedirectToAction("FutureUpdate", "TerminalSetup", new { terminalId = terminalId });
                }
                var url = Url.Action("ViewFutureUpdate", new { id = futureUpdateId, terminalId = terminalId });
                var futureUpdate = _futureUpdateService.GetById(futureUpdateId);
                if (futureUpdate == null)
                {
                    return Redirect(url);
                }
                if (futureUpdate.UpdateDate < DateTime.Now || futureUpdate.FutureDownloadDate < DateTime.Now)
                {
                    ResetTerminalMaster();
                    return RedirectToAction("FutureUpdateExpired", new { terminalId = terminalId });// future update expired
                }
                var terminalMaster = _terminalSsCtxService.GetById(terminalId, p => p.Support, p => p.Sound, p => p.Training, p => p.AutoLogin, p => p.TerminalCloud, p => p.PosRequest, p => p.TerminalSetup, p => p.Date,
                p => p.LanguageBy, p => p.TerminalSecurityAccesses, p => p.FallBack, p => p.CountryTerminal, p => p.MerchantTerminals, p => p.ProcessorTerminals, p => p.UserTerminals);
                var terminalTmp = _terminalFixedService.GetById(terminalId, p => p.MerchantTerminals, p => p.ProcessorTerminals, p => p.UserTerminals);

                if (terminalMaster == null || terminalTmp == null)
                {
                    return Redirect(url);
                }
                //Update Json Future Update
                var terminalMasterJson = (TerminalMaster)JsonConvert.DeserializeObject(futureUpdate.Data, typeof(TerminalMaster));
                Utils.WebUtils.SetSession<string>(Constants.CountrySubmited, null);
                if (Utils.WebUtils.GetSession<bool>(Constants.SupportSubmited)) terminalMasterJson.Support = terminalMaster.Support;
                if (Utils.WebUtils.GetSession<bool>(Constants.SoundSubmited)) terminalMasterJson.Sound = terminalMaster.Sound;
                if (Utils.WebUtils.GetSession<bool>(Constants.TrainingSubmited)) terminalMasterJson.Training = terminalMaster.Training;
                if (Utils.WebUtils.GetSession<bool>(Constants.AutoLoginSubmited)) terminalMasterJson.AutoLogin = terminalMaster.AutoLogin;
                if (Utils.WebUtils.GetSession<bool>(Constants.CloudSubmited)) terminalMasterJson.TerminalCloud = terminalMaster.TerminalCloud;
                if (Utils.WebUtils.GetSession<bool>(Constants.PosRequestSubmited)) terminalMasterJson.PosRequest = terminalMaster.PosRequest;
                if (Utils.WebUtils.GetSession<bool>(Constants.TerminalSetupSubmited)) terminalMasterJson.TerminalSetup = terminalMaster.TerminalSetup;
                if (Utils.WebUtils.GetSession<bool>(Constants.DateTimeZoneSubmited)) terminalMasterJson.Date = terminalMaster.Date;
                if (Utils.WebUtils.GetSession<bool>(Constants.LanguageBySubmited)) terminalMasterJson.LanguageBy = terminalMaster.LanguageBy;
                if (Utils.WebUtils.GetSession<bool>(Constants.SecurityLevelsSubmited)) terminalMasterJson.TerminalSecurityAccesses = terminalMaster.TerminalSecurityAccesses;
                if (Utils.WebUtils.GetSession<bool>(Constants.FallBackSubmited)) terminalMasterJson.FallBack = terminalMaster.FallBack;
                if (Utils.WebUtils.GetSession<bool>(Constants.CountrySubmited)) terminalMasterJson.CountryTerminal = terminalMaster.CountryTerminal;

                if (Utils.WebUtils.GetSession<bool>(Constants.MerchantsSubmited))
                {
                    terminalMasterJson.MerchantTerminalIds = terminalTmp.MerchantTerminals
                    .Where(m => m.IsStatus != MMSConstants.DELETE_RECORD
                    && m.IsStatus != MMSConstants.PRE_DELETE_RECORD)
                    .Select(m => m.Id).ToList();
                }

                if (Utils.WebUtils.GetSession<bool>(Constants.ProcessorsSubmited))
                {
                    terminalMasterJson.ProcessorTerminalIds = terminalTmp.ProcessorTerminals
                    .Where(m => m.IsStatus != MMSConstants.DELETE_RECORD
                    && m.IsStatus != MMSConstants.PRE_DELETE_RECORD)
                    .Select(m => m.Id).ToList();
                }

                if (Utils.WebUtils.GetSession<bool>(Constants.UsersSubmited))
                {
                    terminalMasterJson.UserTerminalIds = terminalTmp.UserTerminals
                    .Where(m => m.IsStatus != MMSConstants.DELETE_RECORD
                    && m.IsStatus != MMSConstants.PRE_DELETE_RECORD)
                    .Select(m => m.Id).ToList();
                }

                futureUpdate.Data = JsonConvert.SerializeObject(terminalMasterJson);
                _futureUpdateService.Update(futureUpdate);
                ResetTerminalMaster();
                ResetSessionFutureUpdate();
                return Redirect(url);
            }
            return RedirectToAction("FutureUpdate", "TerminalSetup", new { terminalId = terminalId });
        }

        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUISetupMenu, Action = Secure.EditAction)]
        public ActionResult SetupUpdateTerminal(int terminalId, string url, int id = 0)
        {
            var tmp = _terminalUpdateScheduleSsCtxService.GetById(id);
            var model = new TerminalSetupModel()
            {
                TerminalId = terminalId,
                RedirectUrl = url
            };
            return View(model);
        }

        [HttpPost]
        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUISetupMenu, Action = Secure.EditAction)]
        public ActionResult SetupUpdateTerminal(TerminalSetupModel model)
        {
            var futureUpdate = new FutureUpdate();
            futureUpdate.TerminalId = model.TerminalId;
            model.SetDateTimeFutureUpdate(futureUpdate);
            validateFutureUpdate(futureUpdate);
            if (!ModelState.IsValid)
            {
                return View(model);
            }

            //Insert Future Update
            var terminalMaster = _terminalSsCtxService.GetById(model.TerminalId, p => p.Support, p => p.Sound, p => p.Training, p => p.AutoLogin, p => p.TerminalCloud, p => p.PosRequest, p => p.TerminalSetup, p => p.Date,
                p => p.LanguageBy, p => p.TerminalSecurityAccesses, p => p.FallBack, p => p.CountryTerminal, p => p.MerchantTerminals, p => p.ProcessorTerminals, p => p.UserTerminals, p => p.TimeZoneMaster);
            var terminalTmp = _terminalFixedService.GetById(model.TerminalId);
            futureUpdate.Data = JsonConvert.SerializeObject(terminalMaster.Copy(terminalTmp));
            _futureUpdateService.Insert(futureUpdate);
            ResetSessionFutureUpdate();
            ResetTerminalMaster();

            if (!string.IsNullOrWhiteSpace(model.RedirectUrl))
            {
                return Redirect(model.RedirectUrl);
            }
            return RedirectToAction("Index", "TerminalSetupGUI", new { id = model.TerminalId, type = Constants.FutureUpdate });
        }

        [AppSetup]
        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUISetupMenu, Action = Secure.EditAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult CancelUpdate(string url, int terminalId, bool isAjax = false, bool isReturnMessage = true)
        {
            _terminalSsCtxService.UndoAll();
            ResetTerminalMaster();
            if (Utils.WebUtils.GetSession<int>(Constants.TypeFutureUpdate) == Constants.ReviewUpdate)
            {
                var futureUpdateId = Utils.WebUtils.GetSession<int>(Constants.FutureUpdateId);
                url = Url.Action("ViewFutureUpdate", new { id = futureUpdateId, terminalId = terminalId });
            }
            ResetSessionFutureUpdate();

            if (!isReturnMessage)
            {
                return Json(new { success = true });
            }

            if (isAjax)
            {
                return Json(new { success = true, message = "Cancel update successful" });
            }

            if (!string.IsNullOrEmpty(url))
            {
                return Redirect(url);
            }
            return RedirectToAction("FutureUpdate", "TerminalSetup", new { terminalId = terminalId });
        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalSetup, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult FutureUpdate(int terminalId = 0)
        {
            if (terminalId <= 0)
            {
                RedirectToAction("Index", "TerminalSetup");
            }
            var terminalMaster = _terminalSsCtxService.GetById(terminalId);
            if (terminalMaster == null)
            {
                return RedirectToAction("Index", "TerminalSetup");
            }

            var model = new FutureUpdateModel(terminalMaster)
            {
                //Style = WebUtils.GetDefaultString(_styleService.GetAllDefaultStyle(),
                //    _styleService.GetByScreenKey(Constants.ScreenKeyViewFutureUpdate, GetLanguage())
                //    , _styleService.GetAllDefaultStyle(GetLanguage())),
                RedirectBackPage = Url.Action("Edit", "TerminalSetup", new { id = terminalId })
            };
            var futureUpdate = _futureUpdateService.GetFutureUpdatesByTerminalId(terminalId);
            if (futureUpdate != null)
            {
                model.FutureUpdate = futureUpdate.Select(x => new FutureUpdate()
                {
                    Id = x.Id,
                    FutureDownloadDate = x.FutureDownloadDate,
                    FutureDownloadTime = x.FutureDownloadTime,
                    IsUpdated = x.IsUpdated,
                    IsDowloaded = x.IsDowloaded,
                    UpdateDate = x.UpdateDate,
                    UpdateTime = x.UpdateTime
                }).ToList();
            }

            return View(model);
        }
        #endregion Setup Update

        #region Session Future Update

        TerminalMaster GetTerminalMasterBySession()
        {
            TerminalMaster terminalMaster = null;
            var futureUpdateId = Utils.WebUtils.GetSession<int>(Constants.FutureUpdateId);
            if (futureUpdateId != 0)
            {
                var futureUpdate = _futureUpdateService.GetById(Utils.WebUtils.GetSession<int>(Constants.FutureUpdateId));
                if (futureUpdate != null)
                {
                    terminalMaster = (TerminalMaster)JsonConvert.DeserializeObject(futureUpdate.Data, typeof(TerminalMaster));
                }
            }
            return terminalMaster;
        }

        bool CheckReviewFutureUpdate(string key)
        {
            return (Utils.WebUtils.GetSession<int>(Constants.TypeFutureUpdate) == Constants.ReviewUpdate && !Utils.WebUtils.GetSession<bool>(key));
        }

        void ResetSessionFutureUpdate()
        {

            Utils.WebUtils.SetSession<string>(Constants.TypeFutureUpdate, null);
            Utils.WebUtils.SetSession<string>(Constants.FutureUpdateId, null);
            Utils.WebUtils.SetSession<string>(Constants.SupportSubmited, null);
            Utils.WebUtils.SetSession<string>(Constants.SoundSubmited, null);
            Utils.WebUtils.SetSession<string>(Constants.TrainingSubmited, null);
            Utils.WebUtils.SetSession<string>(Constants.AutoLoginSubmited, null);
            Utils.WebUtils.SetSession<string>(Constants.CloudSubmited, null);
            Utils.WebUtils.SetSession<string>(Constants.TerminalSetupSubmited, null);
            Utils.WebUtils.SetSession<string>(Constants.ProcessorsSubmited, null);
            Utils.WebUtils.SetSession<string>(Constants.MerchantsSubmited, null);
            Utils.WebUtils.SetSession<string>(Constants.DateTimeZoneSubmited, null);
            Utils.WebUtils.SetSession<string>(Constants.LanguageBySubmited, null);
            Utils.WebUtils.SetSession<string>(Constants.SecurityLevelsSubmited, null);
            Utils.WebUtils.SetSession<string>(Constants.UsersSubmited, null);
            Utils.WebUtils.SetSession<string>(Constants.FallBackSubmited, null);
            Utils.WebUtils.SetSession<string>(Constants.CountrySubmited, null);
            Utils.WebUtils.SetSession<string>(Constants.BinTableSubmited, null);
        }

        private void ResetTerminalMaster()
        {
            string whereDeleteCondition = "where isStatus = " + MMSConstants.PRE_DELETE_RECORD;
            string whereInsertCondition = "where isStatus = " + MMSConstants.PRE_INSERT_RECORD;

            foreach (var tableName in MMSConstants.TerminalTablesName)
            {
                _terminalFixedService.SetIsStatus(tableName, whereDeleteCondition,
                    MMSConstants.NOTCHANGE_RECORD);
                _terminalFixedService.SetIsStatus(tableName, whereInsertCondition,
                    MMSConstants.DELETE_RECORD);
            }

        }

        private void validateFutureUpdate(FutureUpdate futureUpdate)
        {
            if (futureUpdate.UpdateDate < DateTime.Now)
            {
                ModelState.AddModelError(Constants.terminal_setupupdate_update_date,
                    GetStringByKey(Constants.terminal_setupupdate_update_date_validate, "Invalid Upgrade Date"));
            }

            if (futureUpdate.FutureDownloadDate < DateTime.Now)
            {
                ModelState.AddModelError(Constants.terminal_setupupdate_future_download_date,
                    GetStringByKey(Constants.terminal_setupupdate_download_date_validate, "Invalid Future Dowload"));
            }
        }

        #endregion Session Future Update

        #region Host Interfaces
        //[AuthorityFilterFactory(Function = Secure.TerminalSetupGUIHotInterfaceList, Action = Secure.ViewAction, ParentId = Secure.TerminalSetupGUIGlobalSetup, FilterData = FilterDataTable.HostInterface, IsRoot = true, ActionType = Secure.FullAction, GroupId = FilterGroupType.Terminal)]
        //[SecurityCustomName("Host Interface")]
        public ActionResult HotInterfaces(int terminalMasterId)
        {
            var hostInterfaceAccess = GetListHostInterfaceAccess();

            var hostInterfaces = _hostInterfacesSsCtxService.GetGlobalHostInterfaces();

            if (!CheckCurrentUserHasAdminAccess())
            {
                hostInterfaces = hostInterfaces.Where(p => hostInterfaceAccess.Contains(p.Id)).ToList();
            }

            var models = hostInterfaces.ToListItemModel();

            var vm = new HostInterfacesViewModel(terminalMasterId, models);

            return PartialView(vm);
        }

        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUIHotInterfaceList, Action = Secure.AddAction)]
        public ActionResult EditHostInterface(int terminalMasterId, int hostInterfaceId = 0)
        {
            if (terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            var entity = _hostInterfacesSsCtxService.GetHostInterfacesById(hostInterfaceId);
            if (entity == null)
            {
                entity = new HostInterfaces();
                entity.IsStatus = MMSConstants.DELETE_RECORD;
                _hostInterfacesService.Insert(entity);
            }
            var model = entity.ToModel();
            var binTable = _binTableSsCtxService.GetAll(x => x.TerminalId == terminalMasterId);

            var vm = new HostInterfacesViewModel(model, binTable);

            vm.RedirectUrl = Url.Action("SetupHotInterfaces", new { hostInterfaceId = entity.Id });

            return PartialView(vm);
        }

        [HttpPost]
        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUIHotInterfaceList, Action = Secure.AddAction)]
        public async Task<ActionResult> EditHostInterface(HostInterfacesViewModel vm, [FromForm] IFormFile hostInterfaceImageFile)
        {
            var entity = _hostInterfacesSsCtxService.GetHostInterfacesById(vm.HostInterface.Id);

            if (entity.IsStatus == MMSConstants.DELETE_RECORD)
            {
                entity.IsStatus = MMSConstants.PRE_INSERT_RECORD;
                _hostInterfacesService.Update(entity);
            }

            var stringImageUrl = "";
            if (hostInterfaceImageFile != null)
            {
                stringImageUrl = await WebUtils.PathMerchantFolder(vm.HostInterface.IconId, hostInterfaceImageFile);
                entity.IconId = stringImageUrl;
            }

            entity = vm.HostInterface.ToEntity(entity);
            entity.IsUpdated = true;

            _hostInterfacesService.Update(entity, false);

            return Redirect(vm.RedirectUrl);
        }

        [HttpPost]
        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUIHotInterfaceList, Action = Secure.EditAction)]
        public JsonResult DeleteHostInterface(int id = 0)
        {
            var item = _hostInterfacesSsCtxService.GetHostInterfacesById(id);
            if (item == null)
                return Json(false);
            try
            {
                item.IsStatus = MMSConstants.PRE_DELETE_RECORD;
                _hostInterfacesSsCtxService.Update(item);
            }
            catch (Exception)
            {
                return Json(false);
            }

            return Json(true);
        }

        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUIHotInterfaceList, Action = Secure.ViewAction)]
        public ActionResult SetupHotInterfaces(int hostInterfaceId)
        {
            var entity = _hostInterfacesSsCtxService.GetHostInterfacesById(hostInterfaceId);

            IGatewaySetting gatewaySettingInstance = DllInstanceFactory.CreateInstance<IGatewaySetting>("Paymark.dll");

            if (gatewaySettingInstance == null)
            {
                return RedirectToAction("Index", "Error");
            }

            var gatewaySettings = gatewaySettingInstance.GatewaySettings();

            var settingProcessors = GatewaySettingEmm.GetData(entity.SettingProcessor, gatewaySettings);

            var model = entity.ToModel();

            var vm = new HostInterfacesViewModel(model);

            vm.GatewaySettings = settingProcessors;
            vm.RedirectUrl = Url.Action("EditHostInterface", new { terminalMasterId = entity.TerminalMasterId, hostInterfaceId = entity.Id });

            return PartialView(vm);
        }

        [HttpPost]
        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUIHotInterfaceList, Action = Secure.EditAction)]
        public ActionResult SetupHotInterfaces(HostInterfacesViewModel vm)
        {

            var entity = _hostInterfacesSsCtxService.GetHostInterfacesById(vm.HostInterface.Id);

            IGatewaySetting gatewaySettingInstance = DllInstanceFactory.CreateInstance<IGatewaySetting>("Paymark.dll");

            if (gatewaySettingInstance == null)
            {
                return RedirectToAction("Index", "Error");
            }

            var gatewaySettingUpdate = GatewaySettingEmm.ConvertFieldListToByteArray(vm.GatewaySettings);

            var gatewayResponse = gatewaySettingInstance.SetGatewaySettings(gatewaySettingUpdate);

            if (gatewayResponse == true)
                entity.SettingProcessor = gatewaySettingUpdate;

            entity.IsUpdated = true;
            _hostInterfacesService.Update(entity);

            return Redirect(vm.RedirectUrl);
        }

        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUISetupHotInterfaces, Action = Secure.EditAction)]
        public ActionResult PreauthExpireHostInterfaces(int hostInterfaceId)
        {
            var entity = _hostInterfacesSsCtxService.GetHostInterfacesById(hostInterfaceId);

            var model = entity.ToModel();

            var vm = new HostInterfacesViewModel()
            {
                HostInterface = model,
                RedirectUrl = Url.Action("SetupHotInterfaces", new { hostInterfaceId = entity.Id })
            };

            return PartialView(vm);
        }

        [HttpPost]
        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUISetupHotInterfaces, Action = Secure.EditAction)]
        public ActionResult PreauthExpireHostInterfaces(HostInterfacesViewModel vm)
        {
            if (vm.HostInterface.Id <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            var entity = _hostInterfacesSsCtxService.GetHostInterfacesById(vm.HostInterface.Id);

            if (entity.IsStatus == MMSConstants.DELETE_RECORD)
            {
                entity.IsStatus = MMSConstants.PRE_INSERT_RECORD;
                _hostInterfacesService.Update(entity);
            }

            if (entity != null)
            {
                entity = vm.HostInterface.ToEntity(entity, true);
                entity.IsUpdated = true;
                _hostInterfacesSsCtxService.Update(entity, false);
            }

            return Redirect(vm.RedirectUrl);
        }

        /// <summary>
        /// Retrieves and displays a list of host interfaces associated with a specific terminal, filtered by user access rights.
        /// This action is used to render a partial view of global host interfaces for the specified terminal.
        /// If the current user lacks admin access, the list is filtered based on their accessible host interface IDs.
        /// </summary>
        /// <param name="terminalMasterId">The ID of the terminal master to retrieve associated host interfaces for.</param>
        /// <returns>A PartialView containing a HostInterfacesViewModel with the filtered list of host interfaces.</returns>
        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUIHotInterfaceList, Action = Secure.ViewAction, ParentId = Secure.TerminalSetupGUIGlobalSetup, FilterData = FilterDataTable.HostInterface, IsRoot = true, ActionType = Secure.FullAction, GroupId = FilterGroupType.Terminal)]
        [SecurityCustomName("Host Interface")]
        public ActionResult ListGlobalHostInterfaces(int terminalMasterId)
        {
            var hostInterfaceAccess = GetListHostInterfaceAccess();

            var hostInterfaces = _hostInterfacesSsCtxService.GetTerminalHostInterfaces(terminalMasterId);

            if (!CheckCurrentUserHasAdminAccess())
            {
                hostInterfaces = hostInterfaces.Where(p => hostInterfaceAccess.Contains(p.Id)).ToList();
            }

            var models = hostInterfaces.ToListItemModelWithParentOverride();

            var vm = new HostInterfacesViewModel(terminalMasterId, models);

            return PartialView(vm);
        }

        /// <summary>
        /// Displays a selection view for host interfaces that are not yet associated with the current terminal.
        /// This action retrieves available host interfaces and prepares a model for selection, including a redirect URL
        /// to return to the global host interfaces list after selection.
        /// </summary>
        /// <returns>A PartialView containing a HostInterfacesModel with available host interfaces for selection.</returns>
        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUIHotInterfaceList, Action = Secure.ViewAction)]
        public ActionResult SelectGlobalHostInterfaces()
        {
            var availableHostInterfaces = _hostInterfacesSsCtxService.GetHostInterfacesNotSelectedByTerminal(_terminalMasterId).ToListViewItemModel();

            HostInterfacesModel model = new(availableHostInterfaces)
            {
                RedirectUrl = Url.Action("ListGlobalHostInterfaces", new { terminalMasterId = _terminalMasterId })
            };

            return PartialView(model);
        }

        /// <summary>
        /// Processes the selection of host interfaces to associate with the current terminal.
        /// This action converts a list of selected host interface IDs into new HostInterfaces entities,
        /// inserts them into the system, and redirects to the specified URL upon completion.
        /// If no valid IDs are provided, it redirects immediately without processing.
        /// </summary>
        /// <param name="IdSelectedList">A string containing the IDs of selected host interfaces, to be converted into integers.</param>
        /// <param name="vm">The HostInterfacesModel containing additional data, including the redirect URL.</param>
        /// <returns>A RedirectResult to the URL specified in the model, or immediately if no valid IDs are provided.</returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUIHotInterfaceList, Action = Secure.AddAction)]
        public ActionResult SelectGlobalHostInterfaces(string IdSelectedList, HostInterfacesModel vm)
        {
            var hostInterfaceIds = WebUtils.ConvertStringToInts(IdSelectedList);

            if (hostInterfaceIds == null || hostInterfaceIds.Count == 0)
                return Redirect(vm.RedirectUrl);

            var selectedList = _hostInterfacesSsCtxService.GetHostInterfacesByIds(hostInterfaceIds);

            // insert linking host interfaces
            var newHostInterfaces = HostInterfacesModelEmm.MapToHostInterfaces(selectedList, _terminalMasterId);

            _hostInterfacesService.InsertAll(newHostInterfaces);

            return Redirect(vm.RedirectUrl);
        }
        #endregion

        #region Language Terminal
        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUILanguageSupport, Action = Secure.ViewAction, ParentId = Secure.TerminalSetupGUIGlobalSetup, IsRoot = true, ActionType = Secure.FullAction, GroupId = FilterGroupType.Terminal)]
        [SecurityCustomName("Language Support")]
        public ActionResult LanguageSupport()
        {
            var idLanguages = _languagesSupportSsCtxService.GetIdLanguageActiveByTerminalMasterId(_terminalMasterId);

            var languages = LanguageConstants.GetLanguages();
            var languageSupport = LanguageSupportModelEmm.SetActive(languages, idLanguages);

            var terminalMaster = _terminalSsCtxService.GetById(_terminalMasterId);

            var vm = new LanguageSupportViewModel(languageSupport, terminalMaster.IdLanguageDefault, LanguagesSupport.TerminalSupport, "Terminal");

            vm.RedirectBackPage = Url.Action("SetupMenu", "TerminalSetupGUI", new { id = TerminalId });

            return PartialView(vm);
        }

        [HttpPost]
        public ActionResult LanguageSupport(LanguageSupportViewModel vm)
        {

            var merchantTerminalId = _merchantTerminalId != 0 ? _merchantTerminalId : (int?)null;

            if (vm.Type == LanguagesSupport.TerminalSupport)
            {
                var terminalMaster = _terminalSsCtxService.GetById(_terminalMasterId);
                terminalMaster.IdLanguageDefault = vm.IdDefaultLanguage;

                _terminalSsCtxService.Update(terminalMaster, false);
            }
            else
            {
                var merchantTerminal = _merchantTerminalSsCtxService.GetById(_merchantTerminalId);
                merchantTerminal.IdLanguageDefault = vm.IdDefaultLanguage;

                _merchantTerminalSsCtxService.Update(merchantTerminal, false);
            }

            var idLanguages = vm.LanguageSupports.Where(x => x.IsActive == true).Select(p => p.IdLanguage).ToList();

            _languagesSupportSsCtxService.SaveChange(_terminalMasterId, merchantTerminalId, idLanguages, vm.Type);

            return Redirect(vm.RedirectBackPage);
        }

        #endregion

        // Charities design old, using database Charity
        #region CharitiesNew
        [AuthorityFilterFactory(
            Function = Secure.TerminalSetupGUICharitySetup,
            ParentId = Secure.TerminalSetupGUIGlobalSetup,
            Action = Secure.ViewAction,
            IsRoot = true,
            GroupId = FilterGroupType.Terminal,
            ActionType = Secure.FullAction)]
        [SecurityCustomName("Charity Setup")]
        public ActionResult CharitySetup(int id = 0)
        {
            var entities = _charitySsCtxService.GetGlobalCharities(id);
            entities ??= new List<ViewItemModel>();

            var model = new CharityTerminalModel
            {
                TerminalId = id,
                CharityList = new ViewItemsModel { Items = entities }
            };

            return PartialView(model);
        }

        [HttpPost]
        public async Task<ActionResult> CharitySetup(CharityViewModel vm)
        {
            var entity = _charitySsCtxService.GetMerchantCharityByTerminalMasterId(_terminalMasterId);

            if (entity == null)
            {
                entity = new Charity(_terminalMasterId, MMSConstants.NOTCHANGE_RECORD);
                _charitySsCtxService.Insert(entity);
            }

            // save file
            if (vm.OneCharityIconFile != null)
            {
                entity.OneCharityIcon = await WebUtils.PathCharityFolder(vm.CharityModelItem.OneCharityIcon, vm.OneCharityIconFile);
            }

            if (vm.TwoCharitiesIconFile != null)
            {
                entity.TwoCharitiesIcon = await WebUtils.PathCharityFolder(vm.CharityModelItem.TwoCharitiesIcon, vm.TwoCharitiesIconFile);
            }

            if (vm.FourCharitiesIconFile != null)
            {
                entity.FourCharitiesIcon = await WebUtils.PathCharityFolder(vm.CharityModelItem.FourCharitiesIcon, vm.FourCharitiesIconFile);
            }

            if (vm.SelectedCharityIconFile != null)
            {
                entity.SelectedCharityIcon = await WebUtils.PathCharityFolder(vm.CharityModelItem.SelectedCharityIcon, vm.SelectedCharityIconFile);
            }

            if (vm.MultipleCharityIconFile != null)
            {
                entity.MultipleCharityIcon = await WebUtils.PathCharityFolder(vm.CharityModelItem.MultipleCharityIcon, vm.MultipleCharityIconFile);
            }

            entity = vm.CharityModelItem.ToEnityTerminal(entity);
            _charitySsCtxService.Update(entity, false);

            return Redirect(vm.RedirectBackPage);
        }

        #region Select Charity
        //[AuthorityFilterFactory(Function = Secure.TerminalSetupGUIMerchantCharity, Action = Secure.ViewAction)]
        public ActionResult GlobalCharitySelect()
        {
            var availableCharities = _charitySsCtxService.GetAvailableGlobalCharities(_terminalMasterId);

            var model = new MerchantCharityModel
            {
                TerminalId = _terminalMasterId,
                MerchantCharities = new ViewItemsModel
                {
                    Items = availableCharities
                }
            };

            model.RedirectUrl = Url.Action("CharitySetup", new { id = model.TerminalId });

            return PartialView(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        //[AuthorityFilterFactory(Function = Secure.TerminalSetupGUIMerchantCharity, Action = Secure.AddAction)]
        public ActionResult GlobalCharitySelect(string IdSelectedList, MerchantCharityModel vm)
        {
            var charityIds = WebUtils.ConvertStringToInts(IdSelectedList);

            if (charityIds == null || charityIds.Count == 0)
                return Redirect(vm.RedirectUrl);

            var selectedList = _charitySsCtxService.GetById(charityIds);

            // insert linking charity
            var newCharities = selectedList.Select(item => new Charity
            {
                lszCharityName = item.lszCharityName,
                TerminalId = vm.TerminalId,
                ParentCharityId = item.Id,
                IsActive = true,
                fEnabled = item.fEnabled,
                fAdvertising = item.fAdvertising,
                AdvertisingImage = item.AdvertisingImage,
                BannerImage = item.BannerImage,
                IsStatus = MMSConstants.PRE_INSERT_RECORD,
                IsUpdated = false,
            }).ToList();

            _charityService.InsertAll(newCharities);

            return Redirect(vm.RedirectUrl);
        }
        #endregion

        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUICharitySetup, Action = Secure.ViewAction)]
        public ActionResult AddNewCharity(int id)
        {
            var model = new CharityModel
            {
                TerminalId = id,
            };

            return PartialView(model);
        }

        [HttpPost]
        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUICharitySetup, Action = Secure.AddAction)]
        public async Task<ActionResult> AddNewCharity(CharityModel model)
        {
            var entity = new Charity
            {
                TerminalId = model.TerminalId,
                lszCharityName = model.lszCharityName,
                fEnabled = model.fEnabled,
                IsActive = true,
                IsStatus = MMSConstants.PRE_INSERT_RECORD,
                BannerImage = await WebUtils.PathCharityFolder(model.BannerImage, model.Banner),
                AdvertisingImage = await WebUtils.PathCharityFolder(model.AdvertisingImage, model.Advertising)
            };

            _charitySsCtxService.Insert(entity);

            return RedirectToAction("CharitySetup", new { id = model.TerminalId });
        }

        // charity detail for update
        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUICharitySetup, Action = Secure.ViewAction)]
        public ActionResult EditCharity(int terminalMasterId, int charityId = 0)
        {
            var charity = _charitySsCtxService.GetById(charityId, includes: c => c.ParentCharity);

            if (charity is null)
            {
                charity = new Charity(terminalMasterId, MMSConstants.DELETE_RECORD);
                _charityService.Insert(charity);
            }

            var model = new CharityModel();

            if (!charity.IsUpdated)
            {
                charity = charity.LoadDataFromGlobal(charity.ParentCharity);
                model.CurrentCharityId = charityId;
                model.ParentCharityId = charity.ParentCharityId;
            }

            model.SetModel(charity);
            model.RedirectUrl = Url.Action("CharitySetup", new { id = charity.TerminalId });

            return PartialView(model);
        }

        [HttpPost]
        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUICharitySetup, Action = Secure.AddAction)]
        public async Task<ActionResult> EditCharity(CharityModel model)
        {
            if (model.CurrentCharityId.HasValue)
            {
                var currentCharity = _charitySsCtxService.GetById(model.CurrentCharityId.Value);

                if (currentCharity is null)
                    return RedirectToAction("Index", "Error");

                if (currentCharity.IsStatus == MMSConstants.DELETE_RECORD)
                {
                    currentCharity.IsStatus = MMSConstants.PRE_INSERT_RECORD;
                    _charityService.Update(currentCharity);
                }

                currentCharity = model.GetMaster(currentCharity);

                if (model.Banner is not null)
                    currentCharity.BannerImage = await WebUtils.PathCharityFolder(model.BannerImage, model.Banner);

                if (model.Advertising is not null)
                    currentCharity.AdvertisingImage = await WebUtils.PathCharityFolder(model.AdvertisingImage, model.Advertising);

                _charitySsCtxService.Update(currentCharity, false);

                // Copy charity amounts
                var charityAmountList = _charitySsCtxService.GetAmountsByCharityId(model.ParentCharityId.Value);

                var copyAmounts = charityAmountList.Select(charityAmount => new CharityAmount
                {
                    Amount = charityAmount.Amount,
                    CharityId = currentCharity.Id,
                    IsStatus = Core.CoreUTI.Constants.PRE_INSERT_RECORD,
                    IsActive = true,
                }).ToList();

                _charityAmountService.InsertAll(copyAmounts);

                currentCharity.IsUpdated = true;
                _charitySsCtxService.Update(currentCharity, false);
            }
            else
            {
                var charity = _charitySsCtxService.GetById(model.Id);

                if (charity is null)
                    return RedirectToAction("Index", "Error");

                if (charity.IsStatus == MMSConstants.DELETE_RECORD)
                {
                    charity.IsStatus = MMSConstants.PRE_INSERT_RECORD;
                    _charityService.Update(charity);
                }

                charity = model.GetMaster(charity);

                if (model.Banner is not null)
                    charity.BannerImage = await WebUtils.PathCharityFolder(model.BannerImage, model.Banner);

                if (model.Advertising is not null)
                    charity.AdvertisingImage = await WebUtils.PathCharityFolder(model.AdvertisingImage, model.Advertising);

                _charitySsCtxService.Update(charity, false);
            }

            return Redirect(model.RedirectUrl);
        }

        [HttpPost]
        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUICharitySetup, Action = Secure.RemoveAction)]
        public JsonResult CharityDelete(int id)
        {
            var charity = _charitySsCtxService.GetById(id);

            if (charity == null)
                return Json(false);

            try
            {
                charity.IsStatus = MMSConstants.PRE_DELETE_RECORD;
                _charitySsCtxService.Update(charity, false);
            }
            catch (Exception)
            {
                throw;
            }

            return Json(true);
        }

        [AuthorityFilterFactory(
            Function = Secure.TerminalSetupGUICharityAmount,
            ParentId = Secure.TerminalSetupGUICharitySetup,
            Action = Secure.ViewAction,
            IsRoot = true,
            GroupId = FilterGroupType.Terminal,
            ActionType = Secure.FullAction)]
        [SecurityCustomName("Charity Amount")]
        public ActionResult CharityAmount(int charityId = 0)
        {
            var charity = _charitySsCtxService.GetById(charityId);

            var charityAmountList = _charitySsCtxService.GetAmountsByCharityId(charityId);

            if (!charity.IsUpdated)
            {
                charityAmountList = _charitySsCtxService.GetAmountsByCharityId(charity.ParentCharityId.Value);
            }

            var modelList = new List<CharityAmountModel>();

            foreach (var item in charityAmountList)
            {
                var model = new CharityAmountModel(charityId);
                model.SetModel(item);
                modelList.Add(model);
            }

            var viewModel = new CharityAmountViewModel
            {
                CharityId = charityId,
                AmountList = modelList
            };

            return PartialView(viewModel);
        }

        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUICharityAmount, Action = Secure.ViewAction)]
        public ActionResult AddNewAmount(int charityId = 0)
        {
            var charity = _charitySsCtxService.GetById(charityId);

            if (charity is null)
                return RedirectToAction("Index", "Error");

            var model = new CharityAmountModel(charityId);

            if (!charity.IsUpdated)
            {
                model.CurrentCharityId = charityId;
                model.ParentCharityId = charity.ParentCharityId;
            }
            
            model.RedirectUrl = Url.Action("CharityAmount", new { charityId = model.CharityId });

            return PartialView(model);
        }

        [HttpPost]
        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUICharityAmount, Action = Secure.AddAction)]
        public ActionResult AddNewAmount(CharityAmountModel model)
        {
            if (model.CurrentCharityId.HasValue)
            {
                var currentCharity = _charitySsCtxService.GetById(model.CurrentCharityId.Value);

                // Copy charity amounts
                var charityAmountList = _charitySsCtxService.GetAmountsByCharityId(model.ParentCharityId.Value);

                var copyAmounts = charityAmountList.Select(charityAmount => new CharityAmount
                {
                    Amount = charityAmount.Amount,
                    CharityId = currentCharity.Id,
                    IsStatus = Core.CoreUTI.Constants.PRE_INSERT_RECORD,
                    IsActive = true,
                }).ToList();

                copyAmounts.Add(new CharityAmount
                {
                    Amount = model.Amount,
                    CharityId = currentCharity.Id,
                    IsStatus = Core.CoreUTI.Constants.PRE_INSERT_RECORD,
                    IsActive = true,
                });

                _charityAmountService.InsertAll(copyAmounts);

                currentCharity.IsUpdated = true;
                _charitySsCtxService.Update(currentCharity, false);
            }
            else
            {
                var charity = _charitySsCtxService.GetById(model.CharityId);

                if (charity is null)
                    return RedirectToAction("Index", "Error");

                var newAmount = new CharityAmount
                {
                    Amount = model.Amount,
                    CharityId = model.CharityId,
                    IsActive = true,
                    IsStatus = MMSConstants.PRE_INSERT_RECORD,
                };

                _charityAmountService.Insert(newAmount);
            }

            return Redirect(model.RedirectUrl);
        }

        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUICharityAmount, Action = Secure.ViewAction)]
        public ActionResult CharityAmountUpdate(int amountId = 0, int charityId = 0)
        {
            var amount = _charityAmountSsCtxService.GetById(amountId);

            if (amount is null)
            {
                return RedirectToAction("CharityAmount", new { charityId });
            }

            var charity = _charitySsCtxService.GetById(charityId);

            var model = new CharityAmountModel(charityId);
            model.SetModel(amount);
            model.RedirectUrl = Url.Action("CharityAmount", new { charityId = model.CharityId });

            if (!charity.IsUpdated)
            {
                model.CurrentCharityId = charityId;
                model.ParentCharityId = amount.CharityId;
            }

            return PartialView(model);
        }

        [HttpPost]
        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUICharityAmount, Action = Secure.EditAction)]
        public ActionResult CharityAmountUpdate(CharityAmountModel model)
        {
            if (model.CurrentCharityId.HasValue)
            {
                var currentCharity = _charitySsCtxService.GetById(model.CurrentCharityId.Value);

                // Copy charity amounts
                var charityAmountList = _charitySsCtxService.GetAmountsByCharityId(model.ParentCharityId.Value);

                var copyAmounts = charityAmountList.Select(charityAmount => new CharityAmount
                {
                    Amount = charityAmount.Id == model.Id ? model.Amount : charityAmount.Amount,
                    CharityId = currentCharity.Id,
                    IsStatus = Core.CoreUTI.Constants.PRE_INSERT_RECORD,
                    IsActive = true,
                }).ToList();

                _charityAmountService.InsertAll(copyAmounts);

                currentCharity.IsUpdated = true;
                _charitySsCtxService.Update(currentCharity, false);
            }
            else
            {
                var amount = _charityAmountSsCtxService.GetById(model.Id);

                if (amount is not null)
                {
                    amount.Amount = model.Amount;
                    _charityAmountSsCtxService.Update(amount, false);
                }
            }

            return Redirect(model.RedirectUrl);
        }

        [HttpPost]
        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUICharityAmount, Action = Secure.RemoveAction)]
        public JsonResult CharityAmountDelete(int id, int charityId)
        {
            int? parentCharityId = null;

            var currentCharity = _charitySsCtxService.GetById(charityId);

            if (!currentCharity.IsUpdated)
            {
                parentCharityId = currentCharity.ParentCharityId;
            }

            if (parentCharityId.HasValue)
            {
                // Copy charity amounts
                var charityAmountList = _charitySsCtxService.GetAmountsByCharityId(parentCharityId.Value);

                var copyAmounts = charityAmountList.Where(p => p.Id != id).Select(charityAmount => new CharityAmount
                {
                    Amount = charityAmount.Amount,
                    CharityId = currentCharity.Id,
                    IsStatus = Core.CoreUTI.Constants.PRE_INSERT_RECORD,
                    IsActive = true,
                }).ToList();

                _charityAmountService.InsertAll(copyAmounts);

                currentCharity.IsUpdated = true;
                _charitySsCtxService.Update(currentCharity, false);
            }
            else
            {
                var amount = _charityAmountSsCtxService.GetById(id);

                if (amount is null)
                    return Json(false);
                try
                {
                    amount.IsStatus = MMSConstants.PRE_DELETE_RECORD;
                    _charityAmountSsCtxService.Update(amount, false);
                }
                catch (Exception)
                {
                    throw;
                }
            }

            return Json(true);
        }

        #endregion

        #region Country Setup
        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUICountrySetup, Action = Secure.ViewAction, ParentId = Secure.TerminalSetupGUIGlobalSetup, IsRoot = true, ActionType = Secure.FullAction, GroupId = FilterGroupType.Terminal)]
        [SecurityCustomName("Country Setup")]
        public ActionResult CountrySetup()
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            return PartialView();
        }

        public ActionResult CountryCurrency()
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            var terminalMaster = _terminalSsCtxService.GetById(_terminalMasterId, p => p.CountryTerminal);
            var entity = terminalMaster.CountryTerminal;
            if (entity == null)
            {
                entity = new CountryTerminal();
                _countryTerminalService.Insert(entity);

                terminalMaster.CountryTerminalId = entity.Id;
                _terminalSsCtxService.Update(terminalMaster);
            }

            var country = CountryConstants.GetCountries().FirstOrDefault(p => p.usCountryCode == entity.usCountryCode);
            country ??= new Country();

            var alNotesDenominations = _alNotesDenominationsSsCtxService.FirstOrDefault(p => p.Id == entity.alNotesDenominationId);
            var alCoinDenominations = _alCoinDenominationsSsCtxService.FirstOrDefault(p => p.Id == entity.alCoinDenominationId);

            var vm = new CountryTerminalViewModel(country, entity.Id, entity.fShowCurrencySymbol, alNotesDenominations, alCoinDenominations)
            {
                RedirectUrl = Url.Action("SetupMenu", "TerminalSetupGUI", new { id = _terminalMasterId })
            };

            return PartialView(vm);
        }

        [HttpPost]
        public ActionResult CountryCurrency(CountryTerminalViewModel vm)
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            var entity = _countryTerminalSsCtxService.GetById(vm.Id);
            if (entity != null)
            {
                entity.fShowCurrencySymbol = vm.fShowCurrencySymbol;

                var alNotesDenominations = _alNotesDenominationsSsCtxService.FirstOrDefault(p => p.Id == entity.alNotesDenominationId);
                var alCoinDenominations = _alCoinDenominationsSsCtxService.FirstOrDefault(p => p.Id == entity.alCoinDenominationId);

                if (alNotesDenominations == null)
                {
                    alNotesDenominations = new Core.Entities.alNotesDenominations()
                    {
                        usCountryCode = vm.CountryCode
                    };

                    alNotesDenominations = vm.SetDataAlNotesDenominations(alNotesDenominations);
                    _alNotesDenominationsService.Insert(alNotesDenominations);
                }
                else
                {
                    alNotesDenominations = vm.SetDataAlNotesDenominations(alNotesDenominations);
                    alNotesDenominations.usCountryCode = vm.CountryCode;
                    _alNotesDenominationsSsCtxService.Update(alNotesDenominations, false);
                }

                if (alCoinDenominations == null)
                {
                    alCoinDenominations = new Core.Entities.alCoinDenominations()
                    {
                        usCountryCode = vm.CountryCode
                    };

                    alCoinDenominations = vm.SetDataAlCoinDenominations(alCoinDenominations);
                    _alCoinDenominationsService.Insert(alCoinDenominations);
                }
                else
                {
                    alCoinDenominations = vm.SetDataAlCoinDenominations(alCoinDenominations);
                    alCoinDenominations.usCountryCode = vm.CountryCode;
                    _alCoinDenominationsSsCtxService.Update(alCoinDenominations, false);
                }

                entity.alCoinDenominationId = alCoinDenominations.Id;
                entity.alNotesDenominationId = alNotesDenominations.Id;
                _countryTerminalSsCtxService.Update(entity, false);
            }

            return Redirect(vm.RedirectUrl);
        }

        public ActionResult Country()
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            var country = CountryConstants.GetCountries();
            var vm = new CountryTerminalViewModel(country);

            vm.RedirectBackPage = Url.Action("SetupMenu", "TerminalSetupGUI", new { id = _terminalMasterId });

            return PartialView(vm);
        }

        public ActionResult CountrySelect(int usCountryCode)
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            var terminalMaster = _terminalSsCtxService.GetById(_terminalMasterId, p => p.CountryTerminal);

            var entity = terminalMaster.CountryTerminal != null ? terminalMaster.CountryTerminal : new CountryTerminal(MMSConstants.NOTCHANGE_RECORD);

            var country = CountryConstants.GetCountries().FirstOrDefault(p => p.usCountryCode == usCountryCode);
            if (country == null)
            {
                country = new Country();
            }

            if (terminalMaster.CountryTerminal == null)
            {
                _countryTerminalSsCtxService.Insert(entity);
                terminalMaster.CountryTerminalId = entity.Id;
                _terminalSsCtxService.Update(terminalMaster);
            }

            entity = country.ToEntity(entity);
            _countryTerminalSsCtxService.Update(entity, false);

            var url = Url.Action("CountryCurrency", "TerminalSetupGUI");
            return Redirect(url);
        }

        public ActionResult CountryCardSetup()
        {
            if (_terminalMasterId == null)
            {
                return RedirectToAction("Index", "Error");
            }

            var usCoutryCodes = _countryCardSetupTerminalSsCtxService.GetByTerminalMasterId(_terminalMasterId);

            var allCountry = CountryConstants.GetCountries();
            var countries = CountryCardSetupViewModel.GetCountriesByCountryCodes(allCountry, usCoutryCodes);

            var vm = new CountryCardSetupViewModel(countries);

            vm.RedirectBackPage = Url.Action("CountrySetup", "TerminalSetupGUI");

            return PartialView(vm);
        }

        public ActionResult AddCountryCardSetup(int usCountryCode = 0)
        {
            if (_terminalMasterId == null)
            {
                return RedirectToAction("Index", "Error");
            }

            var entity = _countryCardSetupTerminalSsCtxService.GetByCountryCodeAndTerminalMasterId(_terminalMasterId, usCountryCode);
            if (entity == null)
            {
                entity = new CountryCardSetupTerminal();
            }

            var allCountry = CountryConstants.GetCountries();

            var listCountryCodeExsit = _countryCardSetupTerminalSsCtxService.GetByTerminalMasterId(_terminalMasterId);

            var countries = CountryCardSetupViewModel.GetCountriesNotExist(allCountry, listCountryCodeExsit, entity.usCountryCode);

            var vm = new CountryCardSetupViewModel(entity.Id, entity.usCountryCode, countries);

            vm.RedirectBackPage = Url.Action("CountrySetup", "TerminalSetupGUI");

            return PartialView(vm);
        }

        [HttpPost]
        public ActionResult AddCountryCardSetup(CountryCardSetupViewModel vm)
        {
            if (_terminalMasterId == null)
            {
                return RedirectToAction("Index", "Error");
            }

            var entity = _countryCardSetupTerminalSsCtxService.GetById(vm.CountryCardSetupId);
            if (entity == null)
            {
                entity = new CountryCardSetupTerminal(_terminalMasterId, vm.CountryCode, MMSConstants.PRE_INSERT_RECORD);
                _countryCardSetupTerminalSsCtxService.Insert(entity);
            }
            else
            {
                entity.usCountryCode = vm.CountryCode;
                _countryCardSetupTerminalSsCtxService.Update(entity, false);
            }

            return Redirect(vm.RedirectBackPage);
        }

        [HttpPost]
        public JsonResult DeleteCountryCardSetup(int id = 0)
        {
            var entity = _countryCardSetupTerminalSsCtxService.GetByCountryCodeAndTerminalMasterId(_terminalMasterId, id);
            if (entity == null)
            {
                return Json(false);
            }
            entity.IsStatus = MMSConstants.PRE_DELETE_RECORD;
            _countryCardSetupTerminalSsCtxService.Update(entity);
            return Json(true);
        }

        #endregion

        #region Date TimeZone
        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUIDateTimeZone, Action = Secure.ViewAction, ParentId = Secure.TerminalSetupGUIGlobalSetup, IsRoot = true, ActionType = Secure.FullAction, GroupId = FilterGroupType.Terminal)]
        [SecurityCustomName("Date Time Zone")]
        public ActionResult DateTimeZone()
        {
            if (_terminalMasterId == null)
            {
                return RedirectToAction("Index", "Error");
            }

            var dateTimeZone = _terminalSsCtxService.GetDateTimeZoneByTerminalId(_terminalMasterId);
            var timeZones = _timeZoneMasterService.GetAll(includeDeactivated: true);
            var model = new DateTimeZoneModel(dateTimeZone)
            {
                TerminalId = _terminalMasterId,
                TimeZones = timeZones,
            };
            var vm = new TimeZoneViewModel(model, timeZones);
            vm.RedirectBackPage = Url.Action("SetupMenu", "TerminalSetupGUI", new { id = _terminalMasterId });

            return PartialView(vm);
        }

        public async Task<JsonResult> GetTimeZone(int idTimeZone)
        {
            var entity = _timeZoneMasterService.GetById(idTimeZone);

            TimeZoneInfo timeZoneInfo = TimeZoneInfo.Local;

            if (entity != null)
            {
                timeZoneInfo = TimeZoneInfo.FindSystemTimeZoneById(entity.FullName);
            }
            DateTime currentDateTime = TimeZoneInfo.ConvertTime(DateTime.Now, TimeZoneInfo.Local, timeZoneInfo);

            var result = new TimeZoneViewModel(currentDateTime);

            return GetSuccessResult(result);
        }

        [HttpPost]
        public ActionResult DateTimeZone(TimeZoneViewModel vm)
        {
            if (_terminalMasterId == null)
            {
                return RedirectToAction("Index", "Error");
            }

            var dateTimeZone = _terminalSsCtxService.GetDateTimeZoneByTerminalId(_terminalMasterId);
            var timeZoneSelect = vm.DateTimeZone.SelectedTimeZone != 0 ? vm.DateTimeZone.SelectedTimeZone : (int?)null;

            if (dateTimeZone == null)
            {
                dateTimeZone = new DateTimeZoneTerminal(timeZoneSelect, vm.DateTimeZone.DayLightSaving, MMSConstants.PRE_INSERT_RECORD);
                _dateTimeZoneTerminalSsCtxService.Insert(dateTimeZone);

            }
            else
            {
                dateTimeZone.usTimezone = timeZoneSelect;
                dateTimeZone.fDaylightSaving = vm.DateTimeZone.DayLightSaving;
                _dateTimeZoneTerminalSsCtxService.Update(dateTimeZone, false);

            }

            var terminalMaster = _terminalSsCtxService.GetById(_terminalMasterId);
            terminalMaster.DateId = dateTimeZone.Id;

            _terminalSsCtxService.Update(terminalMaster, false);

            return Redirect(vm.RedirectBackPage);
        }
        #endregion

        #region Status Bar
        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUIStatusBar, Action = Secure.ViewAction, ParentId = Secure.TerminalSetupGUIGlobalSetup, IsRoot = true, ActionType = Secure.FullAction, GroupId = FilterGroupType.Terminal)]
        [SecurityCustomName("Status Bar")]
        public ActionResult StatusBar()
        {
            if (_terminalMasterId == null)
            {
                return RedirectToAction("Index", "Error");
            }

            var terminalMaster = _terminalSsCtxService.GetById(_terminalMasterId);
            if (terminalMaster == null)
            {
                terminalMaster = new TerminalMaster();
            }

            var model = new StatusBarModel(terminalMaster.ShowDate, terminalMaster.ShowTime);

            model.RedirectBackPage = Url.Action("SetupMenu", "TerminalSetupGUI", new { id = _terminalMasterId });

            return PartialView(model);
        }

        [HttpPost]
        public ActionResult StatusBar(StatusBarModel model)
        {
            if (_terminalMasterId == null)
            {
                return RedirectToAction("Index", "Error");
            }

            var terminalMaster = _terminalSsCtxService.GetById(_terminalMasterId);

            terminalMaster.ShowDate = model.ShowDate;
            terminalMaster.ShowTime = model.ShowTime;

            _terminalSsCtxService.Update(terminalMaster, false);

            return Redirect(model.RedirectBackPage);
        }
        #endregion

        #region FallBack Setting
        public ActionResult FallBack()
        {
            if (!IsAccessTerminalAccess(_terminalMasterId, Secure.FunctionTerminalFallBack, Secure.ViewAction))
                return RedirectToAction("Index", "Error");

            var terminalMaster = _terminalSsCtxService.GetById(_terminalMasterId, p => p.FallBack);

            var fallback = new FallBackTerminal() { IsStatus = MMSConstants.NOTCHANGE_RECORD };

            if (terminalMaster.FallBack == null)
            {
                _fallBackTerminalService.Insert(fallback);

                terminalMaster.FallBackId = fallback.Id;
                terminalMaster.FallBack = fallback;

                _terminalSsCtxService.Update(terminalMaster, false);

            }
            else
            {
                fallback = terminalMaster.FallBack;
            }

            var model = new FallBackModel(fallback)
            {
                TerminalId = _terminalMasterId,
            };

            model.RedirectUrl = Url.Action("SetupMenu", "TerminalSetupGUI", new { id = _terminalMasterId });

            return PartialView(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult FallBack(FallBackModel model)
        {
            if (!IsAccessTerminalAccess(_terminalMasterId, Secure.FunctionTerminalFallBack, Secure.EditAction))
                return RedirectToAction("Index", "Error");

            var fallBack = _fallBackTerminalSsCtxService.GetById(model.Id);
            if (fallBack != null)
            {
                fallBack = model.GetFallBackTerminal(fallBack, false);
                _fallBackTerminalSsCtxService.Update(fallBack, false);

                Utils.WebUtils.SetSession<bool>(Constants.FallBackSubmited, true);

            }

            return Redirect(model.RedirectUrl);
        }


        public ActionResult FallBackSetup(CardsAcceptedType typeCard)
        {
            if (!IsAccessTerminalAccess(_terminalMasterId, Secure.FunctionTerminalFallBack, Secure.ViewAction))
                return RedirectToAction("Index", "Error");

            FallBackTerminal fallback;
            if (CheckReviewFutureUpdate(Constants.FallBackSubmited))
            {
                var terminalMaster = GetTerminalMasterBySession();
                if (terminalMaster == null)
                {
                    return RedirectToAction("FutureUpdate", new { terminalId = _terminalMasterId });
                }
                fallback = terminalMaster.FallBack;
            }
            else
            {
                fallback = _terminalSsCtxService.GetFallBackByTerminalId(_terminalMasterId);
            }
            var model = new FallBackModel(fallback)
            {
                TypeCard = typeCard,
                TerminalId = _terminalMasterId,
            };
            model.RedirectUrl = Url.Action("SetupMenu", "TerminalSetupGUI", new { id = _terminalMasterId });
            return PartialView(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult FallBackSetup(FallBackModel model)
        {
            if (!IsAccessTerminalAccess(_terminalMasterId, Secure.FunctionTerminalFallBack, Secure.EditAction))
                return RedirectToAction("Index", "Error");

            var fallBack = _fallBackTerminalSsCtxService.GetById(model.Id);
            if (fallBack != null)
            {
                fallBack = model.GetFallBackTerminal(fallBack, true);
                _fallBackTerminalSsCtxService.Update(fallBack, false);

                Utils.WebUtils.SetSession<bool>(Constants.FallBackSubmited, true);

            }

            return Redirect(model.RedirectUrl);
        }

        #endregion

        #region Terminal security Level Setting
        public ActionResult TerminalSecurityAccess()
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            var entities = _terminalUserSecurityAccessSsCtxService.GetAll(p => p.TerminalId == _terminalMasterId);

            var items = entities.ToListModel();

            var vm = new TerminalSecurityAccessViewModel(items);

            return PartialView(vm);
        }

        public ActionResult AddTerminalTemplate(int terminalTemplateId = 0)
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }
            var model = new Models.TerminalSetupGUI.TerminalSecurityAccessModel();

            var entity = _terminalUserSecurityAccessSsCtxService.GetById(terminalTemplateId);
            if (entity == null)
            {
                entity = new TerminalUserSecurityAccess(_terminalMasterId);
                entity.IsStatus = MMSConstants.DELETE_RECORD;
                _terminalUserSecurityAccessSsCtxService.Insert(entity);
            }
            model = entity.ToModel();

            var templates = _terminalUserSecurityAccessSsCtxService.GetAll(p => p.TerminalId == _terminalMasterId);

            var vm = new TerminalSecurityAccessViewModel(model, templates, terminalTemplateId);

            vm.RedirectUrl = Url.Action("TerminalSecurityAccess", "TerminalSetupGUI");

            return PartialView(vm);
        }


        [HttpPost]
        public async Task<ActionResult> AddTerminalTemplate(TerminalSecurityAccessViewModel vm, [FromForm] IFormFile templateIcon)
        {

            var entity = _terminalUserSecurityAccessSsCtxService.GetById(vm.TerminalSecurityAccess.Id);

            if (entity.IsStatus == MMSConstants.DELETE_RECORD)
            {
                entity.IsStatus = MMSConstants.PRE_INSERT_RECORD;
                _terminalUserSecurityAccessSsCtxService.Update(entity);
            }

            var stringImageUrl = "";
            if (templateIcon != null)
            {
                stringImageUrl = await WebUtils.PathMerchantFolder(vm.TerminalSecurityAccess.IconTemplate, templateIcon);
                entity.IconTemplate = stringImageUrl;
            }

            if (vm.TemplateCopyId != 0)
            {
                var entityCopySetting = _terminalUserSecurityAccessSsCtxService.GetById(vm.TemplateCopyId);
                entity.CoppySetting(entityCopySetting);
            }

            entity = vm.TerminalSecurityAccess.ToEntity(entity);


            _terminalUserSecurityAccessSsCtxService.Update(entity, false);

            return Redirect(vm.RedirectUrl);
        }

        public ActionResult SecuritySettings(int terminalTemplateId)
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            var entity = _terminalUserSecurityAccessSsCtxService.GetById(terminalTemplateId);

            var items = MappingItemBoolean.ConvertToListBooleanItem(entity);

            var vm = new TerminalSecurityAccessViewModel(items, entity.Id);

            vm.RedirectUrl = Url.Action("AddTerminalTemplate", "TerminalSetupGUI", new { terminalTemplateId });

            return PartialView(vm);
        }

        [HttpPost]
        public ActionResult SecuritySettings(TerminalSecurityAccessViewModel vm)
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            var entity = _terminalUserSecurityAccessSsCtxService.GetById(vm.TerminalTemplateId);

            if (vm.Items != null)
                MappingItemBoolean.SetValueEntity(entity, vm.Items);

            _terminalUserSecurityAccessSsCtxService.Update(entity, false);

            return Redirect(vm.RedirectUrl);
        }

        [HttpPost]
        public JsonResult DeleteTerminalTemplate(int id = 0)
        {
            var item = _terminalUserSecurityAccessSsCtxService.GetById(id);
            if (item == null)
                return Json(false);
            try
            {
                item.IsStatus = MMSConstants.PRE_DELETE_RECORD;
                _terminalUserSecurityAccessSsCtxService.Update(item);
            }
            catch (Exception)
            {
                return Json(false);
            }

            return Json(true);
        }
        #endregion

        #region ECR Interface
        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUIECRInterface, Action = Secure.ViewAction, ParentId = Secure.TerminalSetupGUIGlobalSetup, IsRoot = true, ActionType = Secure.FullAction, GroupId = FilterGroupType.Terminal)]
        [SecurityCustomName("ECR Interface")]
        public ActionResult ECRInterface()
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            return PartialView();
        }

        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUIECRInterface, Action = Secure.ViewAction)]
        public ActionResult GetECRInterfaceItems()
        {
            var entities = _ECRInterfaceSsCtxService.GetAll(p => p.TerminalMasterId == _terminalMasterId);
            var items = entities.ToListItemModel();

            if (items == null || items.Count <= 0 || items.Count < Constants.LoadItem)
            {
                ViewBag.Index = -1;
            }
            else
            {
                ViewBag.Index = 1;
            }
            var model = new ViewItemsModel()
            {
                Items = items,
            };
            return PartialView(PartialViewConstants.ListTerminalMultiItemSortAndMenu, model);
        }

        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUIECRInterface, Action = Secure.ViewAction)]
        public ActionResult ERCInterfaceDetails(int idECRInterface = 0)
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            var entity = _ECRInterfaceSsCtxService.GetById(idECRInterface);
            if (entity == null)
            {
                entity = new ECRInterface(_terminalMasterId);
            }
            var model = new ECRInterfaceModel(entity);

            model.RedirectUrl = Url.Action("ECRInterface", "TerminalSetupGUI");

            return PartialView(model);
        }

        [HttpPost]
        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUIECRInterface, Action = Secure.AddAction)]
        public async Task<ActionResult> ERCInterfaceDetails(ECRInterfaceModel vm, [FromForm] IFormFile interfaceIconFile)
        {
            var isAdd = vm.Id.Equals(0);

            var entity = _ECRInterfaceSsCtxService.GetById(vm.Id);

            if (entity == null || isAdd)
            {
                entity = new ECRInterface(_terminalMasterId);
            }

            var logoImageUrl = "";
            if (interfaceIconFile != null)
            {
                logoImageUrl = await WebUtils.PathCharityFolder(vm.Icon, interfaceIconFile);
                entity.Icon = logoImageUrl;
            }

            entity = vm.ToEntity(entity);

            if (isAdd)
            {
                entity.IsStatus = MMSConstants.PRE_INSERT_RECORD;
                _ECRInterfaceSsCtxService.Insert(entity);
            }
            else
            {
                _ECRInterfaceSsCtxService.Update(entity, false);
            }

            return Redirect(vm.RedirectUrl);
        }

        [HttpPost]
        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUIECRInterface, Action = Secure.EditAction)]
        public JsonResult ERCInterfaceDelete(int id = 0)
        {
            var item = _ECRInterfaceSsCtxService.GetById(id);
            if (item == null)
                return Json(false);
            try
            {
                item.IsStatus = MMSConstants.PRE_DELETE_RECORD;
                _ECRInterfaceSsCtxService.Update(item);
            }
            catch (Exception)
            {
                return Json(false);
            }

            return Json(true);
        }
        #endregion

        #region ECRRequest
        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUIECRRequests, Action = Secure.ViewAction, ParentId = Secure.TerminalSetupGUIGlobalSetup, IsRoot = true, ActionType = Secure.FullAction, GroupId = FilterGroupType.Terminal)]
        [SecurityCustomName("ECR Request")]
        public ActionResult ECRRequests()
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            var entity = _ECRRequestSsCtxService.GetByTerminalMasterId(_terminalMasterId);

            if (entity == null)
            {
                entity = new ECRRequest(_terminalMasterId, MMSConstants.NOTCHANGE_RECORD);
                _ECRRequestService.Insert(entity);
            }
            var model = entity.ToModel();
            var items = MappingItemBoolean.ConvertToListBooleanItem(entity);

            var vm = new ECRRequestViewModel(model, items);

            vm.RedirectUrl = Url.Action("SetupMenu", "TerminalSetupGUI", new { id = _terminalMasterId });

            return PartialView(vm);
        }

        [HttpPost]
        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUIECRRequests, Action = Secure.AddAction)]
        public ActionResult ECRRequests(ECRRequestViewModel vm)
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            var entity = _ECRRequestSsCtxService.GetByTerminalMasterId(_terminalMasterId);

            vm.ECRRequestModel.ToEntity(entity);
            MappingItemBoolean.SetValueEntity(entity, vm.BooleanItems);

            _ECRRequestSsCtxService.Update(entity, false);

            return Redirect(vm.RedirectUrl);
        }

        public ActionResult HttpsServer(int ecrRequestId)
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            var entity = _systemPosHttpServerSsCtxService.FirstOrDefault(p => p.ECRRequestId == ecrRequestId);

            if (entity == null)
            {
                entity = new SystemPosHttpServer(ecrRequestId, MMSConstants.NOTCHANGE_RECORD);
            }

            var model = entity.ToModel();


            model.RedirectUrl = Url.Action("ECRRequests", "TerminalSetupGUI");

            return PartialView(model);
        }

        [HttpPost]
        public ActionResult HttpsServer(SystemPosHttpServerModel systemPosHttpServerModel)
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            var entity = _systemPosHttpServerSsCtxService.FirstOrDefault(p => p.ECRRequestId == systemPosHttpServerModel.ECRRequestId);

            if (entity == null)
            {
                entity = new SystemPosHttpServer(systemPosHttpServerModel.ECRRequestId.GetValueOrDefault(), MMSConstants.PRE_INSERT_RECORD);
                _systemPosHttpServerService.Insert(entity);
            }

            entity = systemPosHttpServerModel.ToEntity(entity);
            _systemPosHttpServerSsCtxService.Update(entity, false);

            return Redirect(systemPosHttpServerModel.RedirectUrl);
        }

        public ActionResult TerminalComIP(int ecrRequestId)
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            var entity = _systemPosHttpServerService.FirstOrDefault(p => p.ECRRequestId == ecrRequestId);

            if (entity == null)
            {
                entity = new SystemPosHttpServer(ecrRequestId, MMSConstants.NOTCHANGE_RECORD);
            }

            var model = entity.ToModel();


            model.RedirectUrl = Url.Action("ECRRequests", "TerminalSetupGUI");

            return PartialView(model);
        }

        public ActionResult RS232Comms(int ecrRequestId)
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            var entity = _systemPosHttpServerService.FirstOrDefault(p => p.ECRRequestId == ecrRequestId);

            if (entity == null)
            {
                entity = new SystemPosHttpServer(ecrRequestId, MMSConstants.NOTCHANGE_RECORD);
            }

            var model = entity.ToModel();


            model.RedirectUrl = Url.Action("ECRRequests", "TerminalSetupGUI");

            return PartialView(model);
        }

        #endregion

        #region Sounds in TerminalSetup
        public ActionResult Sounds()
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }
            var terminalMaster = _terminalSsCtxService.GetById(_terminalMasterId, x => x.Sound);

            var entitySound = terminalMaster.Sound;

            if (entitySound == null)
            {
                entitySound = new SoundTerminal();
            }

            var model = terminalMaster.Sound == null ? new Sound() : new Sound(terminalMaster.Sound);
            model.TerminalId = _terminalMasterId;

            model.RedirectUrl = Url.Action("TerminalSetup", "TerminalSetupGUI", new { terminalId = _terminalMasterId });

            return PartialView(model);
        }

        [HttpPost]
        public ActionResult Sounds(Sound sound)
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }
            var terminalMaster = _terminalSsCtxService.GetById(_terminalMasterId, x => x.Sound);

            var entitySound = terminalMaster.Sound;

            if (entitySound == null)
            {
                entitySound = new SoundTerminal(MMSConstants.PRE_INSERT_RECORD);
                entitySound = sound.GetSoundTerminal(entitySound);
                _soundTerminalSsCtxService.Insert(entitySound);

                terminalMaster.SoundId = entitySound.Id;
                _terminalSsCtxService.Update(terminalMaster, false);
            }
            else
            {
                entitySound = sound.GetSoundTerminal(entitySound);
                _soundTerminalSsCtxService.Update(entitySound, false);
            }

            return Redirect(sound.RedirectUrl);
        }
        #endregion

        #region Auto Login in TerminalSetup

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalAutoLogin, Action = Secure.ViewAction, ParentId = Secure.FunctionTerminalSetupFunction)]
        public ActionResult AutoLogin()
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            var terminalMaster = _terminalSsCtxService.GetById(_terminalMasterId, p => p.AutoLogin);
            var entity = terminalMaster.AutoLogin;

            var vm = new AutoLoginViewModel();

            if (entity != null)
            {
                vm.UserName = "None";

                var master = _autoLoginTerminalSsCtxService.GetById(entity.Id, p => p.User.User);
                if (master.User != null)
                {
                    vm.UserName = master.User.User.FullName;
                }
            }

            return PartialView(vm);
        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalAutoLogin, Action = Secure.ViewAction, ParentId = Secure.FunctionTerminalSetupFunction)]
        public ActionResult SelectUserAutoLogin()
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            var items = _userTerminalSsCtxService.GetUserTerminalByTerminalId("", 0, Constants.LoadItem, _terminalMasterId);

            var vm = new AutoLoginViewModel(items);

            return PartialView(vm);
        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalAutoLogin, Action = Secure.ViewAction, ParentId = Secure.FunctionTerminalSetupFunction)]
        public ActionResult UserAutoLogin(int userId)
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            var terminalMaster = _terminalSsCtxService.GetById(_terminalMasterId, p => p.AutoLogin);
            var entity = terminalMaster.AutoLogin;

            entity.UserId = userId;
            _autoLoginTerminalSsCtxService.Update(entity, false);

            return Json(true);
        }

        #endregion
        #region Wifi Config
        public ActionResult WifiConfig()
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            var terminalMaster = _terminalSsCtxService.GetById(_terminalMasterId, p => p.TerminalWifi);

            var entity = new TerminalWifi();

            if (terminalMaster.TerminalWifi != null)
            {
                entity = terminalMaster.TerminalWifi;
            }

            var model = new TerminalWifiModel(entity);

            model.RedirectUrl = Url.Action("TerminalSetup", "TerminalSetupGUI", new { terminalId = _terminalMasterId });

            return PartialView(model);
        }

        [HttpPost]
        public ActionResult WifiConfig(TerminalWifiModel vm)
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            var entity = _terminalWifiSsCtxService.GetById(vm.Id);

            if (entity == null)
            {
                entity = new TerminalWifi() { IsStatus = MMSConstants.PRE_INSERT_RECORD };
                _terminalWifiService.Insert(entity);

                var terminalMaster = _terminalSsCtxService.GetById(_terminalMasterId, x => x.TerminalWifi);

                terminalMaster.TerminalWifi = entity;
                terminalMaster.TerminalWifiId = entity.Id;
                _terminalSsCtxService.Update(terminalMaster, false);
            }

            entity = vm.ToEntity(entity);
            _terminalWifiSsCtxService.Update(entity, false);

            return Redirect(vm.RedirectUrl);
        }
        #endregion
        #region Ethernet Config
        public ActionResult EthernetConfig()
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            var terminalMaster = _terminalSsCtxService.GetById(_terminalMasterId, p => p.TerminalWAN);

            var entity = new TerminalWAN();

            if (terminalMaster.TerminalWAN != null)
            {
                entity = terminalMaster.TerminalWAN;
            }

            var model = new TerminalEthernetModel(entity);

            model.RedirectUrl = Url.Action("TerminalSetup", "TerminalSetupGUI", new { terminalId = _terminalMasterId });

            return PartialView(model);
        }

        [HttpPost]
        public ActionResult EthernetConfig(TerminalEthernetModel vm)
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            var entity = _terminalWANSsCtxService.GetById(vm.Id);

            if (entity == null)
            {
                entity = new TerminalWAN() { IsStatus = MMSConstants.PRE_INSERT_RECORD };
                _terminalWANService.Insert(entity);

                var terminalMaster = _terminalSsCtxService.GetById(_terminalMasterId, x => x.TerminalWifi);

                terminalMaster.TerminalWAN = entity;
                terminalMaster.TerminalEthernetId = entity.Id;
                _terminalSsCtxService.Update(terminalMaster, false);
            }

            entity = vm.ToEntity(entity);
            _terminalWANSsCtxService.Update(entity, false);

            return Redirect(vm.RedirectUrl);
        }

        #endregion
        #region Network Info in Terminal Setup
        public ActionResult NetworkInfo()
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            var terminalMaster = _terminalSsCtxService.GetById(_terminalMasterId, p => p.TerminalWAN);

            var terminalWAN = new TerminalEthernetModel();
            var terminalWifi = new TerminalWifiModel();

            if (terminalMaster.TerminalWAN != null)
            {
                terminalWAN = new TerminalEthernetModel(terminalMaster.TerminalWAN);
            }

            if (terminalMaster.TerminalWifi != null)
            {
                terminalWifi = new TerminalWifiModel(terminalMaster.TerminalWifi);
            }

            var vm = new NetworkInfoViewModel(terminalMaster.iInternetOption, terminalWAN, terminalWifi);

            vm.RedirectUrl = Url.Action("TerminalSetup", "TerminalSetupGUI", new { terminalId = _terminalMasterId });

            return PartialView(vm);
        }

        [HttpPost]
        public ActionResult NetworkInfo(NetworkInfoViewModel vm)
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            var terminalMaster = _terminalSsCtxService.GetById(_terminalMasterId);

            terminalMaster.iInternetOption = vm.iInternetOption;
            _terminalSsCtxService.Update(terminalMaster, false);

            return Redirect(vm.RedirectUrl);
        }

        #endregion

        #region Reboot Schedule
        [AuthorityFilterFactory(
            Function = Secure.TerminalSetupGUIRebootSchedule,
            Action = Secure.ViewAction,
            ParentId = Secure.TerminalSetupGUITerminalSetup,
            IsRoot = true,
            ActionType = Secure.FullAction,
            GroupId = FilterGroupType.Terminal)]
        [SecurityCustomName("Reboot Schedule")]
        public ActionResult RebootSchedule(int terminalMasterId = 0)
        {
            if (terminalMasterId <= 0)
                return RedirectToAction("Index", "Error");

            var entity = _terminalRebootScheduleSsCtxService.FirstOrDefault(r => r.TerminalMasterId == terminalMasterId);

            if (entity is null)
            {
                entity = new TerminalRebootSchedule(terminalMasterId);
                _terminalRebootScheduleService.Insert(entity);
            }

            var model = entity.ToModel();

            model.TerminalMasterId = entity.TerminalMasterId;
            model.RedirectUrl = Url.Action("TerminalSetup", new { terminalId = model.TerminalMasterId });

            return PartialView(model);
        }

        [HttpPost]
        [AuthorityFilterFactory(Action = Secure.AddAction, Function = Secure.TerminalSetupGUIRebootSchedule)]
        public ActionResult RebootSchedule(TerminalRebootScheduleModel model)
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values.SelectMany(ms => ms.Errors).Select(e => e.ErrorMessage).ToList();

                return ModelInvalidRequest(errors.FirstOrDefault() ?? "An unknown error occurred.");
            }

            var entity = _terminalRebootScheduleSsCtxService.GetById(model.Id);

            entity = model.ToEntity(entity);

            _terminalRebootScheduleSsCtxService.Update(entity, false);

            return Redirect(model.RedirectUrl);
        }

        #endregion

        #region Setup Base in Terminal Setup
        public ActionResult SetupBase()
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            return PartialView();
        }

        #endregion

        #region Cloud POS Integration in Terminal Setup
        [AuthorityFilterFactory(
            Function = Secure.TerminalSetupGUICloudPosIntegration,
            Action = Secure.ViewAction,
            ParentId = Secure.TerminalSetupGUIPosIntergration,
            IsRoot = true,
            ActionType = Secure.FullAction,
            GroupId = FilterGroupType.Terminal)]
        [SecurityCustomName("Cloud POS Integration")]
        public IActionResult CloudPosIntegration()
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            var terminalMaster = _terminalSsCtxService.GetById(_terminalMasterId, p => p.CloudPosIntegration);


            var entity = new CloudPosIntegrationTerminal();

            if (terminalMaster.CloudPosIntegration != null)
            {
                entity = terminalMaster.CloudPosIntegration;
            }

            var model = entity.ToModel();
            model.RedirectUrl = Url.Action("PosIntegration", "TerminalSetupGUI", new { terminalId = _terminalMasterId });

            return PartialView(model);
        }

        [HttpPost]
        [AuthorityFilterFactory(
            Function = Secure.TerminalSetupGUICloudPosIntegration,
            Action = Secure.AddAction,
            ParentId = Secure.TerminalSetupGUIPosIntergration,
            GroupId = FilterGroupType.Terminal)]
        public IActionResult CloudPosIntegration(CloudPosIntegrationModel vm)
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            var entity = _cloudPosIntegrationTerminalSsCtxService.GetById(vm.Id);

            if (entity == null)
            {
                entity = new CloudPosIntegrationTerminal(MMSConstants.PRE_INSERT_RECORD);
                entity = vm.ToEntity(entity);

                _cloudPosIntegrationTerminalSsCtxService.Insert(entity);

                var terminalMaster = _terminalSsCtxService.GetById(_terminalMasterId, p => p.CloudPosIntegration);

                terminalMaster.CloudPosIntegration = entity;
                terminalMaster.CloudPosIntegrationId = entity.Id;
                _terminalSsCtxService.Update(terminalMaster, false);
            }
            else
            {
                entity = vm.ToEntity(entity);
            }

            _cloudPosIntegrationTerminalSsCtxService.Update(entity, false);

            return Redirect(vm.RedirectUrl);
        }

        #endregion

        #region Direct POS Integration in Terminal Setup
        [AuthorityFilterFactory(
            Function = Secure.TerminalSetupGUIDirectPosIntegration,
            Action = Secure.ViewAction,
            ParentId = Secure.TerminalSetupGUIPosIntergration,
            IsRoot = true,
            ActionType = Secure.FullAction,
            GroupId = FilterGroupType.Terminal)]
        [SecurityCustomName("Direct POS Integration")]
        public IActionResult DirectPosIntegration()
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            var terminalMaster = _terminalSsCtxService.GetById(_terminalMasterId, p => p.PosIntegration);

            var entity = new PosIntegrationTerminal();

            if (terminalMaster.PosIntegration != null)
            {
                entity = terminalMaster.PosIntegration;
            }

            var model = entity.ToModel();
            model.RedirectUrl = Url.Action("PosIntegration", "TerminalSetupGUI", new { terminalId = _terminalMasterId });

            return PartialView(model);
        }

        [HttpPost]
        [AuthorityFilterFactory(
            Function = Secure.TerminalSetupGUIDirectPosIntegration,
            Action = Secure.AddAction,
            ParentId = Secure.TerminalSetupGUIPosIntergration,
            GroupId = FilterGroupType.Terminal)]
        public IActionResult DirectPosIntegration(PosIntegrationModel vm)
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            var entity = _posIntegrationTerminalSsCtxService.GetById(vm.Id);

            if (entity == null)
            {
                entity = new PosIntegrationTerminal(MMSConstants.PRE_INSERT_RECORD);
                entity = vm.ToEntity(entity);

                _posIntegrationTerminalSsCtxService.Insert(entity);

                var terminalMaster = _terminalSsCtxService.GetById(_terminalMasterId, p => p.PosIntegration);

                terminalMaster.PosIntegration = entity;
                terminalMaster.PosIntegrationId = entity.Id;
                _terminalSsCtxService.Update(terminalMaster, false);
            }
            else
            {
                entity = vm.ToEntity(entity);
            }

            _posIntegrationTerminalSsCtxService.Update(entity, false);

            return Redirect(vm.RedirectUrl);
        }

        #endregion

        #region Terminal Style
        [AuthorityFilterFactory(
            Function = Secure.TerminalSetupGUITerminalStyle,
            Action = Secure.ViewAction,
            ParentId = Secure.TerminalSetupGUITerminalSetup,
            IsRoot = true,
            ActionType = Secure.FullAction,
            GroupId = FilterGroupType.Terminal)]
        [SecurityCustomName("Terminal Style")]
        public IActionResult TerminalStyle()
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            var terminalMaster = _terminalSsCtxService.GetById(_terminalMasterId, p => p.TerminalStyle);

            var entity = terminalMaster.TerminalStyle ?? new TerminalStyle();

            var model = entity.ToModel();
            model.RedirectUrl = Url.Action("TerminalSetup", "TerminalSetupGUI", new { terminalId = _terminalMasterId });

            return PartialView(model);
        }

        [HttpPost]
        [AuthorityFilterFactory(
            Function = Secure.TerminalSetupGUITerminalStyle,
            Action = Secure.AddAction,
            ParentId = Secure.TerminalSetupGUITerminalSetup,
            GroupId = FilterGroupType.Terminal)]
        public IActionResult TerminalStyle(TerminalStyleModel vm)
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            var entity = _terminalStyleSsCtxServie.GetById(vm.Id);

            if (entity == null)
            {
                entity = new TerminalStyle(MMSConstants.PRE_INSERT_RECORD);
                entity = vm.ToEntity(entity);

                _terminalStyleSsCtxServie.Insert(entity);

                var terminalMaster = _terminalSsCtxService.GetById(_terminalMasterId, p => p.TerminalStyle);

                terminalMaster.TerminalStyle = entity;
                terminalMaster.TerminalStyleId = entity.Id;
                _terminalSsCtxService.Update(terminalMaster, false);
            }
            else
            {
                entity = vm.ToEntity(entity);
            }

            _terminalStyleSsCtxServie.Update(entity, false);

            return Redirect(vm.RedirectUrl);
        }
        #endregion

        #region Card Reader Mode in Terminal Setup
        [AuthorityFilterFactory(
            Function = Secure.TerminalSetupGUICardReaderMode,
            Action = Secure.ViewAction,
            ParentId = Secure.TerminalSetupGUITerminalSetup,
            IsRoot = true,
            ActionType = Secure.FullAction,
            GroupId = FilterGroupType.Terminal)]
        [SecurityCustomName("Card Reader Mode")]
        public IActionResult CardReaderMode()
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            var terminalMaster = _terminalSsCtxService.GetById(_terminalMasterId);

            if (terminalMaster == null) return RedirectToAction("Index", "Error");

            var terminalSetupId = terminalMaster.TerminalSetupId.GetValueOrDefault();

            if (terminalSetupId < 1) return RedirectToAction("Index", "Error");

            // old version using MerchantGeneralSetup
            var entity = _terminalSetupSsCtxService.GetById(terminalSetupId);
            var model = entity.ToCardReaderModeTerminalModel();

            model.RedirectUrl = Url.Action("TerminalSetup", "TerminalSetupGUI", new { terminalId = _terminalMasterId });

            return PartialView(model);
        }

        [HttpPost]
        [AuthorityFilterFactory(
            Function = Secure.TerminalSetupGUICardReaderMode,
            Action = Secure.AddAction,
            ParentId = Secure.TerminalSetupGUITerminalSetup,
            GroupId = FilterGroupType.Terminal)]
        public IActionResult CardReaderMode(CardReaderModeTerminalModel vm)
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            var terminalMaster = _terminalSsCtxService.GetById(_terminalMasterId);

            if (terminalMaster == null) return RedirectToAction("Index", "Error");

            var terminalSetupId = terminalMaster.TerminalSetupId.GetValueOrDefault();

            if (terminalSetupId < 1) return RedirectToAction("Index", "Error");

            var tmp = _terminalSetupSsCtxService.GetById(terminalSetupId);

            tmp.MsrReaderOnTimeout = vm.MsrReaderOnTimeout;
            tmp.ReadCtlsSwipeMode = vm.ReadCtlsSwipeMode;
            tmp.ContactlessDelayTimeout = vm.ContactlessDelayTimeout;

            _terminalSetupSsCtxService.Update(tmp, false);

            return Redirect(vm.RedirectUrl);
        }

        #endregion

        #region POS Intergration in Terminal Setup
        [AuthorityFilterFactory(
            Function = Secure.TerminalSetupGUIPosIntergration,
            Action = Secure.ViewAction,
            ParentId = Secure.TerminalSetupGUITerminalSetup,
            IsRoot = true,
            ActionType = Secure.FullAction,
            GroupId = FilterGroupType.Terminal)]
        [SecurityCustomName("POS Intergration")]
        public IActionResult PosIntegration()
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            var terminalMaster = _terminalSsCtxService.GetById(_terminalMasterId, p => p.UserTerminals);

            if (terminalMaster == null) return RedirectToAction("Index", "Error");

            var terminalSetupId = terminalMaster.TerminalSetupId.GetValueOrDefault();

            var model = new PosIntergationTerminalModel();

            if (terminalSetupId > 0)
            {
                // old version using MerchantGeneralSetup
                var entity = _terminalSetupSsCtxService.GetById(terminalSetupId);
                model.PosIntegrationProtocol = entity.PosIntegrationProtocol;
            }

            model.RedirectUrl = Url.Action("TerminalSetup", "TerminalSetupGUI", new { terminalId = _terminalMasterId });

            return PartialView(model);
        }

        [HttpPost]
        [AuthorityFilterFactory(
            Function = Secure.TerminalSetupGUIPosIntergration,
            Action = Secure.AddAction,
            ParentId = Secure.TerminalSetupGUITerminalSetup,
            GroupId = FilterGroupType.Terminal)]
        public IActionResult PosIntegration(PosIntergationTerminalModel vm)
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            var terminalMaster = _terminalSsCtxService.GetById(_terminalMasterId);

            if (terminalMaster == null) return RedirectToAction("Index", "Error");

            var terminalSetupId = terminalMaster.TerminalSetupId.GetValueOrDefault();

            if (terminalSetupId < 1) return RedirectToAction("Index", "Error");

            var tmp = _terminalSetupSsCtxService.GetById(terminalSetupId);

            tmp.PosIntegrationProtocol = (PosIntegrationProtocol)vm.PosIntegrationProtocol;

            _terminalSetupSsCtxService.Update(tmp, false);

            return Redirect(vm.RedirectUrl);
        }

        #endregion

        #region Terminal Advertising in Terminal Setup
        [AuthorityFilterFactory(
            Function = Secure.TerminalSetupGUITerminalAdvertising,
            Action = Secure.ViewAction,
            ParentId = Secure.TerminalSetupGUITerminalSetup,
            IsRoot = true,
            ActionType = Secure.FullAction,
            GroupId = FilterGroupType.Terminal)]
        [SecurityCustomName("Terminal Advertising")]
        public IActionResult TerminalAdvertising()
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            var terminalMaster = _terminalSsCtxService.GetById(_terminalMasterId);

            if (terminalMaster == null) return RedirectToAction("Index", "Error");

            var terminalSetupId = terminalMaster.TerminalSetupId.GetValueOrDefault();

            if (terminalSetupId < 1) return RedirectToAction("Index", "Error");

            // old version using MerchantGeneralSetup
            var entity = _terminalSetupSsCtxService.GetById(terminalSetupId);
            var model = entity.ToTerminalAdvertisingModel();

            model.RedirectUrl = Url.Action("TerminalSetup", "TerminalSetupGUI", new { terminalId = _terminalMasterId });

            return PartialView(model);
        }

        [HttpPost]
        [AuthorityFilterFactory(
            Function = Secure.TerminalSetupGUITerminalAdvertising,
            Action = Secure.AddAction,
            ParentId = Secure.TerminalSetupGUITerminalSetup,
            GroupId = FilterGroupType.Terminal)]
        public async Task<IActionResult> TerminalAdvertising(TerminalAdvertisingModel vm)
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            var terminalMaster = _terminalSsCtxService.GetById(_terminalMasterId);

            if (terminalMaster == null) return RedirectToAction("Index", "Error");

            var terminalSetupId = terminalMaster.TerminalSetupId.GetValueOrDefault();

            if (terminalSetupId < 1) return RedirectToAction("Index", "Error");

            var tmp = _terminalSetupSsCtxService.GetById(terminalSetupId);

            tmp.IsTerminalAdvertisingEnabled = vm.IsTerminalAdvertisingEnabled;

            if (vm.Advertising is not null)
                tmp.AdvertisingImageFiles = await WebUtils.PathCloudUserFolder(vm.AdvertisingImageFiles, vm.Advertising, "", UploadType.CompressFile);

            _terminalSetupSsCtxService.Update(tmp, false);

            return Redirect(vm.RedirectUrl);
        }

        #endregion

        #region Test Mode

        [AuthorityFilterFactory(
            Function = Secure.TerminalSetupGUITestMode,
            Action = Secure.ViewAction,
            ParentId = Secure.TerminalSetupGUITerminalSetup,
            IsRoot = true,
            ActionType = Secure.FullAction,
            GroupId = FilterGroupType.Terminal)]
        [SecurityCustomName("Test Mode")]
        public IActionResult TestMode()
        {
            if (_terminalMasterId <= 0) throw new ArgumentException("Invalid Terminal Master id");

            var terminalMaster = _terminalSsCtxService.GetById(_terminalMasterId, p => p.UserTerminals);
            if (terminalMaster == null) return RedirectToAction("Index", "Error");

            var terminalSetupId = terminalMaster.TerminalSetupId.GetValueOrDefault();
            if (terminalSetupId == 0) throw new ArgumentException(string.Format("Terminal master with id {0} has no Terminal Setup", _terminalMasterId));

            var entity = _terminalSetupSsCtxService.GetById(terminalSetupId);
            var model = new TestModeModel();
            model.ToModel(entity);
            model.RedirectUrl = Url.Action("TerminalSetup", "TerminalSetupGUI", new { terminalId = _terminalMasterId });

            return PartialView(model);
        }

        [HttpPost]
        [AuthorityFilterFactory(
            Function = Secure.TerminalSetupGUITestMode,
            Action = Secure.AddAction,
            ParentId = Secure.TerminalSetupGUITerminalSetup,
            GroupId = FilterGroupType.Terminal)]
        public IActionResult TestMode(TestModeModel vm)
        {
            if (_terminalMasterId <= 0) throw new ArgumentException("Invalid Terminal Master id");

            var terminalMaster = _terminalSsCtxService.GetById(_terminalMasterId);
            ArgumentNullException.ThrowIfNull(terminalMaster);

            var terminalSetupId = terminalMaster.TerminalSetupId.GetValueOrDefault();
            if (terminalSetupId < 1) throw new ArgumentException(string.Format("Terminal master with id {0} has no Terminal Setup", _terminalMasterId));

            var tmp = _terminalSetupSsCtxService.GetById(terminalSetupId);
            tmp = vm.ToEntity(tmp);
            _terminalSetupSsCtxService.Update(tmp, false);

            return Redirect(vm.RedirectUrl);
        }

        #endregion

        #region Pos Integrate Options

        [AuthorityFilterFactory(
            Function = Secure.TerminalSetupGUIPosIntegrateOptions,
            Action = Secure.ViewAction,
            ParentId = Secure.TerminalSetupGUITerminalSetup,
            IsRoot = true,
            ActionType = Secure.FullAction,
            GroupId = FilterGroupType.Terminal)]
        [SecurityCustomName("Pos Integrate Options")]
        public IActionResult PosIntegrateOptions()
        {
            if (_terminalMasterId <= 0) throw new ArgumentException("Invalid Terminal Master id");

            var terminalMaster = _terminalSsCtxService.GetById(_terminalMasterId, p => p.UserTerminals);
            if (terminalMaster == null) return RedirectToAction("Index", "Error");

            var terminalSetupId = terminalMaster.TerminalSetupId.GetValueOrDefault();
            if (terminalSetupId == 0) throw new ArgumentException(string.Format("Terminal master with id {0} has no Terminal Setup", _terminalMasterId));

            var entity = _terminalSetupSsCtxService.GetById(terminalSetupId);
            var model = new PosIntegrateOptionsModel();
            model.ToModel(entity);
            model.RedirectUrl = Url.Action("TerminalSetup", "TerminalSetupGUI", new { terminalId = _terminalMasterId });

            return PartialView(model);
        }

        [HttpPost]
        [AuthorityFilterFactory(
            Function = Secure.TerminalSetupGUIPosIntegrateOptions,
            Action = Secure.AddAction,
            ParentId = Secure.TerminalSetupGUITerminalSetup,
            GroupId = FilterGroupType.Terminal)]
        public IActionResult PosIntegrateOptions(PosIntegrateOptionsModel vm)
        {
            if (_terminalMasterId <= 0) throw new ArgumentException("Invalid Terminal Master id");

            var terminalMaster = _terminalSsCtxService.GetById(_terminalMasterId);
            ArgumentNullException.ThrowIfNull(terminalMaster);

            var terminalSetupId = terminalMaster.TerminalSetupId.GetValueOrDefault();
            if (terminalSetupId < 1) throw new ArgumentException(string.Format("Terminal master with id {0} has no Terminal Setup", _terminalMasterId));

            var tmp = _terminalSetupSsCtxService.GetById(terminalSetupId);
            tmp = vm.ToEntity(tmp);
            _terminalSetupSsCtxService.Update(tmp, false);

            return Redirect(vm.RedirectUrl);
        }

        #endregion

        #region Icon Images in Global
        [AuthorityFilterFactory(Function = Secure.TerminalSetupGUIIconImages, Action = Secure.ViewAction, ParentId = Secure.TerminalSetupGUIGlobalSetup, IsRoot = true, ActionType = Secure.FullAction, GroupId = FilterGroupType.Terminal)]
        [SecurityCustomName("Icon Images")]
        public ActionResult IconImages()
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }
            var entities = _iconImageGroupSsCtxService.GetAll(p => p.TerminalMasterId == _terminalMasterId);
            var models = entities.ToListModel();
            var vm = new IconImageViewModel(models);

            return PartialView(vm);
        }

        public ActionResult SetupGroupImage(int groupImageId = 0)
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            var model = new IconImageGroupModel();
            if (groupImageId >= 0)
            {
                var entity = _iconImageGroupSsCtxService.GetById(groupImageId);
                model = entity.ToModel();
            }

            var vm = new IconImageViewModel(model);

            return PartialView(vm);
        }

        [HttpPost]
        public ActionResult SetupGroupImage(IconImageViewModel vm)
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }
            var entity = _iconImageGroupSsCtxService.GetById(vm.IconImageGroup.Id);

            if (entity == null)
            {
                entity = new IconImageGroup(_terminalMasterId, MMSConstants.PRE_INSERT_RECORD);
                _iconImageGroupService.Insert(entity);
            }
            entity = vm.IconImageGroup.ToEntity(entity);
            _iconImageGroupSsCtxService.Update(entity, false);

            return PartialView(vm);
        }

        [HttpPost]
        public JsonResult DeleteGroupImage(int id = 0)
        {
            var entity = _iconImageGroupSsCtxService.GetById(id);
            if (entity == null)
            {
                return Json(false);
            }
            entity.IsStatus = MMSConstants.PRE_DELETE_RECORD;
            _iconImageGroupSsCtxService.Update(entity);
            return Json(true);
        }

        public ActionResult StockImages(int groupImageId)
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }
            var entities = _iconImageSsCtxService.GetListImageSelected(groupImageId, IconImageTypes.AssetIcon);
            var iconImageGroup = _iconImageGroupSsCtxService.GetById(groupImageId);

            var vm = new IconImageViewModel();

            if (entities.Any())
            {
                var path = IconImageViewModelEmm.GetPathImageByType(IconImageTypes.AssetIcon);
                vm.IconImages = entities.ToList();
                vm.ViewItemModels = vm.IconImages.ToListImageItemModel(path);
            }
            vm.IconImageGroup = iconImageGroup.ToModel();

            return PartialView(vm);
        }
        public ActionResult ViewImage(int imageId)
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }
            var entity = _iconImageSsCtxService.GetById(imageId);

            var model = new IconImageModel();
            if (entity != null)
            {
                model = entity.ToModel();
                model.Path = IconImageViewModelEmm.GetPathImageByType(entity.Type);
            }

            return PartialView(model);
        }

        public ActionResult StockImagesShowAll(int groupImageId)
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            var entity = _iconImageGroupSsCtxService.GetById(groupImageId);
            var model = entity.ToModel();

            return PartialView(model);
        }

        public ActionResult StockImagesSelect(int groupImageId)
        {
            var entities = _iconImageSsCtxService.GetListStringSelected(groupImageId, IconImageTypes.AssetIcon).ToList();

            var vm = new IconImageViewModel();
            vm.GetListImages();

            var path = IconImageViewModelEmm.GetPathImageByType(IconImageTypes.AssetIcon);
            var model = new ViewItemsModel()
            {
                Items = vm.ImagesFolder.ToListItemModel(path),
            };

            model.Items = model.Items.Select(x =>
            {
                x.IsActive = entities.Contains(x.Image);
                return x;
            }).ToList();

            foreach (var entity in entities)
            {
                model.SelectedList = model.SelectedList == null ? entity : model.SelectedList + "," + entity;
            }

            return PartialView(PartialViewConstants.GetSelectImagePartial, model);
        }

        [HttpPost]
        public ActionResult StockImagesSelect(int groupImageId, string selectedList)
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }
            var listSelected = Utils.WebUtils.ConvertStringToListString(selectedList);

            _iconImageSsCtxService.SaveChangeStockImage(groupImageId, listSelected);

            var redirectUrl = Url.Action("StockImagesShowAll", "TerminalSetupGUI", new { groupImageId });

            return Redirect(redirectUrl);
        }

        public ActionResult AddImages(int groupImageId)
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }
            var entities = _iconImageSsCtxService.GetListImageSelected(groupImageId, IconImageTypes.GalleryImage);
            var iconImageGroup = _iconImageGroupSsCtxService.GetById(groupImageId);

            var vm = new IconImageViewModel();
            if (entities.Count() != 0)
            {
                var path = IconImageViewModelEmm.GetPathImageByType(IconImageTypes.GalleryImage);
                vm.IconImages = entities.ToList();
                vm.ViewItemModels = vm.IconImages.ToListImageItemModel(path);
            }
            vm.IconImageGroup = iconImageGroup.ToModel();

            return PartialView(vm);
        }

        public ActionResult AddedImagesSelect(int groupImageId)
        {
            var entities = _iconImageSsCtxService.GetListStringSelected(groupImageId, IconImageTypes.GalleryImage).ToList();

            var path = IconImageViewModelEmm.GetPathImageByType(IconImageTypes.GalleryImage);
            var model = new ViewItemsModel()
            {
                Items = entities.ToListItemModel(path),
            };

            foreach (var entity in entities)
            {
                model.SelectedList = model.SelectedList + entity;
            }

            return PartialView(PartialViewConstants.GetSelectImagePartial, model);
        }

        [HttpPost]
        public async Task<ActionResult> AddImages([FromForm] IFormFile addedImageFile, int groupImageId)
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            var stringImageUrl = "";
            if (addedImageFile != null)
            {
                stringImageUrl = await WebUtils.PathGalleryImageFolder(addedImageFile);
            }
            var entity = new IconImage(groupImageId, stringImageUrl, IconImageTypes.GalleryImage, MMSConstants.PRE_INSERT_RECORD);
            entity.IsActive = true;

            _iconImageSsCtxService.Insert(entity);

            return RedirectToAction("AddImages", "TerminalSetupGUI", new { groupImageId = groupImageId });
        }

        [HttpPost]
        public ActionResult DeleteImageAdded(string selectedIds, int groupImageId)
        {
            if (_terminalMasterId <= 0)
            {
                return RedirectToAction("Index", "Error");
            }

            IList<int> listIds = Utils.WebUtils.ConvertStringToInts(selectedIds);

            foreach (var id in listIds)
            {
                var entity = _iconImageSsCtxService.GetById(id);
                if (entity != null)
                {
                    entity.IsStatus = MMSConstants.PRE_DELETE_RECORD;
                    _iconImageService.Update(entity);
                }
            }

            return RedirectToAction("AddImages", "TerminalSetupGUI", new { groupImageId = groupImageId });
        }

        #endregion
    }
}
