﻿using Microsoft.EntityFrameworkCore;
using MMS.Core.dbContext;
using MMS.Core.Entities.TerminalSetupGUI;
using MMS.Core.Entities;
using MMS.Core.Repository.Impl;
using MMS.Core.CoreUTI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Linq.Expressions;

namespace MMS.Core.Repository.Impl
{
    public class HostInterfacesRepository : BaseRepository<HostInterfaces>, IHostInterfacesRepository
    {
        public HostInterfacesRepository(DbContextFactory contextFactory) : base(contextFactory)
        {
        }

        public IList<HostInterfaces> GetHostInterfacesNotSelectedByMerchantId(int merchantTemninalId, int terminalMasterId)
        {
            var context = GetContext();

            var idHostInterfaceSelect = (from p in context.MerchantProcessor
                          where p.MerchantTerminalId == merchantTemninalId
                                && p.IsStatus != Constants.DELETE_RECORD
                                && p.IsStatus != Constants.PRE_DELETE_RECORD
                                && p.HostInterface.IsStatus != Constants.DELETE_RECORD
                                && p.HostInterface.IsStatus != Constants.PRE_DELETE_RECORD
                          select p.HostInterface.Id).ToList();

            var allIdHostInterfacesId = context.HostInterfaces.Where(p => p.TerminalMasterId == terminalMasterId && p.IsStatus != Constants.DELETE_RECORD && p.IsStatus != Constants.PRE_DELETE_RECORD).Select(p => p.Id).AsQueryable().ToList();

            var idHostInterfaceNotSelect = allIdHostInterfacesId.Where(p => !idHostInterfaceSelect.Contains(p)).ToList();

            var includeExpression = new Expression<Func<HostInterfaces, object>>[]
            {
                p => p.Parent,
                p => p.Parent.Parent
            };

            var result = GetAll(false, true, p => idHostInterfaceNotSelect.AsEnumerable().Contains(p.Id), includes: includeExpression);

            CtxDisposeOrNot(context);

            return result;
        }

        public IList<HostInterfaces> GetHostInterfacesNotSelectedByTerminal(int terminalMasterId)
        {
            var context = GetContext();

            var excludedStatuses = new[]
            {
                Constants.DELETE_RECORD,
                Constants.PRE_DELETE_RECORD
            };

            var selectedTerminalHostInterfaceIds = context.HostInterfaces
                .AsNoTracking()
                .Where(p =>
                    p.TerminalMasterId == terminalMasterId &&
                    !excludedStatuses.Contains(p.IsStatus))
                .Select(p => p.ParentId);

            var result = context.HostInterfaces
                .AsNoTracking()
                .Where(p =>
                    p.TerminalMasterId == null &&
                    p.ParentId == null &&
                    !excludedStatuses.Contains(p.IsStatus) &&
                    !selectedTerminalHostInterfaceIds.Contains(p.Id))
                .ToList();

            CtxDisposeOrNot(context);

            return result;
        }

        public IList<HostInterfaces> GetHostInterfacesNotSelectedByMerchantAndSurchargeRule(int merchantTerminalId, int terminalMasterId, int surchargeRuleId)
        {
            var context = GetContext();

            var idHostInterfaceSelect = (from p in context.MerchantPaymentMethod
                                         where p.MerchantProcessor.MerchantTerminalId == merchantTerminalId
                                               && p.SurchargeRuleId == surchargeRuleId
                                               && p.IsStatus != Constants.DELETE_RECORD
                                               && p.IsStatus != Constants.PRE_DELETE_RECORD
                                               && p.MerchantProcessor.IsStatus != Constants.DELETE_RECORD
                                               && p.MerchantProcessor.IsStatus != Constants.PRE_DELETE_RECORD
                                               && p.MerchantProcessor.HostInterface.IsStatus != Constants.DELETE_RECORD
                                               && p.MerchantProcessor.HostInterface.IsStatus != Constants.PRE_DELETE_RECORD
                                         select p.MerchantProcessor.HostInterface.Id).ToList();

            var allIdHostInterfacesId = (from p in context.MerchantProcessor
                                         where p.MerchantTerminalId == merchantTerminalId
                                               && p.HostInterface.TerminalMasterId == terminalMasterId
                                               && p.HostInterface.IsStatus != Constants.DELETE_RECORD
                                               && p.HostInterface.IsStatus != Constants.PRE_DELETE_RECORD
                                               && p.IsStatus != Constants.DELETE_RECORD
                                               && p.IsStatus != Constants.PRE_DELETE_RECORD
                                         select p.HostInterface.Id).ToList();

            var idHostInterfaceNotSelect = allIdHostInterfacesId.Where(p => !idHostInterfaceSelect.Contains(p)).ToList();

            var result = GetAll(false, true, p => idHostInterfaceNotSelect.Contains(p.Id));

            CtxDisposeOrNot(context);

            return result;
        }


        public IList<HostInterfaces> GetHostInterfacesByTerminalMasterId(int terminalMasterId)
        {
            var context = GetContext();

            var q = context.HostInterfaces.Where(p => p.TerminalMasterId == terminalMasterId && p.IsStatus != Constants.DELETE_RECORD && p.IsStatus != Constants.PRE_DELETE_RECORD).AsQueryable();

            var hostInterfaces = q.ToList();

            CtxDisposeOrNot(context);

            return hostInterfaces;
        }

        public IList<HostInterfaces> GetHostInterfaceByMerchantTerminalId(int merchantTemninalId)
        {
            var context = GetContext();

            // query not get value context insert new hostinterfaces...
            var idHostInterfaces = (from p in context.Set<MerchantProcessor>()
                                    where p.MerchantTerminalId == merchantTemninalId
                                          && p.IsStatus != Constants.DELETE_RECORD
                                          && p.IsStatus != Constants.PRE_DELETE_RECORD
                                          && p.HostInterface.IsStatus != Constants.DELETE_RECORD
                                          && p.HostInterface.IsStatus != Constants.PRE_DELETE_RECORD
                                    select p.HostInterface.Id).ToList();

            var result = GetAll(false, true, p => idHostInterfaces.AsEnumerable().Contains(p.Id));

            CtxDisposeOrNot(context);

            return result;
        }

        public IList<HostInterfaces> GetHostInterfacesByMerchantTerminalAndSurchargeRule(int merchantTerminalId, int surchargeRuleId)
        {
            var context = GetContext();

            // query not get value context insert new hostinterfaces...
            var idHostInterfaces = (from p in context.Set<MerchantPaymentMethod>()
                                    where p.MerchantProcessor.MerchantTerminalId == merchantTerminalId
                                          && p.SurchargeRuleId == surchargeRuleId
                                          && p.IsStatus != Constants.DELETE_RECORD
                                          && p.IsStatus != Constants.PRE_DELETE_RECORD
                                          && p.MerchantProcessor.HostInterface.IsStatus != Constants.DELETE_RECORD
                                          && p.MerchantProcessor.HostInterface.IsStatus != Constants.PRE_DELETE_RECORD
                                          && p.MerchantProcessor.IsStatus != Constants.DELETE_RECORD
                                          && p.MerchantProcessor.IsStatus != Constants.PRE_DELETE_RECORD
                                    select p.MerchantProcessor.HostInterface.Id).ToList();

            var result = GetAll(false, true, p => idHostInterfaces.AsEnumerable().Contains(p.Id));

            CtxDisposeOrNot(context);

            return result;
        }

        public IList<HostInterfaces> GetHostInterfacesByIds(IList<int> ids)
        {
            var context = GetContext();

            var query = context.HostInterfaces
                .AsNoTracking()
                .Where(p => p.IsStatus != Constants.DELETE_RECORD
                    && p.IsStatus != Constants.PRE_DELETE_RECORD
                    && ids.Contains(p.Id));

            return [.. query];
        }

        public IList<HostInterfaces> GetGlobalHostInterfaces()
        {
            var context = GetContext();

            var query = context.HostInterfaces
                .AsNoTracking()
                .Where(p => p.IsStatus != Constants.DELETE_RECORD
                    && p.IsStatus != Constants.PRE_DELETE_RECORD
                    && p.TerminalMasterId == null
                    && p.ParentId == null);

            return [.. query];
        }

        public IList<HostInterfaces> GetTerminalHostInterfaces(int terminalMasterId)
        {
            var context = GetContext();

            var query = context.HostInterfaces
                .AsNoTracking()
                .Where(p => p.IsStatus != Constants.DELETE_RECORD
                    && p.IsStatus != Constants.PRE_DELETE_RECORD
                    && p.TerminalMasterId == terminalMasterId
                    && p.ParentId.HasValue)
                .Include(p => p.Parent);

            return [.. query];
        }
    }
}
