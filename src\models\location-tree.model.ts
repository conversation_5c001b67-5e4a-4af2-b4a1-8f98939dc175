import { LocationLevel, LocationLevelId } from '../constants/app.enums';
import {
  ApiBaseModel,
  PaginationParams,
  SelectionDataResponse,
} from './common.model';

export const LocationLevelNames: Record<LocationLevelId, string> = {
  [LocationLevelId.Industry]: 'Industry',
  [LocationLevelId.Chain]: 'Chain',
  [LocationLevelId.Region]: 'Region',
  [LocationLevelId.Country]: 'Country',
  [LocationLevelId.State]: 'State',
  [LocationLevelId.City]: 'City',
  [LocationLevelId.Suburb]: 'Suburb',
  [LocationLevelId.SuburbArea]: 'Suburb Area',
  [LocationLevelId.Locations]: 'Locations',
  [LocationLevelId.LocationAreas]: 'Location Areas',
};

export interface LocationItem extends ApiBaseModel {
  name: string;
  locationId: number;
  hierarchy: string;
  parentId: number | null;
  isSelected: boolean;
  deActivate: boolean;
}

export interface Location extends ApiBaseModel {
  locationName: string;
  locationLevel: number;
  deActivate: boolean;
  isHidden: boolean;
  hideable: boolean;
  hideOptionAll: boolean;
  hasHiddenChild: boolean;
  parentId: number;
  isFinal: boolean;
}

export interface LocationGlobalList {
  location: Location;
  locationItems: LocationItem[];
}

export interface SelectedItem {
  key: number;
  value: number;
}

export interface LocationTreeResponse {
  selecteds: SelectedItem[];
  locationGlobalLists: LocationGlobalList[];
}

export interface LocationTreeApiResponse {
  value: LocationTreeResponse;
}

export interface MerchantLocationArea extends ApiBaseModel {
  name: string;
}

export interface LocationStorageItem {
  level: number;
  id: number;
}

export interface LocationSelectDataRequest extends PaginationParams {
  parentLocationId: number;
  currentLevel: LocationLevel;
  groupMappingId: number;
  selectedFilterType: string;
}

export interface LocationSelectMerchantDataRequest extends PaginationParams {
  locationId: number;
  selectedFilterType: string;
}

export interface LocationSelectRequest {
  parentLocationId: number;
  groupMappingId: number;
  selectedIds: number[];
}

export interface LocationSelectMerchantRequest {
  locationId: number;
  selectedIds: number[];
  isSelectOne: boolean;
}

export interface LocationSelectMerchantResponse<T>
  extends SelectionDataResponse<T> {
  isSelectOne: boolean;
}
