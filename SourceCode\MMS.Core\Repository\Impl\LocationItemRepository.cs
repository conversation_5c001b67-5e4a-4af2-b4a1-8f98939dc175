﻿using Microsoft.EntityFrameworkCore;
using MMS.Core.CoreUTI;
using MMS.Core.CoreUTI.Enum;
using MMS.Core.dbContext;
using MMS.Core.Entities.Locations;
using MMS.Core.Utils;
using MMS.Infrastructure.Commons;
using MMS.Model.ApiModelRequest;
using MMS.Model.ApiModelResponse;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace MMS.Core.Repository.Impl
{
    public class LocationItemRepository : BaseRepository<LocationItem>, ILocationItemRepository
    {
        public LocationItemRepository(DbContextFactory contextFactory, MMSContext scopedContext) : base(contextFactory)
        {
            this.SetScopeMMSContext(scopedContext);
        }

        public async Task<IList<LocationItem>> GetLocationItemSource(int parentId, LocationLevel locationLevel)
        {
            var context = GetContext();

            var query = context.LocationItem.Where(o => o.IsStatus != Constants.DELETE_RECORD);

            if (parentId > 0)
            {
                query = query.Where(o => o.ParentId == parentId);
            }
            else
            {
                query = query.Where(o => o.Location.LocationLevel == locationLevel);
            }

            query = query.Include(o => o.Location);
            query = query.Include(o => o.AddressLocalItem);

            return await query.ToListAsync();
        }

        public async Task<IList<int>> GetLocationAreaIdByParentIdAsync(int parentId)
        {
            var locationArea = await this.ScopedMMSContext.Set<Location>()
                .Where(p => p.LocationLevel == LocationLevel.LocationAreas
                            && p.IsStatus != Constants.DELETE_RECORD
                            && p.IsStatus != Constants.PRE_DELETE_RECORD
                            && p.IsActive).FirstAsync();

            var query = this.ScopedMMSContext.Set<LocationItem>()
                .Where(p => p.LocationId == locationArea.Id
                            && p.IsStatus != Constants.DELETE_RECORD
                            && p.IsStatus != Constants.PRE_DELETE_RECORD);
            query = query
                .Where(b => b.Hierarchy.StartsWith(parentId + ",")
                    || b.Hierarchy.EndsWith("," + parentId)
                    || b.Hierarchy.Contains("," + parentId + ",")
                    || b.Hierarchy.Equals(parentId));

            return await query.Select(p => p.Id).ToListAsync();
        }
        
        public async Task<Dictionary<int, List<int>>> GetLocationAreaIdsByParentIdsAsync(List<int> parentIds)
        {
            var locationArea = await this.ScopedMMSContext.Set<Location>()
                .Where(p => p.LocationLevel == LocationLevel.LocationAreas
                            && p.IsStatus != Constants.DELETE_RECORD
                            && p.IsStatus != Constants.PRE_DELETE_RECORD
                            && p.IsActive)
                .FirstOrDefaultAsync(); 

            if (locationArea == null)
            {
                return new Dictionary<int, List<int>>();
            }

            var query = this.ScopedMMSContext.Set<LocationItem>()
                .Where(p => p.LocationId == locationArea.Id
                            && p.IsStatus != Constants.DELETE_RECORD
                            && p.IsStatus != Constants.PRE_DELETE_RECORD);

            var results = await query.ToListAsync();

            var groupedResults = parentIds
                .ToDictionary(
                    parentId => parentId,
                    parentId => results
                        .Where(item =>
                            item.Hierarchy.StartsWith(parentId + ",") ||
                            item.Hierarchy.EndsWith("," + parentId) ||
                            item.Hierarchy.Contains("," + parentId + ",") ||
                            item.Hierarchy.Equals(parentId.ToString()))
                        .Select(item => item.Id)
                        .ToList()
                );

            return groupedResults;
        }

        public async Task<LocationItem> GetLocationItemIncludeLevelAsync(int locationItemId)
        {
            var locationItem = await ScopedEntities
                .Include(p => p.Location)
                .Include(p => p.AddressLocalItem)
                .FirstOrDefaultAsync(p => p.Id == locationItemId);

            return locationItem;
        }

        public async Task<IList<int>> GetAllLocationAreaIdsAsync()
        {
            var locationArea = await this.ScopedMMSContext.Set<Location>()
                .Where(p => p.LocationLevel == LocationLevel.LocationAreas
                            && p.IsStatus != Constants.DELETE_RECORD
                            && p.IsStatus != Constants.PRE_DELETE_RECORD
                            && p.IsActive).FirstAsync();

            var query = this.ScopedMMSContext.Set<LocationItem>()
                .Where(p => p.LocationId == locationArea.Id
                            && p.IsStatus != Constants.DELETE_RECORD
                            && p.IsStatus != Constants.PRE_DELETE_RECORD);

            return await query.Select(p => p.Id).ToListAsync();
        }

        public async Task<IList<int>> GetLocationAreaIdByStringHierarchy(string locationIds)
        {
            if (string.IsNullOrEmpty(locationIds))
                return await GetAllLocationAreaIdsAsync();

            var intList = UtilConvert.SeparateStringToInt(locationIds);
            const LocationLevel startEnumLevel = LocationLevel.Region;
            const LocationLevel endEnumLevel = LocationLevel.LocationAreas;
            const int levelLength = endEnumLevel - startEnumLevel + 1;

            // only get Location Item id in Hierarchy ids
            if (intList.Count > levelLength)
                intList.RemoveRange(levelLength, intList.Count - levelLength);

            var lastId = intList[^1];

            // if last selected is Location id return it
            if (intList.Count == levelLength && lastId != 0)
            {
                var resultList = new List<int> { lastId };
                return resultList;
            }

            // find nearest id not equals 0 or first level
            while (intList.Count > 1 && lastId == 0)
            {
                intList.RemoveAt(intList.Count - 1);
                lastId = intList[^1];
            }

            return lastId != 0 ? await GetLocationAreaIdByParentIdAsync(lastId) : await GetAllLocationAreaIdsAsync();
        }

        public async Task<List<int>> GetLocationAreaIdsForAllRegionIdsAsync(List<int> locationItemRegionIds)
        {
            var query = this.ScopedMMSContext.Set<LocationItem>()
                .Where(p => p.Location.LocationLevel == LocationLevel.LocationAreas
                            && p.IsStatus != Constants.DELETE_RECORD
                            && p.IsStatus != Constants.PRE_DELETE_RECORD);

            var results = await query.ToListAsync();

            var locationAreaIds = locationItemRegionIds
                .SelectMany(parentId => results
                    .Where(item =>
                        item.Hierarchy.StartsWith(parentId + ",") ||
                        item.Hierarchy.EndsWith("," + parentId) ||
                        item.Hierarchy.Contains("," + parentId + ",") ||
                        item.Hierarchy.Equals(parentId.ToString()))
                    .Select(item => item.Id))
                .ToList();

            return locationAreaIds;
        }

        public async Task<PagingResponse<DataItemResponse>> GetSourceForSelectLocationItemAsync(int parentLocationId, LocationLevel locationLevel, List<int> selectedIds, SelectRemoveFormParameter pagingParameter)
        {
            var context = GetContext();

            var query = context.LocationItem.Where(o => o.IsStatus != Constants.DELETE_RECORD).Include(p => p.AddressLocalItem).AsQueryable();

            if (parentLocationId > 0)
            {
                query = query.Where(o => o.ParentId == parentLocationId);
            }
            else
            {
                query = query.Where(o => o.Location.LocationLevel == locationLevel);
            }

            if ((int)pagingParameter.SelectedFilterType == (int)SelectedFilterType.Selected)
            {
                query = query.Where(p => selectedIds.Contains(p.Id));
            }
            else if ((int)pagingParameter.SelectedFilterType == (int)SelectedFilterType.NotSelected)
            {
                query = query.Where(p => !selectedIds.Contains(p.Id));
            }

            query = SearchUtility.ApplySearch(query, pagingParameter.SearchString, q => q.AddressLocalItem.Name);

            var locationItems = from q in query
                select new DataItemResponse(q.Id, q.AddressLocalItem.Name, q.IsActive);

            return await GetPagingResponseAsync(locationItems, pagingParameter.PageNumber, pagingParameter.PageSize);
        }
    }
}
