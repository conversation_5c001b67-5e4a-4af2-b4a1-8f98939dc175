﻿using MMS.Core.Services.Base;
using MMS.Core.Entities.TerminalSetupGUI;
using MMS.Core.Entities;
using System;
using System.Collections.Generic;
using System.Text;

namespace MMS.Core.Services
{
    public interface IHostInterfacesService : IBaseService<HostInterfaces>
    {
        new IHostInterfacesService UsingSessionContext();
        HostInterfaces GetHostInterfacesById(int id);
        IList<HostInterfaces> GetHostInterfacesByTerminalMasterId(int teminalMasterId);
        IList<HostInterfaces> GetHostInterfaces(int teminalMasterId, IList<int> hostInterfacesIds);
        IList<ViewItemModel> GetHostInterfacesByMerchantTeminalId(int merchantTemninalId);
        IList<ViewItemModel> GetHostInterfacesNotSelectedByMerchantId(int merchantTemninalId, int terminalMasterId);
        IList<ViewItemModel> GetHostInterfacesByMerchantTerminalAndSurchargeRule(int merchantTerminalId, int surchargeRuleId);
        IList<ViewItemModel> GetHostInterfacesNotSelectedByMerchantAndSurchargeRule(int merchantTerminalId, int terminalMasterId, int surchargeRuleId);

        /// <summary>
        /// Gets host interfaces not assigned to the specified terminal, excluding deleted or pre-deleted records.
        /// </summary>
        /// <param name="terminalMasterId">The ID of the terminal master to exclude.</param>
        /// <returns>A list of unassigned, valid host interfaces.</returns>
        IList<HostInterfaces> GetHostInterfacesNotSelectedByTerminal(int terminalMasterId);

        /// <summary>
        /// Retrieves a list of HostInterfaces based on the provided IDs
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        IList<HostInterfaces> GetHostInterfacesByIds(IList<int> ids);

        /// <summary>
        /// Retrieves a list of global HostInterfaces
        /// </summary>
        /// <returns></returns>
        IList<HostInterfaces> GetGlobalHostInterfaces();

        /// <summary>
        /// Retrieves a list of global terminal HostInterfaces by TerminalID
        /// </summary>
        /// <param name="terminalMasterId">The ID of the terminal master to exclude.</param>
        /// <returns></returns>
        IList<HostInterfaces> GetTerminalHostInterfaces(int terminalMasterId);
    }
}
