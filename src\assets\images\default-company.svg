<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1080" height="1080" viewBox="0 0 1080 1080" xml:space="preserve">
<defs>
</defs>
<g transform="matrix(1 0 0 1 540 540)" id="58d1483a-f5ca-49c5-acc9-364c4404736d"  >
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1; visibility: hidden;" vector-effect="non-scaling-stroke"  x="-540" y="-540" rx="0" ry="0" width="1080" height="1080" />
</g>
<g transform="matrix(1 0 0 1 540 540)" id="4b47a284-cfdd-4682-8492-5d8a60600829"  >
</g>
<g transform="matrix(3.6 0 0 3.6 540 540)" id="surface1"  >
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(242,244,247); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  x="-150" y="-200" rx="0" ry="0" width="300" height="400" />
</g>
<g transform="matrix(3.6 0 0 3.6 446.94 401.93)" id="surface1"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(96,114,127); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-128.72, -111.61)" d="M 124.449219 107.34375 L 132.984375 107.34375 L 132.984375 115.878906 L 124.449219 115.878906 Z M 124.449219 107.34375" stroke-linecap="round" />
</g>
<g transform="matrix(3.6 0 0 3.6 508.38 401.93)" id="surface1"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(96,114,127); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-145.78, -111.61)" d="M 141.515625 107.34375 L 150.050781 107.34375 L 150.050781 115.878906 L 141.515625 115.878906 Z M 141.515625 107.34375" stroke-linecap="round" />
</g>
<g transform="matrix(3.6 0 0 3.6 569.83 401.93)" id="surface1"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(96,114,127); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-162.85, -111.61)" d="M 158.585938 107.34375 L 167.121094 107.34375 L 167.121094 115.878906 L 158.585938 115.878906 Z M 158.585938 107.34375" stroke-linecap="round" />
</g>
<g transform="matrix(3.6 0 0 3.6 631.28 401.93)" id="surface1"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(96,114,127); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-179.92, -111.61)" d="M 175.65625 107.34375 L 184.191406 107.34375 L 184.191406 115.878906 L 175.65625 115.878906 Z M 175.65625 107.34375" stroke-linecap="round" />
</g>
<g transform="matrix(3.6 0 0 3.6 446.94 463.32)" id="surface1"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(96,114,127); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-128.72, -128.66)" d="M 124.449219 124.398438 L 132.984375 124.398438 L 132.984375 132.929688 L 124.449219 132.929688 Z M 124.449219 124.398438" stroke-linecap="round" />
</g>
<g transform="matrix(3.6 0 0 3.6 508.38 463.32)" id="surface1"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(96,114,127); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-145.78, -128.66)" d="M 141.515625 124.398438 L 150.050781 124.398438 L 150.050781 132.929688 L 141.515625 132.929688 Z M 141.515625 124.398438" stroke-linecap="round" />
</g>
<g transform="matrix(3.6 0 0 3.6 569.83 463.32)" id="surface1"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(96,114,127); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-162.85, -128.66)" d="M 158.585938 124.398438 L 167.121094 124.398438 L 167.121094 132.929688 L 158.585938 132.929688 Z M 158.585938 124.398438" stroke-linecap="round" />
</g>
<g transform="matrix(3.6 0 0 3.6 631.28 463.32)" id="surface1"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(96,114,127); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-179.92, -128.66)" d="M 175.65625 124.398438 L 184.191406 124.398438 L 184.191406 132.929688 L 175.65625 132.929688 Z M 175.65625 124.398438" stroke-linecap="round" />
</g>
<g transform="matrix(3.6 0 0 3.6 446.94 524.76)" id="surface1"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(96,114,127); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-128.72, -145.73)" d="M 124.449219 141.464844 L 132.984375 141.464844 L 132.984375 150 L 124.449219 150 Z M 124.449219 141.464844" stroke-linecap="round" />
</g>
<g transform="matrix(3.6 0 0 3.6 508.38 524.76)" id="surface1"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(96,114,127); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-145.78, -145.73)" d="M 141.515625 141.464844 L 150.050781 141.464844 L 150.050781 150 L 141.515625 150 Z M 141.515625 141.464844" stroke-linecap="round" />
</g>
<g transform="matrix(3.6 0 0 3.6 569.83 524.76)" id="surface1"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(96,114,127); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-162.85, -145.73)" d="M 158.585938 141.464844 L 167.121094 141.464844 L 167.121094 150 L 158.585938 150 Z M 158.585938 141.464844" stroke-linecap="round" />
</g>
<g transform="matrix(3.6 0 0 3.6 631.28 524.76)" id="surface1"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(96,114,127); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-179.92, -145.73)" d="M 175.65625 141.464844 L 184.191406 141.464844 L 184.191406 150 L 175.65625 150 Z M 175.65625 141.464844" stroke-linecap="round" />
</g>
<g transform="matrix(3.6 0 0 3.6 446.94 586.22)" id="surface1"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(96,114,127); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-128.72, -162.8)" d="M 124.449219 158.535156 L 132.984375 158.535156 L 132.984375 167.070312 L 124.449219 167.070312 Z M 124.449219 158.535156" stroke-linecap="round" />
</g>
<g transform="matrix(3.6 0 0 3.6 508.38 586.22)" id="surface1"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(96,114,127); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-145.78, -162.8)" d="M 141.515625 158.535156 L 150.050781 158.535156 L 150.050781 167.070312 L 141.515625 167.070312 Z M 141.515625 158.535156" stroke-linecap="round" />
</g>
<g transform="matrix(3.6 0 0 3.6 569.83 586.22)" id="surface1"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(96,114,127); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-162.85, -162.8)" d="M 158.585938 158.535156 L 167.121094 158.535156 L 167.121094 167.070312 L 158.585938 167.070312 Z M 158.585938 158.535156" stroke-linecap="round" />
</g>
<g transform="matrix(3.6 0 0 3.6 631.28 586.22)" id="surface1"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(96,114,127); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-179.92, -162.8)" d="M 175.65625 158.535156 L 184.191406 158.535156 L 184.191406 167.070312 L 175.65625 167.070312 Z M 175.65625 158.535156" stroke-linecap="round" />
</g>
<g transform="matrix(3.6 0 0 3.6 262.65 494.04)" id="surface1"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(96,114,127); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-77.53, -137.2)" d="M 73.257812 132.929688 L 81.792969 132.929688 L 81.792969 141.464844 L 73.257812 141.464844 Z M 73.257812 132.929688" stroke-linecap="round" />
</g>
<g transform="matrix(3.6 0 0 3.6 324.1 494.04)" id="surface1"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(96,114,127); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-94.6, -137.2)" d="M 90.328125 132.929688 L 98.863281 132.929688 L 98.863281 141.464844 L 90.328125 141.464844 Z M 90.328125 132.929688" stroke-linecap="round" />
</g>
<g transform="matrix(3.6 0 0 3.6 262.65 555.49)" id="surface1"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(96,114,127); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-77.53, -154.27)" d="M 73.257812 150 L 81.792969 150 L 81.792969 158.535156 L 73.257812 158.535156 Z M 73.257812 150" stroke-linecap="round" />
</g>
<g transform="matrix(3.6 0 0 3.6 324.1 555.49)" id="surface1"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(96,114,127); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-94.6, -154.27)" d="M 90.328125 150 L 98.863281 150 L 98.863281 158.535156 L 90.328125 158.535156 Z M 90.328125 150" stroke-linecap="round" />
</g>
<g transform="matrix(3.6 0 0 3.6 262.65 616.94)" id="surface1"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(96,114,127); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-77.53, -171.34)" d="M 73.257812 167.070312 L 81.792969 167.070312 L 81.792969 175.601562 L 73.257812 175.601562 Z M 73.257812 167.070312" stroke-linecap="round" />
</g>
<g transform="matrix(3.6 0 0 3.6 324.1 616.94)" id="surface1"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(96,114,127); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-94.6, -171.34)" d="M 90.328125 167.070312 L 98.863281 167.070312 L 98.863281 175.601562 L 90.328125 175.601562 Z M 90.328125 167.070312" stroke-linecap="round" />
</g>
<g transform="matrix(3.6 0 0 3.6 262.65 678.38)" id="surface1"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(96,114,127); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-77.53, -188.4)" d="M 73.257812 184.136719 L 81.792969 184.136719 L 81.792969 192.671875 L 73.257812 192.671875 Z M 73.257812 184.136719" stroke-linecap="round" />
</g>
<g transform="matrix(3.6 0 0 3.6 324.1 678.38)" id="surface1"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(96,114,127); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-94.6, -188.4)" d="M 90.328125 184.136719 L 98.863281 184.136719 L 98.863281 192.671875 L 90.328125 192.671875 Z M 90.328125 184.136719" stroke-linecap="round" />
</g>
<g transform="matrix(3.6 0 0 3.6 769.48 463.32)" id="surface1"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(96,114,127); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-218.31, -128.66)" d="M 209.777344 124.398438 L 226.84375 124.398438 L 226.84375 132.929688 L 209.777344 132.929688 Z M 209.777344 124.398438" stroke-linecap="round" />
</g>
<g transform="matrix(3.6 0 0 3.6 769.48 401.93)" id="surface1"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(96,114,127); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-218.31, -111.61)" d="M 209.777344 107.34375 L 226.84375 107.34375 L 226.84375 115.878906 L 209.777344 115.878906 Z M 209.777344 107.34375" stroke-linecap="round" />
</g>
<g transform="matrix(3.6 0 0 3.6 769.48 524.76)" id="surface1"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(96,114,127); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-218.31, -145.73)" d="M 209.777344 141.464844 L 226.84375 141.464844 L 226.84375 150 L 209.777344 150 Z M 209.777344 141.464844" stroke-linecap="round" />
</g>
<g transform="matrix(3.6 0 0 3.6 769.48 586.22)" id="surface1"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(96,114,127); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-218.31, -162.8)" d="M 209.777344 158.535156 L 226.84375 158.535156 L 226.84375 167.070312 L 209.777344 167.070312 Z M 209.777344 158.535156" stroke-linecap="round" />
</g>
<g transform="matrix(3.6 0 0 3.6 769.48 647.66)" id="surface1"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(96,114,127); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-218.31, -179.87)" d="M 209.777344 175.601562 L 226.84375 175.601562 L 226.84375 184.136719 L 209.777344 184.136719 Z M 209.777344 175.601562" stroke-linecap="round" />
</g>
<g transform="matrix(3.6 0 0 3.6 769.48 709.05)" id="surface1"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(96,114,127); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-218.31, -196.92)" d="M 209.777344 192.65625 L 226.84375 192.65625 L 226.84375 201.191406 L 209.777344 201.191406 Z M 209.777344 192.65625" stroke-linecap="round" />
</g>
<g transform="matrix(3.6 0 0 3.6 769.48 770.49)" id="surface1"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(96,114,127); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-218.31, -213.99)" d="M 209.777344 209.722656 L 226.84375 209.722656 L 226.84375 218.257812 L 209.777344 218.257812 Z M 209.777344 209.722656" stroke-linecap="round" />
</g>
<g transform="matrix(3.6 0 0 3.6 523.56 540)" id="surface1"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(96,114,127); fill-rule: nonzero; opacity: 1;" vector-effect="non-scaling-stroke"  transform=" translate(-150, -149.96)" d="M 192.722656 65.015625 L 192.722656 90.277344 L 179.914062 90.277344 L 179.914062 60.34375 L 128.722656 60.34375 L 128.722656 90.277344 L 107.398438 90.277344 L 107.398438 115.863281 L 56.085938 115.757812 L 56.085938 239.484375 L 107.398438 239.585938 L 243.914062 239.585938 L 243.914062 100.84375 Z M 137.257812 68.964844 L 171.378906 68.964844 L 171.378906 90.277344 L 137.257812 90.277344 Z M 107.398438 231.035156 L 64.621094 231.035156 L 64.621094 124.292969 L 107.398438 124.398438 Z M 145.792969 231.035156 L 145.792969 201.191406 L 162.84375 201.191406 L 162.84375 231.035156 Z M 192.722656 231.035156 L 171.378906 231.035156 L 171.378906 192.65625 L 137.257812 192.65625 L 137.257812 231.035156 L 115.929688 231.035156 L 115.929688 98.808594 L 192.722656 98.808594 Z M 235.378906 231.035156 L 201.242188 231.035156 L 201.242188 81.398438 L 235.378906 105.292969 Z M 235.378906 231.035156" stroke-linecap="round" />
</g>
</svg>