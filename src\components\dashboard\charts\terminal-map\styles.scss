.map-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
}

.map-content {
  display: flex;
  flex-direction: column;
  height: calc(100% - 40px);
}

.map-container {
  position: relative;
  flex: 1;
  min-height: 0;
}

.map-tooltip {
  position: absolute;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  pointer-events: none;
  z-index: 1000;
}

.map-legend {
  display: flex;
  gap: 16px;
  margin-top: 12px;
  padding: 8px;
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .legend-item {
    display: flex;
    align-items: center;
    gap: 8px;

    .color-box {
      width: 12px;
      height: 12px;
      border-radius: 2px;
    }
  }
}
