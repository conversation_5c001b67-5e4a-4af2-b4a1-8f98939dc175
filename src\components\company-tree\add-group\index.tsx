import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Form, Input, Select, Row, Col, App } from 'antd';
import CommonModal from '../../../components/shared/modal';
import SwitchComponent from '../../shared/form-component/switch-form-item';
import { CompanyTreeApi } from '../../../apis/company-tree.api';
import {
  CompanyGroupResponse,
  CompanyItem,
} from '../../../models/company-tree.model';
import { CompanyItemType } from '../../../constants/app.enums';
import FontIcon from '../../shared/icons/font-icon';

interface AddGroupModalProps {
  visible: boolean;
  onCancel: () => void;
  onAfterSave: () => void;
  companyId?: number;
  lastSelectedItem?: CompanyItem | null;
}

interface GroupFormProps {
  form: any;
  group?: CompanyGroupResponse;
  linkMerchant: boolean;
  setLinkMerchant: (value: boolean) => void;
  selectedMerchant?: number;
  setSelectedMerchant: (value: number | undefined) => void;
  merchantSearch: string;
  t: any;
}

const GroupForm: React.FC<GroupFormProps> = ({ form, group, t }) => {
  return (
    <Form
      form={form}
      layout="vertical"
      initialValues={{
        groupSystemId: group?.id,
        groupName: group?.name,
        linkMerchant: false,
      }}
    >
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label={t('companyTree.group.groupSystemId')}
            name="groupSystemId"
          >
            <Input disabled />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label={t('companyTree.group.groupName')}
            name="groupName"
            rules={[
              {
                required: true,
                message: t('companyTree.group.groupNameRequired'),
              },
            ]}
          >
            <Input />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={12}>
          <SwitchComponent
            label={t('companyTree.group.linkMerchant')}
            name="linkMerchant"
            valuePropName="checked"
            isDisable={true}
          />
        </Col>
        <Col span={12}>
          <Form.Item label={t('companyTree.group.linkedMerchant')}>
            <Input
              disabled
              suffix={<FontIcon className="icon-advanced-search" />}
            />
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
};

const AddGroupModal = (props: AddGroupModalProps) => {
  const { t } = useTranslation();
  const [linkMerchant, setLinkMerchant] = useState(false);
  const [merchantSearch, setMerchantSearch] = useState('');
  const [selectedMerchant, setSelectedMerchant] = useState<
    number | undefined
  >();
  const [group, setGroup] = useState<CompanyGroupResponse | undefined>();
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const { message } = App.useApp();

  const groupId =
    props.lastSelectedItem?.itemType === CompanyItemType.Group
      ? props.lastSelectedItem?.orgId
      : null;

  const parentId =
    props.lastSelectedItem?.itemType === CompanyItemType.Group
      ? props.lastSelectedItem?.parentId
      : props.lastSelectedItem?.id;

  const fetchGroup = async (groupId: number) => {
    try {
      setLoading(true);
      const { data } = await CompanyTreeApi.getGroupById(groupId);
      setGroup(data);
      form.setFieldsValue({
        groupName: data.name,
        groupSystemId: data.id,
      });
    } catch (error) {
      console.error('Error fetching group:', error);
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    form.resetFields();
    setGroup(undefined);
    setLinkMerchant(false);
    setSelectedMerchant(undefined);
    setMerchantSearch('');
  };

  useEffect(() => {
    if (props.visible) {
      if (
        props.lastSelectedItem?.itemType === CompanyItemType.Group &&
        groupId
      ) {
        fetchGroup(groupId);
      } else {
        resetForm();
      }
    } else {
      resetForm();
    }
  }, [props.visible, props.lastSelectedItem]);

  const handleCancel = () => {
    resetForm();
    props.onCancel();
  };

  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      if (groupId) {
        await CompanyTreeApi.editGroup(groupId, {
          name: values.groupName,
          parentId: parentId,
        });
      } else {
        await CompanyTreeApi.addGroup({
          name: values.groupName,
          parentId: parentId,
        });
      }

      message.success(t('companyTree.group.saveSuccess'));
      resetForm();
      props.onAfterSave();
    } catch (error) {
      console.error('Error saving group:', error);
    }
  };

  return props.visible ? (
    <CommonModal
      visible={props.visible}
      onSave={handleSave}
      onCancel={handleCancel}
      title={
        group
          ? t('companyTree.group.editGroup')
          : t('companyTree.group.createGroup')
      }
      width={592}
      loading={loading}
    >
      <GroupForm
        form={form}
        group={group}
        linkMerchant={linkMerchant}
        setLinkMerchant={setLinkMerchant}
        selectedMerchant={selectedMerchant}
        setSelectedMerchant={setSelectedMerchant}
        merchantSearch={merchantSearch}
        t={t}
      />
    </CommonModal>
  ) : null;
};

export default AddGroupModal;
