{"common": {"add": "Add", "edit": "Edit", "requireValidation": "${label} field is required.", "emailValidation": "Invalid email address format.", "emailReportValidation": "invalid email address format.", "invalidToken": "Invalid <PERSON>", "urlValidation": "Invalid website URL format.", "websiteValidation": "Invalid website URL", "oneLowercaseCharacter": "One lowercase character", "oneUppercaseCharacter": "One uppercase character", "oneNumber": "One number", "8CharactersMinimum": "8 characters minimum", "containSpecialCharacter": "One special character", "sentEmail": "<PERSON><PERSON> sent successfully!", "dashboard": "Dashboard", "insightsHub": "Insights Hub", "salesAnalytics": "Sales Analytics", "contacts": "Contacts", "listings": "Listings", "inbox": "Inbox", "filterPlahoder": "Filter <PERSON>", "searchPlahoder": "Search…", "last7days": "Last 7 days", "lastMonth": "Last month", "last3Months": "Last 3 months", "lastYear": "Last year", "lastWeek": "Last week", "customRange": "Custom range", "thisWeek": "This week", "thisMonth": "This month", "thisYear": "This year", "lastWeekTitle": "Week before last", "lastMonthTitle": "Month before last", "last3MonthsTitle": "3 months before last", "lastYearTitle": "Year before last", "thisWeekTitle": "Last week", "thisMonthTitle": "Last month", "thisYearTitle": "Last year", "copyRight": " Copyright © 2025 MMS team. All Rights Reserved.", "reviews": "Reviews", "apply": "Apply", "noPermission": "You do not have a permission to view this page. If you think this is incorrect, please contact RocketLevel administrator.", "userProfile": "User Profile", "logOut": "Log Out", "rolesPermissions": "Roles & Permissions", "noData": "No data to be displayed.", "active": "Active", "deactivate": "Deactivate", "disabled": "Disabled", "cancel": "Cancel", "delete": "Delete", "saveChanges": "Save Changes", "save": "Save", "remove": "Remove", "close": "Close", "generalSettings": "General Settings", "switchTeam": "Switch Team", "saveChangesSuccessfully": "Save changes successfully!", "changeSaveSuccess": "Changes have successfully saved!", "changeHaveBeenSaveSuccessfully": "Changes have been saved successfully!", "yourChangesSaveSuccess": "Your changes have been successfully saved!", "selectedClientSettingsSaveSuccessfully": "Notification settings have been updated for the selected clients", "move": "Move", "reset": "Reset", "removeConnectionBtn": "Yes, remove it", "social": "Social", "back": "Back", "create": "Create", "serverError": "An error has occurred please contact the system administrator", "decline": "Decline", "enable": "Enable", "disable": "Disable", "copy": "Copy", "schedule": "Schedule", "manage": "Manage", "approve": "Approve", "confirm": "Confirm", "proceed": "Proceed", "createdOn": "Created On", "send": "Send", "done": "Done", "deselectAll": "Deselect All", "selectAll": "Select All", "disableIt": "Yes, disable it", "loading": "Loading...", "refresh": "Refresh", "noDataAvailable": "No data available", "all": "All", "selected": "Selected", "notSelected": "Not Selected", "search": "Search", "clearAll": "Clear All", "searchByName": "Search by Name", "itemSelected": "{{item}} Items Selected"}, "login": {"title": "Log in to your account", "email": "Email", "password": "Password", "signIn": "Sign in", "rememberMe": "Remember me on this device", "forgotYourPassword": "I forgot the password", "logout": "Logout", "emailRequired": "Please input your email!", "passwordRequired": "Please input your password!", "emailInvalid": "The email is not valid", "serverAuthError": "username or password is incorrect", "accessQuickly": "or access quickly with", "googleAccount": "Google Account", "emailPlahoder": "Enter your email", "passwordPlahoder": "Password", "doNotHaveAccount": "Don't have an account yet? <span class=\"color-green\">Sign Up</span>", "passwordSuccessfullyTitle": "Password successfully reset!", "passwordSuccessfullyDescription": "Use your new password to log in.", "emailPlaceholder": "<EMAIL>", "loginButton": "Sign In", "forgotPassword": "Forgot Password?", "dontHaveAccount": "Don't have an account? ", "createAccount": "Create Account", "loginToAccount": "Login to Account", "enterEmailPassword": "Please enter your email and password to continue", "selectCompany": "Please select a company to continue.", "selectCompanyWarning": "Please select a company to continue", "accessCompanyFail": "You do not have access to this company", "sendOtpSuccessTitle": "O<PERSON> Successfully", "sendOtpSuccess": "Your One-Time Password (OTP) has been sent successfully to your email address. Please check your inbox and spam folder to proceed with the verification.", "confirmCode": "Confirm Code", "confirmCodeDescription": "Please enter email confirmation code.", "notReceiveCode": "Dont receive the Code? ", "resendCode": "RESEND CODE", "continue": "Continue", "sendOtpFail": "Failed to send OTP", "inputConfirmCode": "Please enter email confirmation code", "confirmOtpSuccess": "OTP confirmed successfully"}, "tabs": {"email": "Email", "cellNumber": "Cell Number", "faceId": "Face ID", "fingerPrint": "Finger Print"}, "placeholders": {"cellNumber": "Enter Cell Number", "password": "Enter Password"}, "actions": {"scanFaceId": "<PERSON>an Face ID", "scanFingerprint": "Scan Fingerprint"}, "instructions": {"faceId": "Place your face in front of the camera", "fingerPrint": "Place your finger on the scanner"}, "paginationOptions": {"option5": "5 items", "option10": "10 items", "option15": "15 items", "option20": "20 items"}, "personManagement": {"title": "Person Management", "addPerson": "Add Person", "searchString": "Search by", "searchOptiopns": {"name": "Name", "email": "Email"}, "personList": {"title": "Person List", "searchPlaceholder": "Search by", "addPerson": "Add Person", "columns": {"personName": "Person Name", "email": "Email", "lastLogin": "Last Login"}, "actions": {"lockTitle": "Suspended Logon Access", "deleteConfirmTitle": "Delete person", "deleteConfirmDescription": "Are you sure to delete this person?", "deleteConfirmOk": "Yes", "deleteConfirmCancel": "No", "deleteSuccessMessage": "Delete successfully", "deleteSuccessDescription": "The person has been successfully removed."}}, "personDetails": {"title": "Person Details", "personInfo": "Person Info", "mmsLogon": "MMS Logon", "adminPassword": {"title": "Please enter your current admin password", "placeholder": "Admin Password", "verifySuccess": "Admin password verified", "verifySuccessMessage": "You can now continue and save the changes.", "verifyFailed": "Admin password verification failed", "verifyFailedMessage": "The password you entered is incorrect. Please try again."}, "notifications": {"createSuccess": "Person created successfully", "createSuccessMessage": "The person record has been added successfully.", "updateSuccess": "Person updated successfully", "updateSuccessMessage": "The person record has been updated successfully.", "createFailed": "Failed to create user", "updateFailed": "Failed to update user", "logonUpdateFailed": "Failed to update logon setup", "logonUpdateSuccess": "Logon setup updated successfully", "logonUpdateSuccessMessage": "The logon setup has been updated successfully.", "userInfoNotFound": "User information not found."}, "personInfoForm": {"profilePicture": "Profile Picture", "uploadImage": "Upload Image", "systemId": "System ID", "personalIdNumber": "Personal ID Number", "enableAdminAccess": "Enable Admin Access", "preferredName": "Preferred Name", "firstName": "First Name", "surname": "Surname", "middleName": "Middle Name", "gender": "Gender", "preferredLanguage": "Preferred Language", "emailAddress": "Email Address", "phoneNumber": "Phone Number", "address": "Address", "genderOptions": {"other": "Other", "male": "Male", "female": "Female"}, "preferredLanguageOptions": {"none": "None", "english": "English", "chinese": "Chinese", "vietnamese": "Vietnamese"}, "validationMessages": {"firstNameRequire": "First name is required", "firstNameMinLength": "First name must be at least 3 characters long", "genderRequire": "Gender is required", "preferredLanguageRequired": "Preferred language is required", "invalidEmail": "The input is not valid E-mail!"}}, "mmsLogonForm": {"suspendLogonAccess": "Suspend Logon Access", "mmsPassword": "MMS Password", "suspendAdminLogonAccess": "Suspend Admin Logon Access", "mmsAdminPassword": "MMS Admin Password", "activateDateTime": "Activate Date / Time", "activateLogonDate": "Activate Logon Date", "activateLogonTime": "Activate Logon Time", "deactivateDateTime": "Deactivate Date / Time", "deactivateLogonDate": "Deactivate Logon Date", "deactivateLogonTime": "Deactivate Logon Time", "placeholders": {"mmsPassword": "MMS Password", "mmsAdminPassword": "MMS Admin Password"}, "notifications": {"passwordMismatch": "Passwords do not match!", "invalidPassword": "Invalid password format!"}}}, "confirmPassInput": {"modal": {"title": "MMS Password", "passwordRequirements": "Password Requirements"}, "form": {"confirmPasswordLabel": "Confirm", "passwordPlaceholder": "Password", "confirmPasswordPlaceholder": "Confirm Password"}, "requirements": {"diffFromPreviousPass": "Must be different from the previous password", "length": "Must be a minimum of 8 characters", "uppercase": "Must contain 1 upper case letter", "lowercase": "Must contain 1 lower case letter", "specialChar": "Must contain 1 special character (#$!@% etc)", "notSameAsMmsId": "Can not be the same as MMS ID"}, "validation": {"confirmPasswordRequired": "Confirm MMS Password is required", "invalidRequirements": "Password does not valid with the requirements!", "passwordMismatch": "The two passwords that you entered do not match!"}}}, "companyManagement": {"title": "Company Management", "companyList": "Company List", "addCompany": "Add Company", "editCompany": "Edit Company", "deleteCompany": "Delete Company", "companyName": "Company Name", "companyEmail": "Company Email", "companyPhone": "Company Phone", "companyAddress": "Company Address", "companyStatus": "Company Status", "logo": "Company Logo", "companyActions": {"edit": "Edit", "delete": "Delete"}, "selectedUsers": "Selected Users", "selectCompanySuccess": "Company tree updated successfully", "selectCompanyError": "Failed to update company tree", "selectCompanies": "Select Companies", "selectSubsidiaries": "Select Sub-Companies", "selectedCompany": "Selected Company"}, "device": {"assignMerchant": "Assign Merchants to Device", "merchantSelected": "Merchant Selected", "deviceList": "Device List", "assignDevice": "Assign <PERSON>", "availableDevices": "Available Devices", "noDevicesSelected": "Please select at least one device", "searchBySN": "Search by SN", "merchantsAssignedSuccess": "Merchants assigned successfully", "merchantsAssignedError": "Failed to assign merchants", "merchantName": "Merchant Name", "devicesAssignedSuccess": "Device assign successfully", "deviceUnassignedSuccess": "<PERSON><PERSON> unassigned successfully", "deviceUnassignedError": "Failed to unassign device", "confirmUnassignTitle": "Confirm Unassign Device", "confirmUnassignMessage": "Are you sure you want to unassign this device?", "confirmUnassignOk": "Unassign", "confirmUnassignCancel": "Cancel", "selectedDevices": "<PERSON><PERSON>", "columns": {"deviceName": "Device Name", "serialNumber": "SN", "merchant": "Merchant", "brandModel": "Brand/Model", "created": "Created", "status": "Status", "online": "Online", "offline": "Offline", "brand": "Brand", "model": "Model"}, "actions": {"deviceSetup": "<PERSON><PERSON>", "assignMerchants": "Assign Merchants", "unassignDevice": "Unassign Device"}, "schedule": {"downloadDateAfterUpgrade": "Download date cannot be after upgrade date", "mustBeFutureDate": "Date must be today or future date", "upgradeDateBeforeDownload": "Upgrade date cannot be before download date", "downloadTimeFuture": "Download time must be future time", "downloadBeforeUpgrade": "Download time must be before upgrade time on the same day", "upgradeTimeFuture": "Upgrade time must be future time", "upgradeAfterDownload": "Upgrade time must be after download time on the same day", "fillAllFields": "Please fill in all required fields", "invalidDatesNone": "Please select valid dates and times.", "invalidDateImmediate": "Please select a valid future time for upgrade", "invalidDateForce": "Please select a valid future time for download", "checkAllInputs": "Please check all date and time inputs", "upgradeSuccess": "Upgrade scheduled successfully", "upgradeFailed": "Failed to schedule upgrade", "title": "Schedule Upgrade", "downloadDate": "Download Date", "upgradeDate": "Upgrade Date", "downloadTime": "Download Time", "upgradeTime": "Upgrade Time", "syncType": "Sync Type", "retryAmount": "<PERSON><PERSON> Amount", "modalTitle": "Schedule Upgrade Settings", "options": {"none": "None", "immediately": "<PERSON>ush Immediately", "forceUpgrade": "Forced Upgrade"}, "syncTypes": {"partial": "Partial Sync", "full": "Full Sync"}}}, "deviceSchedule": {"appName": "App Name", "appVersion": "App Version", "currentTemplate": "Current Template", "newTemplate": "New Template", "templateName": "Template Name", "templateOptions": "Template Options", "appBaseTemplate": "App Base Template", "scheduleUpgrade": "Schedule Upgrade", "merchants": "Merchants", "templateSetup": "Template Setup", "saveTemplate": "Save Template", "downloadDate": "Download Date", "downloadTime": "Download Time", "upgradeDate": "Upgrade Date", "upgradeTime": "Upgrade Time", "syncType": "Sync Type", "retryAmount": "<PERSON><PERSON> Amount", "companyName": "Company Name", "templateSelectOptions": {"none": "None", "baseTemplate": "Base Template", "existingTemplate": "Existing Template"}, "scheduleOptions": {"none": "None", "pushImmediately": "<PERSON>ush Immediately", "forcedUpgrade": "Forced Upgrade"}, "syncTypeOptions": {"partialSync": "Partial Sync", "fullSync": "Full Sync"}, "messages": {"hasCopiedTemplate": "You have a unsaved template, please save/cancel it first", "selectTemplateFirst": "Please select a template", "templateCopying": "Template is being copy...", "saveTemplateSuccess": "Template saved successfully", "canceledTemplate": "Template canceled", "cancelTemplateNotificationDescription": "Your changes were discarded and the template was not saved.", "confirmation": "Confirmation", "saveTemplateConfirm": "Are you sure you want to save this template?", "save": "Save", "cancel": "Cancel"}}, "appTemplate": {"deleteTemplate": {"title": "Delete Template Confirmation", "content": "Do you want to <b>Delete</b> the following Template:", "okText": "Delete", "cancelText": "Cancel", "success": "Template deleted successfully", "error": "Failed to delete template"}, "searchTemplate": {"placeholder": "Search Template History", "searchNotFound": "No templates found for {{searchValue}}"}}, "deviceInfo": {"taskName": "Task Name", "version": "Version", "deleteTask": {"title": "Confirm delete task", "content": "Are you sure you want to delete this task?", "okText": "Delete", "cancelText": "Cancel", "success": "Task deleted successfully", "error": "Cannot delete task. Please try again later."}}, "notification": {"warning": "Warning", "error": "Failed to process your request", "success": "Operation completed successfully", "missingDataTitle": "Missing Data", "missingCompanyOrGroupDescription": "Please select a company and group before proceeding", "missingLocationAreaDescription": "Please select a location area before proceeding", "cannotDeleteCompanyOrSubCompany": "Unable to <b>Remove</b> Company or Sub Company because it has child items.", "cannotDeleteGroup": "Unable to <b>Remove</b> a Group with <b>Assigned Devices</b>."}, "breadcrumbs": {"dashboard": "Dashboard", "merchantLocations": "Merchant Locations", "deviceSchedule": "Device Schedule", "terminalSetup": "Terminal Setup", "deviceSetup": "Device Setup: {{deviceName}} (SN - {{serialNumber}})", "appBaseTemplate": "App Base Template", "appBaseTemplateSetup": "App Base Template Setup: {{templateName}}", "deviceManager": "Device Manager"}, "appBaseTemplate": {"actions": {"activateTitle": "Activate Template Confirmation", "activateContent": "Do you want to <b>Activate</b> the following Template:", "deactivateTitle": "Deactivate Template Confirmation", "deactivateContent": "Do you want to <b>Deactivate</b> the following Template:", "activateSuccess": "Template activated successfully", "activateError": "Failed to activate template", "deactivateSuccess": "Template deactivated successfully", "deactivateError": "Failed to deactivate template", "alreadyActive": "Template is already active", "alreadyInactive": "Template is already inactive", "deleteTitle": "Delete Template Confirmation", "deleteContent": "Do you want to <b>Delete</b> the following Template:", "deleteSuccess": "Template deleted successfully", "deleteError": "Failed to delete template", "pleaseSelectTemplate": "Please select a template", "copyContent": "Do you want to copy this template?", "copyExistingTemplateContent": "Do you want to override <b>{{targetTemplate}}</b> with the following template <b>{{sourceTemplate}}</b>?", "copySuccess": "Template copied successfully", "copyError": "Failed to copy template", "copyTitle": "Copy Template Confirmation"}, "columnTitles": {"application": "Application", "appVersion": "App Version", "baseTemplate": "Base Template"}, "setup": {"success": "Terminal setup saved successfully", "discardSuccess": "Changes discarded", "discardError": "Failed to discard changes", "sendFailed": "Failed to send request", "saveFailed": "Failed to save changes", "appBaseTemplate": "App Base Template", "copyTemplate": "Copy Template", "discardChanges": "Discard Changes", "save": "Save", "discardingChanges": "Discarding changes...", "savingChanges": "Saving changes...", "saveIncompleteWarning": "Save successful, but template is incomplete, please complete it", "pleaseCompleteSetup": "Please complete the setup according to the following:"}}, "editActionButtons": {"edit": "Edit", "saveChanges": "Save Changes", "discardChanges": "Discard Changes"}, "confirmationModal": {"saveChangesConfirmation": "Save Changes Confirmation", "saveChangesConfirmationMessage": "Do you want to <b>Save</b> the recent changes?", "cancelChangesConfirmation": "Discard Changes", "cancelChangesConfirmationMessage": "Do you want to <b>Cancel</b> the recent changes? "}, "companyTree": {"removeCompany": "Remove Company", "deleteCompanyConfirm": "Are you sure you want to remove <b>{{name}}</b>? This action cannot be undone", "selectCompanies": "Select Companies", "selectSubsidiaries": "Select Sub Companies", "editGroup": "Edit Group", "addGroup": "Add Group", "removeGroup": "Remove Group", "remove": "Remove", "deleteCompanySuccess": "Remove successfully", "unableToRemoveCompanies": "Unable to Remove {{companies}} because it has child items.", "group": {"editGroup": "Edit Group", "createGroup": "Create Group", "groupSystemId": "Group System ID", "groupName": "Group Name", "groupNameRequired": "Group Name is required", "linkMerchant": "<PERSON>", "linkedMerchant": "Linked Merchant", "selectMerchant": "Select a merchant", "saveSuccess": "Group saved successfully"}}, "scheduleSetup": {"canNotScheduleUpgrade": "Can not schedule upgrade for incomplete template", "pleaseCompleteSetup": "Please complete setup for the template before scheduling."}}