﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace MMS.Core.Migrations
{
    /// <inheritdoc />
    public partial class HostInterfaces_Add_FieldsForCheckParentUpdated : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsUpdated",
                table: "HostInterfaces",
                type: "tinyint(1)",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<int>(
                name: "ParentId",
                table: "HostInterfaces",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_HostInterfaces_ParentId",
                table: "HostInterfaces",
                column: "ParentId");

            migrationBuilder.AddForeignKey(
                name: "FK_HostInterfaces_HostInterfaces_ParentId",
                table: "HostInterfaces",
                column: "ParentId",
                principalTable: "HostInterfaces",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_HostInterfaces_HostInterfaces_ParentId",
                table: "HostInterfaces");

            migrationBuilder.DropIndex(
                name: "IX_HostInterfaces_ParentId",
                table: "HostInterfaces");

            migrationBuilder.DropColumn(
                name: "IsUpdated",
                table: "HostInterfaces");

            migrationBuilder.DropColumn(
                name: "ParentId",
                table: "HostInterfaces");
        }
    }
}
