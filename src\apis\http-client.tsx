import axios, {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  ResponseType,
} from 'axios';
import qs from 'qs';
import showNotification from '../components/shared/notification';
import {
  LOCAL_COOKIE_KEY,
  LOCAL_STORAGE_KEY,
} from '../constants/app-constants';
import i18n from '../utils/i18n';
import LocalUtils from '../utils/local.utils';

// New utility function for building URL parameters
export const buildQueryParams = (
  baseParams: Record<string, any> = {},
  additionalParams: Record<string, any> = {},
): string => {
  const queryParams = new URLSearchParams();

  // Add base parameters
  Object.entries(baseParams).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      queryParams.append(key, value.toString());
    }
  });

  // Add optional additional parameters
  Object.entries(additionalParams).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      queryParams.append(key, value.toString());
    }
  });

  return queryParams.toString();
};

const toggleLoading = (value: boolean) => {};

const IS_REFRESHING_TOKEN = false;

const getHeaders = (contentType: string) => {
  let headers: any = {
    'Content-Type': contentType,
  };

  const token = LocalUtils.get(LOCAL_STORAGE_KEY.ACCESS_TOKEN);
  if (token) {
    headers = {
      ...headers,
      Authorization: `Bearer ${token}`,
    };
  }
  // if (LocalUtils.getCookie(LOCAL_COOKIE_KEY.ID_TOKEN)) {
  //   headers = {
  //     ...headers,
  //     Authorization: `Bearer ${LocalUtils.getCookie(LOCAL_COOKIE_KEY.ID_TOKEN)}`,
  //   };
  // }

  return headers;
};

const axiosInstance = (
  contentType: string = 'application/json',
  responseType: ResponseType = 'json',
  isShowLoading: boolean = true,
  isShowErrorMessage = true,
  allowAnonymous = false,
): AxiosInstance => {
  if (isShowLoading) toggleLoading(true);

  const instance = axios.create({
    responseType: responseType,
  });

  instance.interceptors.request.use(async (config: any) => {
    if (allowAnonymous) {
      return config;
    }

    //can check ingore in here
    // await checkRefreshTokenFinished(config);

    // let idToken = LocalUtils.getCookie(LOCAL_COOKIE_KEY.ID_TOKEN);
    // const refreshToken = LocalUtils.get(LOCAL_STORAGE_KEY.REFRESH_TOKEN);
    // const username = LocalUtils.getUserInfo().username;

    // if (!idToken) {
    //   if (refreshToken && username && !IS_REFRESHING_TOKEN) {
    //     try {
    //       IS_REFRESHING_TOKEN = true;
    //       const refreshTokenRefresh: RefreshTokenModel = {
    //         refreshToken: refreshToken,
    //         username: username,
    //       };
    //       const { data } = await AuthApi.refreshToken(refreshTokenRefresh);
    //       const isRememberMe =
    //         LocalUtils.get(LOCAL_STORAGE_KEY.IS_REMEMBER_ME) == "true";
    //       LocalUtils.setAuthenticatedData(data, isRememberMe);
    //     } catch (error) {
    //       console.error(error);
    //       LocalUtils.remove(LOCAL_STORAGE_KEY.REFRESH_TOKEN);
    //     }

    //     IS_REFRESHING_TOKEN = false;
    //   }
    // }

    config.headers = getHeaders(contentType);
    return config;
  });

  instance.interceptors.response.use(
    (response) => {
      if (isShowLoading) toggleLoading(false);

      return response;
    },
    async (error) => {
      if (isShowLoading) toggleLoading(false);

      if (error.response.status === 401) {
        handleUnAuthorize();
      } else {
        const data = error.response.data;
        if (isShowErrorMessage) {
          let message = i18n.t('common.serverError');

          if (data && data.message) {
            message = data.message;
          } else if (typeof data == 'string' && data !== '') {
            message = data;
          }

          showNotification('error', message);
        }
      }

      return Promise.reject(error);
    },
  );

  return instance;
};

export const getAsync = (
  url: string,
  params?: { [key: string]: any },
  isShowLoading: boolean = true,
  isShowErrorMessage = true,
  allowAnonymous = false,
  arrayFormat?: 'repeat' | 'comma',
): Promise<AxiosResponse> => {
  return axiosInstance(
    'application/json',
    'json',
    isShowLoading,
    isShowErrorMessage,
    allowAnonymous,
  ).get(url, {
    params: params,
    paramsSerializer: function (params) {
      return qs.stringify(params, { arrayFormat: arrayFormat || 'comma' });
    },
  });
};

export const getFileAsync = (
  url: string,
  params?: { [key: string]: any },
  isShowLoading: boolean = true,
  isShowErrorMessage = true,
  allowAnonymous = false,
): Promise<AxiosResponse> => {
  return axiosInstance(
    'application/json',
    'blob',
    isShowLoading,
    isShowErrorMessage,
    allowAnonymous,
  ).get(url, {
    params: params,
    paramsSerializer: function (params) {
      return qs.stringify(params, { arrayFormat: 'repeat' });
    },
  });
};

export const postAsync = (
  url: string,
  json?: object,
  isShowLoading = true,
  isShowErrorMessage = true,
  allowAnonymous = false,
): Promise<AxiosResponse> => {
  return axiosInstance(
    'application/json',
    'json',
    isShowLoading,
    isShowErrorMessage,
    allowAnonymous,
  ).post(url, json);
};

export const putAsync = (
  url: string,
  json?: object,
  isShowLoading: boolean = true,
  isShowErrorMessage = true,
  allowAnonymous = false,
): Promise<AxiosResponse> => {
  return axiosInstance(
    'application/json',
    'json',
    isShowLoading,
    isShowErrorMessage,
    allowAnonymous,
  ).put(url, json);
};

export const patchAsync = (
  url: string,
  json?: object,
  isShowLoading: boolean = true,
  isShowErrorMessage = true,
  allowAnonymous = false,
): Promise<AxiosResponse> => {
  return axiosInstance(
    'application/json',
    'json',
    isShowLoading,
    isShowErrorMessage,
    allowAnonymous,
  ).patch(url, json);
};

export const deleteAsync = (
  url: string,
  isShowLoading: boolean = true,
  isShowErrorMessage = true,
  allowAnonymous = false,
): Promise<AxiosResponse> => {
  return axiosInstance(
    'application/json',
    'json',
    isShowLoading,
    isShowErrorMessage,
    (allowAnonymous = false),
  ).delete(url);
};

export const postFormDataAsync = (
  url: string,
  json?: any,
  isShowLoading: boolean = true,
  isShowErrorMessage = true,
  allowAnonymous = false,
  config?: AxiosRequestConfig,
): Promise<AxiosResponse> => {
  return axiosInstance(
    'multipart/form-data',
    'json',
    isShowLoading,
    isShowErrorMessage,
    allowAnonymous,
  ).post(url, parseFormdata(json), config);
};

export const putFormDataAsync = (
  url: string,
  json?: any,
  isShowLoading: boolean = true,
  isShowErrorMessage = true,
  allowAnonymous = false,
): Promise<AxiosResponse> => {
  return axiosInstance(
    'multipart/form-data',
    'json',
    isShowLoading,
    isShowErrorMessage,
    allowAnonymous,
  ).put(url, parseFormdata(json));
};

export const downloadAsync = (
  url: string,
  params?: object,
): Promise<AxiosResponse> => {
  return axiosInstance('application/json', 'blob', true).get(url, { params });
};

const parseFormdata = (model: any) => {
  const formdata = new FormData();
  Object.keys(model || {}).forEach((p) => {
    if (model[p]) {
      if (Array.isArray(model[p])) {
        (model[p] as Array<any>).forEach((q) => {
          formdata.append(p + '[]', q);
        });
      } else {
        formdata.append(p, model[p]);
      }
    }
  });

  return formdata;
};

function handleUnAuthorize() {
  LocalUtils.remove(LOCAL_STORAGE_KEY.ACCESS_TOKEN);
  LocalUtils.removeCookie(LOCAL_COOKIE_KEY.IS_LOGGED_IN, undefined);

  window.location.href = '/sign-in';
}

// function checkRefreshTokenFinished(config: any): Promise<boolean> {
//   return new Promise((resolve) => {
//     const timer = setInterval(() => {
//       if (!IS_REFRESHING_TOKEN) {
//         clearInterval(timer);
//         resolve(true);
//       }
//     }, 100);
//   });
// }
