import React from 'react';
import { Drawer as <PERSON><PERSON><PERSON><PERSON><PERSON>, Button, Space } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import './index.scss';
import FontIcon from '../icons/font-icon';

export interface DrawerProps {
  title: string;
  open: boolean;
  onClose: () => void;
  onReset?: () => void;
  onSave?: () => void;
  children: React.ReactNode;
}

export const DrawerCustom: React.FC<DrawerProps> = ({
  title,
  open,
  onClose,
  onReset,
  onSave,
  children,
}) => {
  return (
    <AntDrawer
      open={open}
      onClose={onClose}
      width={378}
      closable={false}
      className="drawer-custom"
      title={
        <div className="drawer-header-content">
          <span className="drawer-title">{title}</span>
          {onReset && (
            <Button
              type="text"
              icon={<FontIcon size={16} className="icon-reset" />}
              size="small"
              onClick={onReset}
            />
          )}
          <Button
            type="text"
            icon={<CloseOutlined style={{ fontSize: '16px' }} />}
            size="small"
            onClick={onClose}
          />
        </div>
      }
    >
      <div className="drawer-content">
        {children}
        <div className="drawer-footer">
          <Space>
            <Button onClick={onClose}>Cancel</Button>
            <Button type="primary" onClick={onSave}>
              Apply
            </Button>
          </Space>
        </div>
      </div>
    </AntDrawer>
  );
};
