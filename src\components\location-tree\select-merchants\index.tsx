import React, { Key, useEffect, useState } from 'react';
import { Button, Dropdown, Input, message, Table } from 'antd';
import { useTranslation } from 'react-i18next';
import CommonModal from '../../shared/modal';
import FontIcon from '../../shared/icons/font-icon';
import PaginationComponent from '../../shared/pagination';
import { FilterKey } from '../../../constants/app.enums';
import { DEFAULT_PAGE_SIZE } from '../../../constants/device-list.constants';
import './index.scss';
import { ApiBaseModel } from '../../../models/common.model';
import { LocationTreeApi } from '../../../apis/location-tree.api';

interface SelectMerchantsModalProps {
  visible: boolean;
  onCancel: () => void;
  onAfterSave: () => void;
  locationId: number;
}

const SelectMerchantsModal: React.FC<SelectMerchantsModalProps> = ({
  visible,
  onCancel,
  onAfterSave,
  locationId,
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<ApiBaseModel[]>([]);
  const [totalRecords, setTotalRecords] = useState<number>(0);
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);
  const [pageNumber, setPageNumber] = useState(1);
  const [filter, setFilter] = useState<FilterKey>(FilterKey.ALL);
  const [searchValue, setSearchValue] = useState<string>('');
  const [searchKey, setSearchKey] = useState<string>('');
  const [isSearchVisible, setIsSearchVisible] = useState(false);
  const [selectedDatas, setSelectedDatas] = useState<number[]>([]);
  const [selectionsMap, setSelectionsMap] = useState<Map<number, boolean>>(
    new Map(),
  );
  const [isSelectOne, setIsSelectOne] = useState(false);

  const fetchData = async () => {
    try {
      setLoading(true);
      const { data } = await LocationTreeApi.getSelectMerchantData({
        locationId,
        pageNumber: pageNumber,
        pageSize: pageSize,
        searchString: searchKey,
        selectedFilterType: filter,
      });

      setIsSelectOne(data.isSelectOne);
      setData(data.source.data);
      setTotalRecords(data.source.totalRecords);
      if (visible && selectedDatas.length === 0) {
        const initialMap = new Map();
        data.selectedIds.forEach((id) => initialMap.set(id, true));
        setSelectionsMap(initialMap);
        setSelectedDatas(data.selectedIds);
      }
    } catch (error) {
      console.error('Error fetching companies:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (visible) {
      fetchData();
    }
  }, [visible, locationId, pageNumber, pageSize, filter, searchKey]);

  const handleModalCancel = () => {
    setFilter(FilterKey.ALL);
    setPageNumber(1);
    setSearchValue('');
    setSearchKey('');
    setSelectedDatas([]);
    setSelectionsMap(new Map());
    onCancel();
  };

  const handleSave = async () => {
    try {
      setLoading(true);
      await LocationTreeApi.updateLocationSelectionMerchant({
        locationId: locationId,
        selectedIds: selectedDatas,
        isSelectOne: isSelectOne,
      });
      message.success(`Update Merchants successfully`);

      onAfterSave();
    } catch (error) {
      console.error('Error assigning users:', error);
      message.error(`Update Merchants failed`);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    if (!isSearchVisible) {
      setIsSearchVisible(true);
      return;
    }
    if (locationId) {
      setSearchKey(searchValue);
      setIsSearchVisible(false);
    }
  };

  const handleClearSearch = () => {
    setSearchValue('');
    setSearchKey('');
    setFilter(FilterKey.ALL);
    setPageNumber(1);
  };

  const updatedRowSelection = {
    selectedRowKeys: selectedDatas,
    preserveSelectedRowKeys: true,
    onChange: (selectedRowKeys: Key[]) => {
      const newKeys = selectedRowKeys as number[];
      const newMap = new Map(selectionsMap);

      data.forEach((item) => {
        newMap.set(item.id, newKeys.includes(item.id));
      });

      setSelectionsMap(newMap);
      const allSelectedIds = Array.from(newMap.entries())
        .filter(([_, selected]) => selected)
        .map(([id]) => id);
      setSelectedDatas(allSelectedIds);
    },
  };

  const tableColumns = [
    {
      title: <span className="body-2-bold">Merchant</span>,
      dataIndex: 'name',
      key: 'name',
    },
  ];

  return (
    <CommonModal
      visible={visible}
      onCancel={handleModalCancel}
      onSave={handleSave}
      width={582}
      title={`Select Merchants`}
    >
      <div className="selected-devices-section">
        <div className="selected-devices-header">
          <span className="section-title">Selected Merchants</span>
          <div className="section-actions">
            <div className="search-wrapper">
              <Button
                type="text"
                onClick={handleSearch}
                size="small"
                icon={<FontIcon size={16} className="icon-search main-color" />}
              >
                {t('common.search')}
              </Button>
              {isSearchVisible && (
                <div className="search-input">
                  <Input
                    autoFocus
                    placeholder={t('common.searchByName')}
                    value={searchValue}
                    onChange={(e) => setSearchValue(e.target.value)}
                    onPressEnter={() => {
                      if (locationId) {
                        setSearchKey(searchValue);
                        setIsSearchVisible(false);
                      }
                    }}
                    onBlur={() => setIsSearchVisible(false)}
                    style={{ width: 200, height: '36px' }}
                  />
                </div>
              )}
            </div>
            <Button
              type="text"
              icon={<FontIcon size={16} className="icon-close main-color" />}
              size="small"
              onClick={handleClearSearch}
            >
              {t('common.clearAll')}
            </Button>
            <Dropdown
              menu={{
                items: [
                  { key: FilterKey.ALL, label: t('common.all') },
                  { key: FilterKey.SELECTED, label: t('common.selected') },
                  {
                    key: FilterKey.NOT_SELECTED,
                    label: t('common.notSelected'),
                  },
                ],
                onClick: ({ key }) => {
                  console.log('key', key);
                  setFilter(key as FilterKey);
                },
              }}
              trigger={['click']}
              placement="bottomRight"
            >
              <Button
                type="text"
                icon={<FontIcon size={16} className="icon-filter main-color" />}
                size="small"
              >
                Filter
              </Button>
            </Dropdown>
          </div>
        </div>
        <div className="selected-devices-table">
          <Table
            rowSelection={{
              type: 'checkbox',
              ...updatedRowSelection,
            }}
            className="device-assigned"
            columns={tableColumns}
            dataSource={data}
            rowKey="id"
            loading={loading}
            pagination={false}
            scroll={{ y: 230 }}
          />
          <PaginationComponent
            totalRecords={totalRecords}
            pageSize={pageSize}
            setPageSize={setPageSize}
            pageNumber={pageNumber}
            setPageNumber={setPageNumber}
          />
        </div>
      </div>
    </CommonModal>
  );
};

export default SelectMerchantsModal;
