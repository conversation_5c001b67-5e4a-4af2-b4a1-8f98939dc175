// App.scss
.ant-layout.main-layout {
  height: calc(100vh - 64px);
  .main-layout-content-warpper {
    flex: 1;
    display: flex;
    gap: 16px;
    padding: 16px 16px 16px 0px;
  }

  .content-layout {
    display: flex;
    gap: 16px;
    flex-direction: column;
  }

  .content {
    margin-top: 0px;
    min-height: 280px;
    border-radius: 8px;
    overflow: auto;
  }

  display: flex;
  gap: 16px;
}
