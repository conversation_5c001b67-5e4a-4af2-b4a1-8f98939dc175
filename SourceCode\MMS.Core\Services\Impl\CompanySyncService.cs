using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MMS.Core.CoreUTI.Enum;
using MMS.Core.Entities;
using MMS.Core.Repository;
using MMS.Core.Services.Impl.Base;
using MMS.Model.LinklyFake;

namespace MMS.Core.Services.Impl
{
    /// <summary>
    /// Service for synchronizing company data from external sources
    /// </summary>
    public class CompanySyncService : BaseService<CompanyDetails>, ICompanySyncService
    {
        private readonly ICompanyDetailRepository _companyDetailRepository;

        /// <summary>
        /// Constructor for CompanySyncService
        /// </summary>
        /// <param name="companyDetailRepository">Company detail repository</param>
        /// <param name="languageExecuteService">Language execute service</param>
        public CompanySyncService(
            ICompanyDetailRepository companyDetailRepository,
            ILanguageExecuteService languageExecuteService
        ) : base(companyDetailRepository, languageExecuteService)
        {
            _companyDetailRepository = companyDetailRepository ?? throw new ArgumentNullException(nameof(companyDetailRepository));
        }

        /// <inheritdoc />
        public async Task<(int inserted, int updated, int deleted)> SyncCompaniesFromLinklyAsync(List<LinklyCompanyDto> linklyCompanies)
        {
            int inserted = 0, updated = 0, deleted = 0;

            // Always use Linkly source for this method since it's specifically for syncing with Linkly
            var existingLinklyCompanies = await _companyDetailRepository.GetLinklyCompaniesAsync();

            // Get all existing LinklyIds for quick lookup
            var existingLinklyIds = existingLinklyCompanies
                .Where(c => !string.IsNullOrEmpty(c.LinklyId))
                .Select(c => c.LinklyId)
                .ToHashSet();

            // Process each company from Linkly - only insert new ones
            foreach (var linklyCompany in linklyCompanies)
            {
                // Skip if company already exists
                if (!string.IsNullOrEmpty(linklyCompany.LinklyId) && existingLinklyIds.Contains(linklyCompany.LinklyId))
                {
                    continue;
                }

                // Create new company
                var newCompany = MapLinklyCompanyToCompanyDetails(linklyCompany);
                _companyDetailRepository.Insert(newCompany, commit: false);
                inserted++;
            }

            // Save all changes asynchronously
            await _companyDetailRepository.SaveChangesAsync();

            return (inserted, updated, deleted);
        }

        /// <summary>
        /// Maps a LinklyCompanyDto to a CompanyDetails entity
        /// </summary>
        /// <param name="linklyCompany">The Linkly company DTO</param>
        /// <returns>A new CompanyDetails entity</returns>
        private static CompanyDetails MapLinklyCompanyToCompanyDetails(LinklyCompanyDto linklyCompany)
        {
            return new CompanyDetails
            {
                LinklyId = linklyCompany.LinklyId,
                CompanyIdNumber = linklyCompany.CompanyIdNumber,
                CompanyRegisteredName = linklyCompany.CompanyRegisteredName,
                CompanyTradingName = linklyCompany.CompanyTradingName,
                TaxID = linklyCompany.TaxID,
                CompanyPhoto = linklyCompany.CompanyPhoto,
                FormationDate = linklyCompany.FormationDate,
                StartedDoingBusiness = linklyCompany.StartedDoingBusiness,
                FinishedDoingBusiness = linklyCompany.FinishedDoingBusiness,
                BusinessFormationDate = linklyCompany.BusinessFormationDate,
                TurnoverPerAnnum = linklyCompany.TurnoverPerAnnum,
                ServiceSupplier = linklyCompany.ServiceSupplier,
                CompanyRegistration = linklyCompany.CompanyRegistration,
                AfterHoursPhone = linklyCompany.AfterHoursPhone,
                AfterHoursExtension = linklyCompany.AfterHoursExtension,
                UserChangesAllowed = linklyCompany.UserChangesAllowed,
                Email = linklyCompany.Email,
                PhoneNumber = linklyCompany.PhoneNumber,
                Address = linklyCompany.Address,
                IsActive = linklyCompany.IsActive == 1,
                ParentId = linklyCompany.ParentId,
                HasSubColumn = linklyCompany.HasSubPartner,
                IsStatus = CoreUTI.Constants.NOTCHANGE_RECORD,
                Created = linklyCompany.Created,
                Modified = linklyCompany.Modified,
            };
        }


    }
}
