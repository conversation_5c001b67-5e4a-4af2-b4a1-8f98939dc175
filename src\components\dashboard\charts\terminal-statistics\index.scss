.title {
  color: #000;
  font-family: <PERSON>pins;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  margin-bottom: 16px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.terminal-statistics-chart {
  .chart-separator {
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 25%;
    width: 1px;
    background-color: #e8e8e8;
  }

  .chart-container {
    display: flex;
    gap: 16px;
    width: 100%;
    height: 100%;
  }

  .chart-wrapper {
    width: 100%;
    padding: 16px;
    min-height: 350px;
  }

  .chart-item {
    flex: 1;
    width: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
  }

  .apexcharts-legend-series {
    display: flex; /* như bạn đang dùng */
    flex-direction: column;
    align-items: center;
    padding: 0 10px;
    margin: 0 5px;
    border-right: 1px solid #ccc; /* đ<PERSON>ờng kẻ đứng */
  }

  .apexcharts-legend-series:first-of-type {
    margin-left: 30px !important;
  }

  .apexcharts-legend-series:last-of-type {
    border-right: none !important;
  }
}
