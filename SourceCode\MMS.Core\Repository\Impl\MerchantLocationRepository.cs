﻿using Microsoft.EntityFrameworkCore;
using MMS.Core.CoreUTI;
using MMS.Core.CoreUTI.Enum;
using MMS.Core.dbContext;
using MMS.Core.Entities;
using MMS.Core.Entities.Locations;
using MMS.Core.Migrations;
using MMS.Infrastructure.Commons;
using MMS.Model.ApiModelRequest;
using MMS.Model.ApiModelResponse;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace MMS.Core.Repository.Impl
{
    public class MerchantLocationRepository : BaseRepository<MerchantLocations>, IMerchantLocationRepository
    {
        public MerchantLocationRepository(DbContextFactory contextFactory, MMSContext scopedContext) : base(contextFactory)
        {
            SetScopeMMSContext(scopedContext);
        }

        public async Task<IList<MerchantLocations>> GetAllMerchantLocationAsync(int locationItemId)
        {
            var context = GetContext();

            var allMerchantLocation = await context.Set<MerchantLocations>()
                .Where(ml => ml.LocationItemId == locationItemId && ml.IsStatus == Constants.NOTCHANGE_RECORD)
                .ToListAsync();

            CtxDisposeOrNot(context);

            return allMerchantLocation;
        }

        public async Task<MerchantLocations> GetMerchantLocationWithOptionalIncludeAsync(int locationItemId, params Expression<Func<MerchantLocations, object>>[] includes)
        {
            var context = GetContext();

            IQueryable<MerchantLocations> merchantLocation = context.Set<MerchantLocations>().AsQueryable();

            if (includes != null && includes.Length != 0)
            {
                foreach (var include in includes)
                {
                    merchantLocation = merchantLocation.Include(include);
                }
            }
            
            var result = await merchantLocation.FirstOrDefaultAsync(ml => 
                ml.LocationItemId == locationItemId && ml.IsStatus == Constants.NOTCHANGE_RECORD);

            CtxDisposeOrNot(context);

            return result;
        }

        public async Task<List<MerchantLocations>> GetMerchantLocationListByMerchantMasterIdAsync(int merchantMasterId)
        {
            var query = await ScopedEntities
                .Where(p => p.MerchantMasterId == merchantMasterId && p.IsStatus == Constants.NOTCHANGE_RECORD)
                .ToListAsync();

            return query;
        }

        public async Task<(LocationLevel, string, int)> GetLocationItemInforAsync(int locationItemId)
        {
            var query = await ScopedMMSContext.Set<LocationItem>()
                .Include(p => p.Location)
                .FirstOrDefaultAsync(p => p.Id == locationItemId && p.IsStatus == Constants.NOTCHANGE_RECORD);

            if (query is null) return (LocationLevel.Master, string.Empty, 0);

            return (query.Location.LocationLevel, query.GetLocationItemName, query.ParentId ?? 0);
        }

        public async Task<MerchantLocations> GetMerchantLocationIncludeMerchantMasterAsync(int locationItemId)
        {
            var context = GetContext();

            var merchantLocation = await context.Set<MerchantLocations>()
                .Include(ml => ml.MerchantMaster)
                .FirstOrDefaultAsync(ml => 
                    ml.LocationItemId == locationItemId && ml.IsStatus == Constants.NOTCHANGE_RECORD);

            CtxDisposeOrNot(context);

            return merchantLocation;
        }

        public async Task<IList<MerchantLocations>> GetMerchantLocationListByLocationAreaIdIncludeMerchantMasterAsync(int locationAreaId)
        {
            var context = GetContext();

            var merchantLocationList = await context.Set<MerchantLocations>()
                .Where(ml => ml.LocationItemId == locationAreaId && ml.IsStatus == Constants.NOTCHANGE_RECORD)
                .Include(ml => ml.MerchantMaster)
                .ToListAsync();

            CtxDisposeOrNot(context);

            return merchantLocationList;
        }
        
        public async Task<List<MerchantMaster>> GetMerchantLocationListSelectedByOtherLocationAsync(int locationId)
        {
            var locationItem = await ScopedMMSContext.Set<LocationItem>()
                .Include(p => p.Location)
                .FirstOrDefaultAsync(p => p.Id == locationId && p.IsStatus == Constants.NOTCHANGE_RECORD);

            if (locationItem is null) return [];

            if (locationItem.Location is null) return [];

            if (locationItem.Location.LocationLevel == CoreUTI.Enum.LocationLevel.LocationAreas)
                locationId = locationItem.ParentId ?? 0;

            var locationAreaIdsList = ScopedMMSContext.Set<LocationItem>()
                    .Where(p => p.ParentId == locationId)
                    .Select(p => p.Id);

            var merchantLocationSelectedByOtherLocationList = await ScopedEntities
                    .Where(p => 
                        p.LocationItemId != locationId && !locationAreaIdsList.Contains(p.LocationItemId) && 
                        p.IsStatus == Constants.NOTCHANGE_RECORD)
                    .Select(p => p.MerchantMaster)
                    .ToListAsync();

            return merchantLocationSelectedByOtherLocationList;
        }

        public PagingResponse<DataItemResponse> GetPagingSourceMerchantMaster(List<MerchantMaster> sourceList, IEnumerable<int> selectedIds, SelectRemoveFormParameter pagingParameter)
        {
            IEnumerable<MerchantMaster> listMerchants = sourceList;
            if ((int)pagingParameter.SelectedFilterType == (int)SelectedFilterType.Selected)
            {
                listMerchants = sourceList.Where(p => selectedIds.Contains(p.Id));
            }
            else if ((int)pagingParameter.SelectedFilterType == (int)SelectedFilterType.NotSelected)
            {
                listMerchants = sourceList.Where(p => !selectedIds.Contains(p.Id));
            }

            if (!string.IsNullOrEmpty(pagingParameter.SearchString))
                listMerchants = sourceList.Where(p => p.lszMerchantName.Contains(pagingParameter.SearchString));

            var response = new PagingResponse<DataItemResponse>();

            response.TotalRecords = listMerchants.Count();

            if (response.TotalRecords == 0)
                return response;

            int index = (pagingParameter.PageNumber - 1) * pagingParameter.PageSize;

            listMerchants = listMerchants.Skip(index).Take(pagingParameter.PageSize);

            var dataItemResponse = listMerchants.Select(q => new DataItemResponse(q.Id, q.lszMerchantName, q.IsActive)).ToList();

            response.Data = dataItemResponse;

            return response;
        }
    }
}
