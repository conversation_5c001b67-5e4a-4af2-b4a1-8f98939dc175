import { Alert, Spin } from 'antd';
import { useEffect, useState } from 'react';
import Chart from 'react-apexcharts';
import './index.scss';
import { ApexOptions } from 'apexcharts';

interface ChartData {
  series: number[];
  labels: string[];
  total: number;
}

const CHART_COLORS = ['#63AF59', '#FC9818', '#AAAABD'];

const createChartOptions = (data: ChartData): ApexOptions => ({
  series: data.series,
  labels: data.labels,
  dataLabels: {
    enabled: false,
  },
  plotOptions: {
    pie: {
      expandOnClick: false,
      customScale: 1,
      donut: {
        size: '60%',
        labels: {
          show: true,
          name: {
            show: true,
          },
          value: {
            show: true,
            fontSize: '14px',
            fontWeight: 600,
            color: '#000000',
            formatter: () => data.total.toString(),
          },
          total: {
            show: true,
            label: 'Total',
            fontSize: '12px',
            fontWeight: 600,
            color: '#666666e7',
            formatter: () => data.total.toString(),
          },
        },
      },
    },
  },
  colors: CHART_COLORS,
  chart: {
    offsetY: -39,
  },
  legend: {
    offsetY: -50,
    position: 'bottom',
    horizontalAlign: 'center',
    floating: false,
    fontSize: '12px',
    formatter: (seriesName: string, opts: any) => {
      const value = opts.w.globals.series[opts.seriesIndex];
      const color = opts.w.globals.colors[opts.seriesIndex];
      return `
      <div class="custom-legend-item" style="display: flex; flex-direction: column; align-items: center;padding-left: 5px; padding-right: 5px;">
        <span class="legend-value" style="color: ${color}; font-size: 12px; font-weight: 600;">${value}</span>
        <span class="legend-label" style="color: #666666; font-size: 12px; display: block;">${seriesName}</span>
      </div>
      `;
    },
    markers: {
      size: 0,
      strokeWidth: 0,
    },
    itemMargin: {
      horizontal: 5,
      vertical: 5,
    },
  },
});

const LoadingSpinner = () => (
  <div className="loading">
    <Spin tip="Loading..."></Spin>
  </div>
);

function TerminalStatisticsChart() {
  const [isLoading, setLoading] = useState(true);
  const chartData1: ChartData = {
    series: [96, 10, 23],
    labels: ['Active', 'Inactive', 'Disable'],
    total: 129,
  };

  const chartData2: ChartData = {
    series: [4, 82, 10],
    labels: ['Online', 'Offline', 'Unknown'],
    total: 92,
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  const chartOptions1 = createChartOptions(chartData1);
  const chartOptions2 = createChartOptions(chartData2);

  return (
    <>
      {isLoading && <Spin className="loading-container" />}
      {!isLoading && (
        <div className="chart-wrapper terminal-statistics-chart">
          <div
            className="chart-container"
            style={{ display: 'flex', gap: '20px', position: 'relative' }}
          >
            <div className="chart-item" style={{ flex: 1 }}>
              <Chart
                options={chartOptions1}
                series={chartData1.series}
                type="donut"
                width="100%"
                height={250}
              />
            </div>
            <div className="chart-separator" />
            <div className="chart-item" style={{ flex: 1 }}>
              <Chart
                options={chartOptions2}
                series={chartData2.series}
                type="donut"
                width="100%"
                height={250}
              />
            </div>
          </div>
        </div>
      )}
    </>
  );
}

export default TerminalStatisticsChart;
