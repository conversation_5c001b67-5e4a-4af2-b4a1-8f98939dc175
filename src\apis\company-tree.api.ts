import { AxiosResponse } from "axios";
import { buildQueryParams, getAsync, postAsync, putAsync } from "./http-client";
import { CompanyResponse, CompanyGroupResponse, CompanyGroupRequest } from "../models/company-tree.model";
import { SelectionFilterRequest, SelectionDataResponse, SelectionUpdateRequest, ApiBaseModel } from "../models/common.model";

const baseUrl = import.meta.env.VITE_MMS_API_URL;

const getCompanyGroupMapping = (parentId: number | null, isIncludeGroup: boolean = true) : Promise<AxiosResponse<CompanyResponse>> => {
  const url = parentId === null 
    ? `${baseUrl}/company-details/company-group-mapping-load?isIncludeGroup=${isIncludeGroup}`
    : `${baseUrl}/company-details/company-group-mapping-load/?parentId=${parentId}&isIncludeGroup=${isIncludeGroup}`;
  return getAsync(url);
};

function getSelectedCompanies(
  request: SelectionFilterRequest
): Promise<AxiosResponse<SelectionDataResponse<ApiBaseModel>>> {
  const queryString = buildQueryParams(
    request
  );
  const url = `${baseUrl}/company-details/source-and-selected-company?${queryString}`;
  return getAsync(url);
}

function updateCompanySelection(
  parentId: number | null,
  selectionUpdateRequest: SelectionUpdateRequest
): Promise<AxiosResponse<void>> {
  const url = `${baseUrl}/company-details/select-remove-companies/${parentId ?? ''}`;
  return postAsync(url, selectionUpdateRequest, false, false);
}

function removeCompanySelected(id: number): Promise<AxiosResponse<void>> {
  const url = `${baseUrl}/company-details/remove-company-selected/${id}`;
  return putAsync(url);
}

function canDeleteItem(id: number): Promise<AxiosResponse<{ canDeleteItem: boolean }>> {
  const url = `${baseUrl}/company-details/can-delete/${id}`;
  return getAsync(url);
}

function getGroupById(id: number): Promise<AxiosResponse<CompanyGroupResponse>> {
  const url = `${baseUrl}/company-details/group/${id}`;
  return getAsync(url);
}

function addGroup(request: CompanyGroupRequest): Promise<AxiosResponse<void>> {
  const url = `${baseUrl}/company-details/group`;
  return postAsync(url, request);
}

function editGroup(id: number, request: CompanyGroupRequest): Promise<AxiosResponse<void>> {
  const url = `${baseUrl}/company-details/group/${id}`;
  return putAsync(url, request);
}

function searchCompanyTree(searchText: string): Promise<AxiosResponse<CompanyResponse>> {
  const url = `${baseUrl}/company-details/company-tree-search?searchText=${encodeURIComponent(searchText)}`;
  return getAsync(url);
}

export const CompanyTreeApi = {
  getCompanyGroupMapping,
  getSelectedCompanies,
  updateCompanySelection,
  removeCompanySelected,
  canDeleteItem,
  getGroupById,
  addGroup,
  editGroup,
  searchCompanyTree,
};
