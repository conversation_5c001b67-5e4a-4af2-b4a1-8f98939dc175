using MMS.Core.Entities;
using MMS.Infrastructure.Commons;
using MMS.Model.ApiModelRequest;
using MMS.Model.ApiModelResponse;
using MMS.Model.Common;
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace MMS.Core.Repository
{
    public interface IMerchantMasterRepository : IBaseRepository<MerchantMaster>
    {
        MerchantMaster GetByIdWithIncludes(int id, bool isIncludeList, params Expression<Func<MerchantMaster, object>>[] includes);

        /// <summary>
        /// Getting source for select remove form with flag is multi merchant or not.
        /// </summary>
        /// <param name="terminalMasterId"></param>
        /// <returns></returns>
        Task<(IList<MerchantMaster> MerchantMasters, bool MultiMerchantLocation)> GetSourceForDeviceSelectMerchantAsync(int terminalMasterId);

        /// <summary>
        /// Getting source for select remove form with flag is multi merchant or not.
        /// </summary>
        /// <param name="terminalMasterId"></param>
        /// <param name="selectedIds"></param>
        /// <returns></returns>
        Task<(PagingResponse<SelectListItemModel> Merchants, bool MultiMerchantLocation)> GetSourceForSelectMerchantAsync(int terminalMasterId, IList<int> selectedIds, string searchKey, SelectedFilterType filter, PagingParameter pagingParameter);

        /// <summary>
        /// Asynchronously retrieves a list of merchant masters associated with the specified location area ID.
        /// </summary>
        /// <param name="locationAreaId">
        /// The ID of the location area used to filter and select the associated merchants.
        /// </param>
        /// <returns>
        /// A task that represents the asynchronous operation. The task result contains a list of <see cref="MerchantMaster"/> objects associated with the given location area ID.
        /// </returns>
        Task<IList<MerchantMaster>> GetSelectMerchantByLocationItemAsync(int locationAreaId);

        /// <summary>
        /// Retrieves a list of merchant masters from the database where the Company ID is not null.
        /// </summary>
        /// <remarks>
        /// This method queries the database asynchronously to fetch merchant masters with non-null Company IDs, ensuring efficient and non-blocking operations.
        /// </remarks>
        /// <returns>
        /// A task that represents the asynchronous operation.
        /// The task result contains an <see cref="IList{MerchantMaster}"/> of merchant masters where the Company ID is not null.
        /// </returns>
        Task<IList<MerchantMaster>> GetMerchantMasterListWithNotNullMerchantCompanyIdAsync();

        /// <summary>
        /// Retrieves a list of merchant masters from the database that have a LinklyId.
        /// </summary>
        /// <returns>A list of merchant masters with LinklyId</returns>
        Task<IList<MerchantMaster>> GetLinklyMerchantMastersAsync();
    }
}
