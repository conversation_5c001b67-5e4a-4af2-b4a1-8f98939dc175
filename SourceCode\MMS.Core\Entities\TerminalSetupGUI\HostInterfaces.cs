﻿using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Org.BouncyCastle.Asn1.Cms;
using MMS.Core.Attributes;
using MMS.Core.Entities;
using MMS.Core.CoreUTI;
using MMS.Core.CoreUTI.Enum;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;
using System.Xml.Serialization;

namespace MMS.Core.Entities.TerminalSetupGUI
{
    public class HostInterfaces : BaseEntity
    {
        public HostInterfaces() { }

        public HostInterfaces(int teminalMasterId):this()
        {
            this.TerminalMasterId = teminalMasterId;
        }

        [MultiLanguageSupport]
        [TMSTemplateField]
        public string Name { get; set; }

        [TMSTemplateField]
        public bool Enabled { get; set; }
        
        public string IconId { get; set; }
        public ProcessorLibrary LibTrainProcessorId { get; set; } //ENUM
        
        
        public bool BalanceInquiry { get; set; }
        public bool Security { get; set; }
        public int? BinTableId { get; set; }

        [ForeignKey("BinTableId")]
        public virtual BinTable BinTable { get; set; }
        public int DaysExpire { get; set; }
        //using only time 
        public DateTime StartTime { get; set; }
        //using only time 
        public DateTime ExpiryTime { get; set; }
        public int? TerminalMasterId { get; set; }
        public byte[] SettingProcessor{ get; set; }

        [ForeignKey("TerminalMasterId")]
        public virtual TerminalMaster TerminalMaster { get; set; }

        public virtual IList<Batch> Batches { get; set; }

        public virtual IList<MerchantProcessor> MerchantProcessors { get; set; }

        public int? ParentId { get; set; }
        [ForeignKey(nameof(ParentId))]
        public HostInterfaces Parent { get; set; }

        public bool IsUpdated { get; set; }
    }
    public class HostInterfacesMap : IEntityTypeConfiguration<HostInterfaces>
    {
        public void Configure(EntityTypeBuilder<HostInterfaces> builder)
        {
            builder.ToTable(Constants.HostInterfaces);
            builder.HasKey(reg => reg.Id);

            builder.HasMany(h => h.Batches)
                .WithOne(b => b.HostInterface)
                .HasForeignKey(b => b.IdProcessor)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasMany(hi => hi.MerchantProcessors)
                .WithOne(mp => mp.HostInterface)
                .HasForeignKey(mba => mba.IdProcessor)
                .OnDelete(DeleteBehavior.Cascade);
        }
    }
}
