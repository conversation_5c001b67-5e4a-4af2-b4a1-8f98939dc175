﻿using System;
using MMS.Core.Services.Base;
using MMS.Core.Entities;
using MMS.Core.Entities.Locations;
using MMS.Core.CoreUTI;
using MMS.Core.CoreUTI.Enum;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;
using MMS.Model.ApiModelRequest;
using MMS.Model.ApiModelResponse;

namespace MMS.Core.Services.Locations
{
    public interface ILocationItemService : IBaseService<LocationItem>
    {
        void Insert(LocationItem entity);
        void Insert(IEnumerable<LocationItem> entities, bool noTrack = false);
        IList<LocationItem> GetAll(Expression<Func<LocationItem, bool>> expression = null, bool includeDeleted = false,
            bool includeHardCoded = false,
            bool includeDeactivated = false);
        IList<LocationItem> GetAllIndustry();

        IList<LocationItem> GetAllLocations();

        IList<LocationItem> GetAllLocationAreas();
        
        IList<LocationItem> GetAllLocationItemsByLevel(LocationLevel locationLevel);

        IDictionary<int, string> GetIndustryList();

        IList<LocationItem> GetAllChain();

        /// <summary>
        /// Get all activated - except deactivated, hardcoded, deleted.
        /// </summary>
        /// <param name="locationLevel"></param>
        /// <returns></returns>
        IList<LocationItem> GetAllByLocationLevel(LocationLevel locationLevel);
        IList<LocationItem> GetAppsAlongEcommerce(bool isEcommerce);

        IList<LocationItem> GetChainByIndustry(LocationItem industry);
        IList<LocationItem> GetChainByIndustry(int industryId);

        IList<LocationItem> GetByParent(LocationItem parent);

        /// <summary>
        /// Get location item global for select
        /// </summary>
        /// <param name="parentId"></param>
        /// <param name="locationLevel"></param>
        /// <returns></returns>
        Task<IList<LocationItem>> GetLocationSource(int parentId, LocationLevel locationLevel);

        IList<LocationItem> GetByIds(IEnumerable<int> ids);
        IList<LocationItem> GetByIds(IEnumerable<int> ids, bool isActivate);

        IList<LocationItem> GetMenuByParents(int applicationId, int locationId);

        /// <summary>
        /// Get list locationItem with locationItemId.
        /// </summary>
        /// <param name="locationItemId">Is two case:  One is locationItemId, two is LocationId</param>
        /// <param name="locationId"></param>
        /// <returns></returns>
        IList<LocationItem> GetLocationItemById(int locationItemId, int locationId);

        /// <summary>
        /// Get by exception ids.
        /// Get all without deactivate item.
        /// </summary>
        /// <param name="ids"></param>
        /// <returns></returns>
        IList<LocationItem> GetByExceptionIds(int[] ids);

        /// <summary>
        /// Get Industry of current location item
        /// </summary>
        /// <param name="item"></param>
        /// <returns></returns>
        LocationItem FindRoot(LocationItem item);

        /// <summary>
        /// Get Chain of current LocationItem
        /// </summary>
        /// <param name="item"></param>
        /// <returns></returns>
        LocationItem FindChain(LocationItem item);

        /// <summary>
        /// Find by location item with level
        /// </summary>
        /// <param name="item"></param>
        /// <param name="level"></param>
        /// <returns>null if current item is over this level</returns>
        LocationItem FindLevel(LocationItem item, LocationLevel level);

        LocationItem GetIndustryByLocation(LocationItem locationItem);
        LocationItem GetIndustryByLocation(int locationItemId);

        IList<LocationItem> ClearRestrictLocationItemBelow(IList<LocationItem> locationItems);

        IList<LocationItem> GetAllLocationItemBelow(int locationItemId, string parentsStr);
        IList<LocationItem> GetAllLocationItemBelow(int locationItemId, IList<int> parentIds);
        IList<LocationItem> GetAllLocationItemBelow(LocationItem locationItem, IList<int> parentIds);
        IList<LocationItem> GetAllLocationItemBelow(LocationItem locationItem, string locationItemIdsStr);

        bool IsChild(LocationItem locationItem, LocationItem parent);

        int CoutByLocation(int locationId);

        void GenerateHierarchyForAllItem();

        string GenerateHierarchy(LocationItem locationItem);

        LocationItem GetParentAtLevel(LocationItem locationItem, LocationLevel level);

        void UpdateNationality(LocationItem locationItem, int[] ids);

        void UpdateEthnicity(LocationItem locationItem, int[] ids);

        void UpdateLanguage(LocationItem locationItem, int[] ids);

        void UpdateIncomeRange(LocationItem locationItem, int[] ids);

        void UpdateGender(LocationItem locationItem, int[] ids);

        void UpdateSexualPreference(LocationItem locationItem, int[] ids);

        void UpdateAgeRange(LocationItem locationItem, int[] ids);

        /// <summary>
        /// Get list item for LocationId column has parent equal locationItemId
        /// </summary>
        /// <param name="locationItemId"></param>
        /// <param name="locationId"></param>
        /// <param name="includeDeactivated"></param>
        /// <returns></returns>
        IList<LocationItem> GetLocationItems(int locationItemId, int locationId, bool includeDeactivated = false);

        string GetNameById(int locationItemId);

        /// <summary>
        /// get list id of locations over.
        /// Check and update hierarchy
        /// </summary>
        /// <param name="locationItem"></param>
        /// <param name="isUseItself"></param>
        /// <returns></returns>
        IEnumerable<int> GetHierarchy(LocationItem locationItem, bool isUseItself);

        IList<LocationItem> GetRestrict(IEnumerable<int> locationItemsRestrictIds);
        IList<LocationItem> GetWithoutRestrict(IEnumerable<int> locationItemsRestrictIds, int locationiItemIdRootOfRestrict);

        IList<LocationItem> GetMenusByStoreLocation(int storeLocationItemId);
        IList<LocationItem> GetDevicesByStoreLocation(int storeLocationItemId);
        IList<LocationItem> GetDeviceByBrandModel(int brandId, int modelId, DeviceType type);
        IList<LocationItem> GetDevice(int parentId, bool includeDeactivated);
        IList<LocationItem> GetMenusByDevice(int deviceLocationItemId);

        IList<LocationItem> GetMenuByApplication(int deviceId, int applicationId);

        IList<LocationItem> GetMenu(int applicationId);

        /// <summary>
        /// Calculated DeviceId with same parent, it will change data of Devices list.
        /// </summary>
        /// <param name="parentId"></param>
        void CalculateDeviceId(int parentId);

        int GenerateDeviceId(int parentId);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="parentId"></param>
        /// <param name="deviceId"></param>
        /// <param name="id"></param>
        /// <returns>True if existsed</returns>
        bool CheckDeviceIdIsExistsed(int parentId, int deviceId, int id);
        LocationItem GetLastInTheTree(IEnumerable<int> locationItemIds);
        LocationItem GetStoreLocationByDevice(int deviceId);

        /// <summary>
        /// Return a list of ids.
        /// </summary>
        /// <param name="locationItemSelectedIds"></param>
        /// <param name="includeItself"></param>
        /// <returns></returns>
        IList<int> GetForGetNearest(string locationItemSelectedIds, bool includeItself);
        IList<int> GetAllParentByLocation(int locationItemId);

        IDictionary<int, string> ValidateRestrictedIds(IDictionary<int, string> dictRestricted);
        IList<int> GetAllRestrictId(IDictionary<int, string> dictRestricted);

        LocationItem GetByLevel(string parentIdsStr, LocationLevel locationLevel);
        LocationItem GetByLevel(IList<int> parentIds, LocationLevel locationLevel);

        IList<int> GetParentIdsForAPI(LocationItem app, LocationItem device);

        LocationItem GetHighestLevel(IList<int> locationItemIds);

        IList<LocationItem> GetAllApps();

        IList<LocationItem> GetAppsByDevice(int deviceId);     
        IList<LocationItem> GetAppsByDevice(LocationItem device);
        IList<LocationItem> GetAllLocationAreaByParent(int parentId);

        /// <summary>
        /// Asynchronously retrieves a <see cref="LocationItem"/> by its identifier, including the associated level information.
        /// </summary>
        /// <param name="locationItemId">The unique identifier of the <see cref="LocationItem"/> to retrieve.</param>
        /// <returns>
        /// A task that represents the asynchronous operation. The task result contains the <see cref="LocationItem"/>
        /// with the associated level details, or <c>null</c> if no matching item is found.
        /// </returns>
        Task<LocationItem> GetLocationItemIncludeLevelAsync(int locationItemId);

        /// <summary>
        /// Asynchronously retrieves the <see cref="LocationLevel"/> associated with a specific location item.
        /// </summary>
        /// <param name="locationItemId">The unique identifier of the location item to retrieve the level for.</param>
        /// <returns>
        /// A task that represents the asynchronous operation. The task result contains the <see cref="LocationLevel"/>
        /// associated with the given location item, or <c>null</c> if no matching level is found.
        /// </returns>
        Task<LocationLevel> GetLocationLevelByLocationId(int locationItemId);
        IList<int> GetAllParents(string parentIdsStr);

        LocationItem GetIndustry(string parentIds);
        IList<LocationItem> GetAllParentAtLevel(int id, LocationLevel level);
        int GetImmediateParentId(int id);
        Location GetHighestLocation(IList<int> ids, bool isAllowed);

        LocationItem GetAppByAppUsedId(int appUsedId);
        IList<LocationItem> GetDeviceByUsedItemId(int usedItemId);

        IList<LocationItem> SearchDeviceApplication(string name);
        int GetCurrencyTypeIdByItemId(int itemId);
        LocationItem GetDefaultData(string locationItemIdsStr, LocationLevel level, bool includingCurrentLevel);

        void GenerateIFashionRoamingDefault(LocationItem item);

        IList<LocationItem> GetSelectedIndustry(bool useSelectSort, bool includeDeactivated = true);
        void UpdateSelectedIndustry(List<int> selectedIds, List<IndustryType> businessTypes, int locationId, bool useSelectSort = false);
        void UpdateRemovedIndustry(List<int> selectedIds);
        IList<LocationItem> GetStoreLocations(int locationItemId);
        LocationItem GetFirstDefaultData(string locationItemIdsStr);
        Dictionary<int, int> GetLocationItemsSelectedPre(int locationitemId);
        IList<Location> GetAllSelectedLocations(IEnumerable<int> ids);

        /// <summary>
        /// Get location item selected
        /// </summary>
        /// <param name="parentId"></param>
        /// <param name="screenType"></param>
        /// <param name="locationLevel"></param>
        /// <param name="groupMappingId"></param>
        /// <returns></returns>
        Task<IList<LocationItemSelect>> GetSelectedLocations(int? parentId, ScreenType screenType, LocationLevel? locationLevel = null, int? groupMappingId = null);

        /// <summary>
        /// Update location item select
        /// </summary>
        /// <param name="selectedIds"></param>
        /// <param name="parentId"></param>
        /// <param name="screenType"></param>
        /// <param name="groupMappingId"></param>
        /// <returns></returns>
        Task UpdateSelectedLocations(List<int> selectedIds, int? parentId, ScreenType screenType, int? groupMappingId);
        IList<LocationItem> GetSelectedChains(int industryId, bool useSelectSort = false);
        void UpdateSelectedChains(List<int> selectedIds, int industryId, bool useSelectSort = false);
        IList<LocationItem> GetChains(int industryId);
        LocationItem GetLocationAreaFirstPaxStore(string locationName);

        /// <summary>
        /// Get location item source
        /// </summary>
        /// <param name="parentLocationId"></param>
        /// <param name="locationLevel"></param>
        /// <param name="selectedIds"></param>
        /// <param name="selectRemoveFormParameter"></param>
        /// <returns></returns>
        Task<PagingResponse<DataItemResponse>> GetSourceForSelectLocationItemAsync(int parentLocationId, LocationLevel locationLevel, List<int> selectedIds, SelectRemoveFormParameter selectRemoveFormParameter);
    }
}
