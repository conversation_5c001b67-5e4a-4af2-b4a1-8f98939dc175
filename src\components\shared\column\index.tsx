import React, { useState, useMemo, useRef, useEffect } from 'react';
import { Dropdown, List, Input } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import './index.scss';
import FontIcon from '../icons/font-icon';
import type { MenuProps } from 'antd';

export interface DataItem {
  key: number;
  label: string;
  active?: boolean;
}

interface SearchItem {
  key: -1;
  label: 'search';
  isSearch: true;
}

type ListItem = DataItem | SearchItem;

interface DataColumnItemsProps {
  data: DataItem[];
  name: string;
  onSelect: (item: DataItem) => void;
  loading?: boolean;
  selectedKey?: number | null;
  columnWidthClass?: string;
  columnHeightClass?: string;
  menuItems?: MenuProps['items'];
  showMenuIcon?: boolean;
}

const DataColumnItems: React.FC<DataColumnItemsProps> = ({
  data,
  name,
  onSelect,
  loading = false,
  selectedKey = null,
  columnWidthClass,
  columnHeightClass,
  menuItems = [],
  showMenuIcon = true,
}) => {
  const [searchValue, setSearchValue] = useState('');
  const [showSearchInput, setShowSearchInput] = useState(false);
  const inputRef = useRef<any>(null);

  useEffect(() => {
    if (showSearchInput && inputRef.current) {
      inputRef.current.focus();
    }
  }, [showSearchInput]);

  const filteredData = useMemo(() => {
    if (!searchValue) return data;
    return data.filter((item) =>
      item.label.toLowerCase().includes(searchValue.toLowerCase()),
    );
  }, [data, searchValue]);

  const handleSelect = (item: DataItem) => {
    onSelect(item);
  };

  const handleSearchChange = (value: string) => {
    setSearchValue(value);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Escape') {
      if (searchValue) {
        setSearchValue('');
      } else {
        setShowSearchInput(false);
      }
    }
  };

  const allMenuItems: MenuProps['items'] = [
    {
      key: 'search',
      label: showSearchInput ? 'Close Search' : 'Search',
      onClick: () => setShowSearchInput(!showSearchInput),
    },
    ...(menuItems || []),
  ];

  const allItems: ListItem[] = showSearchInput
    ? [{ key: -1, label: 'search', isSearch: true }, ...filteredData]
    : filteredData;

  return (
    <List
      className={`data-items ${columnWidthClass} ${columnHeightClass}`}
      loading={loading}
      header={
        <>
          <span className="column-name">{name}</span>
          <Dropdown
            menu={{ items: allMenuItems }}
            trigger={['click']}
            placement="bottomRight"
            autoAdjustOverflow={true}
          >
            <div className="menu-icon">
              {showMenuIcon && (
                <FontIcon size={16} className="icon-hamburger-menu" />
              )}
            </div>
          </Dropdown>
        </>
      }
      bordered
      dataSource={allItems}
      renderItem={(item) => {
        if ('isSearch' in item) {
          return (
            <List.Item className="search-input-item">
              <Input
                ref={inputRef}
                prefix={<SearchOutlined />}
                placeholder="Search..."
                allowClear
                value={searchValue}
                onChange={(e) => handleSearchChange(e.target.value)}
                onClick={(e) => e.stopPropagation()}
                onKeyDown={handleKeyDown}
                className="search-input-no-border"
              />
            </List.Item>
          );
        }

        return (
          <List.Item
            className={`item body-2-regular ${selectedKey === item.key ? 'selected' : ''}`}
            onClick={() => handleSelect(item)}
          >
            {item.active === false && (
              <FontIcon size={16} className="icon-close" />
            )}
            <span className="item-label">{item.label}</span>
          </List.Item>
        );
      }}
    />
  );
};

export default DataColumnItems;
