using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using MMS.Core.ConfigOptions;
using MMS.Core.CoreUTI;
using MMS.Core.CoreUTI.Enum;
using MMS.Core.dbContext;
using MMS.Core.Entities;
using MMS.Core.Utils;
using MMS.Infrastructure.Commons;
using MMS.Model.ApiModelRequest;
using MMS.Model.ApiModelResponse;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace MMS.Core.Repository.Impl
{
    public class TerminalMasterRepository : BaseRepository<TerminalMaster>, ITerminalMasterRepository
    {
        private const string sqlScriptUpdate = "Update ";
        private readonly IOptions<TerminalSourceOptions> _terminalSourceOptions;

        public TerminalMasterRepository(
            DbContextFactory contextFactory,
            MMSContext scopedMMSContext,
            IOptions<TerminalSourceOptions> terminalSourceOptions = null) : base(contextFactory)
        {
            SetScopeMMSContext(scopedMMSContext);
            _terminalSourceOptions = terminalSourceOptions;
        }

        public IList<TerminalMaster> GetAllTerminalsByApplicationTemplate(int id)
        {
            var context = GetContext();
            var result = context.Set<TerminalMaster>()
                .Include(p => p.MerchantTerminals).ThenInclude(p => p.MerchantMaster)
                .Include(p => p.ApplicationUpgradeTemplate)
                .Include(p => p.ApplicationUpgradeTerminal)
                .Where(p => p.IsStatus != Constants.DELETE_RECORD && p.ApplicationUpgradeTemplate.Id.Equals(id))
                .OrderBy(p => p.TerminalSerialNumber).ToList();

            CtxDisposeOrNot(context);

            return result;
        }

        public T GetNaviProperty<T>(int id, Expression<Func<TerminalMaster, object>> select, params Expression<Func<TerminalMaster, object>>[] includes) where T : class
        {
            T result = default;
            var context = GetContext();
            lock (context)
            {
                var query = context.Set<TerminalMaster>().Where(p => p.Id == id);
                query = query.Include(select);

                foreach (var item in includes)
                {
                    query = query.Include(item);
                }

                var selectSingleT = query.Select(select).FirstOrDefault() as T;

                result = selectSingleT;

            }
            CtxDisposeOrNot(context);

            return result;
        }
        public IList<T> GetNaviPropertyList<T>(int id, Expression<Func<TerminalMaster, object>> select, params Expression<Func<TerminalMaster, object>>[] includes) where T : class
        {
            IList<T> result = new List<T>();
            var context = GetContext();
            lock (context)
            {
                var query = context.Set<TerminalMaster>().Where(p => p.Id == id);
                query = query.Include(select);

                foreach (var item in includes)
                {
                    query = query.Include(item);
                }

                result = (query.Select(select) as IList<T>) ?? new List<T>();

            }
            CtxDisposeOrNot(context);

            return result;
        }

        public TerminalMaster GetTerminalByTerminalId(int terId)
        {
            var context = GetContext();
            var result = context.Set<TerminalMaster>().Where(p => p.IsStatus != Constants.DELETE_RECORD && p.Id == terId)
                .Include(p => p.MerchantTerminals).ThenInclude(p => p.MerchantMaster).FirstOrDefault();

            CtxDisposeOrNot(context);

            return result;
        }

        public async Task<TerminalMaster> GetTerminalByDeviceTemplateId(int templateId, DeviceSetupType type)
        {
            var context = GetContext();
            var result = await context.Set<TerminalMaster>().Where(p => p.IsStatus != Constants.DELETE_RECORD && p.CopyDeviceTemplateId == templateId && p.DeviceSetupType == type).Include(p => p.DeviceTemplate).FirstOrDefaultAsync();

            CtxDisposeOrNot(context);

            return result;
        }

        public async Task<List<TerminalMaster>> GetDeletedTemplateTerminalsAsync()
        {
            var context = GetContext();

            var result = await context.Set<TerminalMaster>()
                .Where(p =>
                    p.CopyDeviceTemplateId.HasValue &&
                    p.DeviceTemplate.IsStatus == Constants.DELETE_RECORD)
                .Include(p => p.DeviceTemplate)
                .AsNoTracking()
                .ToListAsync();

            CtxDisposeOrNot(context);

            return result;
        }


        public TerminalMaster GetMerchantCardFallback(int terId)
        {
            var context = GetContext();
            var result = context.Set<TerminalMaster>().Where(p => p.IsStatus != Constants.DELETE_RECORD && p.Id == terId)
                .Include(p => p.MerchantCardFallbacks).FirstOrDefault();

            CtxDisposeOrNot(context);

            return result;
        }

        public int LastMaster()
        {
            int result = 0;
            var context = GetContext();
            lock (context)
            {
                result = context.Set<TerminalMaster>().OrderByDescending(m => m.Id).Select(p => p.Id).FirstOrDefault();
            }

            CtxDisposeOrNot(context);

            return result;
        }

        public IList<TerminalMaster> TerminalActiveByGroupId(int id, string search, int index, int limit, IEnumerable<int> _groupTerminals)
        {
            var context = GetContext();
            var result = context.Set<TerminalMaster>()
                .Include(p => p.MerchantTerminals).ThenInclude(p => p.MerchantMaster)
                .Include(p => p.GroupMaster).AsQueryable()
                .Where(p => p.IsStatus != Constants.DELETE_RECORD && p.TerminalSerialNumber.Contains(search ?? "") &&
                                _groupTerminals.Contains(p.GroupMaster.Id) &&
                                p.IsActive)
                .OrderBy(o => o.TerminalSerialNumber).ToList();

            CtxDisposeOrNot(context);

            return result;
        }

        public IList<TerminalMaster> TerminalByGroupCompanyModelAndFilter(int modelId)
        {
            var context = GetContext();
            var result = context.Set<TerminalMaster>().Where(p => p.IsStatus != Constants.DELETE_RECORD && p.TerminalModelType.Id.Equals(modelId))
                .Include(p => p.TerminalSetupTemplate)
                .Include(p => p.TerminalModelType)
                .Include(p => p.ApplicationUpgradeTemplate)
                .Include(p => p.FirmwareUpgradeTemplate)
                .Include(p => p.GroupMaster)
                .Include(p => p.MerchantTerminals).ThenInclude(c => c.MerchantMaster)
                .ToList();

            CtxDisposeOrNot(context);

            return result;
        }

        public IList<TerminalMaster> TerminalByGroupCompanyUserAndLevel()
        {
            var context = GetContext();
            var result = context.Set<TerminalMaster>().Where(p => p.IsStatus != Constants.DELETE_RECORD)
                .Include(p => p.MerchantTerminals).ThenInclude(c => c.MerchantMaster)
                .Include(p => p.UsersAccessTerminal).ThenInclude(p => p.SecurityLevelMaster)
                .Include(p => p.UserTerminals)
                .Include(p => p.GroupMaster).ToList();

            CtxDisposeOrNot(context);

            return result;
        }

        public bool SetIsStatus(string table, List<int> ids, int isStatus)
        {
            if (ids == null || ids.Count <= 0)
            {
                return true;
            }

            var whereId = new StringBuilder('(');

            for (var i = 0; i < ids.Count; i++)
            {
                if (i != 0) whereId.Append(',');
                whereId.Append(ids[i]);
            }
            whereId.Append(')');

            var sqlString = new StringBuilder(sqlScriptUpdate)
                .Append(table)
                .Append(' ')
                .Append("SET IsStatus = ")
                .Append(isStatus)
                .Append(" WHERE Id IN ")
                .Append(whereId);

            try
            {
                var context = GetContext();
                lock (context)
                {
                    context.Database.ExecuteSqlRaw(sqlString.ToString());
                }
                CtxDisposeOrNot(context);
            }
            catch
            {
                return false;
            }
            return true;
        }

        public bool SetIsStatus(string table, string whereStatus, int isStatus)
        {
            var sqlString = "";
            if (table == Constants.TerminalReportBatches)
            {
                sqlString = sqlScriptUpdate + table + " " +
                            "set Status = " + isStatus + " " + whereStatus;
            }
            else
            {
                sqlString = sqlScriptUpdate + table + " " +
                            "set IsStatus = " + isStatus + " " + whereStatus;
            }


            try
            {
                var context = GetContext();
                lock (context)
                {
                    context.Database.ExecuteSqlRaw(sqlString);
                }
                CtxDisposeOrNot(context);
            }
            catch
            {
                return false;
            }
            return true;
        }

        public async Task<bool> SetIsStatusWithColumnGroupAsync(string table, string whereStatus, int isStatus, string columnName)
        {

            // SQL to update only the first row per group, setting the rest as deleted (IsStatus = 3) (DELETE_RECORD = 3)
            var sqlString = $@"
                WITH GroupedRows AS (
	                SELECT *,
	                       ROW_NUMBER() OVER (PARTITION BY {columnName} ORDER BY Id) AS RowNum
	                FROM {table}
	                {whereStatus}
	            )

	            UPDATE {table} AS mt
	            JOIN (
	                SELECT Id,
	                       ROW_NUMBER() OVER (PARTITION BY {columnName} ORDER BY Id) AS RowNum
	                FROM {table}
	                {whereStatus}
	            ) AS GroupedRows ON mt.Id = GroupedRows.Id
	            SET mt.IsStatus = CASE
	                WHEN GroupedRows.RowNum = 1 THEN 0  -- Set to active or desired status
	                ELSE 3                              -- Set to deleted or inactive
	            END;
            ";


            try
            {
                var context = GetContext();

                // as this case, we use for api sync up restore, which create context every time,
                // then we don't need to lock the context
                await context.Database.ExecuteSqlRawAsync(sqlString);

                CtxDisposeOrNot(context);
            }
            catch
            {
                return false;
            }
            return true;
        }


        public bool SetIsClientId(string table, List<int> ids, int clientId)
        {
            if (ids == null || ids.Count <= 0)
            {
                return true;
            }
            var whereId = new StringBuilder('(');
            for (var i = 0; i < ids.Count; i++)
            {
                if (i != 0) whereId.Append(',');
                whereId.Append(ids[i]);
            }
            whereId.Append(')');

            var sqlString = new StringBuilder(sqlScriptUpdate)
                .Append(table)
                .Append(' ')
                .Append("set ClientId = ")
                .Append(clientId)
                .Append(" WHERE Id IN ")
                .Append(whereId);

            try
            {
                var context = GetContext();
                lock (context)
                {
                    context.Database.ExecuteSqlRaw(sqlString.ToString());
                }
                CtxDisposeOrNot(context);
            }
            catch
            {
                return false;
            }
            return true;
        }

        public List<int> GetAllTerminalSetupUpdateByTemplateId(int id)
        {
            var result = new List<int>();
            var context = GetContext();
            lock (context)
            {
                result = context.TerminalMaster.Where(m => !(m.IsStatus != Constants.DELETE_RECORD && m.TerminalSetupTemplate == null) && m.TerminalSetupTemplate.Id.Equals(id))
                    .Include(p => p.TerminalSetupTemplate).Include(p => p.TerminalSetupUpdate).Select(m => m.TerminalSetupUpdate.Id).ToList();
            }
            CtxDisposeOrNot(context);
            return result;
        }

        public TerminalMaster GetTerminalBySerialNumber(string serialNumber, string macAddress, int modelId)
        {
            TerminalMaster result = null;
            var context = GetContext();
            lock (context)
            {
                result = GetTerminalBySerialNumberInclude(context.TerminalMaster)
                       .Where(m => m.TerminalSerialNumber.Equals(serialNumber))
                       .Where(m => m.TerminalModelType.Id.Equals(modelId))
                       .Where(p => p.IsStatus != Constants.DELETE_RECORD)
                       .FirstOrDefault(m => m.LogonMacAddress.Equals(macAddress));
            }

            CtxDisposeOrNot(context);
            return result;
        }

        public TerminalMaster GetTerminalBySerialNumber(string serialNumber)
        {
            TerminalMaster result = null;
            var context = GetContext();
            lock (context)
            {
                result = GetTerminalBySerialNumberInclude(context.TerminalMaster)
                       .Where(m => m.TerminalSerialNumber.Equals(serialNumber))
                       .Where(p => p.IsStatus != Constants.DELETE_RECORD)
                       .FirstOrDefault();
            }

            CtxDisposeOrNot(context);
            return result;
        }

        /// <summary>
        /// Including for the method GetTerminalBySerialNumber
        /// </summary>
        /// <param name="terminalMasters"></param>
        /// <returns></returns>
        private static IQueryable<TerminalMaster> GetTerminalBySerialNumberInclude(IQueryable<TerminalMaster> terminalMasters)
        {
            var result = terminalMasters
                    .Include(p => p.PayplusProcessor)
                    .Include(p => p.CountryTerminal)
                    .Include(p => p.FallBack)
                    .Include(p => p.TerminalWAN)
                    .Include(p => p.TerminalWifi)
                    .Include(p => p.CountryTerminal.alCoinDenominations)
                    .Include(p => p.CountryTerminal.alNotesDenominations)

                    .Include(p => p.ApplicationUpgradeTerminal).ThenInclude(c => c.ApplicationVersion)
                    .Include(p => p.FirmwareUpgradeTerminal).ThenInclude(c => c.FirmwareVersion)
                    .Include(p => p.ProcessorTerminals).ThenInclude(m => m.Proccessor)
                    .Include(p => p.ProcessorTerminals).ThenInclude(m => m.TypeValues)
                    .Include(p => p.UserTerminals).ThenInclude(m => m.User)
                    .Include(p => p.TerminalSecurityAccesses).ThenInclude(m => m.TerminalSetupTemplate)
                    .Include(p => p.Support)
                    .Include(p => p.CashBack)
                    .Include(p => p.Date)
                    .Include(p => p.Sound)
                    .Include(p => p.UserTerminals)
                    .Include(p => p.TerminalModelType)
                    .Include(p => p.ApplicationVersion)
                    .Include(p => p.PosRequest).ThenInclude(c => c.Sale)
                    .Include(p => p.PosRequest).ThenInclude(c => c.Bar)
                    .Include(p => p.PosRequest).ThenInclude(c => c.Table)
                    .Include(p => p.PosRequest).ThenInclude(c => c.Customer)
                    .Include(p => p.PosRequest).ThenInclude(c => c.Room)
                    .Include(p => p.Training)
                     .ThenInclude(p => p.User)
                    .Include(p => p.AutoLogin)
                     .ThenInclude(p => p.User)
                    .Include(p => p.Support)
                    .Include(p => p.ExtraModudle)
                    .Include(p => p.ICSSetupByTerminalType)
                    .Include(p => p.ICSSetupByCardDataInput)
                    .Include(p => p.ICSSetupByCVMCapability)
                    .Include(p => p.ICSSetupBySecurityCapability)
                    .Include(p => p.ICSSetupByTmDataInput)
                    .Include(p => p.ICSSetupByTmDataOutput)
                    .Include(p => p.ICSSetupByCDAMode)
                    .Include(p => p.PosIntegration)
                    .Include(p => p.CloudPosIntegration)
                    .Include(p => p.POSInterfaces)
                    .Include(p => p.POSConnections)
                    .Include(p => p.TerminalStyle);

            return result;
        }

        public string GetAllXmlChanges(int terminalMasterId)
        {
            var context = GetContext();
            var result = new StringBuilder("<TerminalChange>");

            var terminalChanges = context.TerminalChange.Local.Where(p => p.TerminalId == terminalMasterId).ToList();

            foreach (var terminalChange in terminalChanges)
            {
                var isAdded = context.Entry(terminalChange).State == EntityState.Added;
                if (!isAdded) continue;

                result.Append(terminalChange.ChangedXml);

                Console.WriteLine(result.ToString());
            }

            CtxDisposeOrNot(context);

            result.Append("</TerminalChange>");

            Console.WriteLine(result.ToString());

            return result.ToString();
        }

        public TerminalMaster GetForDeviceSetupEdit(int terminalMasterId)
        {
            TerminalMaster result = null;
            var context = GetContext();
            lock (context)
            {
                result = context.TerminalMaster
                    .Where(p => p.Id == terminalMasterId)
                    .Include(p => p.TerminalBrandMaster)
                    .Include(p => p.TerminalModelType)
                    .Include(p => p.IoTDevice)  // Device Model
                    .ThenInclude(p => p.Brand) // IOT Device Brand
                    .Include(p => p.IoTDeviceCate) // Device Cate
                    .Include(p => p.IoTDeviceType) // Device Type
                    .Include(p => p.DeviceTemplate) // Template
                    .Include(p => p.IoTDevice.ModelType) // Template
                    .Include(p => p.DeviceIndustry).ThenInclude(p => p.DeviceTypes).ThenInclude(p => p.DeviceCategories)
                    .Include(p => p.FirmwareVersion)
                    .FirstOrDefault();
            }

            CtxDisposeOrNot(context);
            return result;
        }

        public async Task<List<ApplicationDeviceMapping>> GetSelectedApplicationListByTerminalMasterIdAsync(int terminalMaterId, List<int> source)
        {
            var context = GetContext();

            var query = await context.TerminalMaster
                .Include(p => p.ApplicationList.Where(t => t.IsStatus == Constants.NOTCHANGE_RECORD && source.Contains(t.ApplicationId))).ThenInclude(p => p.Application)
                .FirstOrDefaultAsync(p => p.Id == terminalMaterId && p.IsStatus != Constants.DELETE_RECORD && p.IsStatus != Constants.PRE_DELETE_RECORD);

            if (query is null || query.ApplicationList is null) return [];

            return query.ApplicationList;
        }

        public List<TerminalMaster> GetTerminalForPaxStore(Expression<Func<TerminalMaster, bool>> whereClause = null)
        {
            var result = new List<TerminalMaster>();
            var context = GetContext();
            lock (context)
            {
                result = context.TerminalMaster
                    .Where(whereClause)
                    .Include(p => p.IoTDevice)  // Device Model
                    .ThenInclude(p => p.Brand) // IOT Device Brand
                    .Include(p => p.IoTDeviceCate) // Device Cate
                    .Include(p => p.IoTDeviceType) // Device Type
                    .Include(p => p.IoTDevice.ModelType)
                    .Include(p => p.LocationArea)
                    .ThenInclude(p => p.Parent)
                    .ToList();
            }

            CtxDisposeOrNot(context);
            return result;
        }

        public async Task<List<TerminalMaster>> GetAllTerminalUnAssignedDeviceAsync(int companyIdOrg, int brandIdOrg, int modelIdOrg, bool isAssignedDevice = false, string searchKey = "", int companyId = 0, int accessStatusId = 0)
        {
            var context = GetContext();

            var query = from b in context.TerminalMaster.AsNoTracking()
                        where b.IsAssignedDevice == isAssignedDevice
                              && b.AssignedLocationAreaId == null
                              && b.TerminalSerialNumber.Contains(searchKey)
                              && b.IsStatus != Constants.DELETE_RECORD
                              && context.CompanyDeviceSetupMapping.Any(mapping =>
                                    mapping.DeviceBrandId == b.TerminalBrandId &&
                                    mapping.DeviceModelId == b.TerminalModelTypeId &&
                                    mapping.CompanyId == b.TerminalServiceDetail &&
                                    mapping.IsStatus != Constants.DELETE_RECORD)
                        select b;

            // Filter records in the original table that are not deactivated and deleted
            query = query.Where(p =>
                (p.TerminalServiceDetailCompany == null || (p.TerminalServiceDetailCompany.IsActive && p.TerminalServiceDetailCompany.IsStatus != Constants.DELETE_RECORD)) &&
                (p.TerminalBrandMaster == null || (p.TerminalBrandMaster.IsActive && p.TerminalBrandMaster.IsStatus != Constants.DELETE_RECORD)) &&
                (p.TerminalModelType == null || (p.TerminalModelType.IsActive && p.TerminalModelType.IsStatus != Constants.DELETE_RECORD))
            );

            // Apply additional filters
            if (companyIdOrg != 0)
            {
                query = query.Where(b => b.TerminalServiceDetail == companyIdOrg);
            }

            if (brandIdOrg != 0)
            {
                query = query.Where(b => b.TerminalBrandId == brandIdOrg);
            }

            if (modelIdOrg != 0)
            {
                query = query.Where(b => b.TerminalModelTypeId == modelIdOrg);
            }

            if (isAssignedDevice && companyId != 0 && accessStatusId != 0)
            {
                query = query.Where(b => b.IoTDeviceCompanyAssigneds.Any(x => x.CompanyId == companyId) &&
                                     b.IoTDeviceAssociatedStatus.Any(x => x.CompanyAccessStatus.AccessStatusId == accessStatusId));
            }

            // Apply includes after filtering
            var result = query
                .Include(b => b.IoTDeviceCompanyAssigneds)
                .Include(b => b.IoTDeviceAssociatedStatus)
                    .ThenInclude(p => p.CompanyAccessStatus)
                .Include(b => b.IoTDevice)
                .Include(b => b.TerminalBrandMaster)
                .Include(b => b.TerminalModelType);

            return await result.ToListAsync();
        }

        public async Task AssignDeviceToLocationAsync(int terminalId, int locationAreaId, int deviceIndustryId, int groupId)
        {
            var context = GetContext();

            var query = from b in context.TerminalMaster.AsNoTracking()
                        where b.Id == terminalId
                            && b.IsStatus != Constants.DELETE_RECORD
                            && b.IsStatus != Constants.PRE_DELETE_RECORD
                        select b;

            var terminalMaster = await query.FirstOrDefaultAsync();

            if (terminalMaster == null)
                throw new InvalidOperationException("The terminalMaster object was not found. Ensure the query returns a valid result.");

            terminalMaster.AssignedGroupId = groupId;
            terminalMaster.AssignedLocationAreaId = locationAreaId;
            terminalMaster.AssignedDeviceIndustryId = deviceIndustryId;
            terminalMaster.IsAssignedDevice = true;

            Update(terminalMaster);
        }

        public async Task AssignDeviceIndustryAsync(int terminalId, int deviceIndustryId)
        {
            var context = GetContext();

            var query = from b in context.TerminalMaster.AsNoTracking()
                        where b.Id == terminalId
                            && b.IsStatus != Constants.DELETE_RECORD
                            && b.IsStatus != Constants.PRE_DELETE_RECORD
                        select b;

            var terminalMaster = await query.FirstOrDefaultAsync();

            if (terminalMaster == null)
                throw new InvalidOperationException("The terminalMaster object was not found. Ensure the query returns a valid result.");

            terminalMaster.IsAssignedDevice = true;
            terminalMaster.AssignedDeviceIndustryId = deviceIndustryId;

            Update(terminalMaster);
        }

        public async Task<List<TerminalMaster>> GetDeviceAssignedAsync(IList<int> locationAreaIds, IList<int> deviceIndustryIds, int deviceBrandId, int deviceModelId, int companyId, int groupId)
        {
            var context = GetContext();

            var query = from b in context.TerminalMaster.AsNoTracking()
                        where b.AssignedLocationAreaId != null && locationAreaIds.Contains(b.AssignedLocationAreaId!.Value)
                            && b.AssignedDeviceIndustryId != null && deviceIndustryIds.Contains(b.AssignedDeviceIndustryId!.Value)
                            && b.AssignedDeviceIndustryId != null
                            && b.IsStatus != Constants.DELETE_RECORD
                            && b.DeviceSetupType == DeviceSetupType.None
                            && b.IsStatus != Constants.PRE_DELETE_RECORD
                        select b;

            if (deviceBrandId != 0)
                query = query.Where(p => p.TerminalBrandId == deviceBrandId);

            if (deviceModelId != 0)
                query = query.Where(p => p.TerminalModelTypeId == deviceModelId);

            if (companyId != 0)
            {
                query = query.Where(p => p.TerminalServiceDetail == companyId);
            }

            if (groupId != 0)
            {
                query = query.Where(p => p.AssignedGroupId == groupId);
            }

            var terminalMasters = await query.Include(p => p.TerminalBrandMaster).ToListAsync();

            return terminalMasters;
        }

        public async Task<IList<TerminalMaster>> GetListDeviceByListIdAsync(IList<int> ids)
        {
            var result = await ScopedEntities
                    .Where(p => ids.Contains(p.Id) && p.IsStatus == Constants.NOTCHANGE_RECORD)
                    .ToListAsync();

            return result;
        }

        public async Task<List<TerminalMaster>> GetDeviceListByFiltersAsync(int groupId, IList<int> deviceIndustryIds, int deviceBrandId, int deviceModelId, int companyId, IList<int> locationAreaIds)
        {
            var query = ScopedEntities.Where(b =>
                b.TerminalServiceDetail == companyId &&
                b.AssignedLocationAreaId != null && locationAreaIds.Contains(b.AssignedLocationAreaId!.Value) &&
                b.IsStatus != Constants.DELETE_RECORD && b.IsStatus != Constants.PRE_DELETE_RECORD);

            if (deviceIndustryIds.Count == 0)
            {
                return [];
            }

            if (groupId != 0)
                query = query.Where(b => b.AssignedGroupId == groupId);

            query = query.Where(p => p.AssignedDeviceIndustryId != null && deviceIndustryIds.Contains(p.AssignedDeviceIndustryId!.Value));

            if (deviceBrandId != 0)
                query = query.Where(b => b.TerminalBrandId == deviceBrandId);

            if (deviceModelId != 0)
                query = query.Where(p => p.TerminalModelTypeId == deviceModelId);

            var terminalMasters = await query.ToListAsync();

            return terminalMasters;
        }

        public async Task<List<TerminalBrandMaster>> GetTerminalMasterBrandWithoutLocationAreaId(IList<int> deviceIndustryIds)
        {
            var terminalMasterBrands = await ScopedEntities
                .Where(tm =>
                    tm.AssignedDeviceIndustryId != null &&
                    deviceIndustryIds.Contains(tm.AssignedDeviceIndustryId!.Value) &&
                    tm.AssignedDeviceIndustryId != null &&
                    tm.IsStatus != Constants.DELETE_RECORD &&
                    tm.IsStatus != Constants.PRE_DELETE_RECORD)
                .Include(tm => tm.TerminalBrandMaster)
                .Select(tm => tm.TerminalBrandMaster)
                .Distinct()
                .ToListAsync();

            return terminalMasterBrands;
        }

        public async Task<List<TerminalBrandMaster>> GetTerminalBrandMasterOfAssignedDeviceAsync(IList<int> locationAreaIds, IList<int> deviceIndustryIds, IList<int> deviceBrandIds, int companyId, int groupId)
        {
            var context = GetContext();

            var query = from b in context.TerminalMaster.AsNoTracking()
                        where b.AssignedLocationAreaId != null && locationAreaIds.Contains(b.AssignedLocationAreaId!.Value)
                                                               && b.AssignedDeviceIndustryId != null && deviceIndustryIds.Contains(b.AssignedDeviceIndustryId!.Value)
                                                               && b.AssignedDeviceIndustryId != null
                                                               && b.IsStatus != Constants.DELETE_RECORD
                                                               && b.IsStatus != Constants.PRE_DELETE_RECORD
                        select b;

            if (companyId != 0)
            {
                query = query.Where(b => b.TerminalServiceDetail == companyId);
            }

            if (groupId != 0)
            {
                query = query.Where(b => b.AssignedGroupId == groupId);
            }

            if (deviceBrandIds.Any())
            {
                query = query.Where(b => deviceBrandIds.Contains(b.TerminalBrandId.Value));
            }

            var terminalBrandMasters = await query
                .Include(p => p.TerminalBrandMaster)
                .Select(p => p.TerminalBrandMaster)
                .Distinct()
                .ToListAsync();

            return terminalBrandMasters;
        }

        public async Task<List<int>> GetMatchingLocationAreaIdsAsync(List<int> locationAreaIds)
        {
            var context = GetContext();

            var matchingKeys = from b in context.TerminalMaster.AsNoTracking()
                               where locationAreaIds.Contains(b.AssignedLocationAreaId!.Value)
                               select b.AssignedLocationAreaId.Value;

            return await matchingKeys.ToListAsync();
        }

        public async Task<List<int>> GetMatchingDeviceIndustryIdsAsync(List<int> deviceIndustryIds, int? locationAreaId)
        {
            var context = GetContext();

            var matchingKeys =
                from b in context.TerminalMaster.AsNoTracking()
                where deviceIndustryIds.Contains(b.AssignedDeviceIndustryId!.Value) && b.AssignedLocationAreaId!.Value == locationAreaId
                select b.AssignedDeviceIndustryId.Value;

            return await matchingKeys.ToListAsync();
        }

        public async Task<List<TerminalModelMaster>> GetTerminalMasterModelWithoutLocationAreaId(IList<int> deviceIndustryIds, int deviceBrandId, int companyId, int groupId)
        {
            var query = ScopedEntities
                .Where(b =>
                    b.AssignedDeviceIndustryId != null &&
                    deviceIndustryIds.Contains(b.AssignedDeviceIndustryId!.Value) &&
                    b.AssignedDeviceIndustryId != null &&
                    b.IsStatus != Constants.DELETE_RECORD &&
                    b.IsStatus != Constants.PRE_DELETE_RECORD)
                .Include(tm => tm.TerminalModelType).AsNoTracking();

            if (companyId != 0)
            {
                query = query.Where(b => b.TerminalServiceDetail == companyId);
            }

            if (groupId != 0)
            {
                query = query.Where(b => b.AssignedGroupId == groupId);
            }

            var terminalMasterModels = query.Select(tm => tm.TerminalModelType).Distinct();

            if (deviceBrandId != 0)
                terminalMasterModels = terminalMasterModels.Where(tmm => tmm.TerminalBrandId == deviceBrandId);


            return await terminalMasterModels.ToListAsync();
        }

        public async Task<List<TerminalModelMaster>> GetTerminalModelMasterOfAssignedDeviceAsync(IList<int> locationAreaIds, IList<int> deviceIndustryIds, int deviceBrandId, int companyId, int groupId)
        {
            var context = GetContext();

            var query = from b in context.TerminalMaster.AsNoTracking()
                        where b.AssignedLocationAreaId != null && locationAreaIds.Contains(b.AssignedLocationAreaId!.Value)
                                                               && b.AssignedDeviceIndustryId != null && deviceIndustryIds.Contains(b.AssignedDeviceIndustryId!.Value)
                                                               && b.AssignedDeviceIndustryId != null
                                                               && b.IsStatus != Constants.DELETE_RECORD
                                                               && b.IsStatus != Constants.PRE_DELETE_RECORD
                        select b;

            if (companyId != 0)
            {
                query = query.Where(b => b.TerminalServiceDetail == companyId);
            }

            if (groupId != 0)
            {
                query = query.Where(b => b.AssignedGroupId == groupId);
            }

            var terminalModelMasterQuery = query
                .Include(p => p.TerminalModelType)
                .Select(p => p.TerminalModelType)
                .Distinct();

            if (deviceBrandId != 0)
                terminalModelMasterQuery = terminalModelMasterQuery.Where(p => p.TerminalBrandId == deviceBrandId);

            return await terminalModelMasterQuery.ToListAsync();
        }

        public TerminalMaster GetTerminalMasterIncludedById(int terminalMasterId)
        {
            TerminalMaster result = null;
            var context = GetContext();
            lock (context)
            {
                result = GetTerminalBySerialNumberInclude(context.TerminalMaster)
                       .Where(m => m.Id == terminalMasterId)
                       .Where(p => p.IsStatus != Constants.DELETE_RECORD)
                       .FirstOrDefault();
            }

            CtxDisposeOrNot(context);
            return result;
        }

        public Task<TerminalMaster> GetTerminalBySerialNumberNotInclude(string serialNumber)
        {
            var context = GetContext();

            var result = context.TerminalMaster
                    .Where(m => m.TerminalSerialNumber.Equals(serialNumber))
                    .Where(p => p.IsStatus != Constants.DELETE_RECORD);

            return result.FirstOrDefaultAsync();
        }

        public async Task<TerminalMaster> GetTerminalByDeviceTemplateId(int templateId)
        {
            var context = GetContext();
            var result = await context.Set<TerminalMaster>().Where(p => p.IsStatus != Constants.DELETE_RECORD && p.CopyDeviceTemplateId == templateId).FirstOrDefaultAsync();

            CtxDisposeOrNot(context);

            return result;
        }

        public async Task<List<int>> GetAssignedDeviceIdsByLocationAsync(int locationItemId, int groupId)
        {
            var query = ScopedEntities.AsNoTracking()
                .Where(p => p.IsStatus == Constants.NOTCHANGE_RECORD
                         && p.LocationArea.ParentId == locationItemId
                         && p.AssignedGroupId == groupId
                         && p.DeviceSetupType == DeviceSetupType.None)
                .Select(p => p.Id);

            return await query.ToListAsync();
        }

        public async Task<PagingResponse<DeviceAssociatedResponseApiModel>> GetDeviceAssignedAsync(IList<int> locationAreaIds, IList<int> deviceIndustryIds, List<int> deviceBrandIds, List<int> deviceModelIds, List<int> merchantIds, List<int> companyIds, int groupId, string searchValue, SearchAssignDeviceType searchType, PagingParameter pagingParameter, bool? isActivate)
        {
            var context = GetContext();
            var terminalSource = _terminalSourceOptions?.Value?.GetTerminalSource() ?? TerminalSources.Linkly;

            var query = from b in context.TerminalMaster.AsNoTracking()
                        where b.AssignedLocationAreaId != null
                            && b.AssignedDeviceIndustryId != null && deviceIndustryIds.Contains(b.AssignedDeviceIndustryId!.Value)
                            && b.AssignedDeviceIndustryId != null
                            && b.TerminalSource == terminalSource
                            && b.IsStatus != Constants.DELETE_RECORD
                            && b.DeviceSetupType == DeviceSetupType.None
                            && b.IsStatus != Constants.PRE_DELETE_RECORD
                        select b;

            if (isActivate != null)
            {
                query = query.Where(p => p.IsActive == isActivate);
            }

            if (locationAreaIds.Count > 0)
            {
                query = query.Where(p => locationAreaIds.Contains(p.AssignedLocationAreaId!.Value));
            }

            if (deviceBrandIds != null && deviceBrandIds.Count > 0)
            {
                deviceBrandIds = [.. deviceBrandIds.Distinct()];
                query = query.Where(p => p.TerminalBrandId.HasValue && deviceBrandIds.Contains(p.TerminalBrandId.Value));
            }

            if (deviceModelIds != null && deviceModelIds.Count > 0)
            {
                deviceModelIds = [.. deviceModelIds.Distinct()];
                query = query.Where(p => p.TerminalModelTypeId.HasValue && deviceModelIds.Contains(p.TerminalModelTypeId.Value));
            }

            if (companyIds.Count > 0)
            {
                query = query.Where(p => companyIds.Contains(p.TerminalServiceDetail.GetValueOrDefault()));
            }

            if (groupId != 0)
            {
                query = query.Where(p => p.AssignedGroupId == groupId);
            }

            switch (searchType)
            {
                case SearchAssignDeviceType.SN:
                    query = SearchUtility.ApplySearch(query, searchValue, q => q.TerminalSerialNumber);
                    break;
                case SearchAssignDeviceType.Brand:
                    query = SearchUtility.ApplySearch(query, searchValue, q => q.TerminalBrandMaster.TerminalBrandName);
                    break;
                case SearchAssignDeviceType.Model:
                    query = SearchUtility.ApplySearch(query, searchValue, q => q.TerminalModelType.TerminalModelName);
                    break;
            }

            var merchantQueryBase = context.MerchantTerminal
                .AsNoTracking()
                .Where(p => p.MerchantMaster.IsActive
                    && p.IsStatus != Constants.PRE_DELETE_RECORD
                    && p.IsStatus != Constants.DELETE_RECORD
                    && p.MerchantMaster.IsStatus != Constants.DELETE_RECORD);

            if (merchantIds != null && merchantIds.Count > 0)
            {
                merchantQueryBase = merchantQueryBase.Where(p => merchantIds.Contains(p.Id));
            }
                
            var merchantSelected = merchantQueryBase.Select(p => new { p.TerminalId, Name = p.MerchantMaster.lszMerchantName });

            var result = from q in query
                         join brand in context.TerminalBrandMaster.AsNoTracking()
                             on q.TerminalBrandId equals brand.Id
                         join merchant in merchantSelected
                             on q.Id equals merchant.TerminalId into merchantGroup
                         orderby q.Id
                         select new DeviceAssociatedResponseApiModel
                         {
                             Name = q.lszTerminalName,
                             SerialNumber = q.TerminalSerialNumber,
                             BrandName = q.TerminalBrandMaster.Name,
                             ModelName = q.TerminalModelType.Name,
                             MerchantDisplay = !merchantGroup.Any() ? "No Merchants" : merchantGroup.Count() == 1
                             ? merchantGroup.OrderBy(m => m.TerminalId).First().Name
                            : "Multi-Merchant"
                         }.MapBaseProperties(q);

            return await GetPagingResponseAsync(result, pagingParameter.PageNumber, pagingParameter.PageSize);
        }

        public async Task<PagingResponse<AvailableDevicesApiModel>> GetAllTerminalUnAssignedDeviceForApiAsync(int companyId, List<int> brandIds, List<int> modelIds, string keySearch, PagingParameter pagingParameter)
        {
            var context = GetContext();

            // Use the configured terminal source or default to Linkly if not available
            var terminalSource = _terminalSourceOptions?.Value?.GetTerminalSource() ?? TerminalSources.Linkly;

            var query = from b in context.TerminalMaster.AsNoTracking()
                        where b.AssignedLocationAreaId == null
                              && b.IsStatus != Constants.DELETE_RECORD
                              && b.TerminalSource == terminalSource
                              && context.CompanyDeviceSetupMapping.Any(mapping =>
                                    mapping.DeviceBrandId == b.TerminalBrandId &&
                                    mapping.DeviceModelId == b.TerminalModelTypeId &&
                                    mapping.CompanyId == b.TerminalServiceDetail &&
                                    mapping.IsStatus != Constants.DELETE_RECORD)
                        join brand in context.TerminalBrandMaster.AsNoTracking()
                            on b.TerminalBrandId equals brand.Id
                        join model in context.TerminalModelMaster.AsNoTracking()
                            on b.TerminalModelTypeId equals model.Id
                        where brand.IsStatus != Constants.DELETE_RECORD
                              && model.IsStatus != Constants.DELETE_RECORD
                        select b;

            // Apply additional filters
            if (companyId != 0)
            {
                query = query.Where(b => b.TerminalServiceDetail == companyId);
            }

            if (brandIds != null && brandIds.Count > 0)
            {
                query = query.Where(b => brandIds.Contains(b.TerminalBrandId ?? 0));
            }

            if (modelIds != null && modelIds.Count > 0)
            {
                query = query.Where(b => modelIds.Contains(b.TerminalModelTypeId ?? 0));
            }

            // Apply search filter using SearchUtility
            query = SearchUtility.ApplySearch(query, keySearch, q => q.TerminalSerialNumber);

            var merchantQueryBase = context.MerchantTerminal
                .AsNoTracking()
                .Where(p => p.MerchantMaster.IsActive
                         && p.IsStatus != Constants.PRE_DELETE_RECORD
                         && p.IsStatus != Constants.DELETE_RECORD
                         && p.MerchantMaster.IsStatus != Constants.DELETE_RECORD)
                .Include(p => p.MerchantMaster);

            var result = from q in query
                         select new AvailableDevicesApiModel
                         {
                             Name = q.lszTerminalName,
                             SerialNumber = q.TerminalSerialNumber,
                             BrandName = q.TerminalBrandMaster.Name,
                             ModelName = q.TerminalModelType.Name,
                             MerchantDisplay = merchantQueryBase.Count(m => m.TerminalId == q.Id) == 0
                                                    ? "No Merchants" : merchantQueryBase.Count(m => m.TerminalId == q.Id) == 1
                                                    ? merchantQueryBase.FirstOrDefault().Name : "Multi-Merchant"
                         }.MapBaseProperties(q);

            return await GetPagingResponseAsync(result, pagingParameter.PageNumber, pagingParameter.PageSize);
        }

        public async Task AssignDeviceToLocationForApiAsync(int terminalId, int locationAreaId, int groupId)
        {
            var context = GetContext();

            var query = from b in context.TerminalMaster.AsNoTracking()
                        where b.Id == terminalId
                            && b.IsStatus != Constants.DELETE_RECORD
                            && b.IsStatus != Constants.PRE_DELETE_RECORD
                        select b;

            var terminalMaster = await query.SingleOrDefaultAsync();

            if (terminalMaster == null)
                throw new InvalidOperationException("The terminalMaster object was not found. Ensure the query returns a valid result.");

            terminalMaster.AssignedGroupId = groupId;
            terminalMaster.AssignedLocationAreaId = locationAreaId;
            terminalMaster.IsAssignedDevice = true;

            Update(terminalMaster);
        }

        public async Task AssignDevicesToLocationForApiAsync(IList<int> terminalIds, int locationAreaId, int groupId)
        {
            var context = GetContext();

            var query = from b in context.TerminalMaster
                        where terminalIds.Contains(b.Id)
                            && b.IsStatus != Constants.DELETE_RECORD
                            && b.IsStatus != Constants.PRE_DELETE_RECORD
                        select b;

            var terminalMasters = await query.ToListAsync();

            if (terminalMasters.Count < 1)
                throw new InvalidOperationException("The terminalMaster object was not found. Ensure the query returns a valid result.");

            foreach (var terminal in terminalMasters)
            {
                terminal.AssignedGroupId = groupId;
                terminal.AssignedLocationAreaId = locationAreaId;
                terminal.IsAssignedDevice = true;
                terminal.AssignedDeviceIndustryId = 1;
                terminal.Modified = DateTime.Now;
            }

            context.TerminalMaster.UpdateRange(terminalMasters);
            await context.SaveChangesAsync();
        }

        /// <summary>
        /// Gets terminals by source flag
        /// </summary>
        /// <param name="source">The terminal source flag to filter by</param>
        /// <param name="includeDeleted">Whether to include deleted terminals</param>
        /// <param name="includeDeactivated">Whether to include deactivated terminals</param>
        /// <returns>List of terminals matching the source flag</returns>
        public async Task<IList<TerminalMaster>> GetTerminalsBySourceAsync(TerminalSources source, bool includeDeleted = false, bool includeDeactivated = true)
        {
            var context = GetContext();

            // Start with a base query
            var query = context.Set<TerminalMaster>().AsQueryable();

            // Apply source filter using bitwise operation
            query = query.Where(t => (t.TerminalSource & source) == source);

            // Apply status filters
            if (!includeDeleted)
            {
                query = query.Where(t => t.IsStatus != Constants.DELETE_RECORD);
            }

            if (!includeDeactivated)
            {
                query = query.Where(t => t.IsActive);
            }

            // Execute query and return results
            var result = await query.ToListAsync();

            CtxDisposeOrNot(context);

            return result;
        }

        public async Task UnassignDevicesToLocationForApiAsync(IList<int> terminalIds)
        {
            var context = GetContext();

            var query = from b in context.TerminalMaster
                        where terminalIds.Contains(b.Id)
                            && b.IsStatus != Constants.DELETE_RECORD
                            && b.IsStatus != Constants.PRE_DELETE_RECORD
                        select b;

            var terminalMasters = await query.ToListAsync();

            if (terminalMasters.Count < 1)
                throw new InvalidOperationException("The terminalMaster object was not found. Ensure the query returns a valid result.");

            foreach (var terminal in terminalMasters)
            {
                terminal.AssignedGroupId = null;
                terminal.AssignedLocationAreaId = null;
                terminal.IsAssignedDevice = false;
                terminal.Modified = DateTime.Now;
            }

            context.TerminalMaster.UpdateRange(terminalMasters);
            await context.SaveChangesAsync();
        }

        public async Task<bool> HasAnyDeviceAssignedToGroup(int groupId)
        {
            var context = GetContext();
            try
            {
                var hasAssignedDevices = await context.TerminalMaster
                    .AnyAsync(t => t.AssignedGroupId == groupId
                            && t.IsStatus != Constants.DELETE_RECORD
                            && t.IsStatus != Constants.PRE_DELETE_RECORD);

                return hasAssignedDevices;
            }
            finally
            {
                CtxDisposeOrNot(context);
            }
        }
    }
}
