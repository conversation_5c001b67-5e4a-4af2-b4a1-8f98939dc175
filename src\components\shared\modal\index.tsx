import { Mo<PERSON>, <PERSON>, Button } from 'antd';
import './index.scss';
import React from 'react';

interface CommonModalProps {
  visible: boolean; // keeping for backwards compatibility
  onCancel: () => void;
  onSave?: () => void;
  onClose?: () => void;
  title: string;
  width?: number;
  footer?: React.ReactNode;
  children: React.ReactNode;
  loading?: boolean;
  cancelText?: string;
  saveText?: string;
  disableSave?: boolean;
  className?: string;
}

const CommonModal: React.FC<CommonModalProps> = ({
  visible,
  onCancel,
  onSave,
  onClose,
  title,
  width = 560,
  footer,
  children,
  loading = false,
  cancelText = 'Cancel',
  saveText = 'Save',
  disableSave = false,
  className = '',
}) => {
  const [buttonLoading, setButtonLoading] = React.useState(false);

  const handleSave = async () => {
    if (onSave) {
      setButtonLoading(true);
      await onSave();
      setButtonLoading(false);
    }
  };

  const defaultFooter = (
    <div className="modal-footer">
      <Button className="btn-base body-2-regular btn-cancel" onClick={onCancel}>
        {cancelText}
      </Button>
      <Button
        className="btn-base body-2-regular"
        type="primary"
        onClick={handleSave}
        loading={buttonLoading}
        disabled={disableSave}
      >
        {saveText}
      </Button>
    </div>
  );

  return (
    <Modal
      title={<span className="body-2-bold">{title}</span>}
      open={visible}
      onCancel={onClose ?? onCancel}
      footer={footer === undefined ? defaultFooter : footer}
      width={width}
      centered
      destroyOnHidden
      className={`common-modal ${className}`}
    >
      {loading ? (
        <div className="loading-container">
          <Spin />
        </div>
      ) : (
        children
      )}
    </Modal>
  );
};

export default CommonModal;
