import { AxiosResponse } from 'axios';
import { buildQueryParams, getAsync, postAsync } from './http-client';
import {
  LocationTreeApiResponse,
  LocationTreeResponse,
  MerchantLocationArea,
  LocationSelectDataRequest,
  LocationSelectRequest,
  LocationSelectMerchantDataRequest,
  LocationSelectMerchantRequest,
  LocationSelectMerchantResponse,
} from '../models/location-tree.model';
import { SelectionDataResponse } from '../models/common.model';
import { ApiBaseModel } from '../models/common.model';

const baseUrl = import.meta.env.VITE_MMS_API_URL;

function getLocationTree(
  params: {
    locationItemId?: number;
    isHasLocationZone?: boolean;
    screenType?: string;
    cloudSecurityAccessMappingId?: number;
    groupMappingId?: number;
  },
  lastSelectedLocations: Array<{ key: number; value: number }>,
): Promise<AxiosResponse<LocationTreeResponse>> {
  const url = `${baseUrl}/location/reload-all-location-level`;

  // Convert lastSelectedLocations to query string
  const lastSelectedLocationsQuery = lastSelectedLocations
    .map(({ key, value }) => `lastSelectedLocations[${key}]=${value}`)
    .join('&');

  const queryParams = `?screenType=${params.screenType}&isHasLocationZone=${params.isHasLocationZone}&cloudSecurityAccessMappingId=${params.cloudSecurityAccessMappingId}&groupMappingId=${params.groupMappingId}&${lastSelectedLocationsQuery}`;

  return getAsync(url + queryParams);
}

function getLocationBelow(params: {
  locationItemId?: number;
  locationId?: number;
  locationIdsToLoad?: number[];
  parentId?: number;
  locationParentId?: number;
  usedItemId?: number;
  isEcommerce?: boolean;
  locationParentIdsString?: string;
  isMerchantLocation?: boolean;
  screenType?: string;
  cloudSecurityAccessMappingId?: number;
  groupMappingId?: number;
}): Promise<AxiosResponse<LocationTreeApiResponse>> {
  const url = `${baseUrl}/location/load-location-below?locationItemId=${params.locationItemId}&locationId=${params.locationId}&locationIdsToLoad=${params.locationIdsToLoad}&parentId=${params.parentId}&locationParentId=${params.locationParentId}&usedItemId=${params.usedItemId}&isEcommerce=${params.isEcommerce}&locationParentIdsString=${params.locationParentIdsString}&isMerchantLocation=${params.isMerchantLocation}&screenType=${params.screenType}&cloudSecurityAccessMappingId=${params.cloudSecurityAccessMappingId}&groupMappingId=${params.groupMappingId}`;
  return getAsync(url);
}

function getMerchantLocationAreas(params: {
  cloudSecurityAccessMappingId?: number;
  locationAreaId: number;
}): Promise<AxiosResponse<MerchantLocationArea[]>> {
  const url = `${baseUrl}/location/load-merchant-location-areas?cloudSecurityAccessMappingId=${params.cloudSecurityAccessMappingId}&locationAreaId=${params.locationAreaId}`;
  return getAsync(url);
}

function getSelectLocationData(
  locationSelectDataRequest: LocationSelectDataRequest,
): Promise<AxiosResponse<SelectionDataResponse<ApiBaseModel>>> {
  const queryString = buildQueryParams(locationSelectDataRequest);
  const url = `${baseUrl}/location/select-location-data?${queryString}`;
  return getAsync(url);
}

function updateLocationSelection(
  locationSelectRequest: LocationSelectRequest,
): Promise<AxiosResponse<void>> {
  const url = `${baseUrl}/location/select-location-item`;
  return postAsync(url, locationSelectRequest);
}

function getSelectMerchantData(
  locationSelectMerchantDataRequest: LocationSelectMerchantDataRequest,
): Promise<AxiosResponse<LocationSelectMerchantResponse<ApiBaseModel>>> {
  const queryString = buildQueryParams(locationSelectMerchantDataRequest);
  const url = `${baseUrl}/location/select-merchant-data?${queryString}`;
  return getAsync(url);
}

function updateLocationSelectionMerchant(
  locationSelectMerchantRequest: LocationSelectMerchantRequest,
): Promise<AxiosResponse<void>> {
  const url = `${baseUrl}/location/select-merchant-locations-level`;
  return postAsync(url, locationSelectMerchantRequest);
}

export const LocationTreeApi = {
  getLocationTree,
  getLocationBelow,
  getMerchantLocationAreas,
  getSelectLocationData,
  updateLocationSelection,
  getSelectMerchantData,
  updateLocationSelectionMerchant,
};
