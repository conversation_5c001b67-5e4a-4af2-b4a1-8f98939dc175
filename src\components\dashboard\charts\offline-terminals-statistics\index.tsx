import { Al<PERSON>, Spin } from 'antd';
import { useEffect, useState } from 'react';
import Chart from 'react-apexcharts';
import { ApexOptions } from 'apexcharts';
import './index.scss';

interface ChartData {
  series: number[];
  labels: string[];
}

const CHART_DATA: ChartData = {
  series: [3, 3, 12, 16, 43],
  labels: [
    '3 - 7 days',
    '8 - 15 days',
    '16 - 30  days',
    '31 - 90  days',
    '>= 91  days',
  ],
};

const CHART_COLORS = [
  '#1E90FF',
  '#32CD32',
  '#4682B4',
  '#FFA500',
  '#1E90FF',
  '#FF4500',
  '#32CD32',
  '#B22222',
  '#A9A9A9',
  '#8A2BE2',
];

const NON_ACTIVE_TOTAL = 77;

const formatLegendItem = (seriesName: string, opts: any): string => {
  const value = opts.w.globals.series[opts.seriesIndex];
  const color = opts.w.globals.colors[opts.seriesIndex];
  return `
    <div class="custom-legend-item" style="display: flex; align-items: center;">
      <span class="legend-label" style="color: #666666; font-size: 12px; display: block;">${seriesName}</span>
      <span class="legend-value" style="color: ${color}; font-size: 12px; font-weight: 600; margin-left: 10px;">${value}</span>
    </div>
  `;
};

const ChartDisplay = ({
  options,
  series,
}: {
  options: ApexOptions;
  series: number[];
}) => (
  <div className="offline-terminals-statistics-chart">
    <Chart
      options={options}
      series={series}
      type="donut"
      width="100%"
      height={200}
    />
  </div>
);

function OfflineTerminalsStatistics() {
  const [isLoading, setLoading] = useState(true);
  const [chartOptions, setChartOptions] = useState<ApexOptions>({
    series: CHART_DATA.series,
    labels: CHART_DATA.labels,
    dataLabels: {
      enabled: false,
    },
    plotOptions: {
      pie: {
        expandOnClick: true,
        customScale: 1,
        donut: {
          size: '60%',
          labels: {
            show: true,
            name: {
              show: true,
            },
            value: {
              show: true,
              fontSize: '14px',
              fontWeight: 600,
              color: '#000000',
              formatter: () => NON_ACTIVE_TOTAL.toString(),
            },
            total: {
              show: true,
              label: 'Total',
              fontSize: '12px',
              fontWeight: 600,
              color: '#666666e7',
              formatter: () => NON_ACTIVE_TOTAL.toString(),
            },
          },
        },
      },
    },
    colors: CHART_COLORS,
    chart: {
      width: 50,
    },
    legend: {
      position: 'left',
      horizontalAlign: 'left',
      floating: false,
      fontSize: '12px',
      formatter: formatLegendItem,
      itemMargin: {
        horizontal: 10,
        vertical: 2,
      },
    },
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        await new Promise((resolve) => setTimeout(resolve, 2000));
        setLoading(false);
      } catch (error) {
        console.error('Error loading chart data:', error);
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  return (
    <>
      {isLoading && <Spin className="loading-container" />}
      {!isLoading && (
        <ChartDisplay options={chartOptions} series={CHART_DATA.series} />
      )}
    </>
  );
}

export default OfflineTerminalsStatistics;
