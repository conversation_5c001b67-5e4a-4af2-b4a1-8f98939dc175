import React from 'react';
import { MapLegendProps } from '../../../../../../models/map.model';

const MapLegend: React.FC<MapLegendProps> = ({ regions }) => {
  return (
    <div className="map-legend">
      {regions.map((region) => (
        <span key={region.name} className="country">
          {region.name}{' '}
          <span className="terminals">{region.terminals.toLocaleString()}</span>
        </span>
      ))}
    </div>
  );
};

export default MapLegend;
