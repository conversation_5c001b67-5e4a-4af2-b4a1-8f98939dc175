﻿using KellermanSoftware.CompareNetObjects;
using MMS.Api.ApiModels;
using MMS.Core.Attributes;
using MMS.Core.CoreUTI;
using MMS.Core.CoreUTI.Enum;
using MMS.Core.Entities;
using System.Collections;
using System.ComponentModel.DataAnnotations.Schema;
using System.Net.Http.Headers;
using System.Reflection;
using System.Security.Cryptography;

namespace MMS.Api.Utils
{
    public static class ApiUtilCommon
    {
        private static readonly Random RandomNumber = new();
        private static readonly RandomNumberGenerator _rng = RandomNumberGenerator.Create();

        public static int GetRandomNumber()
        {
            return RandomNumber.Next();
        }

        public static long BuildId()
        {
            var randomId = (uint)GetRandomNumber();
            var dateTime = DateTime.Now;
            var julianDate = ToJulian(dateTime);
            var bitmaskLength = GetBitMaskLength(randomId);
            long id = randomId;
            id = id << 4 - bitmaskLength;
            id |= (uint)Rand(); //maximum 20 bit
            id = id << 16;
            id |= (ushort)julianDate;
            id = id << 5;
            id |= (ushort)dateTime.Hour;
            id = id << 6;
            id |= (ushort)dateTime.Minute;
            id = id << 6;
            id |= (ushort)dateTime.Second;
            id = id << 10;
            id |= (ushort)dateTime.Millisecond;

            return id;
        }

        public static int GetBitMaskLength(uint n)
        {
            return Convert.ToString(n, 2).Length;
        }

        public static int ToJulian(DateTime dateTime)
        {
            int day = dateTime.Day;
            int month = dateTime.Month;
            int year = dateTime.Year;

            if (month < 3)
            {
                month = month + 12;
                year = year - 1;
            }

            return day + (153 * month - 457) / 5 + 365 * year + (year / 4) - (year / 100) + (year / 400);// + 1721119;
        }

        public static int Rand()
        {
            const int maxValue = 2 ^ 20;

            var buffer = new byte[4];
            int bits, val;

            do
            {
                _rng.GetBytes(buffer);
                bits = BitConverter.ToInt32(buffer, 0) & 0x7FFFFFFF;
                val = bits % maxValue;
            } while (bits - val + (maxValue - 1) < 0);


            return val;
        }

        public static async Task<HttpResponseMessage> ResyncPrepare(TerminalMaster terminal, bool isValidate)
        {
            using var httpClient = new HttpClient();

            var mmsWebURL = Environment.GetEnvironmentVariable(Constants.ENV_MMS_WEB_URL)
                ?? throw new InvalidOperationException($"Environment variable {Constants.ENV_MMS_WEB_URL} is not configured.");
            httpClient.BaseAddress = new Uri(mmsWebURL);
            httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

            var requestModel = new RequestTerminalModel
            {
                TerminalSerialNumber = terminal.TerminalSerialNumber,
                TerminalMacAddress = terminal.LogonMacAddress,
                TerminalModelId = terminal.TerminalModelTypeId ?? 0,
                IsNoEncrypt = true,
                IsCompressed = false,
            };

            var queryParams = new Dictionary<string, string>
            {
                { "isSetupUpgrade", "true" },
                { "type", "json" },
                { "isRawData", "true" },
                { "isPaymentRequest", "false" },
                { "isValidate", isValidate.ToString() }
            };
            var queryString = string.Join("&", queryParams.Select(p => $"{p.Key}={Uri.EscapeDataString(p.Value)}"));

            var requestUrl = $"api/synchronize/resyncfile/prepare?{queryString}";

            using var formData = new MultipartFormDataContent
            {
                { new StringContent(terminal.DeviceSetupType == DeviceSetupType.BaseTemplate ? "no-serial-number" : requestModel.TerminalSerialNumber ?? ""), "TerminalSerialNumber" },
                { new StringContent(requestModel.TerminalMacAddress ?? ""), "TerminalMacAddress" },
                { new StringContent(requestModel.TerminalModelId.ToString()), "TerminalModelId" },
                { new StringContent(requestModel.IsNoEncrypt.ToString()), "IsNoEncrypt" },
                { new StringContent(requestModel.IsCompressed.ToString()), "IsCompressed" },
                { new StringContent(terminal.Id.ToString()), "Id" },
            };

            return await httpClient.PostAsync(requestUrl, formData);
        }

        /// <summary>
        /// Recursively collects the names of members (properties) to ignore for a given type, based on certain conditions.
        /// </summary>
        /// <param name="type">The type to inspect for members to ignore.</param>
        /// <param name="membersToIgnore">A list of member names that should be ignored, which will be populated by this method.</param>
        /// <param name="processedTypes">A set of already processed types to avoid redundant processing and potential infinite recursion.</param>
        /// <remarks>
        /// The method checks for the following conditions to add a property to the ignore list:
        /// <list type="bullet">
        /// <item><description>If the property name ends with "Id", it will be ignored.</description></item>
        /// <item><description>If the property has the <see cref="CompareIgnoreAttribute"/>, it will be ignored.</description></item>
        /// <item><description>If the property is an enumerable type (except string), the method will recursively inspect its element type.</description></item>
        /// <item><description>If the property is a class type (except string), the method will recursively inspect the class type.</description></item>
        /// </list>
        /// The method ensures that a type is processed only once by tracking it in the <paramref name="processedTypes"/> set.
        /// </remarks>
        private static void CollectMembersToIgnore(Type type, List<string> membersToIgnore, HashSet<Type> processedTypes)
        {
            if (processedTypes.Contains(type))
                return;

            processedTypes.Add(type);

            PropertyInfo[] properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance);

            foreach (var prop in properties)
            {
                // ignore property that has CompareIgnoreAttribute
                if (prop.GetCustomAttributes(typeof(CompareIgnoreAttribute), true).Length != 0)
                {
                    membersToIgnore.Add(prop.Name);
                }

                // ignore foreign key
                var foreignKeyAttr = prop.GetCustomAttribute<ForeignKeyAttribute>();

                if (foreignKeyAttr is not null) membersToIgnore.Add(foreignKeyAttr.Name);

                // ignore another types
                if (typeof(IEnumerable).IsAssignableFrom(prop.PropertyType) && prop.PropertyType != typeof(string))
                {
                    if (prop.PropertyType.IsGenericType)
                    {
                        Type itemType = prop.PropertyType.GetGenericArguments()[0];
                        CollectMembersToIgnore(itemType, membersToIgnore, processedTypes);
                    }
                }
                else if (prop.PropertyType.IsClass && prop.PropertyType != typeof(string))
                {
                    CollectMembersToIgnore(prop.PropertyType, membersToIgnore, processedTypes);
                }
            }
        }

        /// <summary>
        /// Compares an original <see cref="TerminalMaster"/> template with a copy template to determine if they are identical.
        /// </summary>
        /// <param name="originTemplate">The original <see cref="TerminalMaster"/> template.</param>
        /// <param name="destinationTemplate">The copy of the <see cref="TerminalMaster"/> template to compare with the original.</param>
        /// <returns>
        /// A <see cref="CopyTerminalResultModel"/> indicating whether the templates are identical. 
        /// If both templates are null, the result will indicate no differences. 
        /// If one template is null, the result will indicate a failure due to missing data.
        /// If differences are found between the original and the copy, those differences will be included in the result.
        /// </returns>
        /// <remarks>
        /// The comparison ignores certain fields that are either system-generated or irrelevant for this purpose, 
        /// such as "Id", "Created", "Modified", and "IsActive". Additional members may be dynamically ignored 
        /// by the <see cref="CollectMembersToIgnore"/> method.
        /// </remarks>
        public static bool ValidateTerminalTemplateCopy(TerminalMaster originTemplate, TerminalMaster destinationTemplate)
        {
            // use Compare NET Object library
            var membersToIgnore = new List<string>
            {
                "Id", "Created", "Modified", "IsActive"
            };

            var processedTypes = new HashSet<Type>();

            CollectMembersToIgnore(typeof(TerminalMaster), membersToIgnore, processedTypes);

            var compareLogic = new CompareLogic();
            compareLogic.Config.MembersToIgnore.AddRange(membersToIgnore);

            var result = compareLogic.Compare(originTemplate, destinationTemplate);

            return result.AreEqual;
        }
    }
}
