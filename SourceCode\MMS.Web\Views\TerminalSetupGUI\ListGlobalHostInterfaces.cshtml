﻿@using MMS.Web
@using MMS.Web.Models
@using MMS.Web.Utils
@model MMS.Web.Models.TerminalSetupGUI.HostInterfacesViewModel
<head>
    <style>
        @Html.Raw(ViewBag.Style)
    </style>
    <script src="~/Scripts/Main/TerminalSetupGUI/ListGlobalHostInterfaces.js"></script>
</head>

<div class="@CssConstants.Form1ColTerminal @CssConstants.Color @CssConstants.Middle" id="HostInterfacesList">
    <input type="hidden" value="@Model.TeminalMasterId" id="teminalMasterId" />

    @await Html.PartialAsync(PartialViewConstants.TerminalHeaderPartialView, new TerminalHeaderModel()
{
    Label = @_helper.GetLanguageText("Host Interfaces")
})
    <div class="@CssConstants.FormDetails @CssConstants.Color @CssConstants.BorderForm @CssConstants.TerminalContent">
        <div class="@CssConstants.FatherDiv @CssConstants.MenuDescriptionContent">
            <p class="@Constants.terminal_setupupdate_merchantsetup @CssConstants.EditLangAble" id="@Constants.terminal_setupupdate_merchantsetup">
                @_helper.GetStringByKey("Host Interfaces")
            </p>
        </div>
        <div class="@CssConstants.Content @CssConstants.IndexList  @CssConstants.TerminalList @CssConstants.List @CssConstants.AjaxRequest @CssConstants.TerminalList">
            @foreach (var item in Model.HostInterfaces)
            {
                <div class="@CssConstants.ListItemName  @CssConstants.TerminalListItem">
                    <input type="hidden" value="@item.Id" id="id">
                    <img class="@CssConstants.TerminalItemImage" src="@Url.Content(@item.IconId)" />
                    <div class="terminal-list-item-text">
                        <div class="text-wrapper">
                            <p>@item.Name</p>
                        </div>
                    </div>
                    <div class="@CssConstants.TerminalItemIcon">
                        <div class="toEditAction @CssConstants.TerminalIconArea">
                            <i class="@CssConstants.Sicon @CssConstants.TerminalIconEdit"></i>
                        </div>
                        <div class="@CssConstants.TerminalIconArea">
                            <i class="@CssConstants.Sicon @CssConstants.TerminalIconClose"></i>
                        </div>
                    </div>
                </div>
            }
            <div class="@CssConstants.TerminalAddArea">
                <div class="@CssConstants.TerminalAddBorder">
                    <div class="@CssConstants.TerminalAdd">
                        <i class="@CssConstants.Sicon @CssConstants.TerminalIconAdd"></i>
                        @_helper.GetStringByKey("Add New")
                    </div>
                </div>
            </div>
        </div>
    </div>
    @await Html.PartialAsync(PartialViewConstants.TerminalFooterPartialView, new TerminalFooterModel()
        {
        })
</div>

<div class="partial">
    @await Html.PartialAsync(Constants.UrlPopupPartialView, new PopupModels(PartialViewConstants.REMOVE_POPUP)
{
    DefaultTitle = "Merchant User",
    Label = _helper.GetLanguageText("Do you want to remove this host interfaces item?"),
})
</div>
