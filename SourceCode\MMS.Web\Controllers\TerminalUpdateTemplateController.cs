using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using MMS.Core.CoreUTI;
using MMS.Core.CoreUTI.Enum;
using MMS.Core.Entities;
using MMS.Core.Entities.TerminalUpdateTemplate;
using MMS.Core.Services;
using MMS.Core.Services.Base;
using MMS.Core.Services.Commons;
using MMS.Core.Services.Locations;
using MMS.Web.EntityModelMapping;
using MMS.Web.Filters;
using MMS.Web.Models;
using MMS.Web.Models.LocationSetupTypes;
using MMS.Web.Models.TerminalUpdateTemplate;
using MMS.Web.Models.ViewModels.Company;
using MMS.Web.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Constants = MMS.Web.Utils.Constants;
using MMSConstants = MMS.Core.CoreUTI.Constants;
using RedirectUrl = MMS.Core.CoreUTI.Enum.RedirectUrl;

namespace MMS.Web.Controllers
{

    public class TerminalUpdateTemplateController : BaseController
    {
        private readonly ITerminalSetupTemplateService _terminalSetupTemplateService;
        private readonly ITerminalService _terminalService;
        private readonly ICompanyMasterService _companyService;
        private readonly IGroupMasterService _groupMasterService;
        private readonly ITerminalTypeMasterService _terminalTypeMasterService;
        private readonly ITimeZoneMasterService _timezoneService;
        private readonly IAddressDetailsService _addressDetailService;
        private readonly ICharityService _charityService;
        private readonly IMerchantMasterService _merchantMasterService;
        private readonly IUserTerminalService _userTerminalService;
        private readonly IMerchantTerminalService _merchantTerminalService;
        private readonly IMerchantGeneralSetupService _merchantGeneralSetupService;
        private readonly IUserMasterService _userService;
        private readonly ITipService _tipService;
        private readonly IEftPaymentsService _eftPaymentsService;
        private readonly ICalculatePayService _calculatePayService;
        private readonly IBaseService<SupportTerminal> _supportTerminalService;
        private readonly IMerchantUserService _merchantUserService;
        private readonly ITerminalModelMasterService _terminalModelMasterService;
        private readonly ITimeZoneDetailService _timeZoneDetailService;
        private readonly IServiceItemService _serviceItemService;
        private readonly ISurveyItemService _surveyItemService;
        private readonly IMerchantUserLogonService _merchantUserLogonService;
        private readonly IBaseService<LanguageOptionTerminal> _languageOptionTerminalService;
        private readonly IBaseService<LanguageByTerminal> _languageByTerminalService;
        private readonly IBaseService<TerminalUserSecurityAccess> _terminalUserSecurityAccess;
        private readonly IBaseService<ManualCardTerminal> _manualCardTerminalService;
        private readonly IBaseService<StoreForwardTerminal> _storeForwardTerminalService;
        private readonly IBaseService<SoundTerminal> _soundTerminalService;
        private readonly IBaseService<DateTimeZoneTerminal> _dateTimeZoneTerminalService;
        private readonly IBaseService<DefaultSaleTypeTerminal> _defaultSaleTypeTerminalService;
        private readonly IBaseService<PosRequestTerminal> _posRequestTerminalService;
        private readonly IBaseService<DeditCardBackTerminal> _deditCardBackTerminalService;
        private readonly IBaseService<CreditCashOutTerminal> _creditCashOutTerminalService;
        private readonly IBaseService<SurchargeTerminal> _surchargeTerminalService;
        private readonly IBaseService<ComTerminal> _comTerminalService;
        private readonly IDeviceTemplatesMappingService _deviceTemplatesMappingService;
        private readonly IDeviceTemplatesService _deviceTemplatesService;
        private readonly IAddressLocalItemService _addressLocalItemService;
        private readonly IIOTDeviceTypesService _iOTDeviceTypesService;
        private readonly IIOTDevicesService _iOTDevicesService;
        private readonly IDeviceCategoriesService _deviceCategoriesService;
        private readonly IApplicationDeviceMappingService _applicationDeviceMappingService;


        public TerminalUpdateTemplateController(
            IDeviceCategoriesService deviceCategoriesService,
            IIOTDevicesService iOTDevicesService,
            IIOTDeviceTypesService iOTDeviceTypesService,
            IAddressLocalItemService addressLocalItemService,
            IDeviceTemplatesService deviceTemplatesService,
            IDeviceTemplatesMappingService deviceTemplatesMappingService,
            ITerminalSetupTemplateService terminalSetupTemplateService,
            ITerminalService terminalService,
            IUserTerminalService userTerminalService,
            ICompanyMasterService companyService,
            IGroupMasterService groupMasterService,
            ITerminalTypeMasterService terminalTypeMasterService,
            ITimeZoneMasterService timezoneService,
            IAddressDetailsService addressDetailService,
            ILanguageMasterService languageMasterService,
            IUserMasterService userSystemService,
            ISystemHistoryService systemHistoryService,
            IStyleService styleService,
            IUserMasterService userService,
            ICharityService charityService,
            IMerchantMasterService merchantMasterService,
            IMerchantTerminalService merchantTerminalService,
            IMerchantGeneralSetupService merchantGeneralSetupService,
            ITipService tipService,
            IEftPaymentsService eftPaymentsService,
            ICalculatePayService calculatePayService,
            IBaseService<SupportTerminal> supportTerminalService,
            IMerchantUserService merchantUserService,
            ITerminalModelMasterService terminalModelMasterService,
            ITimeZoneDetailService timeZoneDetailService,
            IServiceItemService serviceItemService,
            ISurveyItemService surveyItemService,
            IMerchantUserLogonService merchantUserLogonService,
            ICommonMessageService commonMessageService,
            IBaseService<LanguageOptionTerminal> languageOptionTerminalService,
            IBaseService<LanguageByTerminal> languageByTerminalService,
            IBaseService<TerminalUserSecurityAccess> terminalUserSecurityAccess,
            IBaseService<ManualCardTerminal> manualCardTerminalService,
            IBaseService<StoreForwardTerminal> storeForwardTerminalService,
            IBaseService<SoundTerminal> soundTerminalService,
            IBaseService<DateTimeZoneTerminal> dateTimeZoneTerminalService,
            IBaseService<DefaultSaleTypeTerminal> defaultSaleTypeTerminalService,
            IBaseService<PosRequestTerminal> posRequestTerminalService,
            IBaseService<DeditCardBackTerminal> deditCardBackTerminalService,
            IBaseService<CreditCashOutTerminal> creditCashOutTerminalService,
            IBaseService<SurchargeTerminal> surchargeTerminalService,
            IBaseService<ComTerminal> comTerminalService,
            IActionOfFunctionService actionOfFunctionService,
            IApplicationDeviceMappingService applicationDeviceMappingService
            ) : base(systemHistoryService, styleService, userSystemService, languageMasterService, commonMessageService, actionOfFunctionService)
        {
            _iOTDevicesService = iOTDevicesService;
            _deviceCategoriesService = deviceCategoriesService;
            _iOTDeviceTypesService = iOTDeviceTypesService;
            _addressLocalItemService = addressLocalItemService;
            _deviceTemplatesService = deviceTemplatesService;
            _deviceTemplatesMappingService = deviceTemplatesMappingService;
            _terminalSetupTemplateService = terminalSetupTemplateService;
            _terminalService = terminalService;
            _companyService = companyService;
            _groupMasterService = groupMasterService;
            _terminalTypeMasterService = terminalTypeMasterService;
            _timezoneService = timezoneService;
            _addressDetailService = addressDetailService;
            _charityService = charityService;
            _merchantMasterService = merchantMasterService;
            _userTerminalService = userTerminalService;
            _userService = userService;
            _merchantTerminalService = merchantTerminalService;
            _merchantGeneralSetupService = merchantGeneralSetupService;
            _tipService = tipService;
            _eftPaymentsService = eftPaymentsService;
            _calculatePayService = calculatePayService;
            _supportTerminalService = supportTerminalService;
            _merchantUserService = merchantUserService;
            _terminalModelMasterService = terminalModelMasterService;
            _timeZoneDetailService = timeZoneDetailService;
            _serviceItemService = serviceItemService;
            _surveyItemService = surveyItemService;
            _merchantUserLogonService = merchantUserLogonService;
            _languageOptionTerminalService = languageOptionTerminalService;
            _languageByTerminalService = languageByTerminalService;
            _terminalUserSecurityAccess = terminalUserSecurityAccess;
            _manualCardTerminalService = manualCardTerminalService;
            _storeForwardTerminalService = storeForwardTerminalService;
            _soundTerminalService = soundTerminalService;
            _dateTimeZoneTerminalService = dateTimeZoneTerminalService;
            _defaultSaleTypeTerminalService = defaultSaleTypeTerminalService;
            _posRequestTerminalService = posRequestTerminalService;
            _deditCardBackTerminalService = deditCardBackTerminalService;
            _creditCashOutTerminalService = creditCashOutTerminalService;
            _surchargeTerminalService = surchargeTerminalService;
            _comTerminalService = comTerminalService;
            _applicationDeviceMappingService = applicationDeviceMappingService;
        }

        #region Load Index and Add Or Update Template

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        [SecurityCustomName("Terminal Update Template")]
        public ActionResult Index(int selectedItem = 0)
        {

            return View(new TerminalUpdateTemplateModel()
            {
                SelectedItem = selectedItem,
                TerminalSetupTemplates = _terminalSetupTemplateService.GetAll(null, false, true, m => m.UpdateName),
            });
        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult GetItems(string searchKey = "")
        {
            var model = new TerminalUpdateTemplateModel();
            var terminal = _terminalSetupTemplateService.GetAll(null, false, true, m => m.UpdateName);
            model.TerminalSetupTemplates = string.IsNullOrWhiteSpace(searchKey) ? terminal : terminal.Where(x => x.UpdateName.Contains(searchKey)).ToList();

            return PartialView(model);
        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.AddAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult Add()
        {
            return View(new TerminalUpdateItemModel()
            {
                TerminalMasters = _terminalService.GetAllTerminals(),
                TimeZones = _timezoneService.GetAll(includeDeactivated: true),
            });
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.AddAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult Add(TerminalUpdateItemModel model, List<TimeZoneDetailModel> timezoneDetail,
            int isTerminalUpdate = 0, int isTerminalSetupTemplate = 0)
        {
            Validate(model);
            var list = new List<TimeZoneDetail>();
            //add list of timezone detail
            if (timezoneDetail != null)
            {
                foreach (var each in timezoneDetail)
                {
                    var timezone = new TimeZoneDetail();
                    timezone = each.GetTimeZoneDetail(timezone);
                    if (timezone != null)
                    {
                        list.Add(timezone);
                    }
                }
            }
            if (!ModelState.IsValid)
            {
                model.TerminalMasters = _terminalService.GetAllTerminals();
                model.TimeZones = _timezoneService.GetAll(includeDeactivated: true);
                model.TimeZoneDetails = list;
                return View(model);
            }

            var itemModel = model.GetTerminalSetupUpdate(new TerminalSetupTemplate());
            itemModel.TimeZoneDetails = list;
            _terminalSetupTemplateService.Insert(itemModel);

            //update edit link menu-bar
            var editUrl = Url.Action("Edit", "TerminalUpdateTemplate", new { Id = itemModel.Id });/* "/TerminalSetup/Edit?Id=2";*/
            MMS.Web.Utils.WebUtils.UpdateMenuBarAddScreen(editUrl, MMS.Web.Utils.WebUtils.GenerateScreenKey("Edit", "TerminalUpdateTemplate"));

            if (isTerminalSetupTemplate == 1)
            {
                return RedirectToAction("SetupTemplate", "TerminalUpdateTemplate", new { templateId = itemModel.Id });
            }
            return isTerminalUpdate == 1 ? RedirectToAction("TemplateSetupTemplate", "TerminalUpdateTemplate", new { itemModel.Id })
                                         : RedirectToAction("Index", new { selectedItem = itemModel.Id });

        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.EditAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult Edit(int id)
        {
            var tmp = _terminalSetupTemplateService.GetById(id);
            if (tmp != null)
            {
                return View(new TerminalUpdateItemModel(tmp)
                {
                    TerminalMasters = _terminalService.GetAllTerminals(),
                    TimeZoneDetails = tmp.TimeZoneDetails,
                    TimeZones = _timezoneService.GetAll(includeDeactivated: true),
                });
            }
            return RedirectToAction("Index");
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.EditAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult Edit(TerminalUpdateItemModel model, List<TimeZoneDetailModel> timezoneDetail, int isTerminalUpdate
            , int isTerminalSetupTemplate = 0)
        {
            Validate(model);
            var list = new List<TimeZoneDetail>();
            //add list of timezone detail
            if (timezoneDetail != null)
            {
                foreach (var each in timezoneDetail)
                {
                    var timezone = new TimeZoneDetail();
                    timezone = each.GetTimeZoneDetail(timezone);
                    if (timezone != null)
                    {
                        list.Add(timezone);
                    }
                }
            }
            if (!ModelState.IsValid)
            {
                model.TerminalMasters = _terminalService.GetAllTerminals();
                model.TimeZones = _timezoneService.GetAll(includeDeactivated: true);
                model.TimeZoneDetails = list;
                return View(model);
            }
            var itemModel = _terminalSetupTemplateService.GetById(model.Id);
            var original = new TerminalUpdateItemModel(itemModel).GetTerminalSetupUpdate(null);
            itemModel = model.GetTerminalSetupUpdate(itemModel);

            if (itemModel != null)
            {
                if (itemModel.TimeZoneDetails != null)
                {
                    var currentTimezoneDetails = itemModel.TimeZoneDetails;
                    for (int i = 0; i < currentTimezoneDetails.Count(); i++)
                    {
                        var each = itemModel.TimeZoneDetails.ElementAt(i);
                        //tmp.TimeZoneDetails.Remove(each);
                        _timeZoneDetailService.Delete(each);
                    }
                }
                foreach (var each in list)
                {
                    each.TerminalSetupTemplateId = itemModel.Id;
                    _timeZoneDetailService.Insert(each);
                }
                _terminalSetupTemplateService.Update(itemModel);
                var ids = _terminalService.GetAllTerminalsIdByUpdateTemplateId(model.Id);
                //update status changed for terminal
                _terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.TerminalMaster, ids, false);
                //uipdate status changed for setup update terminal 
                var setupIds = _terminalService.GetAllTerminalSetupUpdateByTemplateId(model.Id);
                _terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.TerminalSetupUpdate, setupIds, false);
                if (isTerminalSetupTemplate == 1)
                {
                    return RedirectToAction("SetupTemplate", "TerminalUpdateTemplate", new { templateId = model.Id });
                }
                return isTerminalUpdate == 1 ? RedirectToAction("TemplateSetupTemplate", "TerminalUpdateTemplate", new { model.Id }) : RedirectToAction("Index");
            }

            return RedirectToAction("Index", new { selectedItem = model.Id });
        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ActiveAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult Activate(int id)
        {
            try
            {
                if (id <= 0)
                {
                    return Json(false);
                }
                var tmp = _terminalSetupTemplateService.GetById(id);
                if (tmp == null)
                {
                    return Json(false);
                }
                if (!tmp.IsActive)
                {
                    tmp.IsActive = true;
                    _terminalSetupTemplateService.Update(tmp);
                }
                LogAction(Secure.StrFunctionTerminalUpgradeTemplate, Constants.ActionActive);
                return Json(true);
            }
            catch (Exception)
            {
                LogAction(Secure.StrFunctionTerminalUpgradeTemplate, Constants.ActionActive, true);
                return Json(true);
            }
        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ActiveAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult Deactivate(int id)
        {
            try
            {
                if (id <= 0)
                {
                    return Json(false);
                }
                var tmp = _terminalSetupTemplateService.GetById(id);
                if (tmp == null)
                {
                    return Json(false);
                }
                if (tmp.IsActive)
                {
                    tmp.IsActive = false;
                    _terminalSetupTemplateService.Update(tmp);
                }
                LogAction(Secure.StrFunctionTerminalUpgradeTemplate, Constants.ActionActive);
                return Json(true);
            }
            catch (Exception)
            {
                LogAction(Secure.StrFunctionTerminalUpgradeTemplate, Constants.ActionActive, true);
                return Json(true);
            }
        }

        [HttpPost]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.EditAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public JsonResult Remove(int id)
        {
            var tmp = _terminalSetupTemplateService.GetById(id);
            if (tmp == null)
            {
                return Json(false);
            }

            tmp.IsStatus = MMS.Core.CoreUTI.Constants.DELETE_RECORD;
            _terminalSetupTemplateService.Update(tmp);

            return Json(true);
        }
        #endregion

        #region Load Terminal And assign to template

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult TemplateSetupTemplate(int id)
        {
            var tmp = _terminalSetupTemplateService.GetById(id);
            var model = new UpgradeTemplateGroupModel
            {
                TemplateId = tmp.Id,
                TeminalMasters = _terminalService.GetAllTerminalsByUpdateTemplateId(tmp.Id),
                Brands = _terminalTypeMasterService.GetAllBrand(),
                Companies = _companyService.GetAll(includeDeactivated: true),
            };
            model.Models = _terminalModelMasterService.GetAllModelByBrand(model.BrandId);
            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.EditAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult TemplateSetupTemplate(int id, List<int> terminalIds)
        {
            var tmp = _terminalSetupTemplateService.GetById(id, p => p.TerminalSecurityAccesses);
            if (tmp != null)
            {
                //var isTimeZoneDetail = tmp.TimeZoneDetails != null;
                var oldTerminalIds = _terminalService.GetAllTerminalsIdByUpdateTemplateId(id);
                //remove old terminals 
                _terminalService.SetSetupTemplate(0, oldTerminalIds);

                if (terminalIds != null)
                {
                    //remove all old function set isUpdated = false for other
                    //assign terminal for template
                    _terminalService.SetSetupTemplate(id, terminalIds);
                    //mark terminal isUpdated = false
                    _terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.TerminalMaster, terminalIds,
                        false);
                    //set flag isUpdated false
                    //setup update
                    var terminalSetupdateIds = _terminalService.GetAllTerminalSetupUpdateByTemplateId(id);
                    _terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.TerminalSetupUpdate, terminalSetupdateIds,
                        false);
                    //setup support
                    //if (tmp.Support != null)
                    //{
                    //    var terminalSupportIds = _terminalService.GetAllSupportIdByTemplateId(id);
                    //    _terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.SupportTerminal, terminalSupportIds,
                    //   false);
                    //}
                    ////setup Date time zone
                    //if (tmp.Date != null)
                    //{
                    //    var terminalDatimeZoneIds = _terminalService.GetAllDateTimeZoneIdByTemplateId(id);
                    //    _terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.DateTimeZoneTerminal, terminalDatimeZoneIds,
                    //    false);
                    //}

                    ////setup Lang option
                    //var langOptions = _terminalService.GetAllLanguageOptionIdByTemplateId(tmp.Id);
                    //if (langOptions != null)
                    //{
                    //    //this function will be handle later
                    //    var terminalLangOptionIds = _terminalService.GetAllLanguageOptionIdByTemplateId(id);
                    //    _terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.LanguageOptionTerminal, terminalLangOptionIds,
                    //   false);
                    //}
                    ////setup language by
                    //if (tmp.Language != null)
                    //{
                    //    var itemIds = _terminalService.GetAllLanguageByIdTemplateId(id);
                    //    _terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.LanguageByTerminal, itemIds,
                    //   false);
                    //}

                    ////setup Default Sale Type
                    //if (tmp.SaleType != null)
                    //{
                    //    var itemIds = _terminalService.GetAllDefaultSaleTypeIdByTemplateId(id);
                    //    _terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.DefaultSaleTypeTerminal, itemIds,
                    //    false);
                    //}

                    //setup POS Requests
                    //if (tmp.PosRequest != null)
                    //{
                    //    var itemIds = _terminalService.GetAllPosRequestIdByTemplateId(id);
                    //    _terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.PosRequestTerminal, itemIds,
                    //    false);
                    //}

                    //setup Cards Accepted
                    //if (tmp.CardsAccepted != null)
                    //{
                    //    var itemIds = _terminalService.GetAllCardAcceptIdByTemplateId(id);
                    //    _terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.CardAcceptTerminal, itemIds,
                    //    false);
                    //}
                    //setup Terminal Functions

                    //setup Security Levels
                    if (tmp.TerminalSecurityAccesses != null)
                    {
                        //var itemIds = _terminalService.GetAllTerminalMaster(id);
                        //_terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.TerminalUserSecurityAccess, itemIds,
                        //false);
                    }
                    //setup Terminal Setup
                    //if (tmp.Setup != null)
                    //{
                    //    var itemIds = _terminalService.GetAllSetupSystemIdByTemplateId(id);
                    //    _terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.SetupSystemTerminal, itemIds,
                    //    false);
                    //}
                    //setup Manual Cards
                    //if (tmp.ManualCards != null)
                    //{
                    //    var itemIds = _terminalService.GetAllManualCardIdByTemplateId(id);
                    //    _terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.ManualCardTerminal, itemIds,
                    //    false);
                    //}
                    ////setup Store Forward
                    //if (tmp.StoreForward != null)
                    //{
                    //    var itemIds = _terminalService.GetAllStoreForwardIdByTemplateId(id);
                    //    _terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.StoreForwardTerminal, itemIds,
                    //    false);
                    //}
                    ////setup Sounds
                    //if (tmp.Sound != null)
                    //{
                    //    var itemIds = _terminalService.GetAllSoundTerminalIdByTemplateId(id);
                    //    _terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.SoundTerminal, itemIds,
                    //    false);
                    //}
                    ////setup Cash Back Debit
                    //if (tmp.CashBack != null)
                    //{
                    //    var itemIds = _terminalService.GetAllDeditCardBackIdByTemplateId(id);
                    //    _terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.DeditCardBackTerminal, itemIds,
                    //    false);
                    //}
                    ////setup Cash Out Credit
                    //if (tmp.CashOut != null)
                    //{
                    //    var itemIds = _terminalService.GetAllCreditCashOutIdByTemplateId(id);
                    //    _terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.CreditCashOutTerminal, itemIds,
                    //    false);
                    //}
                    ////setup Surcharge 
                    //if (tmp.Surcharge != null)
                    //{
                    //    var itemIds = _terminalService.GetAllSurchangeIdByTemplateId(id);
                    //    _terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.SurchargeTerminal, itemIds,
                    //    false);
                    //}
                    ////setup Tips
                    //if (tmp.Tips != null)
                    //{
                    //    var itemIds = _terminalService.GetAllTipsIdByTemplateId(id);
                    //    _terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.TipsTerminal, itemIds,
                    //    false);
                    //}
                    ////setup Terminal COM IP
                    //if (tmp.Com != null)
                    //{
                    //    var itemIds = _terminalService.GetAllCommunicationIdByTemplateId(id);
                    //    _terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.ComTerminal, itemIds,
                    //    false);
                    //}
                    //setup Terminal Com Dial
                }
            }
            return RedirectToAction("Edit", "TerminalUpdateTemplate", new { id });
        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult LoadTerminalByGroup(int templateId, int modelId, int companyId, int groupId
            , bool isSelected = true, bool isUnselect = true, bool isSelectedByOther = true, string terminalNumber = "")
        {
            var model = new UpgradeTemplateGroupModel
            {
                TerminalMasterUnbinding = _groupMasterService.TerminalByGroupCompanyModelAnfFilter(templateId,
                    modelId, Constants.UPDATE_TEMPLATE, companyId, groupId, terminalNumber, isSelected, isUnselect,
                    isSelectedByOther),
                TemplateId = templateId
            };
            return PartialView(model);
        }

        #endregion

        #region Submenu for Setting template for terminal

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult SetupTemplate(int templateId)
        {
            var model = new BaseModel()
            {
                Id = templateId,
            };
            return View(model);
        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult FunctionTemplate(int templateId)
        {
            var model = new BaseModel()
            {
                Id = templateId,
            };
            return View(model);
        }

        #endregion

        #region Setting Details Template for Terminal

        #region setting Language By

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult SettingLanguageBy(int id)
        {
            if (id <= 0)
            {
                return RedirectToAction("Index");
            }
            var tmp = _terminalSetupTemplateService.GetById(id, p => p.Language);
            if (tmp == null)
            {
                return RedirectToAction("Index");
            }
            var model = tmp.Language == null ? new LanguageByModel() : new LanguageByModel(tmp.Language);
            //terminal Id in this case mean Terminal template id
            model.TerminalId = id;
            var languageList = _languageOptionTerminalService.GetAll(x => x.MerchantTerminalId == id);
            if (languageList == null || !languageList.Any())
            {
                languageList.Add(new LanguageOptionTerminal()
                {
                    LanguageId = 0,
                    sName = "English"
                });
                languageList.Add(new LanguageOptionTerminal()
                {
                    LanguageId = 1,
                    sName = "French"
                });
                languageList.Add(new LanguageOptionTerminal()
                {
                    LanguageId = 2,
                    sName = "Spanish"
                });
            }
            model.LanguageMasters = languageList;
            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.EditAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult SettingLanguageBy(LanguageByModel model)
        {
            if (model.Id == 0)
            {
                //insert new Card setup
                var tmp = new LanguageByTerminal();
                tmp = model.GetLanguageByTerminal(tmp);
                _languageByTerminalService.Insert(tmp);
                var template = _terminalSetupTemplateService.GetById(model.TerminalId);
                template.LanguageId = tmp.Id;
                _terminalSetupTemplateService.Update(template);
            }
            else
            {
                //update setup
                var tmp = _languageByTerminalService.GetById(model.Id);
                tmp = model.GetLanguageByTerminal(tmp);
                _languageByTerminalService.Update(tmp);
            }
            //update terminal
            var terminals = _terminalService.GetAllTerminalsIdByUpdateTemplateId(model.TerminalId);
            _terminalService.SetIsUpdate(MMSConstants.TerminalMaster, terminals, false);

            //update edit function table
            var items = _terminalSetupTemplateService.GetAllLanguageByIdTemplateId(model.TerminalId);
            _terminalService.SetIsUpdate(MMSConstants.LanguageByTerminal, items, false);
            return RedirectToAction("SetupTemplate", new { templateId = model.TerminalId });
        }

        #endregion

        #region Setting Support

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult SupportSetup(int id)
        {
            if (id <= 0)
            {
                return RedirectToAction("Index");
            }
            var tmp = _terminalSetupTemplateService.GetById(id);
            if (tmp == null)
            {
                return RedirectToAction("Index");
            }
            SupportModel model = null;
            if (tmp.Support == null)
            {
                model = new SupportModel();
            }
            else
            {
                model = new SupportModel(tmp.Support);
            }

            model.TemplateId = id;
            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.EditAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult SupportSetup([FromForm] IFormFile logoImageFile, SupportModel model, int deleteFile)
        {
            if (model.Id == 0)
            {
                //insert new support setup
                var tmp = new SupportTerminal();
                tmp = model.GetSupportTerminal(tmp);
                _supportTerminalService.Insert(tmp);
                var template = _terminalSetupTemplateService.GetById(model.TemplateId);
                template.SupportId = tmp.Id;
                if (logoImageFile != null)
                {
                    Utils.WebUtils.PathCloudUserFolder(tmp.lszLogo, logoImageFile, "terminal_support_" + tmp.Id + ".png");
                    tmp.lszLogo = "terminal_support_" + tmp.Id + ".png";
                }
                else if (deleteFile == 1 && !string.IsNullOrWhiteSpace(tmp.lszLogo))
                {
                    var oldFilePath = Utils.WebUtils.PathCloudUserFolder(tmp.lszLogo);
                    if (System.IO.File.Exists(oldFilePath))
                    {
                        System.IO.File.Delete(oldFilePath);
                    }
                    tmp.lszLogo = "";
                }
                _terminalSetupTemplateService.Update(template);

            }
            else
            {
                //update support setup
                var tmp = _supportTerminalService.GetById(model.Id);
                if (tmp != null)
                {
                    if (logoImageFile != null)
                    {
                        Utils.WebUtils.PathCloudUserFolder(tmp.lszLogo, logoImageFile, "terminal_support_" + tmp.Id + ".png");
                        tmp.lszLogo = "terminal_support_" + tmp.Id + ".png";
                    }
                    else if (deleteFile == 1 && !string.IsNullOrWhiteSpace(tmp.lszLogo))
                    {
                        var oldFilePath = Utils.WebUtils.PathCloudUserFolder(tmp.lszLogo);
                        if (System.IO.File.Exists(oldFilePath))
                        {
                            System.IO.File.Delete(oldFilePath);
                        }
                        tmp.lszLogo = "";
                    }
                    tmp = model.GetSupportTerminal(tmp);
                    _supportTerminalService.Update(tmp);
                }
            }
            return RedirectToAction("SetupTemplate", new { templateId = model.TemplateId });
        }

        #endregion

        #region Setting Card Accepted

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.EditAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult SetupCardAccepted(int id)
        {
            var tmp = _terminalSetupTemplateService.GetById(id);
            var model = new CardAccepted();

            model.TerminalId = id;

            model.RedirectBackPage = Url.Action("SetupTemplate", "TerminalUpdateTemplate",
                new { templateId = id });
            model.ActionUrl = Url.Action("SetupCardAccepted", "TerminalUpdateTemplate");
            return View(model);
        }

        //[HttpPost]
        //[ValidateAntiForgeryToken]
        //[AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.EditAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        //public ActionResult SetupCardAccepted(CardAccepted model)
        //{
        //    //update Card terminal
        //    //  var terminals = _terminalService.GetAllTerminalsByUpdateTemplateId(model.TerminalId);
        //    if (model.Id == 0)
        //    {
        //        //insert new Card setup
        //        var tmp = new CardAcceptTerminal();
        //        tmp = model.GetCardAcceptTerminal(tmp);
        //        _terminalService.Insert(tmp);
        //        var template = _terminalSetupTemplateService.GetById(model.TerminalId);
        //        //template.CardsAcceptedId = tmp.Id;
        //        _terminalSetupTemplateService.Update(template);
        //    }
        //    else
        //    {
        //        //update Card setup
        //        var tmp = _terminalService.GetCardAcceptById(model.Id);
        //        tmp = model.GetCardAcceptTerminal(tmp);
        //        _terminalService.Update(tmp);
        //    }
        //    //update terminal
        //    var terminals = _terminalService.GetAllTerminalsIdByUpdateTemplateId(model.TerminalId);
        //    _terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.TerminalMaster, terminals, false);
        //    //update edit function table 
        //    var items = _terminalService.GetAllCardAcceptIdByTemplateId(model.TerminalId);
        //    _terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.CardAcceptTerminal, items, false);
        //    return RedirectToAction("SetupTemplate", new { templateId = model.TerminalId });
        //}

        #endregion

        #region Setting Language Option

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.EditAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult LanguageOption(int id)
        {
            var model = new TerminalLanguageOption
            {
                TemplateId = id,
            };
            // funtion not implement
            // var option = _terminalService.GetAllLanguageOptionByTemplateId(id);
            // var language = _languageMasterService.GetAllActiveMasterNotSort();
            //model.Langs = language;
            //if (option == null || !option.Any())
            //{
            //    option = new List<LanguageOptionTerminal>();
            //    var count = 1;
            //    foreach (var each in language)
            //    {
            //        var item = new LanguageOptionTerminal()
            //        {
            //            TemplateId = id,
            //            fUsed = 0,
            //            LanguageId = each.Id,
            //            iOderId = count,
            //            TerminalId = null
            //        };
            //        _terminalService.Insert(item);
            //        option.Add(item);
            //        count++;
            //    }
            //}
            //else if (option.Count < language.Count)
            //{
            //    foreach (var master in language)
            //    {
            //        var addNew = option.All(each => each.LanguageId != master.Id);
            //        if (addNew)
            //        {
            //            var item = new LanguageOptionTerminal()
            //            {
            //                TemplateId = id,
            //                fUsed = 0,
            //                LanguageId = master.Id,
            //                iOderId = option.Count + 1,
            //                TerminalId = null
            //            };
            //            _terminalService.Insert(item);
            //            option.Add(item);
            //        }
            //    }
            //}
            // model.LangOptions = option;
            return View(model);
        }

        [HttpPost]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.EditAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult LanguageOption(List<TerminalLanguageOption> model, int templateId)
        {
            foreach (var each in model)
            {
                var tmp = _languageOptionTerminalService.GetById(each.Id);
                if (tmp != null)
                {
                    tmp.LanguageId = each.LangId;
                    //tmp.TemplateId = templateId;
                    tmp.fUsed = each.IsUse;
                    tmp.IsUpdated = false;
                }
                _languageOptionTerminalService.Update(tmp);
            }
            //update terminal
            var terminals = _terminalService.GetAllTerminalsIdByUpdateTemplateId(templateId);
            _terminalService.SetIsUpdate(MMSConstants.TerminalMaster, terminals, false);
            //update edit function table - todo: handle get ByTemplateId
            //var items = _terminalSetupTemplateService.GetAllLanguageOptionIdByTemplateId(templateId);
            //_terminalService.SetIsUpdate(MMSConstants.LanguageOptionTerminal, items, false);
            return RedirectToAction("SetupTemplate", new { templateId = templateId });
        }

        #endregion

        #region Setting Manual Card

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.EditAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult SettingManualCard(int id)
        {
            var tmp = _terminalSetupTemplateService.GetById(id, p => p.ManualCards);
            var model = tmp.ManualCards == null ? new ManualCard() : new ManualCard(tmp.ManualCards);
            //terminal Id in this case mean Terminal template id
            model.TerminalId = id;
            model.ActionUrl = Url.Action("SettingManualCard", "TerminalUpdateTemplate");
            model.RedirectBackPage = Url.Action("FunctionTemplate", "TerminalUpdateTemplate",
                new { templateId = id });
            model.ScreenKey = Constants.ScreenKeySetupMenuFunctionManualCards;
            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.EditAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult SettingManualCard(ManualCard model)
        {
            if (model.Id == 0)
            {
                //insert new Card setup
                var tmp = new ManualCardTerminal();
                tmp = model.GetManualCardTerminal(tmp);
                _manualCardTerminalService.Insert(tmp);
                var template = _terminalSetupTemplateService.GetById(model.TerminalId);
                template.ManualCardsId = tmp.Id;
                _terminalSetupTemplateService.Update(template);
            }
            else
            {
                //update Card setup
                var tmp = _manualCardTerminalService.GetById(model.Id);
                tmp = model.GetManualCardTerminal(tmp);
                _manualCardTerminalService.Update(tmp);
            }
            //update terminal
            var terminals = _terminalService.GetAllTerminalsIdByUpdateTemplateId(model.TerminalId);
            _terminalService.SetIsUpdate(MMSConstants.TerminalMaster, terminals, false);
            //update edit function table - todo: handle get ByTemplateId
            //var items = _terminalService.GetAllManualCardIdByTemplateId(model.TerminalId);
            //_terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.ManualCardTerminal, items, false);
            return RedirectToAction("FunctionTemplate", new { templateId = model.TerminalId });
        }

        #endregion

        #region Setting Store Forward
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.EditAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult SettingStoreForward(int id)
        {
            var tmp = _terminalSetupTemplateService.GetById(id, p => p.StoreForward);
            var model = tmp.StoreForward == null ? new StoreForward() : new StoreForward(tmp.StoreForward);
            //terminal Id in this case mean Terminal template id
            model.TerminalId = id;
            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.EditAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult SettingStoreForward(StoreForward model)
        {
            if (model.Id == 0)
            {
                //insert new Card setup
                var tmp = new StoreForwardTerminal();
                tmp = model.GetStoreForwardTerminal(tmp, 0);
                _storeForwardTerminalService.Insert(tmp);
                var template = _terminalSetupTemplateService.GetById(model.TerminalId);
                template.StoreForwardId = tmp.Id;
                _terminalSetupTemplateService.Update(template);
            }
            else
            {
                //update Card setup
                var tmp = _storeForwardTerminalService.GetById(model.Id);
                tmp = model.GetStoreForwardTerminal(tmp, 0);
                _storeForwardTerminalService.Update(tmp);
            }
            //update terminal
            var terminals = _terminalService.GetAllTerminalsIdByUpdateTemplateId(model.TerminalId);
            _terminalService.SetIsUpdate(MMSConstants.TerminalMaster, terminals, false);

            //update edit function tables - todo: handle get ByTemplateId
            //var items = _terminalService.GetAllStoreForwardIdByTemplateId(model.TerminalId);
            //_terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.StoreForwardTerminal, items, false);
            return RedirectToAction("FunctionTemplate", new { templateId = model.TerminalId });
        }

        #endregion

        #region Setting Date Time Zone
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.EditAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult SettingDateTimeZone(int id)
        {
            if (id <= 0)
            {
                return RedirectToAction("Index");
            }
            var tmp = _terminalSetupTemplateService.GetById(id, p => p.Date);
            if (tmp == null)
            {
                return RedirectToAction("Index");
            }
            var model = tmp.Date == null ? new DateTimeZoneModel() : new DateTimeZoneModel(tmp.Date);
            //terminal Id in this case mean Terminal template id
            model.TerminalId = id;
            model.Countries = _addressDetailService.GetAllCountries()
                .Select(x => new ViewItemModel
                {
                    Id = x.Id,
                    Label = x.CountryName
                }).ToList();
            model.TimeZones = _timezoneService.GetAll(includeDeactivated: true);
            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.EditAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult SettingDateTimeZone(DateTimeZoneModel model)
        {
            if (model.Id == 0)
            {
                //insert new Card setup
                var tmp = new DateTimeZoneTerminal();
                tmp = model.GetDateTimeZoneTerminal(tmp, _timezoneService.GetAll(includeDeactivated: true));
                _dateTimeZoneTerminalService.Insert(tmp);
                var template = _terminalSetupTemplateService.GetById(model.TerminalId);
                template.DateId = tmp.Id;
                _terminalSetupTemplateService.Update(template);
            }
            else
            {
                //update Card setup
                var tmp = _dateTimeZoneTerminalService.GetById(model.Id);
                tmp = model.GetDateTimeZoneTerminal(tmp, _timezoneService.GetAll(includeDeactivated: true));
                _dateTimeZoneTerminalService.Update(tmp);
            }
            //update terminal
            var terminals = _terminalService.GetAllTerminalsIdByUpdateTemplateId(model.TerminalId);
            _terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.TerminalMaster, terminals, false);
            //update edit function table
            var items = _terminalService.GetAllDateTimeZoneIdByTemplateId(model.TerminalId);
            _terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.DateTimeZoneTerminal, items, false);
            return RedirectToAction("SetupTemplate", new { templateId = model.TerminalId });
        }

        #endregion

        #region Setting default sale type
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.EditAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult SettingDefaultSaleType(int id)
        {
            var tmp = _terminalSetupTemplateService.GetById(id, p => p.SaleType);
            var model = tmp.SaleType == null ? new DefaultSaleType() : new DefaultSaleType(tmp.SaleType);
            //terminal Id in this case mean Terminal template id
            model.TerminalId = id;
            var salesType = new Dictionary<int, string>
            {
                {Constants.DEFAULT_SALE_TYPE_TRANS_TYPE_KEY_NONE, Constants.DEFAULT_SALE_TYPE_TRANS_TYPE_VALUE_NONE},
                {
                    Constants.DEFAULT_SALE_TYPE_TRANS_TYPE_KEY_PREAUTH,
                    Constants.DEFAULT_SALE_TYPE_TRANS_TYPE_VALUE_PREAUTH
                },
                {Constants.DEFAULT_SALE_TYPE_TRANS_TYPE_KEY_REFUND, Constants.DEFAULT_SALE_TYPE_TRANS_TYPE_VALUE_REFUND},
                {Constants.DEFAULT_SALE_TYPE_TRANS_TYPE_KEY_SALE, Constants.DEFAULT_SALE_TYPE_TRANS_TYPE_VALUE_SALE}
            };
            var posType = new Dictionary<int, string>
            {
                {
                    Constants.DEFAULT_SALE_TYPE_TRANS_TYPE_KEY_NONE,
                    Constants.DEFAULT_SALE_TYPE_TRANS_TYPE_VALUE_NONE
                },
                {
                    Constants.DEFAULT_SALE_TYPE_POS_TRANS_TYPE_KEY_SALES_TICKET,
                    Constants.DEFAULT_SALE_TYPE_POS_TRANS_TYPE_VALUE_SALES_TICKET
                },
                {
                    Constants.DEFAULT_SALE_TYPE_POS_TRANS_TYPE_KEY_BAR_TAB,
                    Constants.DEFAULT_SALE_TYPE_POS_TRANS_TYPE_VALUE_BAR_TAB
                },
                {
                    Constants.DEFAULT_SALE_TYPE_POS_TRANS_TYPE_KEY_TABLE_CHECK,
                    Constants.DEFAULT_SALE_TYPE_POS_TRANS_TYPE_VALUE_TABLE_CHECK
                }
            };
            model.TransType = salesType;
            model.PosTransType = posType;
            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.EditAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult SettingDefaultSaleType(DefaultSaleType model)
        {
            if (model.Id == 0)
            {
                //insert new Card setup
                var tmp = new DefaultSaleTypeTerminal();
                tmp = model.GetDefaultSaleTypeTerminal(tmp);
                _defaultSaleTypeTerminalService.Insert(tmp);
                var template = _terminalSetupTemplateService.GetById(model.TerminalId);
                template.SaleTypeId = tmp.Id;
                _terminalSetupTemplateService.Update(template);
            }
            else
            {
                //update Card setup
                var tmp = _defaultSaleTypeTerminalService.GetById(model.Id);
                tmp = model.GetDefaultSaleTypeTerminal(tmp);
                _defaultSaleTypeTerminalService.Update(tmp);
            }
            //update terminal
            var terminals = _terminalService.GetAllTerminalsIdByUpdateTemplateId(model.TerminalId);
            _terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.TerminalMaster, terminals, false);
            //update edit function table- todo: handle get ByTemplateId
            //var items = _terminalService.GetAllDefaultSaleTypeIdByTemplateId(model.TerminalId);
            //_terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.DefaultSaleTypeTerminal, items, false);
            return RedirectToAction("SetupTemplate", new { templateId = model.TerminalId });
        }

        #endregion

        #region Setting POS request

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.EditAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult SettingPosRequest(int id)
        {
            var tmp = _terminalSetupTemplateService.GetById(id, p => p.PosRequest.PushModeImageList);
            var model = tmp.PosRequest == null ? new PosRequest() : new PosRequest(tmp.PosRequest);
            if (tmp.PosRequest == null)
            {
                var newPos = new PosRequestTerminal();
                newPos = new PosRequest().GetPosRequestTerminal(newPos);
                _posRequestTerminalService.Insert(newPos);
                tmp.PosRequestId = newPos.Id;
                _terminalSetupTemplateService.Update(tmp);
                model.Id = newPos.Id;
            }
            //terminal Id in this case mean Terminal template id
            model.TerminalId = id;
            model.ActionUrl = Url.Action("SettingPosRequest", "TerminalUpdateTemplate");
            model.RedirectBackPage = Url.Action("SetupTemplate", "TerminalUpdateTemplate", new { templateId = id });
            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.EditAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public async Task<ActionResult> SettingPosRequest(PosRequest model, [FromForm] IFormFile interfaceFileSelected, int deletefile, int isTerminalComIP = 0,
            int isRS232Comms = 0)
        {
            if (model.Id == 0)
            {
                //insert new Card setup
                var tmp = new PosRequestTerminal();
                tmp = model.GetPosRequestTerminal(tmp);
                _posRequestTerminalService.Insert(tmp);
                var template = _terminalSetupTemplateService.GetById(model.TerminalId);
                template.PosRequestId = tmp.Id;
                _terminalSetupTemplateService.Update(template);
            }
            else
            {
                //update Card setup
                var tmp = _posRequestTerminalService.GetById(model.Id);
                tmp = model.GetPosRequestTerminal(tmp);
                _posRequestTerminalService.Update(tmp);

                if (deletefile == 1)
                {
                    tmp.lszPOSCommsDLL = await Utils.WebUtils.PathProcessorsFolder(tmp.lszPOSCommsDLL, null);
                }
                else if (interfaceFileSelected != null)
                {
                    model.lszPOSCommsDLL = await Utils.WebUtils.PathProcessorsFolder(tmp.lszPOSCommsDLL, interfaceFileSelected);
                }

                tmp = model.GetPosRequestTerminal(tmp);
                tmp.IsStatus = MMS.Core.CoreUTI.Constants.EDIT_RECORD;
                _posRequestTerminalService.Update(tmp);
            }

            //update terminal
            var terminals = _terminalService.GetAllTerminalsIdByUpdateTemplateId(model.TerminalId);
            _terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.TerminalMaster, terminals, false);
            //update edit function table              
            var items = _terminalService.GetAllPosRequestIdByTemplateId(model.TerminalId);
            _terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.PosRequestTerminal, items, false);
            if (isTerminalComIP == 1)
            {
                return RedirectToAction("SettingComIp", new { Id = model.TerminalId });
            }
            if (isRS232Comms == 1)
            {
                //return RedirectToAction("RS232Comms", new { Id = model.TerminalId });
            }
            return RedirectToAction("SetupTemplate", new { templateId = model.TerminalId });
        }

        #endregion

        #region Setting Sounds

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.EditAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult SettingSound(int id)
        {
            if (id <= 0)
            {
                return RedirectToAction("Index");
            }
            var tmp = _terminalSetupTemplateService.GetById(id);
            if (tmp == null)
            {
                return RedirectToAction("Index");
            }
            var model = tmp.Sound == null ? new Sound() : new Sound(tmp.Sound);
            //terminal Id in this case mean Terminal template id
            model.TerminalId = id;
            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.EditAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public async Task<ActionResult> SettingSound(Sound model, [FromForm] IFormFile soundApprovedFile,
            [FromForm] IFormFile soundSavedFile, [FromForm] IFormFile soundDeclinedFile,
            bool isDeleteSoundApproved = false, bool isDeleteSoundSaved = false, bool isDeleteSoundDeclined = false)
        {
            var tmp = _soundTerminalService.GetById(model.Id);
            if (tmp == null)
            {
                tmp = new SoundTerminal();
            }
            if (isDeleteSoundApproved)
            {
                tmp.lszApproved = await WebUtils.PathSoundsFolder(tmp.lszApproved, null);
            }
            else if (soundApprovedFile != null)
            {
                tmp.lszApproved = await WebUtils.PathSoundsFolder(tmp.lszApproved, soundApprovedFile);
            }

            if (isDeleteSoundSaved)
            {
                tmp.lszSaved = await WebUtils.PathSoundsFolder(tmp.lszSaved, null);
            }
            else if (soundSavedFile != null)
            {
                tmp.lszSaved = await WebUtils.PathSoundsFolder(tmp.lszSaved, soundSavedFile);
            }

            if (isDeleteSoundDeclined)
            {
                tmp.lszDeclined = await WebUtils.PathSoundsFolder(tmp.lszDeclined, null);
            }
            else if (soundDeclinedFile != null)
            {
                tmp.lszDeclined = await WebUtils.PathSoundsFolder(tmp.lszDeclined, soundDeclinedFile);
            }
            if (model.Id == 0)
            {
                //insert new Card setup
                tmp = model.GetSoundTerminal(tmp);
                _soundTerminalService.Insert(tmp);
                var template = _terminalSetupTemplateService.GetById(model.TerminalId);
                template.SoundId = tmp.Id;
                _terminalSetupTemplateService.Update(template);
            }
            else
            {
                //update Card setup
                tmp = model.GetSoundTerminal(tmp);
                _soundTerminalService.Update(tmp);
            }
            //update terminal
            var terminals = _terminalService.GetAllTerminalsIdByUpdateTemplateId(model.TerminalId);
            _terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.TerminalMaster, terminals, false);
            //update edit function table
            var items = _terminalService.GetAllSoundTerminalIdByTemplateId(model.TerminalId);
            _terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.SoundTerminal, items, false);
            return RedirectToAction("SettingTerminalSetup", new { id = model.TerminalId });
        }

        #endregion

        #region Setting Cash Back Debit
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.EditAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult SettingCashBackDebit(int id)
        {
            var tmp = _terminalSetupTemplateService.GetById(id, p => p.CashBack);
            var model = tmp.CashBack == null ? new DebitCashBack() : new DebitCashBack(tmp.CashBack);
            //terminal Id in this case mean Terminal template id
            model.TerminalId = id;
            model.RedirectBackPage = Url.Action("FunctionTemplate", "TerminalUpdateTemplate", new { templateId = id });
            model.ActionUrl = Url.Action("SettingCashBackDebit", "TerminalUpdateTemplate");
            model.ScreenKey = Constants.ScreenKeySetupMenuFunctionCashBackDebit;
            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.EditAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult SettingCashBackDebit(DebitCashBack model)
        {
            if (model.Id == 0)
            {
                //insert new Card setup
                var tmp = new DeditCardBackTerminal();
                tmp = model.GetDeditCardBackTerminal(tmp);
                _deditCardBackTerminalService.Insert(tmp);
                var template = _terminalSetupTemplateService.GetById(model.TerminalId);
                template.CashBackId = tmp.Id;
                _terminalSetupTemplateService.Update(template);
            }
            else
            {
                //update Card setup
                var tmp = _deditCardBackTerminalService.GetById(model.Id);
                tmp = model.GetDeditCardBackTerminal(tmp);
                _deditCardBackTerminalService.Update(tmp);
            }
            //update terminal
            var terminals = _terminalService.GetAllTerminalsIdByUpdateTemplateId(model.TerminalId);
            _terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.TerminalMaster, terminals, false);
            //update edit function table              
            var items = _terminalService.GetAllDeditCardBackIdByTemplateId(model.TerminalId);
            _terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.DeditCardBackTerminal, items, false);
            return RedirectToAction("FunctionTemplate", new { templateId = model.TerminalId });
        }

        #endregion

        #region Setting Cash Out Credit

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.EditAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult SettingCashOutCredit(MerchantCash model)
        {
            if (model.Id == 0)
            {
                //insert new Card setup
                var tmp = new CreditCashOutTerminal();
                tmp = model.GetCreditCashOutTerminal(tmp);
                _creditCashOutTerminalService.Insert(tmp);
                var template = _terminalSetupTemplateService.GetById(model.TerminalId);
                template.CashOutId = tmp.Id;
                _terminalSetupTemplateService.Update(template);
            }
            else
            {
                //update Card setup
                var tmp = _creditCashOutTerminalService.GetById(model.Id);
                tmp = model.GetCreditCashOutTerminal(tmp);
                _creditCashOutTerminalService.Update(tmp);
            }
            //update terminal
            var terminals = _terminalService.GetAllTerminalsIdByUpdateTemplateId(model.TerminalId);
            _terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.TerminalMaster, terminals, false);
            //update edit function table - todo: handle get By Template Id
            //var items = _terminalService.GetAllCreditCashOutIdByTemplateId(model.TerminalId);
            //_terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.CreditCashOutTerminal, items, false);
            return RedirectToAction("FunctionTemplate", new { templateId = model.TerminalId });
        }

        public ActionResult SettingCashOutCredit(int id)
        {
            return View();
        }

        #endregion

        #region Setting Surcharge

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.EditAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult SettingSurcharge(int id)
        {
            var tmp = _terminalSetupTemplateService.GetById(id, p => p.Surcharge);
            var model = tmp.Surcharge == null ? new Surcharge() : new Surcharge(tmp.Surcharge);
            //terminal Id in this case mean Terminal template id
            model.TerminalId = id;
            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.EditAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult SettingSurcharge(Surcharge model)
        {
            if (model.Id == 0)
            {
                //insert new Card setup
                var tmp = new SurchargeTerminal();
                _surchargeTerminalService.Insert(tmp);
                var template = _terminalSetupTemplateService.GetById(model.TerminalId);
                template.SurchargeId = tmp.Id;
                _terminalSetupTemplateService.Update(template);
            }
            else
            {
                //update Card setup
                var tmp = _surchargeTerminalService.GetById(model.Id);
                _surchargeTerminalService.Update(tmp);
            }
            //update terminal
            var terminals = _terminalService.GetAllTerminalsIdByUpdateTemplateId(model.TerminalId);
            _terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.TerminalMaster, terminals, false);
            //update edit function table todo: handle get ByTemplateId
            //var items = _terminalService.GetAllSurchangeIdByTemplateId(model.TerminalId);
            //_terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.SurchargeTerminal, items, false);
            return RedirectToAction("FunctionTemplate", new { templateId = model.TerminalId });
        }

        #endregion

        #region Setting Tip

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.EditAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult SettingTips(int id)
        {
            var tmp = _terminalSetupTemplateService.GetById(id);
            var model = tmp.Tips == null ? new Tip() : new Tip(tmp.Tips);
            //terminal Id in this case mean Terminal template id
            model.TerminalId = id;
            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.EditAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult SettingTips(Tip model)
        {
            if (model.Id == 0)
            {
                //insert new Card setup
                var tmp = new TipsTerminal();
                tmp = model.GetTipsTerminal(tmp, 0);
                _tipService.Insert(tmp);
                var template = _terminalSetupTemplateService.GetById(model.TerminalId);
                template.TipsId = tmp.Id;
                _terminalSetupTemplateService.Update(template);
            }
            else
            {
                //update setup
                var tmp = _tipService.GetById(model.Id);
                tmp = model.GetTipsTerminal(tmp, 0);
                _tipService.Update(tmp);
            }
            //update terminal
            var terminals = _terminalService.GetAllTerminalsIdByUpdateTemplateId(model.TerminalId);
            _terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.TerminalMaster, terminals, false);
            //update edit function table : handle get ByTemplateId
            //var items = _terminalService.GetAllTipsIdByTemplateId(model.TerminalId);
            //_terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.TipsTerminal, items, false);
            return RedirectToAction("FunctionTemplate", new { templateId = model.TerminalId });
        }

        #endregion

        #region Setting Communication Ip

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.EditAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult SettingComIp(int id)
        {
            var tmp = _terminalSetupTemplateService.GetById(id, p => p.Com);
            var model = tmp.Com == null ? new ComIP() : new ComIP(tmp.Com);
            //terminal Id in this case mean Terminal template id
            model.TerminalId = id;
            var ipSetting = new Dictionary<int, string>
            {
                {Constants.IP_SETTING_KEY_NONE, Constants.IP_SETTING_VALUE_NONE},
                {Constants.IP_SETTING_KEY_STATIC, Constants.IP_SETTING_VALUE_STATIC},
                {Constants.IP_SETTING_KEY_DYNAMIC, Constants.IP_SETTING_VALUE_DYNAMIC}
            };
            model.IPSettingList = ipSetting;
            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.EditAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult SettingComIp(ComIP model)
        {
            //if (model.Id == 0)
            //{
            //    //insert new Card setup
            //    var tmp = new ComTerminal();
            //    tmp = model.GetComTerminal(tmp);
            //    _terminalService.Insert(tmp);
            //    var template = _terminalSetupTemplateService.GetById(model.TerminalId);
            //    template.ComId = tmp.Id;
            //    _terminalSetupTemplateService.Update(template);
            //}
            //else
            //{
            //    //update setup
            //    var tmp = _terminalService.GetCommunicationById(model.Id);
            //    tmp = model.GetComTerminal(tmp);
            //    _terminalService.Update(tmp);
            //}
            ////update terminal
            //var terminals = _terminalService.GetAllTerminalsIdByUpdateTemplateId(model.TerminalId);
            //_terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.TerminalMaster, terminals, false);
            ////update edit function table              
            //var items = _terminalService.GetAllCommunicationIdByTemplateId(model.TerminalId);
            //_terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.ComTerminal, items, false);
            return RedirectToAction("FunctionTemplate", new { templateId = model.TerminalId });
        }

        #endregion

        #region Setting Commincation Dial
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.EditAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult SettingComDial(int id)
        {
            var tmp = _terminalSetupTemplateService.GetById(id, p => p.Com);
            var model = tmp.Com == null ? new ComDial() : new ComDial(tmp.Com);
            //terminal Id in this case mean Terminal template id
            model.TerminalId = id;
            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.EditAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult SettingComDial(ComDial model)
        {
            if (model.Id == 0)
            {
                //insert new Card setup
                var tmp = new ComTerminal();
                tmp = model.GetComTerminal(tmp);
                _comTerminalService.Insert(tmp);
                var template = _terminalSetupTemplateService.GetById(model.TerminalId);
                template.ComId = tmp.Id;
                _terminalSetupTemplateService.Update(template);
            }
            else
            {
                //update setup
                var tmp = _comTerminalService.GetById(model.Id);
                tmp = model.GetComTerminal(tmp);
                _comTerminalService.Update(tmp);
            }
            //update terminal
            var terminals = _terminalService.GetAllTerminalsIdByUpdateTemplateId(model.TerminalId);
            _terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.TerminalMaster, terminals, false);
            //update edit function table : handle get ByTemplateId
            //var items = _terminalService.GetAllCommunicationIdByTemplateId(model.TerminalId);
            //_terminalService.SetIsUpdate(MMS.Core.CoreUTI.Constants.ComTerminal, items, false);
            return RedirectToAction("FunctionTemplate", new { templateId = model.TerminalId });
        }

        #endregion

        #region Setting Terminal Setup

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.EditAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult SettingTerminalSetup(int id)
        {
            var tmp = _terminalSetupTemplateService.GetById(id, p => p.MerchantGeneralSetup);
            var model = tmp.MerchantGeneralSetup == null ? new SystemSetupModel() : new SystemSetupModel(tmp.MerchantGeneralSetup);
            model.TemplateId = id;
            model.ActionUrl = Url.Action("SettingTerminalSetup", "TerminalUpdateTemplate");
            model.RedirectBackPage = Url.Action("SetupTemplate", "TerminalUpdateTemplate", new { templateId = id });
            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.EditAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult SettingTerminalSetup(SystemSetupModel model)
        {
            if (model.Id == 0)
            {
                //insert new Card setup
                var tmp = new MerchantGeneralSetup();
                tmp = model.GetSetupSystemTerminal(tmp);
                _merchantGeneralSetupService.Insert(tmp);
                var template = _terminalSetupTemplateService.GetById(model.TemplateId);
                template.MerchantGeneralSetupId = tmp.Id;
                _terminalSetupTemplateService.Update(template);
            }
            else
            {
                //update setup
                var tmp = _merchantGeneralSetupService.GetById(model.Id);
                tmp = model.GetSetupSystemTerminal(tmp);
                _merchantGeneralSetupService.Update(tmp);
            }
            return RedirectToAction("SetupTemplate", new { templateId = model.TemplateId });
        }

        #endregion

        #region Setting Charity 
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult Charity(int selectedItem = 0, int templateId = 0)
        {
            var model = new CharityTemplateModel()
            {
                SelectedItem = selectedItem,
                TemplateId = templateId,
            };
            return View(model);
        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalCharity, Action = Secure.ViewAction)]
        public ActionResult GetCharityItems(int item = 0, string searchKey = "", int index = 0, int templateId = 0)
        {

            var skip = 0;
            if (index != 0)
            {
                skip = index * Constants.LoadItem;
            }
            var items = _charityService.GetCharitiesByTemplateId(templateId, searchKey ?? "").ToListItemModel();

            if (items == null || items.Count <= 0 || items.Count < Constants.LoadItem)
            {
                ViewBag.Index = -1;
            }
            else
            {
                ViewBag.Index = index + 1;
            }
            var model = new ViewItemsModel()
            {
                Items = items,
                SelectedId = item
            };
            return PartialView(PartialViewConstants.ListTerminalMultipleItemStandard, model);
        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalCharity, Action = Secure.ActiveAction)]
        public JsonResult CharityActivate(int id = 0)
        {
            try
            {
                var charity = _charityService.GetById(id);
                if (charity.IsActive)
                {
                    return Json(false);
                }
                charity.IsActive = true;
                _charityService.Update(charity);
                LogAction(Secure.StrFunctionTerminalCharity, Constants.ActionActive);
                return Json(true);
            }
            catch (System.Exception ex)
            {
                LogAction(Secure.StrFunctionTerminalCharity, Constants.ActionActive, true);
                return Json(false);
            }
        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalCharity, Action = Secure.ActiveAction)]
        public JsonResult CharityDeactivate(int id = 0)
        {
            try
            {
                var charity = _charityService.GetById(id);
                if (!charity.IsActive)
                {
                    return Json(false);
                }
                charity.IsActive = false;
                _charityService.Update(charity);
                LogAction(Secure.StrFunctionTerminalCharity, Constants.ActionDeactive);
                return Json(true);
            }
            catch (System.Exception ex)
            {
                LogAction(Secure.StrFunctionTerminalCharity, Constants.ActionDeactive, true);
                return Json(false);
            }
        }

        [HttpPost]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalCharity, Action = Secure.EditAction)]
        public JsonResult CharityRemove(int id = 0)
        {
            if (_charityService.NotExistOrDeleted(id))
                return Json(false);

            _charityService.Delete(id);
            return Json(true);
        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalCharity, Action = Secure.ActiveAction)]
        public ActionResult CharitySetup(int id = 0, int templateId = 0)
        {
            if (templateId <= 0)
            {
                return RedirectToAction("Index");
            }
            var model = new CharityTemplateModel();
            if (!id.Equals(0))
            {
                var charity = _charityService.GetById(id);
                if (charity != null)
                    model.SetModel(charity);
            }
            model.TemplateId = templateId;
            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalCharity, Action = Secure.EditAction)]
        public async Task<ActionResult> CharitySetup(CharityTemplateModel model, [FromForm] IFormFile charityPicture, int deletePictureFile, [FromForm] IFormFile charityListPicture, int deleteListPictureFile)
        {
            if (!ModelState.IsValid)
            {
                model.lszSummaryPicture = Constants.PATH_IMAGE_RESOURCE + Constants.NO_IMAGE;
                LogAction(Secure.StrFunctionTerminalCharity, Constants.ActionEdit, true);
                return View(model);
            }

            var pictureUrl = "";
            var listPictureUrl = "";

            if (deletePictureFile == 1)
            {
                pictureUrl = await WebUtils.PathCharityFolder(model.lszSummaryPicture, null);
            }
            else if (charityPicture != null)
            {
                pictureUrl = await WebUtils.PathCharityFolder(model.lszSummaryPicture, charityPicture);
            }

            if (deleteListPictureFile == 1)
            {
                listPictureUrl = await WebUtils.PathCharityFolder(model.lszListPicture, null);
            }
            else if (charityListPicture != null)
            {
                listPictureUrl = await WebUtils.PathCharityFolder(model.lszListPicture, charityListPicture);
            }

            Charity master = null;
            if (model.Id.Equals(0))
            {
                //add
                master = model.GetMaster(new Charity());
                if (charityPicture != null || deletePictureFile == 1)
                    master.lszSummaryPicture = pictureUrl;
                if (charityListPicture != null || deleteListPictureFile == 1)
                    master.lszListPicture = listPictureUrl;
                master.TemplateId = model.TemplateId;
                _charityService.Insert(master);
            }
            else
            {
                //edit
                master = _charityService.GetById(model.Id);
                if (master != null)
                {
                    master = model.GetMaster(master);
                    if (charityPicture != null || deletePictureFile == 1)
                        master.lszSummaryPicture = pictureUrl;
                    if (charityListPicture != null || deleteListPictureFile == 1)
                        master.lszListPicture = listPictureUrl;
                    master.TemplateId = model.TemplateId;
                    _charityService.Update(master);
                }
            }
            return RedirectToAction("Charity", new { selectedItem = master.Id, templateId = model.TemplateId });
        }

        #endregion

        #region Setting MerchantSetup
        #region Merchant
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult MerchantSetup(int selectedItem = 0, int templateId = 0)
        {
            var model = new MerchantSetupModel()
            {
                SelectedItem = selectedItem,
                TemplateId = templateId,
            };
            return View(model);
        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult GetMerchantSetupItems(int item = 0, string searchKey = "", int index = 0, int templateId = 0)
        {

            var skip = 0;
            if (index != 0)
            {
                skip = index * Constants.LoadItem;
            }
            var items = _merchantTerminalService.GetMerchantTerminalsByTemplateId(templateId, searchKey ?? "").ToSetupListViewItemModel();

            if (items == null || items.Count <= 0 || items.Count < Constants.LoadItem)
            {
                ViewBag.Index = -1;
            }
            else
            {
                ViewBag.Index = index + 1;
            }
            var model = new ViewItemsModel()
            {
                Items = items,
                SelectedId = item
            };
            return PartialView(PartialViewConstants.ListTerminalMultipleItemStandard, model);
        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalCharity, Action = Secure.ActiveAction)]
        public JsonResult MerchantSetupActivate(int id = 0)
        {
            try
            {
                var item = _merchantTerminalService.GetById(id);
                if (item.IsActive)
                {
                    return Json(false);
                }
                item.IsActive = true;
                _merchantTerminalService.Update(item);
                LogAction(Secure.StrFunctionTerminalCharity, Constants.ActionDeactive);
                return Json(true);
            }
            catch (System.Exception ex)
            {
                LogAction(Secure.StrFunctionTerminalCharity, Constants.ActionActive, true);
                return Json(false);
            }
        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalCharity, Action = Secure.ActiveAction)]
        public JsonResult MerchantSetupDeactivate(int id = 0)
        {
            try
            {
                var item = _merchantTerminalService.GetById(id);
                if (item.IsActive)
                {
                    return Json(false);
                }
                item.IsActive = false;
                _merchantTerminalService.Update(item);
                LogAction(Secure.StrFunctionTerminalCharity, Constants.ActionDeactive);
                return Json(true);
            }
            catch (System.Exception ex)
            {
                LogAction(Secure.StrFunctionTerminalCharity, Constants.ActionDeactive, true);
                return Json(false);
            }
        }

        [HttpPost]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.EditAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public JsonResult MerchantSetupRemove(int id = 0)
        {
            var item = _merchantTerminalService.GetById(id);
            if (item.IsStatus == MMS.Core.CoreUTI.Constants.DELETE_RECORD || item == null)
            {
                return Json(false);
            }
            _merchantTerminalService.Delete(item, false);
            return Json(true);

        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult SettingMerchant(int id = 0, int templateId = 0)
        {
            if (templateId <= 0)
            {
                return RedirectToAction("Index");
            }
            var model = new MerchantSetupModel();
            if (!id.Equals(0))
            {
                var entity = _merchantTerminalService.GetById(id);
                if (entity != null)
                    model = new MerchantSetupModel(entity);
            }
            model.TemplateId = templateId;
            model.Id = id;
            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public async Task<ActionResult> SettingMerchant(MerchantSetupModel model, [FromForm] IFormFile merchantImage, int deleteMerchantImageFile)
        {
            var merchantImgUrl = "";
            if (deleteMerchantImageFile == 1)
            {
                merchantImgUrl = await WebUtils.PathCharityFolder(model.Image, null);
            }
            else if (merchantImage != null)
            {
                merchantImgUrl = await WebUtils.PathCharityFolder(model.Image, merchantImage);
            }

            MerchantTerminal merchantTerminal = null;

            if (model.Id.Equals(0))
            {
                //add
                merchantTerminal = model.ToEntity(new MerchantTerminal());
                if (merchantImage != null || deleteMerchantImageFile == 1)
                    merchantTerminal.Image = merchantImgUrl;
                _merchantTerminalService.Insert(merchantTerminal);
            }
            else
            {
                //edit
                merchantTerminal = _merchantTerminalService.GetById(model.Id);
                if (merchantTerminal != null)
                {
                    merchantTerminal = model.ToEntity(merchantTerminal);
                    if (merchantImage != null || deleteMerchantImageFile == 1)
                        merchantTerminal.Image = merchantImgUrl;
                    _merchantTerminalService.Update(merchantTerminal);
                }
            }
            switch (model.RedirectUrl)
            {
                case Utils.RedirectUrl.MerchantDetailsUrl:
                    return RedirectToAction("MerchantDetails", new { id = merchantTerminal.Id });
                case Utils.RedirectUrl.PaymentsAcceptedUrl:
                    return RedirectToAction("PaymentsAccepted", new { id = merchantTerminal.Id });
                case Utils.RedirectUrl.AdvanceSetupUrl:
                    return RedirectToAction("AdvanceSetup", new { id = merchantTerminal.Id });
                case Utils.RedirectUrl.GeneralSetupUrl:
                    return RedirectToAction("GeneralSetup", new { id = merchantTerminal.Id });
                default:
                    return RedirectToAction("MerchantSetup", new { selectedItem = merchantTerminal.Id, templateId = model.TemplateId });
            }
        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult MerchantDetails(int id = 0)
        {
            if (id <= 0)
            {
                return RedirectToAction("Index");
            }

            var merchant = _merchantTerminalService.GetById(id);

            var model = merchant == null ? new MerchantDetailsModel() : new MerchantDetailsModel(merchant);


            model.RedirectBackPage = Url.Action("SettingMerchant", "TerminalUpdateTemplate", new { id, templateId = merchant?.TemplateId });

            return View(model);
        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult PaymentsAccepted(int id = 0)
        {
            if (id <= 0)
            {
                return RedirectToAction("Index");
            }
            var merchant = _merchantTerminalService.GetById(id);
            if (merchant == null)
            {
                return RedirectToAction("Index");
            }

            var model = new BaseModel();

            model.Id = id;
            model.ScreenKey = Constants.ScreenKeyTerminalSetupTemplatePaymentsAccepted;

            model.RedirectBackPage = Url.Action("SettingMerchant", "TerminalUpdateTemplate", new { id, templateId = merchant?.TemplateId });
            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult MerchantDetails(MerchantDetailsModel model)
        {

            var merchantTerminal = _merchantTerminalService.GetById(model.Id);

            if (merchantTerminal != null)
            {
                merchantTerminal = model.ToEntity(merchantTerminal);

                _merchantTerminalService.Update(merchantTerminal);
            }
            else
            {
                return RedirectToAction("Index");
            }

            return RedirectToAction("SettingMerchant", new { id = merchantTerminal.Id, templateId = merchantTerminal.TemplateId });
        }
        #endregion

        #region Advance Setup
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult AdvanceSetup(int id = 0)
        {
            if (id <= 0)
            {
                return RedirectToAction("Index");
            }
            var merchant = _merchantTerminalService.GetById(id);
            if (merchant == null)
            {
                return RedirectToAction("Index");
            }

            var model = new BaseModel();

            model.Id = id;
            model.ScreenKey = Constants.ScreenKeyTerminalSetupTemplateAdvanceSetup;

            model.RedirectBackPage = Url.Action("SettingMerchant", "TerminalUpdateTemplate", new { id, templateId = merchant?.TemplateId });
            return View(model);
        }

        #region Calculate Pay
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult CalculatePay(int merchantId = 0)
        {
            if (merchantId <= 0)
            {
                return RedirectToAction("Index");
            }

            var calculatePay = _calculatePayService.GetByMerchanTerminalId(merchantId);

            var model = calculatePay == null ? new CalculatePayModel() : new CalculatePayModel(calculatePay);

            model.MerchantTerminalId = merchantId;

            model.RedirectBackPage = Url.Action("AdvanceSetup", "TerminalUpdateTemplate", new { id = merchantId });

            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult CalculatePay(CalculatePayModel model)
        {
            var calculatePay = _calculatePayService.GetByMerchanTerminalId(model.MerchantTerminalId);
            if (calculatePay != null)
            {
                _calculatePayService.Update(model.ToEntity(calculatePay));
            }
            else
            {
                _calculatePayService.Insert(model.ToEntity(new CalculatePay()));
            }

            switch (model.RedirectUrl)
            {
                case Utils.RedirectUrl.CalculatePayTax1Url:
                    return RedirectToAction("CalculatePayTax1", new { id = model.MerchantTerminalId });
                case Utils.RedirectUrl.CalculatePayTax2Url:
                    return RedirectToAction("CalculatePayTax2", new { id = model.MerchantTerminalId });
                case Utils.RedirectUrl.CalculatePayTax3Url:
                    return RedirectToAction("CalculatePayTax3", new { id = model.MerchantTerminalId });
                case Utils.RedirectUrl.CalculatePayTax4Url:
                    return RedirectToAction("CalculatePayTax4", new { id = model.MerchantTerminalId });
                case Utils.RedirectUrl.CalculatePayServiceFeeUrl:
                    return RedirectToAction("CalculatePayServiceFee", new { id = model.MerchantTerminalId });
                case Utils.RedirectUrl.CalculatePayForcedTipUrl:
                    return RedirectToAction("CalculatePayForcedTip", new { id = model.MerchantTerminalId });
                case Utils.RedirectUrl.CalculatePayDiscountUrl:
                    return RedirectToAction("CalculatePayDiscount", new { id = model.MerchantTerminalId });
                default:
                    return RedirectToAction("AdvanceSetup", new { id = model.MerchantTerminalId });
            }

        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult CalculatePayDiscount(int id = 0)
        {
            if (id <= 0)
            {
                return RedirectToAction("Index");
            }
            var cp = _calculatePayService.GetByMerchanTerminalId(id);

            var model = cp == null ? new CalculatePayDiscountModel() : new CalculatePayDiscountModel(cp);

            model.MerchantTerminalId = id;

            model.RedirectBackPage = Url.Action("CalculatePay", "TerminalUpdateTemplate", new { merchantId = id });

            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult CalculatePayDiscount(CalculatePayDiscountModel model)
        {
            var cp = _calculatePayService.GetByMerchanTerminalId(model.MerchantTerminalId);
            if (cp != null)
            {
                _calculatePayService.Update(model.ToEntity(cp));
            }
            else
            {
                return RedirectToAction("Index");
            }

            return RedirectToAction("CalculatePay", new { merchantId = model.MerchantTerminalId });

        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult CalculatePayServiceFee(int id = 0)
        {
            if (id <= 0)
            {
                return RedirectToAction("Index");
            }
            var cp = _calculatePayService.GetByMerchanTerminalId(id);

            var model = cp == null ? new CalculatePayServiceFeeModel() : new CalculatePayServiceFeeModel(cp);

            model.MerchantTerminalId = id;

            model.RedirectBackPage = Url.Action("CalculatePay", "TerminalUpdateTemplate", new { merchantId = id });

            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult CalculatePayServiceFee(CalculatePayServiceFeeModel model)
        {
            var cp = _calculatePayService.GetByMerchanTerminalId(model.MerchantTerminalId);
            if (cp != null)
            {
                _calculatePayService.Update(model.ToEntity(cp));
            }
            else
            {
                return RedirectToAction("Index");
            }

            return RedirectToAction("CalculatePay", new { merchantId = model.MerchantTerminalId });

        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult CalculatePayForcedTip(int id = 0)
        {
            if (id <= 0)
            {
                return RedirectToAction("Index");
            }
            var cp = _calculatePayService.GetByMerchanTerminalId(id);

            var model = cp == null ? new CalculatePayForcedTipModel() : new CalculatePayForcedTipModel(cp);

            model.MerchantTerminalId = id;
            model.RedirectBackPage = Url.Action("CalculatePay", "TerminalUpdateTemplate", new { merchantId = id });

            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult CalculatePayForcedTip(CalculatePayForcedTipModel model)
        {
            var cp = _calculatePayService.GetByMerchanTerminalId(model.MerchantTerminalId);
            if (cp != null)
            {
                _calculatePayService.Update(model.ToEntity(cp));
            }
            else
            {
                return RedirectToAction("Index");
            }

            return RedirectToAction("CalculatePay", new { merchantId = model.MerchantTerminalId });

        }


        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult CalculatePayTax1(int id = 0)
        {
            if (id <= 0)
            {
                return RedirectToAction("Index");
            }
            var cp = _calculatePayService.GetByMerchanTerminalId(id);

            var model = cp == null ? new CalculatePayTax1Model() : new CalculatePayTax1Model(cp);

            model.MerchantTerminalId = id;

            model.RedirectBackPage = Url.Action("CalculatePay", "TerminalUpdateTemplate", new { merchantId = id });

            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult CalculatePayTax1(CalculatePayTax1Model model)
        {
            var cp = _calculatePayService.GetByMerchanTerminalId(model.MerchantTerminalId);
            if (cp != null)
            {
                _calculatePayService.Update(model.ToEntity(cp));
            }
            else
            {
                return RedirectToAction("Index");
            }

            return RedirectToAction("CalculatePay", new { merchantId = model.MerchantTerminalId });

        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult CalculatePayTax2(int id = 0)
        {
            if (id <= 0)
            {
                return RedirectToAction("Index");
            }
            var cp = _calculatePayService.GetByMerchanTerminalId(id);

            var model = cp == null ? new CalculatePayTax2Model() : new CalculatePayTax2Model(cp);

            model.MerchantTerminalId = id;

            model.RedirectBackPage = Url.Action("CalculatePay", "TerminalUpdateTemplate", new { merchantId = id });

            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult CalculatePayTax2(CalculatePayTax2Model model)
        {
            var cp = _calculatePayService.GetByMerchanTerminalId(model.MerchantTerminalId);
            if (cp != null)
            {
                _calculatePayService.Update(model.ToEntity(cp));
            }
            else
            {
                return RedirectToAction("Index");
            }

            return RedirectToAction("CalculatePay", new { merchantId = model.MerchantTerminalId });

        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult CalculatePayTax3(int id = 0)
        {
            if (id <= 0)
            {
                return RedirectToAction("Index");
            }
            var cp = _calculatePayService.GetByMerchanTerminalId(id);

            var model = cp == null ? new CalculatePayTax3Model() : new CalculatePayTax3Model(cp);

            model.MerchantTerminalId = id;

            model.RedirectBackPage = Url.Action("CalculatePay", "TerminalUpdateTemplate", new { merchantId = id });

            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult CalculatePayTax3(CalculatePayTax3Model model)
        {
            var cp = _calculatePayService.GetByMerchanTerminalId(model.MerchantTerminalId);
            if (cp != null)
            {
                _calculatePayService.Update(model.ToEntity(cp));
            }
            else
            {
                return RedirectToAction("Index");
            }

            return RedirectToAction("CalculatePay", new { merchantId = model.MerchantTerminalId });

        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult CalculatePayTax4(int id = 0)
        {
            if (id <= 0)
            {
                return RedirectToAction("Index");
            }
            var cp = _calculatePayService.GetByMerchanTerminalId(id);

            var model = cp == null ? new CalculatePayTax4Model() : new CalculatePayTax4Model(cp);

            model.MerchantTerminalId = id;

            model.RedirectBackPage = Url.Action("CalculatePay", "TerminalUpdateTemplate", new { merchantId = id });

            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult CalculatePayTax4(CalculatePayTax4Model model)
        {
            var cp = _calculatePayService.GetByMerchanTerminalId(model.MerchantTerminalId);
            if (cp != null)
            {
                _calculatePayService.Update(model.ToEntity(cp));
            }
            else
            {
                return RedirectToAction("Index");
            }

            return RedirectToAction("CalculatePay", new { merchantId = model.MerchantTerminalId });

        }

        #endregion

        #region Service Setup 
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult ServiceSetup(int selectedItem = 0, int merchantId = 0)
        {
            var model = new ServiceSetupModel()
            {
                SelectedItem = selectedItem,
                MerchantId = merchantId,
            };
            return View(model);
        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult GetServiceSetupItems(int merchantId = 0)
        {

            var items = _serviceItemService.GetByMerchanTerminalId(merchantId).ToListItemModel();

            if (items == null || items.Count <= 0 || items.Count < Constants.LoadItem)
            {
                ViewBag.Index = -1;
            }
            else
            {
                ViewBag.Index = 1;
            }
            var model = new ViewItemsModel()
            {
                Items = items,
            };
            return PartialView(PartialViewConstants.ListTerminalMultiItemSortAndMenu, model);
        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ActiveAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult ServiceDetails(int id = 0, int merchantId = 0)
        {
            if (merchantId <= 0)
            {
                return RedirectToAction("Index");
            }
            var model = new ServiceSetupModel();
            if (!id.Equals(0))
            {
                var serviceItem = _serviceItemService.GetById(id);
                if (serviceItem != null)
                    model = new ServiceSetupModel(serviceItem);
            }
            model.MerchantId = merchantId;
            model.RedirectBackPage = Url.Action("ServiceSetup", "TerminalUpdateTemplate", new { merchantId = model.MerchantId });

            return View(model);
        }


        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalCharity, Action = Secure.EditAction)]
        public async Task<ActionResult> ServiceDetails(ServiceSetupModel model, [FromForm] IFormFile serviceIconFile)
        {
            var serviceItem = _serviceItemService.GetById(model.Id) ?? new MMS.Core.Entities.Ratings.ServiceItem();
            var logoImageUrl = "";
            if (serviceIconFile != null)
            {
                logoImageUrl = await WebUtils.PathCharityFolder(model.Icon, serviceIconFile);
            }
            serviceItem = model.ToEntity(serviceItem);
            if (serviceIconFile != null)
            {
                serviceItem.Icon = logoImageUrl;
            }
            if (model.Id.Equals(0))
            {
                _serviceItemService.Insert(serviceItem);
            }
            else
            {
                _serviceItemService.Update(serviceItem);
            }
            return RedirectToAction("ServiceSetup", new { selectedItem = serviceItem.Id, merchantId = model.MerchantId });
        }

        #endregion

        #region Survey Setup
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult SurveySetup(int selectedItem = 0, int merchantId = 0)
        {
            var model = new SurveySetupModel()
            {
                SelectedItem = selectedItem,
                MerchantId = merchantId,
            };
            return View(model);
        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult GetSurveySetupItems(int merchantId = 0)
        {

            var items = _surveyItemService.GetByMerchanTerminalId(merchantId).ToListItemModel();

            if (items == null || items.Count <= 0 || items.Count < Constants.LoadItem)
            {
                ViewBag.Index = -1;
            }
            else
            {
                ViewBag.Index = 1;
            }
            var model = new ViewItemsModel()
            {
                Items = items,
            };
            return PartialView(PartialViewConstants.ListTerminalMultiItemSortAndMenu, model);
        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ActiveAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult SurveyDetails(int id = 0, int merchantId = 0)
        {
            if (merchantId <= 0)
            {
                return RedirectToAction("Index");
            }
            var model = new SurveySetupModel();
            if (!id.Equals(0))
            {
                var surveyItem = _surveyItemService.GetById(id);
                if (surveyItem != null)
                    model = new SurveySetupModel(surveyItem);
            }
            model.MerchantId = merchantId;
            model.RedirectBackPage = Url.Action("SurveySetup", "TerminalUpdateTemplate", new { merchantId = model.MerchantId });

            return View(model);
        }


        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalCharity, Action = Secure.EditAction)]
        public async Task<ActionResult> SurveyDetails(SurveySetupModel model, [FromForm] IFormFile surveyIconFile)
        {
            var surveyItem = _surveyItemService.GetById(model.Id) ?? new MMS.Core.Entities.Ratings.SurveyItem();

            var logoImageUrl = "";
            if (surveyIconFile != null)
            {
                logoImageUrl = await WebUtils.PathCharityFolder(model.Icon, surveyIconFile);
            }

            surveyItem = model.ToEntity(surveyItem);
            if (surveyIconFile != null)
            {
                surveyItem.Icon = logoImageUrl;
            }
            if (model.Id.Equals(0))
            {
                _surveyItemService.Insert(surveyItem);
            }
            else
            {
                _surveyItemService.Update(surveyItem);
            }
            return RedirectToAction("SurveySetup", new { selectedItem = surveyItem.Id, merchantId = model.MerchantId });
        }

        #endregion

        #endregion

        #region GeneralSetup
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult GeneralSetup(int id = 0)
        {
            if (id <= 0)
            {
                return RedirectToAction("Index");
            }
            var merchant = _merchantTerminalService.GetById(id);
            if (merchant == null)
            {
                return RedirectToAction("Index");
            }

            var merchantGeneralSetup = _merchantGeneralSetupService.GetByMerchantTerminalId(id);

            var model = new MerchantGeneralSetupModel();
            if (merchantGeneralSetup != null)
            {
                model = model.ToGeneralSetupModel(merchantGeneralSetup);
            }

            model.MerchantTerminalId = id;

            model.RedirectBackPage = Url.Action("SettingMerchant", "TerminalUpdateTemplate", new { id, templateId = merchant?.TemplateId });
            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult GeneralSetup(MerchantGeneralSetupModel model)
        {
            var merchant = _merchantTerminalService.GetById(model.MerchantTerminalId.GetValueOrDefault());

            if (merchant == null)
            {
                return RedirectToAction("Index");
            }

            var merchantGeneralSetup = _merchantGeneralSetupService.GetByMerchantTerminalId(model.MerchantTerminalId.GetValueOrDefault());
            if (merchantGeneralSetup != null)
            {
                _merchantGeneralSetupService.Update(model.ToGeneralSetupEntity(merchantGeneralSetup));
            }
            else
            {
                _merchantGeneralSetupService.Insert(model.ToGeneralSetupEntity(new MerchantGeneralSetup()));
            }


            switch (model.RedirectUrl)
            {
                case Utils.RedirectUrl.PrintingSetupUrl:
                    return RedirectToAction("PrintingSetup", new { id = model.MerchantTerminalId });
                case Utils.RedirectUrl.TimeOutsUrl:
                    return RedirectToAction("TimeOuts", new { id = model.MerchantTerminalId });
                case Utils.RedirectUrl.PasscodeAccessUrl:
                    return RedirectToAction("PasscodeAccess", new { id = model.MerchantTerminalId });
                default:
                    return RedirectToAction("SettingMerchant", new { id = model.MerchantTerminalId, templateId = merchant?.TemplateId });
            }

        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult PrintingSetup(int id = 0)
        {
            if (id <= 0)
            {
                return RedirectToAction("Index");
            }
            var merchant = _merchantTerminalService.GetById(id);
            if (merchant == null)
            {
                return RedirectToAction("Index");
            }

            var merchantGeneralSetup = _merchantGeneralSetupService.GetByMerchantTerminalId(id);

            var model = new MerchantGeneralSetupModel();
            if (merchantGeneralSetup != null)
            {
                model = model.ToPrintingSetupModel(merchantGeneralSetup);
            }

            model.MerchantTerminalId = id;

            model.RedirectBackPage = Url.Action("GeneralSetup", "TerminalUpdateTemplate", new { id });
            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult PrintingSetup(MerchantGeneralSetupModel model)
        {
            var merchant = _merchantTerminalService.GetById(model.MerchantTerminalId.GetValueOrDefault());

            if (merchant == null)
            {
                return RedirectToAction("Index");
            }

            var merchantGeneralSetup = _merchantGeneralSetupService.GetByMerchantTerminalId(model.MerchantTerminalId.GetValueOrDefault());

            merchantGeneralSetup = model.ToPrintingSetupEntity(merchantGeneralSetup);

            if (merchantGeneralSetup == null)
            {
                return RedirectToAction("Error");
            }

            _merchantGeneralSetupService.Update(merchantGeneralSetup);


            switch (model.RedirectUrl)
            {
                case Utils.RedirectUrl.PrinterFormatsUrl:
                    return RedirectToAction("PrinterFormats", new { id = model.MerchantTerminalId });
                default:
                    return RedirectToAction("GeneralSetup", new { id = model.MerchantTerminalId });
            }

        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult UserLogon(int id = 0)
        {
            if (id <= 0)
            {
                return RedirectToAction("Index");
            }
            var merchant = _merchantTerminalService.GetById(id);
            if (merchant == null)
            {
                return RedirectToAction("Index");
            }

            var merchantUserLogon = _merchantUserLogonService.GetByMerchanTerminalId(id);

            var model = new MerchantUserLogonModel();
            if (merchantUserLogon != null)
            {
                model = new MerchantUserLogonModel(merchantUserLogon);
            }

            model.MerchantId = id;

            model.RedirectBackPage = Url.Action("GeneralSetup", "TerminalUpdateTemplate", new { id });
            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult UserLogon(MerchantUserLogonModel model)
        {
            var merchant = _merchantTerminalService.GetById(model.MerchantId);

            if (merchant == null)
            {
                return RedirectToAction("Index");
            }

            var merchantUserLogon = _merchantUserLogonService.GetByMerchanTerminalId(model.MerchantId);

            merchantUserLogon = merchantUserLogon == null ? model.ToEntity(new MMS.Core.Entities.Merchants.MerchantUserLogon()) : model.ToEntity(merchantUserLogon);

            if (model.Id == 0)
            {
                _merchantUserLogonService.Insert(merchantUserLogon);
            }
            else
            {
                _merchantUserLogonService.Update(merchantUserLogon);
            }

            return RedirectToAction("GeneralSetup", new { id = model.MerchantId });

        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult TimeOuts(int id = 0)
        {
            if (id <= 0)
            {
                return RedirectToAction("Index");
            }
            var merchant = _merchantTerminalService.GetById(id);
            if (merchant == null)
            {
                return RedirectToAction("Index");
            }

            var merchantGeneralSetup = _merchantGeneralSetupService.GetByMerchantTerminalId(id);

            var model = new MerchantGeneralSetupModel();
            if (merchantGeneralSetup != null)
            {
                model = model.ToTimeOutsModel(merchantGeneralSetup);
            }

            model.MerchantTerminalId = id;

            model.RedirectBackPage = Url.Action("GeneralSetup", "TerminalUpdateTemplate", new { id });
            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult TimeOuts(MerchantGeneralSetupModel model)
        {
            var merchant = _merchantTerminalService.GetById(model.MerchantTerminalId.GetValueOrDefault());

            if (merchant == null)
            {
                return RedirectToAction("Index");
            }

            var merchantGeneralSetup = _merchantGeneralSetupService.GetByMerchantTerminalId(model.MerchantTerminalId.GetValueOrDefault());

            merchantGeneralSetup = model.ToTimeOutsEntity(merchantGeneralSetup);

            if (merchantGeneralSetup == null)
            {
                return RedirectToAction("Error");
            }

            _merchantGeneralSetupService.Update(merchantGeneralSetup);

            return RedirectToAction("GeneralSetup", new { id = model.MerchantTerminalId });

        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult PasscodeAccess(int id = 0)
        {
            if (id <= 0)
            {
                return RedirectToAction("Index");
            }
            var merchant = _merchantTerminalService.GetById(id);
            if (merchant == null)
            {
                return RedirectToAction("Index");
            }

            var merchantGeneralSetup = _merchantGeneralSetupService.GetByMerchantTerminalId(id);

            var model = new MerchantGeneralSetupModel();
            if (merchantGeneralSetup != null)
            {
                model = model.ToPasscodeAccessModel(merchantGeneralSetup);
            }

            model.MerchantTerminalId = id;

            model.RedirectBackPage = Url.Action("GeneralSetup", "TerminalUpdateTemplate", new { id });
            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult PasscodeAccess(MerchantGeneralSetupModel model)
        {
            var merchant = _merchantTerminalService.GetById(model.MerchantTerminalId.GetValueOrDefault());

            if (merchant == null)
            {
                return RedirectToAction("Index");
            }

            var merchantGeneralSetup = _merchantGeneralSetupService.GetByMerchantTerminalId(model.MerchantTerminalId.GetValueOrDefault());

            merchantGeneralSetup = model.ToPasscodeAccessEntity(merchantGeneralSetup);

            if (merchantGeneralSetup == null)
            {
                return RedirectToAction("Error");
            }

            _merchantGeneralSetupService.Update(merchantGeneralSetup);

            return RedirectToAction("GeneralSetup", new { id = model.MerchantTerminalId });

        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult PrinterFormats(int id = 0)
        {
            if (id <= 0)
            {
                return RedirectToAction("Index");
            }
            var merchant = _merchantTerminalService.GetById(id);
            if (merchant == null)
            {
                return RedirectToAction("Index");
            }

            var merchantGeneralSetup = _merchantGeneralSetupService.GetByMerchantTerminalId(id);

            var model = new MerchantGeneralSetupModel();
            if (merchantGeneralSetup != null)
            {
                model = model.ToPrinterFormatsModel(merchantGeneralSetup);
            }

            model.MerchantTerminalId = id;

            model.RedirectBackPage = Url.Action("PrintingSetup", "TerminalUpdateTemplate", new { id });
            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public async Task<ActionResult> PrinterFormats(MerchantGeneralSetupModel model, [FromForm] IFormFile logoImageFile)
        {
            var merchant = _merchantTerminalService.GetById(model.MerchantTerminalId.GetValueOrDefault());

            var logoImageUrl = "";
            if (logoImageFile != null)
            {
                logoImageUrl = await WebUtils.PathCharityFolder(model.lszLogo, logoImageFile);
            }

            if (merchant == null)
            {
                return RedirectToAction("Index");
            }

            var merchantGeneralSetup = _merchantGeneralSetupService.GetByMerchantTerminalId(model.MerchantTerminalId.GetValueOrDefault());

            merchantGeneralSetup = model.ToPrinterFormatsEntity(merchantGeneralSetup);

            if (merchantGeneralSetup == null)
            {
                return RedirectToAction("Error");
            }

            if (logoImageFile != null)
                merchantGeneralSetup.lszLogo = logoImageUrl;

            _merchantGeneralSetupService.Update(merchantGeneralSetup);

            return RedirectToAction("PrintingSetup", new { id = model.MerchantTerminalId });

        }
        #endregion

        #region Tips
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult Tips(int id = 0)
        {
            if (id <= 0)
            {
                return RedirectToAction("Index");
            }
            var tips = _tipService.GetByMerchanTerminalId(id);

            var model = tips == null ? new TipsSetupModel() : new TipsSetupModel(tips);

            model.MerchantTerminalId = id;

            model.RedirectBackPage = Url.Action("PaymentsAccepted", "TerminalUpdateTemplate", new { id });

            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult Tips(TipsSetupModel model)
        {
            var tips = _tipService.GetByMerchanTerminalId(model.MerchantTerminalId);
            if (tips != null)
            {
                _tipService.Update(model.ToEntity(tips));
            }
            else
            {
                _tipService.Insert(model.ToEntity(new TipsTerminal()));
            }

            switch (model.RedirectUrl)
            {
                case Utils.RedirectUrl.SuggestTiveTipsUrl:
                    return RedirectToAction("SuggestiveTips", new { id = model.MerchantTerminalId });
                case Utils.RedirectUrl.TipExcludesUrl:
                    return RedirectToAction("TipExcludes", new { id = model.MerchantTerminalId });
                default:
                    return RedirectToAction("PaymentsAccepted", new { id = model.MerchantTerminalId });
            }

        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult TipExcludes(int id = 0)
        {
            if (id <= 0)
            {
                return RedirectToAction("Index");
            }
            var tips = _tipService.GetByMerchanTerminalId(id);

            var model = tips == null ? new TipExcludesModel() : new TipExcludesModel(tips);

            model.MerchantTerminalId = id;

            model.RedirectBackPage = Url.Action("Tips", "TerminalUpdateTemplate", new { id });

            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult TipExcludes(TipExcludesModel model)
        {
            var tips = _tipService.GetByMerchanTerminalId(model.MerchantTerminalId);
            if (tips != null)
            {
                _tipService.Update(model.ToEntity(tips));
            }
            else
            {
                return RedirectToAction("Index");
            }

            return RedirectToAction("Tips", new { id = model.MerchantTerminalId });

        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult SuggestiveTips(int id = 0)
        {
            if (id <= 0)
            {
                return RedirectToAction("Index");
            }

            var tips = _tipService.GetByMerchanTerminalId(id);

            var model = tips == null ? new SuggestiveTipsModel() : new SuggestiveTipsModel(tips);

            model.MerchantTerminalId = id;

            model.RedirectBackPage = Url.Action("Tips", "TerminalUpdateTemplate", new { id });

            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult SuggestiveTips(SuggestiveTipsModel model)
        {
            var tips = _tipService.GetByMerchanTerminalId(model.MerchantTerminalId);

            if (tips != null)
            {
                _tipService.Update(model.ToEntity(tips));
            }
            else
            {
                return RedirectToAction("Index");
            }

            return RedirectToAction("Tips", new { id = model.MerchantTerminalId });

        }
        #endregion

        #region EFT Payments
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult EFTPayments(int selectedItem = 0, int merchantId = 0)
        {
            var model = new EFTPaymentsModel()
            {
                SelectedItem = selectedItem,
                MerchantTerminalId = merchantId,
            };
            return View(model);
        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult GetEFTPaymentsItems(int item = 0, string searchKey = "", int index = 0, int merchantId = 0)
        {
            var skip = 0;
            if (index != 0)
            {
                skip = index * Constants.LoadItem;
            }
            var items = _eftPaymentsService.GetEFTPaymentsByMerchantId(merchantId, searchKey ?? "").ToListViewItemModel(); //TODO Replace

            if (items == null || items.Count <= 0 || items.Count < Constants.LoadItem)
            {
                ViewBag.Index = -1;
            }
            else
            {
                ViewBag.Index = index + 1;
            }
            var model = new ViewItemsModel()
            {
                Items = items,
                SelectedId = item
            };
            return PartialView(PartialViewConstants.ListMultipleItemStandard, model);
        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ActiveAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public JsonResult EFTPaymentActivate(int id = 0)
        {
            try
            {
                var eftPayments = _eftPaymentsService.GetById(id);
                if (eftPayments.IsActive)
                {
                    return Json(false);
                }
                eftPayments.IsActive = true;
                _eftPaymentsService.Update(eftPayments);
                LogAction(Secure.StrFunctionTerminalCharity, Constants.ActionActive);
                return Json(true);
            }
            catch (System.Exception ex)
            {
                LogAction(Secure.StrFunctionTerminalCharity, Constants.ActionActive, true);
                return Json(false);
            }
        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ActiveAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public JsonResult EFTPaymentDeactivate(int id = 0)
        {
            try
            {
                var eftPayments = _eftPaymentsService.GetById(id);
                if (!eftPayments.IsActive)
                {
                    return Json(false);
                }
                eftPayments.IsActive = false;
                _eftPaymentsService.Update(eftPayments);
                LogAction(Secure.StrFunctionTerminalCharity, Constants.ActionActive);
                return Json(true);
            }
            catch (System.Exception ex)
            {
                LogAction(Secure.StrFunctionTerminalCharity, Constants.ActionActive, true);
                return Json(false);
            }
        }

        [HttpPost]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.EditAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public JsonResult EFTPaymentRemove(int id = 0)
        {
            var item = _eftPaymentsService.GetById(id);
            if (item.IsStatus == MMS.Core.CoreUTI.Constants.DELETE_RECORD || item == null)
            {
                return Json(false);
            }
            _eftPaymentsService.Delete(item, false);
            return Json(true);
        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUpgradeTemplate, Action = Secure.ActiveAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public ActionResult EFTPaymentSetup(int id = 0, int merchantId = 0)
        {
            if (merchantId <= 0)
            {
                return RedirectToAction("Index");
            }
            var model = new EFTPaymentsModel();
            if (!id.Equals(0))
            {
                var eftPayment = _eftPaymentsService.GetById(id);
                if (eftPayment != null)
                    model = new EFTPaymentsModel(eftPayment);
            }
            model.MerchantTerminalId = merchantId;
            return View(model);
        }


        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorityFilterFactory(Function = Secure.FunctionTerminalCharity, Action = Secure.EditAction)]
        public ActionResult EFTPaymentSetup(EFTPaymentsModel model)
        {
            var eFTPayments = _eftPaymentsService.GetById(model.Id) ?? new EFTPayments();

            eFTPayments = model.ToEntity(eFTPayments);

            if (model.Id.Equals(0))
            {
                _eftPaymentsService.Insert(eFTPayments);
            }
            else
            {
                _eftPaymentsService.Update(eFTPayments);
            }
            return RedirectToAction("EFTPayments", new { selectedItem = eFTPayments.Id, merchantId = model.MerchantTerminalId });
        }

        #endregion

        #region Merchant User
        public ActionResult MerchantUsers(int id, int templateId)
        {
            var model = new MerchantUsersModel
            {
                TemplateId = templateId,
                MerchantId = id,
            };
            return View(model);
        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUsers, Action = Secure.ViewAction)]
        public ActionResult GetMerchantUsers(int item = 0, string searchKey = "", int index = 0, int id = 0)
        {
            var skip = 0;
            if (index != 0)
            {
                skip = index * Constants.LoadItem;
            }

            IList<ViewItemModel> items;

            items = _merchantUserService.
                GetUserTerminalByMerchantId(MMSConstants.UserMaterProperties, GetLanguage(), typeof(UserMaster).Name,
                searchKey, skip, Constants.LoadItem, id);

            if (items == null || items.Count <= 0 || items.Count < Constants.LoadItem)
            {
                ViewBag.Index = -1;
            }
            else
            {
                ViewBag.Index = index + 1;
            }
            var model = new ViewItemsModel()
            {
                Items = items,
            };
            return PartialView(PartialViewConstants.ListTerminalMultipleItemStandard, model);
        }
        #endregion

        #endregion

        //#region Setting Termainal User
        //public ActionResult Users(int id)
        //{
        //    var model = new UsersTerminalModel
        //    {
        //        TerminalId = id,
        //      //  Users = users,
        //        Style = WebUtils.GetDefaultString(_styleService.GetAllDefaultStyle(),
        //            _styleService.GetByScreenKey(Constants.ScreenKeySetupMenuUsers, GetLanguage())
        //            , _styleService.GetAllDefaultStyle(GetLanguage()))
        //    };
        //    return View(model);
        //}

        //[AuthorityFilterFactory(Function = Secure.FunctionTerminalUsers, Action = Secure.ViewAction)]
        //public ActionResult GetUserTerminals(int item = 0, string searchKey = "", int index = 0, int id = 0)
        //{
        //    var skip = 0;
        //    if (index != 0)
        //    {
        //        skip = index * Constants.LoadItem;
        //    }
        #region Setting Termainal User
        public ActionResult Users(int id)
        {
            var model = new UsersTerminalModel
            {
                TemplateId = id,
                //  Users = users,
            };
            return View(model);
        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUsers, Action = Secure.ViewAction)]
        public ActionResult GetUserTerminals(int item = 0, string searchKey = "", int index = 0, int id = 0)
        {
            var skip = 0;
            if (index != 0)
            {
                skip = index * Constants.LoadItem;
            }

            IList<ViewItemModel> items;

            items = _userTerminalService.
                GetUserTerminalByTemplateId(searchKey, skip, Constants.LoadItem, id);

            if (items == null || items.Count <= 0 || items.Count < Constants.LoadItem)
            {
                ViewBag.Index = -1;
            }
            else
            {
                ViewBag.Index = index + 1;
            }
            var model = new ViewItemsModel()
            {
                Items = items,
            };
            return PartialView(PartialViewConstants.ListTerminalMultipleItemStandard, model);
        }

        public JsonResult ActivateTerminalUser(int id)
        {
            try
            {
                if (id <= 0)
                {
                    return Json(false);
                }
                var tmp = _userTerminalService.GetById(id);
                if (tmp == null)
                {
                    return Json(false);
                }
                if (!tmp.IsActive)
                {
                    tmp.IsActive = true;
                    _userTerminalService.Update(tmp);
                }
                LogAction(Secure.StrFunctionTerminalUsers, Constants.ActionActive);
                return Json(true);
            }
            catch (Exception)
            {
                LogAction(Secure.StrFunctionTerminalUsers, Constants.ActionActive, true);
                return Json(false);
            }
        }

        public JsonResult DeactivateTerminalUser(int id)
        {
            try
            {
                if (id <= 0)
                {
                    return Json(false);
                }
                var tmp = _userTerminalService.GetById(id);
                if (tmp == null)
                {
                    return Json(false);
                }

                if (tmp.IsActive)
                {
                    tmp.IsActive = false;
                    _userTerminalService.Update(tmp);
                }
                LogAction(Secure.StrFunctionTerminalUsers, Constants.ActionActive);
                return Json(true);
            }
            catch (Exception)
            {
                LogAction(Secure.StrFunctionTerminalUsers, Constants.ActionActive, true);
                return Json(false);
            }
        }

        public ActionResult SelectUser(int id)
        {
            var model = new UsersTerminalModel
            {
                TemplateId = id,
            };
            return View(model);
        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUsers, Action = Secure.ViewAction)]
        public ActionResult GetUsersItem(int templateId, string searchKey = "", int index = 0)
        {
            IList<int> userIds = _userTerminalService.Select(x => x.UserMasterId, x => x.TerminalTempalteId == templateId
                                && x.IsStatus != MMSConstants.DELETE_RECORD
                                && x.IsStatus != MMSConstants.PRE_DELETE_RECORD).ToList() as IList<int>;

            var skip = 0;
            if (index != 0)
            {
                skip = index * Constants.LoadItem;
            }
            var items = _userMasterService.GetViewListByLang(skip, Constants.LoadItem, searchKey)
                .Select(x => new ViewItemModel()
                {
                    Id = x.Id,
                    Label = x.Label,
                    IsActive = userIds.Contains(x.Id)
                }).ToList();
            if (items == null || items.Count <= 0 || items.Count < Constants.LoadItem)
            {
                ViewBag.Index = -1;
            }
            else
            {
                ViewBag.Index = index + 1;
            }

            var model = new ViewItemsModel()
            {
                Items = items,

            };
            return PartialView(model);
        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUsers, Action = Secure.ViewAction)]
        public ActionResult GetUserSelectedItems(int templateId, int index = 0)
        {
            var skip = 0;
            if (index != 0)
            {
                skip = index * Constants.LoadItem;
            }

            IList<ViewItemModel> items;

            items = _userTerminalService.GetUserMasterByTemplateId("", skip, Constants.LoadItem, templateId);

            if (items == null || items.Count <= 0 || items.Count < Constants.LoadItem)
            {
                ViewBag.Index = -1;
            }
            else
            {
                ViewBag.Index = index + 1;
            }
            var model = new ViewItemsModel()
            {
                Items = items,
            };
            return PartialView(model);
        }

        [HttpPost]
        public ActionResult AddUser(int id, IList<int> userIds)
        {
            var userTerminals = _userTerminalService.GetUserByTemplateId(id);
            foreach (var user in userTerminals)
            {
                if (userIds != null)
                {
                    var checkDelete = !userIds.Any(userId => userId.Equals(user.User.Id));
                    if (!checkDelete) continue;

                    user.IsStatus = MMSConstants.PRE_DELETE_RECORD;
                    _userTerminalService.Update(user);
                }
                else
                {
                    user.IsStatus = MMSConstants.PRE_DELETE_RECORD;
                    _userTerminalService.Update(user);
                }
            }
            if (userIds == null) return RedirectToAction("Users", new { id = id });

            foreach (var tmp in from userId in userIds
                                let checkAddNew = !userTerminals.Any(user => userId.Equals(user.UserMasterId))
                                where checkAddNew
                                select new UserTerminal
                                {
                                    UserMasterId = userId,
                                    TerminalTempalteId = id,
                                    IsStatus = MMSConstants.PRE_INSERT_RECORD,
                                })
            {
                _userTerminalService.Insert(tmp);
            }

            Utils.WebUtils.SetSession<bool>(Constants.UsersSubmited, true);
            return RedirectToAction("Users", "TerminalUpdateTemplate", new { id = id });
        }

        public ActionResult UpdateUserTerminal(int userId = 0, int templateId = 0)
        {
            // var merchants = _terminalService.GetMerchantByTerminal(templateId);
            var userTerminal = _userTerminalService.GetById(userId, p => p.User);
            var usersMaster = userTerminal.User;
            //if (merchantId == 0)
            //{
            //    merchantId = (merchants != null && merchants.Count != 0) ? merchants.FirstOrDefault().Id : 0;
            //}
            // var userMerchant = _terminalService.GetUserMerchantByUserIdAndMerchantId(userId, merchantId);
            var model = new UserTerminalModel(userTerminal)
            {
                TemplateId = templateId,
                //MerchantId = merchantId,
                // Merchants = merchants,
                Id = userId,
                LanguageMasters = _languageOptionTerminalService.GetAll(x => x.MerchantTerminalId == templateId)
            };
            if (usersMaster != null)
            {
                model.TemplateId = templateId;
                model.UserName = usersMaster.FullName;
                model.UserMasterId = usersMaster.Id;

                //model.EmailList = _contactDetailsService.GetContactsByContactType(usersMaster.Id, MMS.Core.CoreUTI.ContactDetailsType.PersonalDetails, MMS.Core.CoreUTI.ContactType.Email)
                //                                        .Select(x => x.DisplayName).ToList();
                //model.CellPhones = _contactDetailsService.GetContactsByContactType(usersMaster.Id, MMS.Core.CoreUTI.ContactDetailsType.PersonalDetails, MMS.Core.CoreUTI.ContactType.Cell)
                //                                        .Select(x => x.DisplayName).ToList();

            }
            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult UpdateUserTerminal(UserTerminalModel model)
        {
            UserTerminal userTerminal = model.Id == 0
                ? new UserTerminal()
                : _userTerminalService.GetById(model.Id);
            userTerminal = model.ToEntity(userTerminal);
            if (model.Id == 0)
            {
                _userTerminalService.Insert(userTerminal);
            }
            else
            {
                _userTerminalService.Update(userTerminal);
            }

            if (model.IsOut)
                return RedirectToAction("UpdateUserTerminal", new { userId = model.Id, templateId = model.TemplateId });
            else
                return RedirectToAction("Users", "TerminalUpdateTemplate", new { Id = model.TemplateId });
        }

        public ActionResult UserCellNumber(int userId = 0, int templateId = 0)
        {
            var userTerminal = _userTerminalService.GetById(userId, p => p.User);
            var usersMaster = userTerminal.User;

            if (userTerminal == null)
                return RedirectToAction("Users", "TerminalUpdateTemplate", new { Id = templateId });

            var model = new UserTerminalModel(userTerminal)
            {
                TemplateId = templateId,
                Id = userId,
                LanguageMasters = _languageOptionTerminalService.GetAll(x => x.MerchantTerminalId == templateId)
            };
            if (usersMaster != null)
            {
                model.TemplateId = templateId;
                model.UserName = usersMaster.FullName;
                model.UserMasterId = usersMaster.Id;
            }
            return View(model);
        }
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult UserCellNumber(UserTerminalModel model)
        {
            UserTerminal userTerminal = _userTerminalService.GetById(model.Id);
            if (userTerminal == null)
                return RedirectToAction("Users", "TerminalUpdateTemplate", new { Id = model.TemplateId });

            userTerminal = model.ToCellEntity(userTerminal);

            _userTerminalService.Update(userTerminal);

            return RedirectToAction("UpdateUserTerminal", new { userId = model.Id, templateId = model.TemplateId });

        }
        public ActionResult UserEmail(int userId = 0, int templateId = 0)
        {
            var userTerminal = _userTerminalService.GetById(userId, p => p.User);
            var usersMaster = userTerminal.User;

            if (userTerminal == null)
                return RedirectToAction("Users", "TerminalUpdateTemplate", new { Id = templateId });

            var model = new UserTerminalModel(userTerminal)
            {
                TemplateId = templateId,
                Id = userId,
                LanguageMasters = _languageOptionTerminalService.GetAll(x => x.MerchantTerminalId == templateId)
            };
            if (usersMaster != null)
            {
                model.TemplateId = templateId;
                model.UserName = usersMaster.FullName;
                model.UserMasterId = usersMaster.Id;
            }
            return View(model);
        }
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult UserEmail(UserTerminalModel model)
        {
            UserTerminal userTerminal = _userTerminalService.GetById(model.Id);
            if (userTerminal == null)
                return RedirectToAction("Users", "TerminalUpdateTemplate", new { Id = model.TemplateId });

            userTerminal = model.ToEmailEntity(userTerminal);

            _userTerminalService.Update(userTerminal);

            return RedirectToAction("UpdateUserTerminal", new { userId = model.Id, templateId = model.TemplateId });
        }
        public ActionResult UserM2M(int userId = 0, int templateId = 0)
        {
            var userTerminal = _userTerminalService.GetById(userId, p => p.User);
            var usersMaster = userTerminal.User;

            if (userTerminal == null)
                return RedirectToAction("Users", "TerminalUpdateTemplate", new { Id = templateId });

            var model = new UserTerminalModel(userTerminal)
            {
                TemplateId = templateId,
                Id = userId,
                LanguageMasters = _languageOptionTerminalService.GetAll(x => x.MerchantTerminalId == templateId)
            };
            if (usersMaster != null)
            {
                model.TemplateId = templateId;
                model.UserName = usersMaster.FullName;
                model.UserMasterId = usersMaster.Id;
            }
            return View(model);
        }
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult UserM2M(UserTerminalModel model)
        {
            UserTerminal userTerminal = _userTerminalService.GetById(model.Id);
            if (userTerminal == null)
                return RedirectToAction("Users", "TerminalUpdateTemplate", new { Id = model.TemplateId });

            userTerminal = model.ToM2MEntity(userTerminal);

            _userTerminalService.Update(userTerminal);

            return RedirectToAction("UpdateUserTerminal", new { userId = model.Id, templateId = model.TemplateId });
        }

        [AuthorityFilterFactory(Function = Secure.PersonCompanyPersonAndCompanies, Action = Secure.ViewAction, ParentId = Secure.MyTerminalsIOTDeviceManager)]
        public JsonResult LoadUserMasterDetails(int id)
        {
            var user = _userMasterService.GetById(id);
            return Json(user);
        }

        [AuthorityFilterFactory(Function = Secure.FunctionTerminalUsers, Action = Secure.ViewAction)]
        public JsonResult CheckTerminalBeforeSettingUser(int id)
        {
            // var tmp = _terminalService.GetById(id);
            return Json(false);
        }

        #endregion

        #endregion

        #region Validate

        public JsonResult CheckExistItem(string input, int id)
        {
            //return message if exist and return empty string if not 
            var check = _terminalSetupTemplateService.CheckSetupNameExist(input);
            if (check != null)
            {
                foreach (var each in check)
                {
                    //each get id 
                    var idItem = Int32.Parse(each.Split('.').ElementAt(2));
                    if (id != idItem)
                    {
                        //if not current id , return message 
                        return Json(Utils.WebUtils.GetMessage(GetStringByKey(Constants.TerminalSetupTemplateName, "Template Name"),
                            GetStringByKey(Constants.ErrorMessageKeyFieldIsExist, "{0} is already existed")));
                    }
                }
            }
            return Json("");
        }

        private void Validate(TerminalUpdateItemModel model)
        {
            if (string.IsNullOrWhiteSpace(model.UpdateName))
            {
                ModelState.AddModelError(Constants.TerminalSetupTemplateName,
                    Utils.WebUtils.GetMessage(GetStringByKey(Constants.TerminalSetupTemplateName, "Template Name")
                        , GetStringByKey(Constants.ErrorMessageKeyFieldIsRequired, " {0} is required")));
            }

            //Next download Date
            if (string.IsNullOrWhiteSpace(model.NextDownloadDate))
            {
                ModelState.AddModelError(Constants.TerminalSetupTemplateNextDownloadDate, Utils.WebUtils.GetMessage(
                    GetStringByKey(Constants.TerminalSetupTemplateNextDownloadDate, "Next Download Date"),
                    GetStringByKey(Constants.ErrorMessageKeyFieldIsRequired, " {0} is required")));
            }
            else
            {
                if (!Utils.WebUtils.IsValidDateFormat(model.NextDownloadDate, Constants.FORMAT_MM_DD_YYYY))
                {
                    ModelState.AddModelError(Constants.TerminalSetupTemplateNextDownloadDate, Utils.WebUtils.GetMessage(
                        "'" + model.NextDownloadDate + "'",
                        GetStringByKey(Constants.ErrorMessageKeyDateIsInvalidFormat, "{0} is invalid Date format.")));
                }
            }
            //next download time
            if (string.IsNullOrWhiteSpace(model.NextDownloadTime))
            {
                ModelState.AddModelError(Constants.TerminalSetupTemplateNextDownloadTime, Utils.WebUtils.GetMessage(
                    GetStringByKey(Constants.TerminalSetupTemplateNextDownloadTime, "Next Download Time"),
                    GetStringByKey(Constants.ErrorMessageKeyFieldIsRequired, " {0} is required")));
            }
            else
            {
                if (!Utils.WebUtils.IsValidDateFormat(model.NextDownloadTime, Constants.FORMAT_TIME_HH_MM))
                {
                    ModelState.AddModelError(Constants.TerminalSetupTemplateNextDownloadTime, Utils.WebUtils.GetMessage(
                        "'" + model.NextDownloadTime + "'",
                        GetStringByKey(Constants.ErrorMessageKeyTimeIsInvalidFormat, "{0} is invalid Date format")));
                }
            }

            //Next Upgrade Date
            if (string.IsNullOrWhiteSpace(model.NextUpgradeDate))
            {
                ModelState.AddModelError(Constants.TerminalSetupTemplateNextUpgradeDate, Utils.WebUtils.GetMessage(
                    GetStringByKey(Constants.TerminalSetupTemplateNextUpgradeDate, "Next Upgrade Date"),
                    GetStringByKey(Constants.ErrorMessageKeyFieldIsRequired, "{0} is required")));
            }
            else
            {
                if (!Utils.WebUtils.IsValidDateFormat(model.NextUpgradeDate, Constants.FORMAT_MM_DD_YYYY))
                {
                    ModelState.AddModelError(Constants.TerminalSetupTemplateNextUpgradeDate, Utils.WebUtils.GetMessage(
                        "'" + model.NextUpgradeDate + "'",
                        GetStringByKey(Constants.ErrorMessageKeyDateIsInvalidFormat, "{0} is invalid Date format")));
                }
            }
            //next Upgrade time
            if (string.IsNullOrWhiteSpace(model.NextUpgradeTime))
            {
                ModelState.AddModelError(Constants.TerminalSetupTemplateNextUpgradeTime, Utils.WebUtils.GetMessage(
                    GetStringByKey(Constants.TerminalSetupTemplateNextUpgradeTime, "Next Upgrade Time"),
                    GetStringByKey(Constants.ErrorMessageKeyFieldIsRequired, "{0} is required")));
            }
            else
            {
                if (!Utils.WebUtils.IsValidDateFormat(model.NextUpgradeTime, Constants.FORMAT_TIME_HH_MM))
                {
                    ModelState.AddModelError(Constants.TerminalSetupTemplateNextUpgradeTime, Utils.WebUtils.GetMessage(
                        "'" + model.NextUpgradeTime + "'",
                        GetStringByKey(Constants.ErrorMessageKeyTimeIsInvalidFormat, "{0} is invalid Time format")));
                }
            }
        }

        #endregion

        private void CheckRequiredField<T>(T value)
        {
            if (value == null ||
                (typeof(T) == typeof(string) && string.IsNullOrWhiteSpace(value.ToStr())))
            {
                ModelState.AddModelError(Constants.KeyCommonFieldIsRequired,
                    GetStringByKey(Constants.ErrorMessageKeyCommonFieldIsRequired, "This field is required"));
            }
        }

        #region Base Application Templates
        public ActionResult BaseApplicationTemplates()
        {
            var locationMerchant = new LocationMerchantViewModel
            {
                LocationLevelViewModel = WebUtil.LoadLocationLevel(maxLevel: LocationLevel.DevicesUsed),
                CompanyGroupViewModel = new CompanyGroupPartialViewModel()
            };

            locationMerchant.LocationLevelViewModel.ScreenType = ScreenType.MerchantLocation;

            return View(locationMerchant);
        }

        public ActionResult BaseTemplatesCopy(int baseTemplateId = 0)
        {
            string baseTemplateName = string.Empty;

            if (baseTemplateId != 0)
            {
                var baseTemplate = _deviceTemplatesService.GetById(baseTemplateId);
                baseTemplateName = baseTemplate?.Name;
            }

            var locationMerchant = new LocationMerchantViewModel
            {
                LocationLevelViewModel = WebUtil.LoadLocationLevel(maxLevel: LocationLevel.DevicesUsed),
                CompanyGroupViewModel = new CompanyGroupPartialViewModel()
            };

            locationMerchant.LocationLevelViewModel.ScreenType = ScreenType.MerchantLocation;
            locationMerchant.DeviceTemplatesModel.Id = baseTemplateId;
            locationMerchant.DeviceTemplatesModel.Name = baseTemplateName;

            return View(locationMerchant);
        }

        public ActionResult BaseTemplatesEdit(int id = 0, int? companyGroupMappingId = null, int? appVersionId = null)
        {
            var baseTemplates = _deviceTemplatesService.GetById(id, includes: p => p.CopyDeviceTemplate)
                ?? new DeviceTemplate() { CompanyGroupMappingId = companyGroupMappingId, AppVersionId = appVersionId };

            var terminalMaster = _terminalService.GetTerminalByTemplateId(baseTemplates.Id);

            var model = new DeviceTemplatesModel(baseTemplates);

            if (terminalMaster != null)
            {
                model.TerminalMasterId = terminalMaster.Id;

                Utils.WebUtils.SetSession(MMS.Web.Utils.Constants.TERMINAL_MASTER_ORG_ID, terminalMaster.Id);
            }

            model.CopyDeviceTemplateName = baseTemplates.CopyDeviceTemplate != null ? baseTemplates.CopyDeviceTemplate.Name : "None";

            SetReturnUrl();

            return View(model);
        }

        [HttpPost]
        public ActionResult BaseTemplatesEdit(DeviceTemplatesModel vModel)
        {
            var entity = _deviceTemplatesService.GetById(vModel.Id) ?? new DeviceTemplate() { CompanyGroupMappingId = vModel.CompanyGroupMappingId, AppVersionId = vModel.AppVersionId, IsActive = true };

            entity = vModel.ToEntity(entity);

            var terminalMasterId = vModel.TerminalMasterId;

            if (entity.Id == 0)
            {
                _deviceTemplatesService.Insert(entity);

                var terminalMaster = new TerminalMaster() { DeviceSetupType = DeviceSetupType.BaseTemplate, CopyDeviceTemplateId = entity.Id };
                _terminalService.Insert(terminalMaster);

                terminalMasterId = terminalMaster.Id;
                terminalMaster.TerminalSerialNumber = $"basetemplate-{terminalMasterId}";
                _terminalService.Update(terminalMaster);
            }
            else
            {
                _deviceTemplatesService.Update(entity);
            }

            WebUtils.UpdateMenuBarAddScreen(Url.Action("BaseTemplatesEdit", "TerminalUpdateTemplate", new { id = entity.Id, companyGroupMappingId = entity.CompanyGroupMappingId, appVersionId = entity.AppVersionId }), Utils.WebUtils.GenerateScreenKey("BaseTemplatesEdit", "TerminalUpdateTemplate"));

            switch (vModel.RedirectUrlEnum)
            {
                case RedirectUrl.BaseTemplatesCopy:
                    SetCustomUrl(Url.Action("BaseTemplatesCopy", "TerminalUpdateTemplate", new { baseTemplateId = entity.Id }));
                    break;

                case RedirectUrl.GUISetup:
                    var redirectUrlTemplate = Url.Action("BaseTemplatesEdit", "TerminalUpdateTemplate", new { id = entity.Id, companyGroupMappingId = entity.CompanyGroupMappingId, appVersionId = entity.AppVersionId });
                    SetCustomUrl(Url.Action("Index", "TerminalSetupGUI", new { id = terminalMasterId, type = Constants.ImmediateUpdate, redirectUrlTemplate = redirectUrlTemplate }));
                    break;

                default:
                    SetCustomUrl(Url.Action("BaseApplicationTemplates", "TerminalUpdateTemplate"));
                    break;
            }

            Utils.WebUtils.SetSession(MMS.Web.Utils.Constants.TERMINAL_MASTER_ORG_ID, terminalMasterId);

            return RedirectLink();
        }

        public async Task<ActionResult> LoadBaseTemplates(int companyGroupMappingId, int currentTemplateId = 0)
        {
            var baseTemplates = await _deviceTemplatesService.GetByAppVersionAsync(companyGroupMappingId);

            if (currentTemplateId != 0)
            {
                baseTemplates = baseTemplates.Where(p => p.Id != currentTemplateId).ToList();
            }

            var vm = baseTemplates.Select(x => new ViewItemModel
            {
                Id = x.Id,
                Label = x.Name,
                IsActive = x.IsActive
            }).ToList();

            return PartialView(PartialViewConstants.ListMultipleItemMappingStandard, vm);
        }

        [HttpPost]
        public async Task<IActionResult> CopyTemplateToTemplate(int srcTemplateId, int destTemplateId)
        {
            var srcTemplate = _deviceTemplatesService.GetById(srcTemplateId);
            if (srcTemplate == null)
                return RedirectError(CommonMessage.Error_NotExistedId);

            var destTemplate = _deviceTemplatesService.GetById(destTemplateId);
            if (destTemplate == null)
                return RedirectError(CommonMessage.Error_NotExistedId);

            var terminalSrcTemplate = _terminalService.GetTerminalByTemplateId(srcTemplateId);
            var terminalDestTemplate = _terminalService.GetTerminalByTemplateId(destTemplateId);

            var terminalSrcId = terminalSrcTemplate.Id;
            terminalSrcTemplate = await _terminalService.GetTerminalMasterForCopy(terminalSrcId, isBaseToBase: true)
                ?? throw new InvalidOperationException($"The source terminal was not found with ID={terminalSrcId}");

            await _terminalService.CopyTemplate(terminalDestTemplate.Id, terminalSrcTemplate);

            var copiedTerminal = await _terminalService.GetTerminalMasterForCopy(terminalDestTemplate.Id, isBaseToBase: true)
                ?? throw new InvalidOperationException($"The copied terminal was not found with ID={terminalDestTemplate.Id}");

            var copyValidationResult = UtilCommon.ValidateTerminalTemplateCopy(terminalSrcTemplate, copiedTerminal);

            if (copyValidationResult.IsValid)
            {
                copyValidationResult.RedirectUrl = Url.Action("BaseTemplatesEdit", "TerminalUpdateTemplate", new { id = destTemplateId });
                destTemplate.CopyTemplateId = srcTemplateId;
                _deviceTemplatesService.Update(destTemplate);
            }

            return Json(copyValidationResult);
        }

        #endregion

        #region Device Templates 
        public ActionResult DeviceTemplates()
        {
            return View(new BaseModel());
        }
        public ActionResult DeviceTemplatesSelect(DeviceTemplateType type, int? parentId = null)
        {
            if (type != DeviceTemplateType.Country && _deviceTemplatesMappingService.GetById(parentId.GetValueOrDefault()) == null)
            {
                return RedirectError(CommonMessage.Error_NotExistedId);
            }

            var vm = new DeviceTemplateSelectModel();

            var listItemSelected = _deviceTemplatesMappingService.GetSelected(type, parentId, getDeactive: true);
            var listItemSource = new List<ViewItemModel>();
            var listItemSelectedIds = new List<int>();


            switch (type)
            {
                case DeviceTemplateType.Country:
                    var countries = _addressLocalItemService.GetAll(includes: p => p.Parent).Where(p => p.TypeId == (int)AddressLocalTypeLevel.Region && p.Parent.IsStatus != Core.CoreUTI.Constants.DELETE_RECORD).ToList();
                    listItemSource = countries.ToViewItemModel().ToList();
                    listItemSelectedIds = listItemSelected.Select(p => p.CountryId.GetValueOrDefault()).ToList();
                    break;
                case DeviceTemplateType.IoTDeviceType:
                    listItemSource = _iOTDeviceTypesService.GetAll(orderBy: p => p.OrderIndex, includeDeactivated: false).ToViewItemModel().ToList();
                    listItemSelectedIds = listItemSelected.Select(p => p.IOTDeviceTypeId.GetValueOrDefault()).ToList();
                    break;
                case DeviceTemplateType.IoTDeviceCategory:
                    listItemSource = _deviceCategoriesService.GetAll(orderBy: p => p.OrderIndex, includeDeactivated: false).ToViewItemModel().ToList();
                    listItemSelectedIds = listItemSelected.Select(p => p.IOTDeviceCategoryId.GetValueOrDefault()).ToList();
                    break;
                case DeviceTemplateType.IoTDevice:
                    listItemSource = _iOTDevicesService.GetAll(orderBy: p => p.OrderIndex, includeDeactivated: false).ToViewItemModel().ToList();
                    listItemSelectedIds = listItemSelected.Select(p => p.IOTDeviceModelId.GetValueOrDefault()).ToList();
                    break;
                default:
                    break;
            }

            vm.SelectFormVM.SourceList.SetData(listItemSource);
            vm.SelectFormVM.Selected = MMS.Core.CoreUTI.UtilConvert.MergeIntToString(listItemSelectedIds);

            vm.ParentId = parentId;
            vm.Type = type;

            SetReturnUrl();

            return View(vm);
        }

        [HttpPost]
        public ActionResult DeviceTemplatesSelect(DeviceTemplateSelectModel vm)
        {
            var selectedIds = MMS.Core.CoreUTI.UtilConvert.SeparateStringToInt(vm.SelectFormVM.Selected);

            var deviceTemplatesMapping = _deviceTemplatesMappingService.GetById(vm.ParentId.GetValueOrDefault()) ?? new DeviceTemplatesMapping();


            if (vm.Type == DeviceTemplateType.Country)
            {
            }
            else
            {
                deviceTemplatesMapping.UseSort = vm.SelectFormVM.UseSelectSort;

                _deviceTemplatesMappingService.Update(deviceTemplatesMapping);
            }

            _deviceTemplatesMappingService.UpdateSelected(selectedIds, vm.Type, vm.ParentId, vm.SelectFormVM.UseSelectSort);

            return RedirectReturnOr(RedirectToAction("DeviceTemplates"));
        }


        public ActionResult LoadDeviceTemplateListItem(DeviceTemplateType type, int? CountryId = null, int? IoTDeviceTypeId = null, int? IoTDeviceCateId = null, string keySearch = "")
        {
            var listItems = new List<ViewItemModel>();
            var listMappingItems = new List<DeviceTemplatesMapping>();

            if (type == DeviceTemplateType.Country)
            {
                listMappingItems = _deviceTemplatesMappingService.GetSelected(type, parentId: null, keySearch: keySearch).ToList();
            }
            else if (type == DeviceTemplateType.IoTDeviceType)
            {
                listMappingItems = _deviceTemplatesMappingService.GetSelected(type, parentId: CountryId, keySearch: keySearch).ToList();
            }
            else if (type == DeviceTemplateType.IoTDeviceCategory)
            {
                listMappingItems = _deviceTemplatesMappingService.GetSelected(type, parentId: IoTDeviceTypeId, keySearch: keySearch).ToList();
            }
            else if (type == DeviceTemplateType.IoTDevice)
            {
                listMappingItems = _deviceTemplatesMappingService.GetSelected(type, parentId: IoTDeviceCateId, keySearch: keySearch).ToList();
            }

            listItems = listMappingItems.ToViewItemModel();

            for (int i = 0; i < listItems.Count(); i++)
            {
                if (type == DeviceTemplateType.Country)
                {
                    listItems[i].OrgId = listMappingItems[i].CountryId.GetValueOrDefault();
                }
                else if (type == DeviceTemplateType.IoTDeviceType)
                {
                    listItems[i].OrgId = listMappingItems[i].IOTDeviceTypeId.GetValueOrDefault();
                }
                else if (type == DeviceTemplateType.IoTDeviceCategory)
                {
                    listItems[i].OrgId = listMappingItems[i].IOTDeviceCategoryId.GetValueOrDefault();
                }
                else if (type == DeviceTemplateType.IoTDevice)
                {
                    listItems[i].OrgId = listMappingItems[i].IOTDeviceModelId.GetValueOrDefault();
                }
            }


            return PartialView(PartialViewConstants.ListMultipleItemMappingStandard, listItems);
        }

        [HttpPost]
        public JsonResult ActivateOrDeactivateDeviceTemplatesMapping(IList<int> ids, bool activate)
        {
            var items = _deviceTemplatesMappingService.GetById(ids, includeDeactivated: true);

            if (items == null)
            {
                return GetFailResult();
            }

            foreach (var item in items)
            {
                item.IsActive = activate;

                _deviceTemplatesMappingService.Update(item);
            }

            return GetSuccessResult();
        }

        [HttpPost]
        public JsonResult ActivateOrDeactivateDeviceTemplates(IList<int> ids, bool activate)
        {
            var items = _deviceTemplatesService.GetById(ids, includeDeactivated: true);

            if (items == null)
            {
                return GetFailResult();
            }

            foreach (var item in items)
            {
                item.IsActive = activate;

                _deviceTemplatesService.Update(item);
            }

            return GetSuccessResult();
        }

        [HttpPost]
        public JsonResult RemoveDeviceTemplates(IList<int> ids)
        {
            var items = _deviceTemplatesService.GetById(ids, includeDeactivated: true);

            if (items == null)
            {
                return GetFailResult();
            }

            foreach (var item in items)
            {
                _deviceTemplatesService.Delete(item);
            }

            return GetSuccessResult();
        }

        #endregion

    }
}
