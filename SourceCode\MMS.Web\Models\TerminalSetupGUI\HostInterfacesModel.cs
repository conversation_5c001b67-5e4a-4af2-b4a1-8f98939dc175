﻿using MMS.Core.Entities.TerminalSetupGUI;
using MMS.Core.CoreUTI;
using MMS.Core.CoreUTI.Enum;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Constants = MMS.Web.Utils.Constants;
using MMS.Core.Entities;
using MMS.Web.Utils;

namespace MMS.Web.Models.TerminalSetupGUI
{
    public class HostInterfacesModel : BaseModel
    {
        public string Name { get; set; }
        public string IconId { get; set; }
        public ProcessorLibrary LibTrainProcessorId { get; set; }
        public bool Enabled { get; set; }
        public bool BalanceInquiry { get; set; }
        public bool Security { get; set; }
        public int BinTableId { get; set; }
        public string StringImage { get; set; }
        public int DaysExpire { get; set; }
        public string StartTime { get; set; }
        public string ExpiryTime { get; set; }
        public int? TerminalMasterId { get; set; }

        public ViewItemsModel HostInterfaces { get; set; }

        public HostInterfacesModel()
        {
        }

        public HostInterfacesModel(IList<ViewItemModel> hostInterfaces) : base()
        {
            HostInterfaces = new ViewItemsModel
            {
                Items = hostInterfaces
            };
        }
    }
    public static class HostInterfacesModelEmm
    {
        /// <summary>
        /// convert entity to model
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public static HostInterfacesModel ToModel(this HostInterfaces entity)
        {
            var result = new HostInterfacesModel();
            if (entity == null)
                return result;
            else
                result = new HostInterfacesModel
                {
                    Id = entity.Id,
                    Name = entity.Name,
                    LibTrainProcessorId = entity.LibTrainProcessorId,
                    Enabled = entity.Enabled,
                    BalanceInquiry = entity.BalanceInquiry,
                    Security = entity.Security,
                    BinTableId = entity.BinTableId ?? 0,
                    DaysExpire = entity.DaysExpire,
                    TerminalMasterId = entity.TerminalMasterId,
                    StartTime = GetTimeInDateTime(entity.StartTime),
                    ExpiryTime = GetTimeInDateTime(entity.ExpiryTime)
                };

            if (!string.IsNullOrWhiteSpace(entity.IconId))
            {
                if (entity.IconId != Constants.PATH_IMAGE_RESOURCE.Replace("~/", "/") + Constants.NO_IMAGE)
                    result.IconId = Constants.PATH_MERCHANT.Replace("~/", "/") + "/" + entity.IconId;
            }
            else
            {
                result.IconId = Constants.PATH_IMAGE_RESOURCE.Replace("~/", "/") + Constants.NO_IMAGE;
            }

            return result;
        }

        /// <summary>
        /// convert entity to model and load data from parent if entity is not yet modified and Parent exists
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        public static HostInterfacesModel ToModelWithParentOverride(this HostInterfaces entity)
        {
            var result = new HostInterfacesModel();
            if (entity == null)
                return result;

            var source = !entity.IsUpdated && entity.ParentId.HasValue ? entity.Parent : entity;

            result = new HostInterfacesModel
            {
                Id = source.Id,
                Name = source.Name,
                LibTrainProcessorId = source.LibTrainProcessorId,
                Enabled = source.Enabled,
                BalanceInquiry = source.BalanceInquiry,
                Security = source.Security,
                BinTableId = source.BinTableId ?? 0,
                DaysExpire = source.DaysExpire,
                TerminalMasterId = source.TerminalMasterId,
                StartTime = GetTimeInDateTime(source.StartTime),
                ExpiryTime = GetTimeInDateTime(source.ExpiryTime),
                IconId = WebUtils.GetImagePath(source.IconId)
            };

            return result;
        }

        /// <summary>
        /// convent list entity HostInterfaces  to list Model
        /// </summary>
        /// <param name="entities"></param>
        /// <returns></returns>
        public static IList<HostInterfacesModel> ToListItemModel(this IList<HostInterfaces> entities)
        {
            return entities.Select(x => x.ToModel()).ToList();
        }

        /// <summary>
        /// convent list entity HostInterfaces  to list Model with override parent
        /// </summary>
        /// <param name="entities"></param>
        /// <returns></returns>
        public static IList<HostInterfacesModel> ToListItemModelWithParentOverride(this IList<HostInterfaces> entities)
        {
            return [.. entities.Select(x => x.ToModelWithParentOverride())];
        }

        public static HostInterfaces ToEntity(this HostInterfacesModel model, HostInterfaces entity,bool isSetUp = false)
        {
            if (!isSetUp)
            {
                entity.Name = model.Name;
                entity.LibTrainProcessorId = model.LibTrainProcessorId;
                entity.Enabled = model.Enabled;
                entity.BalanceInquiry = model.BalanceInquiry;
                entity.Security = model.Security;
                entity.BinTableId = model.BinTableId != 0 ? model.BinTableId : (int?)null;
            }
            else
            {
                entity.DaysExpire = model.DaysExpire;
                entity.StartTime = SetTimeToDateTime(model.StartTime);
                entity.ExpiryTime = SetTimeToDateTime(model.ExpiryTime);
            }

            return entity;
        }

        /// <summary>
        /// get string Time in DateTime
        /// </summary>
        /// <param name="dateTime"></param>
        /// <returns></returns>
        public static string GetTimeInDateTime(DateTime dateTime)
        {
            var timeString = dateTime.TimeOfDay.ToString();

            return timeString;
        }

        /// <summary>
        /// Set Time to DateTime
        /// </summary>
        /// <param name="dateTime"></param>
        /// <param name="time"></param>
        /// <returns></returns>
        public static DateTime SetTimeToDateTime( string time = null)
        {
            return DateTime.Parse(Constants.DefaultDateTimeValue.ToShortDateString() + " " + time);
        }

        public static IList<ViewItemModel> ToListViewItemModel(this IList<HostInterfaces> hostInterfaces)
        {
            if (hostInterfaces.Count == 0)
                return [];

            return [.. hostInterfaces.Select(p => new ViewItemModel
            {
                Id = p.Id,
                Image = p.IconId,
                ImagePath = Constants.PATH_MERCHANT,
                Label = p.Name,
            })];
        }

        /// <summary>
        /// Maps a list of HostInterfaces objects to a new list with updated properties based on the provided terminalMasterId.
        /// This method transforms each item in the input list into a new HostInterfaces object, copying existing properties
        /// </summary>
        /// <param name="selectedList">The input list of HostInterfaces objects to be transformed. Can be null.</param>
        /// <param name="terminalMasterId">The terminal master identifier to be assigned to each new HostInterfaces object.</param>
        /// <returns>A new IList of HostInterfaces objects with the transformed properties. Returns an empty list if the input is null.</returns>
        public static IList<HostInterfaces> MapToHostInterfaces(IList<HostInterfaces> selectedList, int terminalMasterId)
        {
            return [.. selectedList.Select(item => new HostInterfaces
            {
                Name = item.Name,
                TerminalMasterId = terminalMasterId,
                IconId = item.IconId,
                LibTrainProcessorId = item.LibTrainProcessorId,
                Enabled = item.Enabled,
                BalanceInquiry = item.BalanceInquiry,
                Security = item.Security,
                BinTableId = item.BinTableId,
                SettingProcessor = item.SettingProcessor,
                ParentId = item.Id,
                IsActive = true,
                IsStatus = Core.CoreUTI.Constants.PRE_INSERT_RECORD
            })];
        }
    }
}
